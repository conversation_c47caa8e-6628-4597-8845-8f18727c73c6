#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的成本分解测试
"""

import sys
import random
import numpy as np
from alns import *

def simple_test():
    """简单测试成本分解"""
    print("简单成本分解测试")
    print("=" * 40)
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    try:
        # 创建测试实例
        problem = create_deterministic_example_instance()
        print("✅ 问题实例创建成功")
        
        # 生成需求样本
        demand_samples = []
        for _ in range(2):
            sample = {}
            for customer_id in problem.customers:
                expected = problem.expected_demand[customer_id]
                sample[customer_id] = max(0, int(np.random.poisson(expected)))
            demand_samples.append(sample)
        print("✅ 需求样本生成成功")
        
        # 创建ALNS求解器
        alns_solver = ALNS_Solver(problem_instance=problem, demand_samples=demand_samples)
        print("✅ ALNS求解器创建成功")
        
        # 测试解
        test_solution = {
            'y': {1: 1, 2: 1, 3: 0, 4: 1, 5: 0},
            'n': {1: 1, 2: 1, 3: 0, 4: 1, 5: 0}
        }
        print("✅ 测试解定义成功")
        
        # 启发式评估
        print("\n进行启发式评估...")
        heuristic_obj = alns_solver._calculate_objective_heuristic(test_solution, 0)
        print(f"✅ 启发式评估: {heuristic_obj:.2f}")
        
        # 精确评估
        print("\n进行精确评估...")
        exact_obj = alns_solver.calculate_objective_direct(test_solution)
        print(f"✅ 精确评估: {exact_obj:.2f}")
        
        # 误差分析
        error = abs(exact_obj - heuristic_obj)
        error_pct = (error / max(exact_obj, 1e-6)) * 100
        print(f"✅ 评估误差: {error:.2f} ({error_pct:.1f}%)")
        
        # 测试详细成本分解
        print("\n测试详细成本分解...")
        try:
            breakdown = alns_solver._get_detailed_cost_breakdown(test_solution, use_exact=False)
            if breakdown:
                print(f"✅ 启发式成本分解成功:")
                print(f"   第一阶段: {breakdown['first_stage']:.2f}")
                print(f"   第二阶段: {breakdown['second_stage']:.2f}")
                print(f"   卡车成本: {breakdown['truck_cost']:.2f}")
                total = breakdown['first_stage'] + breakdown['second_stage'] + breakdown['truck_cost']
                print(f"   总计: {total:.2f}")
            else:
                print("⚠️ 启发式成本分解返回None")
        except Exception as e:
            print(f"❌ 启发式成本分解失败: {e}")
        
        print("\n🎉 所有测试完成")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    simple_test()
