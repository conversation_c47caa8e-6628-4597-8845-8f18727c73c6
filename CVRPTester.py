# AM模型  添加了2-opt操作(正常运行)

import torch
import os
from logging import getLogger
import matplotlib.pyplot as plt  # For plotting
import numpy as np  # For easier array manipulation for plotting

from CVRPEnv import CVRPEnv as Env
from CVRPModel import CVRPModel as Model
from utils.utils import *  # Make sure this imports AverageMeter and TimeEstimator


# Helper function for plotting (can be outside the class or a static method)
def plot_cvrp_solution(depot_node_xy, route, title, problem_idx, save_dir="plots"):
    """
    Plots a single CVRP solution.
    :param depot_node_xy: Tensor of shape (problem_size+1, 2) with coordinates. First is depot.
    :param route: Tensor of shape (seq_len,) representing the tour. Node 0 is the depot.
    :param title: Title for the plot.
    :param problem_idx: Index of the problem/batch item for unique filenames.
    :param save_dir: Directory to save plots.
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)

    plt.figure(figsize=(10, 10))

    # Detach tensors and move to CPU for numpy conversion
    depot_node_xy_np = depot_node_xy.cpu().numpy()
    route_np = route.cpu().numpy()

    depot_coords = depot_node_xy_np[0]
    customer_coords = depot_node_xy_np[1:]

    # Plot customers
    plt.scatter(customer_coords[:, 0], customer_coords[:, 1], c='skyblue', label='Customers', s=50, zorder=2)
    # Plot depot
    plt.scatter(depot_coords[0], depot_coords[1], c='red', marker='s', s=100, label='Depot', zorder=3)

    # Annotate customer nodes with their indices (1 to N)
    # for i, (x, y) in enumerate(customer_coords):
    #     plt.text(x, y, str(i + 1), fontsize=9, ha='center', va='bottom')

    # Clean up the route: remove trailing zeros used for padding and ensure it forms complete tours
    # Find the last occurrence of a non-zero node if any, then find the next depot visit after it.
    non_zero_indices = np.where(route_np != 0)[0]
    if len(non_zero_indices) > 0:
        last_customer_visit_idx_in_route = non_zero_indices[-1]
        # Find the first depot visit (0) at or after this last customer visit
        depot_after_last_customer = np.where(route_np[last_customer_visit_idx_in_route:] == 0)[0]
        if len(depot_after_last_customer) > 0:
            true_route_len = last_customer_visit_idx_in_route + depot_after_last_customer[0] + 1
            route_np = route_np[:true_route_len]
        else:  # Should not happen in a valid CVRP tour, implies route ends at customer
            route_np = route_np[:last_customer_visit_idx_in_route + 1]  # take up to last customer
    else:  # Route is all zeros (or empty after processing)
        route_np = np.array([0])  # Just the depot

    # Plot routes
    vehicle_colors = ['#e41a1c', '#377eb8', '#4daf4a', '#984ea3', '#ff7f00', '#ffff33', '#a65628', '#f781bf']
    current_vehicle_idx = 0
    sub_route_nodes = []

    for i in range(len(route_np)):
        node_index = route_np[i]
        sub_route_nodes.append(node_index)

        # A sub-route ends when we return to the depot (node 0),
        # or it's the end of the entire route sequence.
        is_end_of_sub_route = (node_index == 0 and len(sub_route_nodes) > 1)
        is_end_of_full_route = (i == len(route_np) - 1)

        if is_end_of_sub_route or (is_end_of_full_route and len(sub_route_nodes) > 1):
            # Ensure the sub-route actually visits customers
            if not all(n == 0 for n in sub_route_nodes) and any(n != 0 for n in sub_route_nodes):
                # Get coordinates for the current sub-route
                path_coords = depot_node_xy_np[sub_route_nodes]

                color = vehicle_colors[current_vehicle_idx % len(vehicle_colors)]
                plt.plot(path_coords[:, 0], path_coords[:, 1], color=color,
                         marker='o', markersize=4, linestyle='-', linewidth=1.5,
                         label=f'Vehicle {current_vehicle_idx + 1}' if not plt.gca().lines else None)  # Avoid duplicate labels

                current_vehicle_idx += 1

            if is_end_of_sub_route and not is_end_of_full_route:  # Reset for next vehicle, starting from depot
                sub_route_nodes = [0]  # Start next sub-route from depot (if it was a depot return)
            else:
                sub_route_nodes = []  # Clear if it was the end of the full route

    plt.title(title)
    plt.xlabel("X-coordinate")
    plt.ylabel("Y-coordinate")

    # Create a unique legend
    handles, labels = plt.gca().get_legend_handles_labels()
    if handles:  # Only show legend if there's something to label
        by_label = dict(zip(labels, handles))
        plt.legend(by_label.values(), by_label.keys(), loc='best')

    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()

    # Sanitize title for filename
    filename_title = title.replace(" ", "_").replace(":", "").replace("/", "")
    save_path = os.path.join(save_dir, f"problem_{problem_idx}_{filename_title}.png")
    plt.savefig(save_path)
    # print(f"Plot saved to {save_path}")
    plt.close()  # Close the figure to free memory


class CVRPTester:
    def __init__(self,
                 env_params,
                 model_params,
                 tester_params):

        # save arguments
        self.env_params = env_params
        self.model_params = model_params
        self.tester_params = tester_params

        # result folder, logger
        self.logger = getLogger(name='trainer')  # Make sure logger is configured elsewhere
        self.result_folder = get_result_folder()
        self.plot_save_dir = os.path.join(self.result_folder, "plots")  # Directory for saving plots
        if self.tester_params.get('make_plots', False) and not os.path.exists(self.plot_save_dir):
            os.makedirs(self.plot_save_dir)

        # cuda
        USE_CUDA = self.tester_params['use_cuda']
        if USE_CUDA:
            cuda_device_num = self.tester_params['cuda_device_num']
            torch.cuda.set_device(cuda_device_num)
            device = torch.device('cuda', cuda_device_num)
            torch.set_default_tensor_type('torch.cuda.FloatTensor')
        else:
            device = torch.device('cpu')
            torch.set_default_tensor_type('torch.FloatTensor')
        self.device = device

        # ENV and MODEL
        self.env = Env(**self.env_params)
        self.model = Model(**self.model_params)

        # Restore
        model_load = tester_params['model_load']
        checkpoint_fullname = '{path}/checkpoint-{epoch}.pt'.format(**model_load)
        checkpoint = torch.load(checkpoint_fullname, map_location=device)
        self.model.load_state_dict(checkpoint['model_state_dict'])

        # utility
        self.time_estimator = TimeEstimator()

    def run(self, save_result=False):
        self.time_estimator.reset()

        score_AM = AverageMeter()
        aug_score_AM = AverageMeter()  # This will be the score after AM but before 2-opt
        opt2_score_AM = AverageMeter()  # This will be the score after 2-opt

        if self.tester_params['test_data_load']['enable']:
            self.env.use_saved_problems(self.tester_params['test_data_load']['filename'], self.device)

        test_num_episode = self.tester_params['test_episodes']
        episode = 0
        
        # For saving results
        all_routes = []
        all_costs = []

        while episode < test_num_episode:
            remaining = test_num_episode - episode
            batch_size = min(self.tester_params['test_batch_size'], remaining)

            # Pass current episode index for plotting (if enabled)
            # The batch_idx inside _test_one_batch will be relative to the current batch (0 to batch_size-1)
            # We need a global episode index for unique plot filenames.
            no_aug_score_batch, aug_score_batch, opt2_score_batch, routes_batch, costs_batch = self._test_one_batch(batch_size, episode)

            score_AM.update(no_aug_score_batch, batch_size)
            aug_score_AM.update(aug_score_batch, batch_size)
            opt2_score_AM.update(opt2_score_batch, batch_size)
            
            # Store routes and costs if save_result is True
            if save_result:
                all_routes.extend(routes_batch)
                all_costs.extend(costs_batch)

            episode += batch_size

            ############################
            # Logs
            ############################
            elapsed_time_str, remain_time_str = self.time_estimator.get_est_string(episode, test_num_episode)
            self.logger.info(
                "episode {:3d}/{:3d}, Elapsed[{}], Remain[{}], score_no_aug:{:.3f}, score_aug:{:.3f}, score_2opt:{:.3f}".format(
                    episode, test_num_episode, elapsed_time_str, remain_time_str, no_aug_score_batch, aug_score_batch,
                    opt2_score_batch))

            all_done = (episode == test_num_episode)

            if all_done:
                self.logger.info(" *** Test Done *** ")
                self.logger.info(" NO-AUG SCORE (AM only, first augmentation): {:.4f} ".format(score_AM.avg))
                self.logger.info(" AUGMENTATION SCORE (Best AM among augmentations): {:.4f} ".format(aug_score_AM.avg))
                self.logger.info(" 2-OPT SCORE (Best AM + 2-opt): {:.4f} ".format(opt2_score_AM.avg))
        
        if save_result:
            return all_routes, all_costs

    def _test_one_batch(self, batch_size, current_episode_offset):  # Added current_episode_offset
        # Augmentation
        ###############################################
        if self.tester_params['augmentation_enable']:
            aug_factor = self.tester_params['aug_factor']
        else:
            aug_factor = 1

        # Ready
        ###############################################
        self.model.eval()
        with torch.no_grad():
            self.env.load_problems(batch_size, aug_factor)
            reset_state, _, _ = self.env.reset()
            self.model.pre_forward(reset_state)

        # POMO Rollout
        ###############################################
        state, reward, done = self.env.pre_step()
        while not done:
            selected = self.model(state)
            if isinstance(selected, tuple):
                selected = selected[0]

            if len(selected.shape) == 3:
                selected = selected.argmax(dim=2)

            state, reward, done = self.env.step(selected)

        # reward shape: (aug_batch_size, pomo_size)
        # aug_batch_size = aug_factor * batch_size

        # Calculate rewards before 2-opt
        ###############################################
        aug_reward_reshaped = reward.reshape(aug_factor, batch_size, self.env.pomo_size)

        # Score for NO Augmentation (uses the first augmentation data, best of its POMO rollouts)
        max_pomo_reward_no_aug, _ = aug_reward_reshaped[0].max(dim=1)  # shape: (batch_size,)
        no_aug_score = -max_pomo_reward_no_aug.float().mean()

        # Score for Augmentation (best over all augmentations and POMO rollouts for each problem instance)
        max_pomo_reward_all_augs, _ = aug_reward_reshaped.max(dim=2)  # shape: (aug_factor, batch_size)
        best_reward_after_aug, best_aug_idx = max_pomo_reward_all_augs.max(dim=0)  # shape: (batch_size,)
        aug_score = -best_reward_after_aug.float().mean()

        # 2-opt improvement
        ###############################################

        # Helper functions for 2-opt (can be nested or class methods if preferred)
        def calculate_distance(route, depot_node_xy_single_instance):
            if len(route.shape) == 1 and len(
                    depot_node_xy_single_instance.shape) > 2:  # Should not happen if passing single instance
                depot_node_xy_single_instance = depot_node_xy_single_instance[0]

            # Ensure route is on the same device as coordinates
            route_on_device = route.to(depot_node_xy_single_instance.device)

            # Remove padding zeros for distance calculation
            # Find the last occurrence of a non-zero node if any, then find the next depot visit after it.
            route_np_temp = route_on_device.cpu().numpy()
            non_zero_indices_temp = np.where(route_np_temp != 0)[0]
            if len(non_zero_indices_temp) > 0:
                last_customer_visit_idx_in_route_temp = non_zero_indices_temp[-1]
                depot_after_last_customer_temp = np.where(route_np_temp[last_customer_visit_idx_in_route_temp:] == 0)[0]
                if len(depot_after_last_customer_temp) > 0:
                    true_route_len_temp = last_customer_visit_idx_in_route_temp + depot_after_last_customer_temp[0] + 1
                    actual_route = route_on_device[:true_route_len_temp]
                else:
                    actual_route = route_on_device[:last_customer_visit_idx_in_route_temp + 1]
            else:
                actual_route = route_on_device[:1]  # Just the depot

            if len(actual_route) <= 1: return torch.tensor(0.0, device=depot_node_xy_single_instance.device)

            points = depot_node_xy_single_instance[actual_route]
            total_dist = torch.norm(points[:-1] - points[1:], dim=-1).sum()
            return total_dist

        def two_opt_move(route, i, j):
            new_route = route.clone()
            new_route[i:j + 1] = route[i:j + 1].flip(dims=[0])  # inclusive j
            return new_route

        def two_opt_single_problem(initial_route, depot_node_xy_single_instance,
                                   max_iterations=self.tester_params.get('2opt_iterations', 200)):
            # initial_route is a 1D tensor
            # depot_node_xy_single_instance is (problem_size+1, 2)

            best_route = initial_route.clone()
            best_distance = calculate_distance(best_route, depot_node_xy_single_instance)

            # Determine actual length of the tour (excluding padding)
            # Find the last occurrence of a non-zero node if any, then find the next depot visit after it.
            route_np_temp = best_route.cpu().numpy()
            non_zero_indices_temp = np.where(route_np_temp != 0)[0]
            if len(non_zero_indices_temp) > 0:
                last_customer_visit_idx_in_route_temp = non_zero_indices_temp[-1]
                depot_after_last_customer_temp = np.where(route_np_temp[last_customer_visit_idx_in_route_temp:] == 0)[0]
                if len(depot_after_last_customer_temp) > 0:
                    route_len = last_customer_visit_idx_in_route_temp + depot_after_last_customer_temp[0] + 1
                else:
                    route_len = last_customer_visit_idx_in_route_temp + 1
            else:
                route_len = 1  # Just the depot

            if route_len <= 3:  # Not enough nodes for 2-opt within a sub-tour
                return best_route, best_distance

            for _ in range(max_iterations):
                improved_in_iter = False
                # Iterate over all possible pairs (i, j) for 2-opt swap
                # We need to respect sub-tours (vehicle routes)
                # Find depot positions to identify sub-tours
                depot_indices = (best_route[:route_len] == 0).nonzero(as_tuple=True)[0]

                current_best_iter_route = best_route.clone()
                current_best_iter_distance = best_distance

                for k_sub in range(len(depot_indices) - 1):
                    start_node_idx = depot_indices[k_sub].item()  # inclusive start of sub-tour segment in best_route
                    end_node_idx = depot_indices[k_sub + 1].item()  # inclusive end of sub-tour segment in best_route

                    # 2-opt is applied between start_node_idx+1 and end_node_idx-1
                    # sub-tour length must be at least 4 (0-c1-c2-0) to do a 2-opt (swap c1, c2)
                    if end_node_idx - start_node_idx < 3:  # e.g., 0-c1-0 length 3.
                        continue

                    for i in range(start_node_idx + 1, end_node_idx - 1):  # node after depot to node before next depot
                        for j in range(i + 1, end_node_idx):  # from i+1 to node before next depot
                            new_route_candidate = two_opt_move(best_route, i, j)  # Pass the full best_route
                            new_dist_candidate = calculate_distance(new_route_candidate, depot_node_xy_single_instance)

                            if new_dist_candidate < current_best_iter_distance:
                                current_best_iter_route = new_route_candidate.clone()
                                current_best_iter_distance = new_dist_candidate
                                improved_in_iter = True

                if improved_in_iter:
                    best_route = current_best_iter_route.clone()
                    best_distance = current_best_iter_distance
                else:
                    break  # No improvement in this iteration, stop

            return best_route, best_distance

        # Apply 2-opt to the best solution from AM+Augmentation for each problem instance
        improved_rewards_after_2opt = torch.zeros(batch_size, device=self.device)

        # Find the pomo_idx corresponding to the best_aug_idx
        # aug_reward_reshaped: (aug_factor, batch_size, pomo_size)
        # best_aug_idx: (batch_size) index of best aug for each batch item
        # We need the pomo index within that best augmentation
        # Gather the rewards for the best augmentation factor for each batch item
        # best_aug_rewards will be (batch_size, pomo_size)
        best_aug_rewards = aug_reward_reshaped.gather(0, best_aug_idx.view(1, batch_size, 1).expand(-1, -1,
                                                                                                    self.env.pomo_size)).squeeze(
            0)
        _, best_pomo_idx_for_best_aug = best_aug_rewards.max(dim=1)  # (batch_size,)

        routes_batch = []
        costs_batch = []

        for b_idx in range(batch_size):
            # Get the specific augmentation factor that was best for this b_idx
            aug_select_idx = best_aug_idx[b_idx].item()
            # Get the specific pomo rollout that was best for this (b_idx, aug_select_idx)
            pomo_select_idx = best_pomo_idx_for_best_aug[b_idx].item()

            # The index in the flat (aug_factor * batch_size, ...) tensors
            flat_idx = aug_select_idx * batch_size + b_idx

            route_before_2opt = self.env.selected_node_list[flat_idx, pomo_select_idx]
            coords_for_problem = self.env.depot_node_xy[flat_idx]  # (problem_size+1, 2)

            improved_route, improved_distance = two_opt_single_problem(
                route_before_2opt, coords_for_problem
            )
            improved_rewards_after_2opt[b_idx] = -improved_distance  # Store negative distance as reward

            if self.tester_params.get('make_plots', False) and b_idx == 0:  # Plot for first item in batch
                plot_cvrp_solution(coords_for_problem, improved_route,
                                   f"2Opt_Ep{current_episode_offset + b_idx}_Cost{-improved_rewards_after_2opt[b_idx].item():.4f}",
                                   current_episode_offset + b_idx, self.plot_save_dir)

            routes_batch.append(improved_route)
            costs_batch.append(-improved_rewards_after_2opt[b_idx].item())

        opt2_score = improved_rewards_after_2opt.float().mean()  # This is already negative mean distance

        return no_aug_score.item(), aug_score.item(), opt2_score.item(), routes_batch, costs_batch

