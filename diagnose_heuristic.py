#!/usr/bin/env python3
"""
诊断启发式评估函数的具体问题
"""

import time
import sys
import os
import math

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def diagnose_heuristic_components():
    """诊断启发式评估函数各组件的准确性"""
    print("="*80)
    print("启发式评估函数组件诊断")
    print("="*80)
    
    try:
        # 导入模块
        from importlib import import_module
        module_3 = import_module('3')
        
        print("✅ 成功导入3.py模块")
        
        # 创建测试实例
        print("\n1. 创建测试实例...")
        instance = module_3.create_deterministic_example_instance(
            num_customers=10,  # 小规模便于分析
            num_sites=5,      
            use_kmeans_clustering=False,
            demand_level="medium",
            locker_cost_level="medium",
            drone_cost_level="medium",
            drone_transport_cost_level="medium"
        )
        
        print(f"   客户数量: {len(instance['customers'])}")
        print(f"   储物柜数量: {len(instance['sites'])}")
        
        # 创建优化器
        optimizer = module_3.StochasticDroneDeliveryOptimizerSAA()
        
        # 修复参数名称
        instance_params = instance.copy()
        if 'demand_deterministic' in instance_params:
            instance_params['expected_demand'] = instance_params.pop('demand_deterministic')
        
        optimizer.set_parameters(**instance_params)
        
        # 生成需求样本
        demand_samples = optimizer._generate_demand_samples(20)
        
        # 创建ALNS求解器
        alns_solver = module_3.ALNS_Solver(optimizer, demand_samples, {})
        
        print("   优化器创建成功")
        
        # 生成测试解
        print("\n2. 生成测试解...")
        test_solution = alns_solver.create_initial_solution()
        if test_solution is None:
            print("❌ 无法生成初始解")
            return
        
        selected_lockers = [j for j, v in test_solution['y'].items() if v > 0.5]
        drone_config = {j: test_solution['n'][j] for j in selected_lockers}
        print(f"   测试解: 储物柜{selected_lockers}, 无人机{drone_config}")
        
        # 分解启发式评估
        print("\n3. 分解启发式评估组件...")
        
        # 第一阶段成本（应该是精确的）
        first_stage_cost = (sum(optimizer.locker_fixed_cost.get(j, 0) for j in selected_lockers) +
                           sum(optimizer.drone_cost * test_solution['n'].get(j, 0) for j in selected_lockers))
        print(f"   第一阶段成本: {first_stage_cost:.2f} 元/天")
        
        # 风险调整需求
        z_score = alns_solver.z_score
        risk_adjusted_demand = {
            i: math.ceil(demand + z_score * math.sqrt(demand))
            for i, demand in optimizer.expected_demand.items()
        }
        
        total_expected = sum(optimizer.expected_demand.values())
        total_risk_adjusted = sum(risk_adjusted_demand.values())
        print(f"   期望需求总量: {total_expected}")
        print(f"   风险调整需求总量: {total_risk_adjusted} (+{(total_risk_adjusted-total_expected)/total_expected*100:.1f}%)")
        
        # 服务成本分解
        transport_cost, penalty_cost, assigned_demand_to_locker = alns_solver._estimate_service_costs(
            test_solution, risk_adjusted_demand
        )
        
        total_assigned = sum(assigned_demand_to_locker.values())
        total_unassigned = total_risk_adjusted - total_assigned
        
        print(f"   运输成本: {transport_cost:.2f} 元/天")
        print(f"   惩罚成本: {penalty_cost:.2f} 元/天")
        print(f"   已分配需求: {total_assigned}")
        print(f"   未分配需求: {total_unassigned}")
        
        # 卡车成本估算
        truck_cost_heuristic = alns_solver._estimate_truck_cost_fast_v2(assigned_demand_to_locker)
        print(f"   启发式卡车成本: {truck_cost_heuristic:.2f} 元/天")
        
        # 启发式总成本
        heuristic_total = first_stage_cost + transport_cost + penalty_cost + truck_cost_heuristic
        print(f"   启发式总成本: {heuristic_total:.2f} 元/天")
        
        # 精确评估分解
        print("\n4. 精确评估分解...")
        exact_total = alns_solver.calculate_objective_cached(test_solution)
        print(f"   精确总成本: {exact_total:.2f} 元/天")
        
        # 获取精确评估的详细分解
        print("   正在获取精确评估的详细分解...")
        
        # 使用期望需求进行精确评估（模拟真实情况）
        exact_transport_cost = 0
        exact_penalty_cost = 0
        exact_truck_cost = 0
        
        # 简化：使用一个代表性场景
        sample_demand = demand_samples[0]  # 使用第一个样本
        
        # 使用FastAssignmentSolver进行分配
        if hasattr(optimizer, 'fast_solver') and optimizer.fast_solver:
            assignment = optimizer.fast_solver.solve_assignment_heuristic(
                test_solution['y'], test_solution['n'], selected_lockers, sample_demand
            )
            
            # 计算运输成本
            for (i, j), quantity in assignment.items():
                if quantity > 0:
                    exact_transport_cost += 2 * optimizer.transport_unit_cost * optimizer.distance.get((i, j), 0) * quantity
            
            # 计算惩罚成本
            assigned_per_customer = {}
            for (i, j), quantity in assignment.items():
                assigned_per_customer[i] = assigned_per_customer.get(i, 0) + quantity
            
            for i, demand in sample_demand.items():
                unassigned = max(0, demand - assigned_per_customer.get(i, 0))
                exact_penalty_cost += unassigned * optimizer.penalty_cost_unassigned
            
            # 计算储物柜需求分布
            exact_locker_demands = {}
            for (i, j), quantity in assignment.items():
                exact_locker_demands[j] = exact_locker_demands.get(j, 0) + quantity
            
            # 使用DRL计算卡车成本
            active_lockers_info = {
                j: {'coord': optimizer.site_coords[j], 'demand': round(demand)}
                for j, demand in exact_locker_demands.items() if demand > 0.5
            }
            
            if active_lockers_info:
                try:
                    exact_truck_cost = optimizer.calculate_truck_cost(
                        [], {}, make_plots=False, active_lockers_info_override=active_lockers_info
                    )
                except:
                    exact_truck_cost = truck_cost_heuristic  # 回退到启发式估算
        
        exact_second_stage = exact_transport_cost + exact_penalty_cost + exact_truck_cost
        
        print(f"   精确运输成本: {exact_transport_cost:.2f} 元/天")
        print(f"   精确惩罚成本: {exact_penalty_cost:.2f} 元/天")
        print(f"   精确卡车成本: {exact_truck_cost:.2f} 元/天")
        print(f"   精确第二阶段成本: {exact_second_stage:.2f} 元/天")
        
        # 组件误差分析
        print("\n5. 组件误差分析...")
        
        transport_error = abs(transport_cost - exact_transport_cost) / max(exact_transport_cost, 1) * 100
        penalty_error = abs(penalty_cost - exact_penalty_cost) / max(exact_penalty_cost, 1) * 100
        truck_error = abs(truck_cost_heuristic - exact_truck_cost) / max(exact_truck_cost, 1) * 100
        
        print(f"   运输成本误差: {transport_error:.1f}%")
        print(f"   惩罚成本误差: {penalty_error:.1f}%")
        print(f"   卡车成本误差: {truck_error:.1f}%")
        
        # 找出主要误差来源
        max_error = max(transport_error, penalty_error, truck_error)
        if max_error == transport_error:
            main_error_source = "运输成本"
        elif max_error == penalty_error:
            main_error_source = "惩罚成本"
        else:
            main_error_source = "卡车成本"
        
        print(f"   主要误差来源: {main_error_source} ({max_error:.1f}%)")
        
        # 总误差
        total_error = abs(heuristic_total - exact_total) / exact_total * 100
        print(f"   总体误差: {total_error:.1f}%")
        
        # 改进建议
        print("\n6. 改进建议...")
        if truck_error > 20:
            print("   🔧 卡车成本估算误差较大，建议:")
            print("     - 改进TSP近似算法")
            print("     - 使用更准确的距离计算")
            print("     - 考虑卡车容量约束")
        
        if penalty_error > 20:
            print("   🔧 惩罚成本估算误差较大，建议:")
            print("     - 检查需求分配逻辑")
            print("     - 调整风险调整系数")
            print("     - 改进容量约束建模")
        
        if transport_error > 20:
            print("   🔧 运输成本估算误差较大，建议:")
            print("     - 检查距离计算")
            print("     - 改进客户分配算法")
            print("     - 调整拥堵成本模型")
        
        print("\n" + "="*80)
        print("诊断完成")
        print("="*80)
        
    except Exception as e:
        print(f"❌ 诊断失败: {str(e)}")
        import traceback
        print(f"错误详情:\n{traceback.format_exc()}")

if __name__ == "__main__":
    diagnose_heuristic_components()
