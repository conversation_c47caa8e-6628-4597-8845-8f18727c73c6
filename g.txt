D:\download\Anaconda\envs\pytorch\python.exe E:\代码\self\对比\gurobi_SAA\g_i.py
设置全局随机种子: 602
聚类功能可用

创建随机需求的示例数据 (使用期望需求)...
============================================================
[g_i.py] 成本计算方法改进：统一时间单位
============================================================
修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用
修正后方案：使用资本回收因子将所有固定成本统一为日成本单位
优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差
============================================================
  [g_i.py] 成本计算参数:
    年利率 (IR): 4.0%
    设备生命周期 (T_life): 10年
    资本回收因子: 0.123291
    年运营天数: 365天
  [g_i.py] 储物柜成本转换:
    初始建设成本: 15,000元
    年化成本: 1,849.36元/年
    日均固定成本: 5.07元/天
  [g_i.py] 无人机成本转换:
    初始采购成本: 4,000元
    年化成本: 493.16元/年
    日均固定成本: 1.35元/天

  [g_i.py] 成本单位统一性检查:
    储物柜固定成本: 5.07 元/天
    无人机固定成本: 1.35 元/天
    无人机运输成本: 0.020 元/公里 (按实际运输量)
    卡车固定成本: 100 元/天
    卡车运输成本: 0.5 元/公里 (按实际运输量)
    ✓ 所有固定成本已统一为日成本单位
    ✓ 运输成本保持按实际使用量计费
随机需求数据 (期望值) 已创建。

所有客户期望需求 (λᵢ_bar):
  客户 1: 2 订单/天 (期望)
  客户 2: 3 订单/天 (期望)
  客户 3: 2 订单/天 (期望)
  客户 4: 2 订单/天 (期望)
  客户 5: 3 订单/天 (期望)
  客户 6: 4 订单/天 (期望)
  客户 7: 4 订单/天 (期望)
  客户 8: 3 订单/天 (期望)
  客户 9: 2 订单/天 (期望)
  客户 10: 3 订单/天 (期望)
  客户 11: 3 订单/天 (期望)
  客户 12: 3 订单/天 (期望)
  客户 13: 2 订单/天 (期望)
  客户 14: 2 订单/天 (期望)
  客户 15: 4 订单/天 (期望)
总期望需求: 42 订单/天
参数设置完成: 15个客户, 4个候选站点, 最大1辆卡车

============================================================
求解带随机需求的无人机配送网络设计问题 (SAA)
============================================================

求解参数设置:
  每次复制时间限制: 3600 秒 (1小时)
  MIP Gap容忍度: 2% (0.02)
  注意: 如果求解状态显示TIME_LIMIT，建议增加时间限制

开始SAA整体优化，最多 10 次复制...
SAA终止条件 (必须同时满足):
  1. 相对差距阈值: Gap/UB ≤ 3% 且 Gap ≥ 0
  2. 方差阈值: δ²_Gap/UB ≤ 5%
  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)
  最少需要 2 次有效复制
生成 2000 个固定验证样本 (所有复制共用)...
  验证样本生成完成，第一个样本总需求: 38.0
  前3个客户在第一个样本中的需求: [1.0, 1.0, 3.0]

--- SAA 复制 1/10 ---
  已生成 40 个需求场景用于求解。
  调试：前3个场景的客户1需求: [1.0, 1.0, 0.0]
  调试：第1个场景总需求: 43.0
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 1 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 1 求解完成，耗时: 115.78 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 139.47
    最佳界限: 128.62
    MIP Gap: 7.78%
    ✓ 找到最优解
  复制 1 在 40 个样本上的目标值: 139.47
  评估复制 1 的解在 2000 个固定验证样本上的性能...
  复制 1 在 2000 个样本上的平均目标值 (UB估计): 139.65
  复制 1 在 2000 个样本上的平均卡车成本: 115.98

--- SAA 复制 2/10 ---
  已生成 40 个需求场景用于求解。
  调试：前3个场景的客户1需求: [2.0, 2.0, 5.0]
  调试：第1个场景总需求: 42.0
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 2 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 2 求解完成，耗时: 233.38 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 130.98
    最佳界限: 117.88
    MIP Gap: 10.00%
    ✓ 找到最优解
  复制 2 在 40 个样本上的目标值: 130.98
  评估复制 2 的解在 2000 个固定验证样本上的性能...
  复制 2 在 2000 个样本上的平均目标值 (UB估计): 131.21
  复制 2 在 2000 个样本上的平均卡车成本: 112.58
  当前SAA状态: 相对差距: -3.06% ✗ (阈值: ≤3%), 方差比例: 13.75% ✗ (阈值: ≤5%), LB(m=2): 135.23, UB: 131.21, Gap: -4.02, δ²_Gap: 18.0470

--- SAA 复制 3/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 3 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 3 求解完成，耗时: 674.08 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 130.77
    最佳界限: 117.71
    MIP Gap: 9.99%
    ✓ 找到最优解
  复制 3 在 40 个样本上的目标值: 130.77
  评估复制 3 的解在 2000 个固定验证样本上的性能...
  复制 3 在 2000 个样本上的平均目标值 (UB估计): 131.21
  复制 3 在 2000 个样本上的平均卡车成本: 112.58
  当前SAA状态: 相对差距: -1.93% ✗ (阈值: ≤3%), 方差比例: 6.27% ✗ (阈值: ≤5%), LB(m=3): 133.74, UB: 131.21, Gap: -2.53, δ²_Gap: 8.2223

--- SAA 复制 4/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 4 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 4 求解完成，耗时: 124.91 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 140.01
    最佳界限: 129.25
    MIP Gap: 7.68%
    ✓ 找到最优解
  复制 4 在 40 个样本上的目标值: 140.01
  评估复制 4 的解在 2000 个固定验证样本上的性能...
  复制 4 在 2000 个样本上的平均目标值 (UB估计): 139.65
  复制 4 在 2000 个样本上的平均卡车成本: 115.98
  当前SAA状态: 相对差距: -3.13% ✗ (阈值: ≤3%), 方差比例: 5.01% ✗ (阈值: ≤5%), LB(m=4): 135.31, UB: 131.21, Gap: -4.10, δ²_Gap: 6.5690

--- SAA 复制 5/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 5 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 5 求解完成，耗时: 101.55 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 139.48
    最佳界限: 127.42
    MIP Gap: 8.64%
    ✓ 找到最优解
  复制 5 在 40 个样本上的目标值: 139.48
  评估复制 5 的解在 2000 个固定验证样本上的性能...
  复制 5 在 2000 个样本上的平均目标值 (UB估计): 138.88
  复制 5 在 2000 个样本上的平均卡车成本: 116.02
  当前SAA状态: 相对差距: -3.76% ✗ (阈值: ≤3%), 方差比例: 3.54% ✓ (阈值: ≤5%), LB(m=5): 136.14, UB: 131.21, Gap: -4.93, δ²_Gap: 4.6395

--- SAA 复制 6/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 6 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 6 求解完成，耗时: 220.92 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 130.78
    最佳界限: 117.78
    MIP Gap: 9.94%
    ✓ 找到最优解
  复制 6 在 40 个样本上的目标值: 130.78
  评估复制 6 的解在 2000 个固定验证样本上的性能...
  复制 6 在 2000 个样本上的平均目标值 (UB估计): 131.21
  复制 6 在 2000 个样本上的平均卡车成本: 112.58
  当前SAA状态: 相对差距: -3.08% ✗ (阈值: ≤3%), 方差比例: 2.97% ✓ (阈值: ≤5%), LB(m=6): 135.25, UB: 131.21, Gap: -4.04, δ²_Gap: 3.8953

--- SAA 复制 7/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 7 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 7 求解完成，耗时: 406.56 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 131.16
    最佳界限: 118.06
    MIP Gap: 9.99%
    ✓ 找到最优解
  复制 7 在 40 个样本上的目标值: 131.16
  评估复制 7 的解在 2000 个固定验证样本上的性能...
  复制 7 在 2000 个样本上的平均目标值 (UB估计): 131.21
  复制 7 在 2000 个样本上的平均卡车成本: 112.58
  当前SAA状态: 相对差距: -2.63% ✗ (阈值: ≤3%), 方差比例: 2.38% ✓ (阈值: ≤5%), LB(m=7): 134.66, UB: 131.21, Gap: -3.46, δ²_Gap: 3.1252

--- SAA 复制 8/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 8 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 8 求解完成，耗时: 734.54 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 130.82
    最佳界限: 117.74
    MIP Gap: 10.00%
    ✓ 找到最优解
  复制 8 在 40 个样本上的目标值: 130.82
  评估复制 8 的解在 2000 个固定验证样本上的性能...
  复制 8 在 2000 个样本上的平均目标值 (UB估计): 131.21
  复制 8 在 2000 个样本上的平均卡车成本: 112.58
  当前SAA状态: 相对差距: -2.27% ✗ (阈值: ≤3%), 方差比例: 1.96% ✓ (阈值: ≤5%), LB(m=8): 134.18, UB: 131.21, Gap: -2.98, δ²_Gap: 2.5766

--- SAA 复制 9/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 9 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 9 求解完成，耗时: 245.83 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 130.83
    最佳界限: 117.77
    MIP Gap: 9.99%
    ✓ 找到最优解
  复制 9 在 40 个样本上的目标值: 130.83
  评估复制 9 的解在 2000 个固定验证样本上的性能...
  复制 9 在 2000 个样本上的平均目标值 (UB估计): 131.21
  复制 9 在 2000 个样本上的平均卡车成本: 112.58
  当前SAA状态: 相对差距: -1.98% ✗ (阈值: ≤3%), 方差比例: 1.63% ✓ (阈值: ≤5%), LB(m=9): 133.81, UB: 131.21, Gap: -2.60, δ²_Gap: 2.1439

--- SAA 复制 10/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 10 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 10 求解完成，耗时: 205.31 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 130.54
    最佳界限: 117.77
    MIP Gap: 9.78%
    ✓ 找到最优解
  复制 10 在 40 个样本上的目标值: 130.54
  评估复制 10 的解在 2000 个固定验证样本上的性能...
  复制 10 在 2000 个样本上的平均目标值 (UB估计): 131.21
  复制 10 在 2000 个样本上的平均卡车成本: 112.58
  当前SAA状态: 相对差距: -1.74% ✗ (阈值: ≤3%), 方差比例: 1.39% ✓ (阈值: ≤5%), LB(m=10): 133.48, UB: 131.21, Gap: -2.28, δ²_Gap: 1.8235

📊 SAA 统计结果汇总 (10 次有效复制，使用Gurobi)
============================================================
  下界估计 cost_N^m: 133.48 元/天
    ↳ 计算方法: 前10次复制的小样本优化成本的算术平均
    ↳ 含义: 系统真实期望成本的下界估计
    - 下界标准差: 4.04
    - 下界方差 δ²(cost_N): 0.1816
  上界估计 cost_2000(ŝ): 133.66 元/天
    ↳ 计算方法: 最佳解在2000个大样本场景下的平均成本
    ↳ 含义: 最佳方案长期运营的期望日均成本
    - 上界标准差: 3.76
  SAA Gap: 0.18 元/天 (0.1%)
  ⏱️ 求解时间统计:
    SAA总求解时间: 3256.02 秒
    平均每次复制求解时间: 306.29 秒
    复制求解总时间: 3062.86 秒
    验证评估时间: 193.17 秒

🏆 最佳解详情 (来自复制 2)
  总成本: 131.21 元/天
  开放储物柜: 2 个
  成本构成: 储物柜 10.13 + 无人机(部署+运输) 8.27 + 卡车(固定+运输) 112.58 + 惩罚 0.00

============================================================
SAA 优化结果 (Gurobi精确求解)
============================================================
  正在使用精确求解结果进行成本分解（基于2000个验证样本）...
    场景1: 总需求=38.0, 运输成本=4.91, 惩罚成本=0.00
    场景2: 总需求=42.0, 运输成本=5.12, 惩罚成本=0.00
    场景3: 总需求=56.0, 运输成本=8.61, 惩罚成本=0.00
    已处理 500/2000 个场景...
    已处理 1000/2000 个场景...
    已处理 1500/2000 个场景...
    已处理 2000/2000 个场景...
  精确计算结果: 无人机运输成本=5.58, 惩罚成本=0.22 (基于2000个有效场景)
  📊 核心指标:
    开放储物柜数量: 2 个
    总成本: 131.21 元/天
    无人机成本(部署+运输): 8.28 元/天 (6.3%)
    卡车成本(固定+运输): 112.58 元/天 (85.8%)

  💰 详细成本分解:
    储物柜固定成本: 10.13 元/天 (7.7%)
    无人机成本(部署+运输): 8.28 元/天 (6.3%)
    卡车成本(固定+运输): 112.58 元/天 (85.8%)
    其他成本(惩罚): 0.22 元/天 (0.2%)

  🏪 储物柜配置:
    选定站点: [2, 3]
    位置 2: 1 架无人机
    位置 3: 1 架无人机
    无人机总数: 2 架

  📈 运营指标:
    总期望需求量: 42.00 订单/天
    平均每储物柜服务: 21.00 订单/天
    平均每无人机服务: 21.00 订单/天

  🎯 求解质量:
    总复制次数: 10
    达到最优解的复制: 10 (100.0%)
    ✓ 所有复制都找到了最优解，结果可信度高

  📝 模型说明:
    第一阶段决策：储物柜选址和无人机配置（已确定）
    第二阶段决策：根据实际需求场景动态优化客户分配和配送
    成本为考虑需求不确定性后的期望日均成本

  显示SAA最终解的可视化:
  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）
  ↳ 客户分配将在第二阶段根据实际需求场景动态优化
  ↳ 上述成本是考虑需求不确定性后的期望值

SAA模型求解总耗时: 3256.31 秒

总运行时间: 3264.38 秒
测试完成。如果图像窗口仍然打开，请手动关闭。

进程已结束,退出代码0
