D:\download\Anaconda\envs\pytorch\python.exe E:\代码\self\对比\gurobi_SAA\g_i.py
设置全局随机种子: 616
聚类功能可用

创建随机需求的示例数据 (使用期望需求)...
============================================================
[g_i.py] 成本计算方法改进：统一时间单位
============================================================
修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用
修正后方案：使用资本回收因子将所有固定成本统一为日成本单位
优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差
============================================================
  [g_i.py] 成本计算参数:
    年利率 (IR): 4.0%
    设备生命周期 (T_life): 10年
    资本回收因子: 0.123291
    年运营天数: 365天
  [g_i.py] 储物柜成本转换:
    初始建设成本: 15,000元
    年化成本: 1,849.36元/年
    日均固定成本: 5.07元/天
  [g_i.py] 无人机成本转换:
    初始采购成本: 4,000元
    年化成本: 493.16元/年
    日均固定成本: 1.35元/天

  [g_i.py] 成本单位统一性检查:
    储物柜固定成本: 5.07 元/天
    无人机固定成本: 1.35 元/天
    无人机运输成本: 0.020 元/公里 (按实际运输量)
    卡车固定成本: 100 元/天
    卡车运输成本: 0.5 元/公里 (按实际运输量)
    ✓ 所有固定成本已统一为日成本单位
    ✓ 运输成本保持按实际使用量计费
随机需求数据 (期望值) 已创建。

所有客户期望需求 (λᵢ_bar):
  客户 1: 3 订单/天 (期望)
  客户 2: 4 订单/天 (期望)
  客户 3: 2 订单/天 (期望)
  客户 4: 2 订单/天 (期望)
  客户 5: 3 订单/天 (期望)
  客户 6: 3 订单/天 (期望)
  客户 7: 2 订单/天 (期望)
  客户 8: 2 订单/天 (期望)
  客户 9: 3 订单/天 (期望)
  客户 10: 2 订单/天 (期望)
  客户 11: 2 订单/天 (期望)
  客户 12: 3 订单/天 (期望)
  客户 13: 4 订单/天 (期望)
  客户 14: 4 订单/天 (期望)
  客户 15: 4 订单/天 (期望)
总期望需求: 43 订单/天
参数设置完成: 15个客户, 4个候选站点, 最大1辆卡车

============================================================
求解带随机需求的无人机配送网络设计问题 (SAA)
============================================================

求解参数设置:
  每次复制时间限制: 3600 秒 (1小时)
  MIP Gap容忍度: 2% (0.02)
  注意: 如果求解状态显示TIME_LIMIT，建议增加时间限制

开始SAA整体优化，最多 10 次复制...
SAA终止条件 (必须同时满足):
  1. 相对差距阈值: Gap/UB ≤ 3% 且 Gap ≥ 0
  2. 方差阈值: δ²_Gap/UB ≤ 5%
  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)
  最少需要 2 次有效复制
生成 2000 个固定验证样本 (所有复制共用)...
  验证样本生成完成，第一个样本总需求: 40.0
  前3个客户在第一个样本中的需求: [3.0, 1.0, 3.0]

--- SAA 复制 1/10 ---
  已生成 40 个需求场景用于求解。
  调试：前3个场景的客户1需求: [3.0, 5.0, 1.0]
  调试：第1个场景总需求: 41.0
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 1 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 1 求解完成，耗时: 126.24 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 147.98
    最佳界限: 136.76
    MIP Gap: 7.58%
    ✓ 找到最优解
  复制 1 在 40 个样本上的目标值: 147.98
  评估复制 1 的解在 2000 个固定验证样本上的性能...
  复制 1 在 2000 个样本上的平均目标值 (UB估计): 143.58
  复制 1 在 2000 个样本上的平均卡车成本: 119.54

--- SAA 复制 2/10 ---
  已生成 40 个需求场景用于求解。
  调试：前3个场景的客户1需求: [2.0, 2.0, 4.0]
  调试：第1个场景总需求: 43.0
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 2 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 2 求解完成，耗时: 122.01 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 152.59
    最佳界限: 137.71
    MIP Gap: 9.75%
    ✓ 找到最优解
  复制 2 在 40 个样本上的目标值: 152.59
  评估复制 2 的解在 2000 个固定验证样本上的性能...
  复制 2 在 2000 个样本上的平均目标值 (UB估计): 148.79
  复制 2 在 2000 个样本上的平均卡车成本: 119.55
  当前SAA状态: 相对差距: -4.67% ✗ (阈值: ≤3%), 方差比例: 3.71% ✓ (阈值: ≤5%), LB(m=2): 150.28, UB: 143.58, Gap: -6.71, δ²_Gap: 5.3277

--- SAA 复制 3/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 3 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 3 求解完成，耗时: 117.58 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 143.69
    最佳界限: 138.41
    MIP Gap: 3.68%
    ✓ 找到最优解
  复制 3 在 40 个样本上的目标值: 143.69
  评估复制 3 的解在 2000 个固定验证样本上的性能...
  复制 3 在 2000 个样本上的平均目标值 (UB估计): 143.58
  复制 3 在 2000 个样本上的平均卡车成本: 119.54
  当前SAA状态: 相对差距: -3.14% ✗ (阈值: ≤3%), 方差比例: 4.60% ✓ (阈值: ≤5%), LB(m=3): 148.09, UB: 143.58, Gap: -4.51, δ²_Gap: 6.6053

--- SAA 复制 4/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 4 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 4 求解完成，耗时: 107.10 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 148.49
    最佳界限: 133.96
    MIP Gap: 9.78%
    ✓ 找到最优解
  复制 4 在 40 个样本上的目标值: 148.49
  评估复制 4 的解在 2000 个固定验证样本上的性能...
  复制 4 在 2000 个样本上的平均目标值 (UB估计): 147.54
  复制 4 在 2000 个样本上的平均卡车成本: 116.54
  当前SAA状态: 相对差距: -3.21% ✗ (阈值: ≤3%), 方差比例: 2.31% ✓ (阈值: ≤5%), LB(m=4): 148.19, UB: 143.58, Gap: -4.61, δ²_Gap: 3.3141

--- SAA 复制 5/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 5 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 5 求解完成，耗时: 125.22 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 152.84
    最佳界限: 138.60
    MIP Gap: 9.32%
    ✓ 找到最优解
  复制 5 在 40 个样本上的目标值: 152.84
  评估复制 5 的解在 2000 个固定验证样本上的性能...
  复制 5 在 2000 个样本上的平均目标值 (UB估计): 148.79
  复制 5 在 2000 个样本上的平均卡车成本: 119.55
  当前SAA状态: 相对差距: -3.86% ✗ (阈值: ≤3%), 方差比例: 1.99% ✓ (阈值: ≤5%), LB(m=5): 149.12, UB: 143.58, Gap: -5.54, δ²_Gap: 2.8556

--- SAA 复制 6/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 6 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 6 求解完成，耗时: 109.20 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 147.31
    最佳界限: 132.64
    MIP Gap: 9.96%
    ✓ 找到最优解
  复制 6 在 40 个样本上的目标值: 147.31
  评估复制 6 的解在 2000 个固定验证样本上的性能...
  复制 6 在 2000 个样本上的平均目标值 (UB估计): 147.54
  复制 6 在 2000 个样本上的平均卡车成本: 116.54
  当前SAA状态: 相对差距: -3.65% ✗ (阈值: ≤3%), 方差比例: 1.39% ✓ (阈值: ≤5%), LB(m=6): 148.82, UB: 143.58, Gap: -5.24, δ²_Gap: 1.9954

--- SAA 复制 7/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 7 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 7 求解完成，耗时: 160.03 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 151.76
    最佳界限: 136.74
    MIP Gap: 9.90%
    ✓ 找到最优解
  复制 7 在 40 个样本上的目标值: 151.76
  评估复制 7 的解在 2000 个固定验证样本上的性能...
  复制 7 在 2000 个样本上的平均目标值 (UB估计): 148.79
  复制 7 在 2000 个样本上的平均卡车成本: 119.55
  当前SAA状态: 相对差距: -3.94% ✗ (阈值: ≤3%), 方差比例: 1.12% ✓ (阈值: ≤5%), LB(m=7): 149.24, UB: 143.58, Gap: -5.66, δ²_Gap: 1.6022

--- SAA 复制 8/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 8 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 8 求解完成，耗时: 120.49 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 153.23
    最佳界限: 138.25
    MIP Gap: 9.78%
    ✓ 找到最优解
  复制 8 在 40 个样本上的目标值: 153.23
  评估复制 8 的解在 2000 个固定验证样本上的性能...
  复制 8 在 2000 个样本上的平均目标值 (UB估计): 148.79
  复制 8 在 2000 个样本上的平均卡车成本: 119.55
  当前SAA状态: 相对差距: -4.29% ✗ (阈值: ≤3%), 方差比例: 1.01% ✓ (阈值: ≤5%), LB(m=8): 149.74, UB: 143.58, Gap: -6.16, δ²_Gap: 1.4514

--- SAA 复制 9/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 9 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 9 求解完成，耗时: 121.76 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 153.34
    最佳界限: 138.12
    MIP Gap: 9.93%
    ✓ 找到最优解
  复制 9 在 40 个样本上的目标值: 153.34
  评估复制 9 的解在 2000 个固定验证样本上的性能...
  复制 9 在 2000 个样本上的平均目标值 (UB估计): 151.15
  复制 9 在 2000 个样本上的平均卡车成本: 116.55
  当前SAA状态: 相对差距: -4.57% ✗ (阈值: ≤3%), 方差比例: 0.90% ✓ (阈值: ≤5%), LB(m=9): 150.14, UB: 143.58, Gap: -6.56, δ²_Gap: 1.2905

--- SAA 复制 10/10 ---
  已生成 40 个需求场景用于求解。
Set parameter TimeLimit to value 3600
Set parameter MIPGap to value 0.1
  开始求解复制 10 的SAA模型...
    模型统计: 4008 个变量, 7245 个约束
  复制 10 求解完成，耗时: 100.46 秒，状态: 2 (最优解 (OPTIMAL))
    找到解的数量: 10
    最佳目标值: 151.76
    最佳界限: 136.60
    MIP Gap: 9.99%
    ✓ 找到最优解
  复制 10 在 40 个样本上的目标值: 151.76
  评估复制 10 的解在 2000 个固定验证样本上的性能...
  复制 10 在 2000 个样本上的平均目标值 (UB估计): 147.54
  复制 10 在 2000 个样本上的平均卡车成本: 116.54
  当前SAA状态: 相对差距: -4.68% ✗ (阈值: ≤3%), 方差比例: 0.74% ✓ (阈值: ≤5%), LB(m=10): 150.30, UB: 143.58, Gap: -6.72, δ²_Gap: 1.0591

📊 SAA 统计结果汇总 (10 次有效复制，使用Gurobi)
============================================================
  下界估计 cost_N^m: 150.30 元/天
    ↳ 计算方法: 前10次复制的小样本优化成本的算术平均
    ↳ 含义: 系统真实期望成本的下界估计
    - 下界标准差: 3.08
    - 下界方差 δ²(cost_N): 0.1056
  上界估计 cost_2000(ŝ): 147.61 元/天
    ↳ 计算方法: 最佳解在2000个大样本场景下的平均成本
    ↳ 含义: 最佳方案长期运营的期望日均成本
    - 上界标准差: 2.25
  SAA Gap: -2.69 元/天 (-1.8%)
  ⏱️ 求解时间统计:
    SAA总求解时间: 1956.18 秒
    平均每次复制求解时间: 121.01 秒
    复制求解总时间: 1210.08 秒
    验证评估时间: 746.10 秒

🏆 最佳解详情 (来自复制 1)
  总成本: 143.58 元/天
  开放储物柜: 3 个
  成本构成: 储物柜 15.20 + 无人机(部署+运输) 8.74 + 卡车(固定+运输) 119.54 + 惩罚 0.00

============================================================
SAA 优化结果 (Gurobi精确求解)
============================================================
  正在使用精确求解结果进行成本分解（基于2000个验证样本）...
    场景1: 总需求=40.0, 运输成本=4.53, 惩罚成本=0.00
    场景2: 总需求=33.0, 运输成本=3.78, 惩罚成本=0.00
    场景3: 总需求=36.0, 运输成本=3.97, 惩罚成本=0.00
    已处理 500/2000 个场景...
    已处理 1000/2000 个场景...
    已处理 1500/2000 个场景...
    已处理 2000/2000 个场景...
  精确计算结果: 无人机运输成本=4.79, 惩罚成本=0.00 (基于2000个有效场景)
  📊 核心指标:
    开放储物柜数量: 3 个
    总成本: 143.58 元/天
    无人机成本(部署+运输): 8.84 元/天 (6.2%)
    卡车成本(固定+运输): 119.54 元/天 (83.3%)

  💰 详细成本分解:
    储物柜固定成本: 15.20 元/天 (10.6%)
    无人机成本(部署+运输): 8.84 元/天 (6.2%)
    卡车成本(固定+运输): 119.54 元/天 (83.3%)
    其他成本(惩罚): 0.00 元/天 (0.0%)

  🏪 储物柜配置:
    选定站点: [2, 3, 4]
    位置 2: 1 架无人机
    位置 3: 1 架无人机
    位置 4: 1 架无人机
    无人机总数: 3 架

  📈 运营指标:
    总期望需求量: 43.00 订单/天
    平均每储物柜服务: 14.33 订单/天
    平均每无人机服务: 14.33 订单/天

  🎯 求解质量:
    总复制次数: 10
    达到最优解的复制: 10 (100.0%)
    ✓ 所有复制都找到了最优解，结果可信度高

  📝 模型说明:
    第一阶段决策：储物柜选址和无人机配置（已确定）
    第二阶段决策：根据实际需求场景动态优化客户分配和配送
    成本为考虑需求不确定性后的期望日均成本

  显示SAA最终解的可视化:
  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）
  ↳ 客户分配将在第二阶段根据实际需求场景动态优化
  ↳ 上述成本是考虑需求不确定性后的期望值

SAA模型求解总耗时: 1958.19 秒

总运行时间: 2000.35 秒
测试完成。如果图像窗口仍然打开，请手动关闭。

进程已结束,退出代码0
