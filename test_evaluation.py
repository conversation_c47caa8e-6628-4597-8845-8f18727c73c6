#!/usr/bin/env python3
"""
测试修复后的ALNS评估函数
验证是否能正确识别[1,2,3]方案的优势
"""

import sys
import os
import time
import numpy as np

# 导入ALNS模块
from alns import *

def test_evaluation_accuracy():
    """测试评估函数的准确性"""
    print("=" * 60)
    print("测试修复后的ALNS评估函数")
    print("=" * 60)
    
    # 创建问题实例
    problem_data = create_deterministic_example_instance(
        num_customers=15,
        num_sites=4,
        use_kmeans_clustering=False,
        demand_level="medium",
        random_seed=611
    )
    
    # 创建问题对象
    problem = ProblemInstance()
    problem.set_parameters(**problem_data)
    
    # 生成需求样本
    demand_samples = []
    for _ in range(40):  # 使用40个样本，与ALNS一致
        scenario = {}
        for i in problem.customers:
            scenario[i] = np.random.poisson(problem.expected_demand[i])
        demand_samples.append(scenario)
    
    # 创建ALNS求解器
    alns_solver = ALNS_Solver(problem, demand_samples)
    
    # 测试不同方案的评估
    test_solutions = [
        {'name': '[1,2]', 'y': {1: 1, 2: 1, 3: 0, 4: 0}, 'n': {1: 1, 2: 1, 3: 0, 4: 0}},
        {'name': '[1,3]', 'y': {1: 1, 2: 0, 3: 1, 4: 0}, 'n': {1: 1, 2: 0, 3: 1, 4: 0}},
        {'name': '[2,3]', 'y': {1: 0, 2: 1, 3: 1, 4: 0}, 'n': {1: 0, 2: 1, 3: 1, 4: 0}},
        {'name': '[1,2,3]', 'y': {1: 1, 2: 1, 3: 1, 4: 0}, 'n': {1: 1, 2: 1, 3: 1, 4: 0}},
    ]
    
    print("\n🔍 测试不同方案的精确评估成本：")
    print("-" * 50)
    
    results = []
    for sol in test_solutions:
        print(f"\n评估方案 {sol['name']}...")
        start_time = time.time()
        
        try:
            # 使用修复后的精确评估
            cost = alns_solver.calculate_objective_direct(sol)
            eval_time = time.time() - start_time
            
            results.append({
                'name': sol['name'],
                'cost': cost,
                'time': eval_time
            })
            
            print(f"  方案 {sol['name']}: {cost:.2f} 元/天 (耗时: {eval_time:.2f}秒)")
            
        except Exception as e:
            print(f"  方案 {sol['name']}: 评估失败 - {str(e)}")
            results.append({
                'name': sol['name'],
                'cost': float('inf'),
                'time': 0
            })
    
    # 排序并显示结果
    results.sort(key=lambda x: x['cost'])
    
    print("\n🏆 方案成本排序（从低到高）：")
    print("-" * 50)
    for i, result in enumerate(results):
        if result['cost'] != float('inf'):
            print(f"  {i+1}. {result['name']}: {result['cost']:.2f} 元/天")
        else:
            print(f"  {i+1}. {result['name']}: 评估失败")
    
    # 检查是否[1,2,3]是最优的
    best_solution = results[0]
    if best_solution['name'] == '[1,2,3]':
        print(f"\n✅ 成功！[1,2,3]被正确识别为最优方案")
        print(f"   成本: {best_solution['cost']:.2f} 元/天")
    else:
        print(f"\n❌ 问题仍存在！最优方案是 {best_solution['name']}")
        print(f"   成本: {best_solution['cost']:.2f} 元/天")
        
        # 查找[1,2,3]的排名
        for i, result in enumerate(results):
            if result['name'] == '[1,2,3]':
                print(f"   [1,2,3]排名第{i+1}，成本: {result['cost']:.2f} 元/天")
                break
    
    return results

if __name__ == "__main__":
    print("开始测试修复后的ALNS评估函数...")
    
    # 测试评估函数准确性
    evaluation_results = test_evaluation_accuracy()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
