#!/usr/bin/env python3
"""
验证阶段批量求解修复测试脚本

用于快速验证修复后的验证阶段是否正确使用批量求解
"""

import time
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_validation_batch_fix():
    """测试验证阶段批量求解修复"""
    print("=" * 60)
    print("验证阶段批量求解修复测试")
    print("=" * 60)
    
    try:
        # 导入必要的模块
        from alns import StochasticDroneDeliveryOptimizerSAA, create_deterministic_example_instance
        print("✅ 成功导入模块")
        
        # 创建小规模测试问题
        print("\n创建测试问题...")
        problem_instance = create_deterministic_example_instance(
            num_customers=10,
            num_sites=4,
            use_kmeans_clustering=False,
            demand_level="low",
            locker_cost_level="low",
            drone_cost_level="low",
            drone_transport_cost_level="low",
            use_generated_distances=True,
            random_seed=611
        )
        
        # 创建SAA优化器
        print("初始化SAA优化器...")
        optimizer = StochasticDroneDeliveryOptimizerSAA(
            problem_instance,
            num_scenarios_k=20,  # 训练场景数
            num_scenarios_k_prime=500,  # 验证场景数（触发批量求解）
            random_seed=611
        )
        
        print(f"问题规模:")
        print(f"  客户数量: {len(problem_instance['customers'])}")
        print(f"  储物柜站点: {len(problem_instance['sites'])}")
        print(f"  训练场景: 20")
        print(f"  验证场景: 500 (应触发批量求解)")
        
        # 创建一个简单的测试解
        print("\n创建测试解...")
        test_solution = {
            'y': {1: 1, 2: 1, 3: 0, 4: 0},  # 选择前两个储物柜
            'n': {1: 1, 2: 1, 3: 0, 4: 0}   # 每个选中的储物柜配置1架无人机
        }
        
        # 测试验证阶段评估
        print("\n测试验证阶段评估...")
        print("注意：应该看到'验证阶段：使用批量求解 500 个场景...'消息")
        
        start_time = time.time()
        
        # 创建临时ALNS求解器（模拟验证阶段）
        from alns import ALNS_Solver
        temp_alns_solver = ALNS_Solver(
            problem_instance=optimizer,
            demand_samples=optimizer.fixed_validation_samples
        )
        
        # 调用验证阶段评估
        avg_obj, avg_truck_cost = temp_alns_solver.calculate_objective_unified(
            test_solution,
            optimizer.fixed_validation_samples,
            evaluation_mode='validation',
            return_details=True
        )
        
        eval_time = time.time() - start_time
        
        print(f"\n✅ 验证阶段评估完成!")
        print(f"评估时间: {eval_time:.2f}秒")
        print(f"平均目标值: {avg_obj:.2f}")
        print(f"平均卡车成本: {avg_truck_cost:.2f}")
        print(f"平均每场景: {eval_time/500*1000:.1f}毫秒")
        
        # 清理
        del temp_alns_solver
        import gc
        gc.collect()
        
        # 性能基准检查
        print(f"\n📊 性能检查:")
        if eval_time < 30:
            print(f"✅ 验证时间优秀: {eval_time:.2f}秒 < 30秒")
        elif eval_time < 60:
            print(f"✅ 验证时间良好: {eval_time:.2f}秒 < 60秒")
        else:
            print(f"⚠️  验证时间较慢: {eval_time:.2f}秒 > 60秒")
        
        avg_time_per_scenario = eval_time / 500 * 1000
        if avg_time_per_scenario < 50:
            print(f"✅ 每场景时间优秀: {avg_time_per_scenario:.1f}毫秒 < 50毫秒")
        elif avg_time_per_scenario < 100:
            print(f"✅ 每场景时间良好: {avg_time_per_scenario:.1f}毫秒 < 100毫秒")
        else:
            print(f"⚠️  每场景时间较慢: {avg_time_per_scenario:.1f}毫秒 > 100毫秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("验证阶段批量求解修复测试")
    print("作者: AI Assistant")
    print("日期: 2024-07-24")
    print()
    
    success = test_validation_batch_fix()
    
    if success:
        print("\n🎉 修复测试完成!")
        print("\n关键检查点:")
        print("1. ✅ 是否看到'验证阶段：使用批量求解 500 个场景...'消息")
        print("2. ✅ 是否看到分步骤的批量求解进度")
        print("3. ✅ 验证时间是否显著减少（< 60秒）")
        print("4. ✅ 每场景时间是否合理（< 100毫秒）")
        print("\n如果以上都满足，说明验证阶段批量求解修复成功！")
    else:
        print("\n❌ 修复测试失败，请检查错误信息")

if __name__ == "__main__":
    main()
