#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试 ALNS 修复效果
"""

import sys
import random
import numpy as np
from alns import *

def quick_test():
    """快速测试修复效果"""
    print("快速测试 ALNS 修复效果")
    print("=" * 50)
    
    # 设置随机种子
    random.seed(42)
    np.random.seed(42)
    
    # 创建测试实例
    problem = create_deterministic_example_instance()
    
    # 生成少量需求样本
    demand_samples = []
    for _ in range(3):
        sample = {}
        for customer_id in problem.customers:
            expected = problem.expected_demand[customer_id]
            sample[customer_id] = max(0, int(np.random.poisson(expected)))
        demand_samples.append(sample)
    
    # 创建ALNS求解器
    alns_solver = ALNS_Solver(problem_instance=problem, demand_samples=demand_samples)
    
    # 测试解
    test_solution = {
        'y': {1: 1, 2: 1, 3: 0, 4: 1, 5: 0},
        'n': {1: 1, 2: 1, 3: 0, 4: 1, 5: 0}
    }
    
    print("测试解: 储物柜 [1,2,4], 各1架无人机")
    print("-" * 30)
    
    try:
        # 启发式评估
        heuristic_obj = alns_solver._calculate_objective_heuristic(test_solution, 0)
        print(f"启发式评估: {heuristic_obj:.2f}")
        
        # 精确评估
        exact_obj = alns_solver.calculate_objective_direct(test_solution)
        print(f"精确评估: {exact_obj:.2f}")
        
        # 误差分析
        error = abs(exact_obj - heuristic_obj)
        error_pct = (error / max(exact_obj, 1e-6)) * 100
        print(f"评估误差: {error:.2f} ({error_pct:.1f}%)")
        
        # 详细成本分解
        print("\n详细成本分解:")
        alns_solver._analyze_cost_breakdown_detailed(test_solution, "测试方案", exact_obj, heuristic_obj)
        
        print("\n✅ 测试完成，未发现严重错误")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
