#!/usr/bin/env python3
"""
调试ALNS精确评估与问题实例evaluate_objective的一致性问题

测试[1,2,4]解在两种评估方法下的结果差异
"""

import sys
sys.path.append('.')

from alns import create_deterministic_example_instance, ALNS_Solver, StochasticDroneDeliveryOptimizerSAA
import numpy as np

def test_evaluation_consistency():
    """测试评估一致性"""
    print("=== 调试评估一致性问题 ===")

    # 1. 创建相同的问题实例
    print("1. 创建问题实例...")
    problem_data = create_deterministic_example_instance(
        num_customers=15, num_sites=5, demand_level='medium'
    )

    # 创建问题实例对象
    problem_instance = StochasticDroneDeliveryOptimizerSAA()

    # 修正参数名称
    if 'demand_deterministic' in problem_data:
        problem_data['expected_demand'] = problem_data.pop('demand_deterministic')

    problem_instance.set_parameters(**problem_data)
    
    # 2. 生成相同的需求样本
    print("2. 生成需求样本...")

    np.random.seed(608)  # 使用相同的种子
    demand_samples = []
    for _ in range(40):  # 使用40个样本，与ALNS一致
        sample = {}
        for customer in problem_instance.customers:
            expected = problem_instance.expected_demand[customer]
            sample[customer] = max(0, int(np.random.poisson(expected)))
        demand_samples.append(sample)
    
    print(f"   生成了{len(demand_samples)}个需求样本")
    
    # 3. 定义测试解[1,2,4]
    test_solution = {
        'y': {1: 1, 2: 1, 3: 0, 4: 1, 5: 0},
        'n': {1: 1, 2: 1, 3: 0, 4: 1, 5: 0}
    }
    
    print("3. 测试解: 储物柜[1,2,4], 每个1架无人机")
    
    # 4. 创建ALNS求解器
    print("4. 创建ALNS求解器...")
    alns_solver = ALNS_Solver(problem_instance, demand_samples)
    
    # 5. 使用问题实例的calculate_objective方法
    print("5. 使用问题实例的calculate_objective方法...")
    try:
        obj_value_problem = alns_solver.problem.calculate_objective(test_solution, demand_samples)
        print(f"   问题实例评估结果: {obj_value_problem:.2f}")
    except Exception as e:
        print(f"   问题实例评估失败: {e}")
        obj_value_problem = None
    
    # 6. 使用ALNS的calculate_objective_direct方法
    print("6. 使用ALNS的calculate_objective_direct方法...")
    try:
        obj_value_alns = alns_solver.calculate_objective_direct(test_solution)
        print(f"   ALNS精确评估结果: {obj_value_alns:.2f}")
    except Exception as e:
        print(f"   ALNS精确评估失败: {e}")
        obj_value_alns = None
    
    # 7. 使用ALNS的启发式评估方法
    print("7. 使用ALNS的启发式评估方法...")
    try:
        obj_value_heuristic = alns_solver._calculate_objective_heuristic(test_solution, 0)
        print(f"   ALNS启发式评估结果: {obj_value_heuristic:.2f}")
    except Exception as e:
        print(f"   ALNS启发式评估失败: {e}")
        obj_value_heuristic = None
    
    # 8. 对比结果
    print("\n=== 结果对比 ===")
    if obj_value_problem is not None:
        print(f"问题实例评估: {obj_value_problem:.2f}")
    if obj_value_alns is not None:
        print(f"ALNS精确评估:  {obj_value_alns:.2f}")
    if obj_value_heuristic is not None:
        print(f"ALNS启发式评估: {obj_value_heuristic:.2f}")
    
    # 计算差异
    if obj_value_problem is not None and obj_value_alns is not None:
        diff = abs(obj_value_problem - obj_value_alns)
        diff_pct = (diff / obj_value_problem) * 100
        print(f"\n精确评估差异: {diff:.2f} ({diff_pct:.1f}%)")
        
        if diff_pct > 10:
            print("❌ 精确评估差异过大，存在实现不一致问题")
        else:
            print("✅ 精确评估差异可接受")
    
    if obj_value_problem is not None and obj_value_heuristic is not None:
        diff_h = abs(obj_value_problem - obj_value_heuristic)
        diff_h_pct = (diff_h / obj_value_problem) * 100
        print(f"启发式评估差异: {diff_h:.2f} ({diff_h_pct:.1f}%)")
        
        if diff_h_pct < 10:
            print("✅ 启发式评估准确性良好")
        else:
            print("⚠️ 启发式评估存在较大偏差")

if __name__ == "__main__":
    test_evaluation_consistency()
