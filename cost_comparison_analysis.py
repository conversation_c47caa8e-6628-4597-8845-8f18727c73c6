#!/usr/bin/env python3
"""
成本对比分析工具
用于详细分析 saa_g_r.py 和 g_i.py 之间的成本差异
"""

import random
import numpy as np
from typing import Dict, List, Tuple
import math

# 设置随机种子以确保可重复性
RANDOM_SEED = 606
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

def analyze_cost_differences():
    """分析两种方法的成本差异"""
    
    print("=" * 80)
    print("SAA_G_R.PY vs G_I.PY 成本差异分析报告")
    print("=" * 80)
    
    # 模拟参数设置
    customers = list(range(1, 21))  # 20个客户
    sites = [1, 2]  # 2个储物柜
    expected_demand = {i: random.randint(2, 3) for i in customers}
    
    # 成本参数
    locker_fixed_cost = 800
    drone_cost = 3500
    transport_unit_cost = 0.015
    penalty_cost_unassigned = 50.0
    truck_fixed_cost = 500
    truck_km_cost = 2.0
    
    # 距离矩阵（简化）
    distance = {}
    for i in customers:
        for j in sites:
            distance[i, j] = random.uniform(2, 8)
    
    print("\n1. 两阶段成本分配策略对比")
    print("-" * 50)
    
    # 假设的第一阶段解
    y_star = {1: 1, 2: 1}  # 两个储物柜都开放
    n_star = {1: 1, 2: 1}  # 每个储物柜配备1架无人机
    
    # 模拟第一阶段分配
    x_qty_star = {}
    total_expected_demand = sum(expected_demand.values())
    
    for i in customers:
        # 简单分配策略：平均分配到两个储物柜
        x_qty_star[i, 1] = expected_demand[i] * 0.5
        x_qty_star[i, 2] = expected_demand[i] * 0.5
    
    print(f"总期望需求: {total_expected_demand}")
    print(f"开放储物柜: {[j for j, val in y_star.items() if val > 0.5]}")
    
    # 计算第一阶段固定成本（两种方法相同）
    locker_cost_fixed = sum(locker_fixed_cost * y_star[j] for j in sites)
    drone_deployment_cost_fixed = sum(drone_cost * n_star[j] for j in sites)
    first_stage_fixed_cost = locker_cost_fixed + drone_deployment_cost_fixed
    
    print(f"\n第一阶段固定成本（两种方法相同）:")
    print(f"  储物柜成本: {locker_cost_fixed}")
    print(f"  无人机成本: {drone_deployment_cost_fixed}")
    print(f"  固定成本总计: {first_stage_fixed_cost}")
    
    # SAA_G_R.PY 方法：第一阶段包含运输成本和惩罚成本
    print(f"\n2. SAA_G_R.PY 成本分配:")
    print("-" * 30)
    
    # 基于期望需求的无人机运输成本
    saa_gr_transport_cost = 0
    for i in customers:
        for j in sites:
            if (i, j) in distance:
                saa_gr_transport_cost += 2 * transport_unit_cost * distance[i, j] * x_qty_star[i, j]
    
    # 基于期望需求的未分配惩罚（假设完全分配，惩罚为0）
    saa_gr_penalty_cost = 0
    for i in customers:
        total_assigned = sum(x_qty_star[i, j] for j in sites)
        unassigned = max(0, expected_demand[i] - total_assigned)
        saa_gr_penalty_cost += penalty_cost_unassigned * unassigned
    
    saa_gr_first_stage_total = first_stage_fixed_cost + saa_gr_transport_cost + saa_gr_penalty_cost
    
    print(f"  第一阶段总成本: {saa_gr_first_stage_total:.2f}")
    print(f"    - 固定成本: {first_stage_fixed_cost}")
    print(f"    - 无人机运输成本: {saa_gr_transport_cost:.2f}")
    print(f"    - 未分配惩罚: {saa_gr_penalty_cost:.2f}")
    print(f"  第二阶段成本: 仅卡车成本（约1000-1500）")
    
    # G_I.PY 方法：第一阶段仅固定成本
    print(f"\n3. G_I.PY 成本分配:")
    print("-" * 25)
    
    gi_first_stage_total = first_stage_fixed_cost
    
    print(f"  第一阶段总成本: {gi_first_stage_total:.2f}")
    print(f"    - 固定成本: {first_stage_fixed_cost}")
    print(f"  第二阶段成本: 运输成本 + 惩罚成本 + 卡车成本")
    
    # 模拟一个随机需求场景
    print(f"\n4. 单个随机场景成本对比:")
    print("-" * 35)
    
    # 生成随机需求场景
    scenario_demand = {}
    for i in customers:
        # 泊松分布，期望值为expected_demand[i]
        scenario_demand[i] = max(1, np.random.poisson(expected_demand[i]))
    
    total_scenario_demand = sum(scenario_demand.values())
    print(f"场景实际需求: {total_scenario_demand} (期望: {total_expected_demand})")
    
    # G_I.PY 在该场景下的第二阶段成本
    gi_transport_cost_scenario = 0
    gi_penalty_cost_scenario = 0
    
    for i in customers:
        # 计算实际配送量（不超过计划量和实际需求）
        planned_total = sum(x_qty_star[i, j] for j in sites)
        actual_total = min(planned_total, scenario_demand[i])
        shortage = max(0, scenario_demand[i] - actual_total)
        
        # 运输成本（基于实际配送量）
        for j in sites:
            if (i, j) in distance and planned_total > 0:
                actual_delivery = actual_total * (x_qty_star[i, j] / planned_total)
                gi_transport_cost_scenario += 2 * transport_unit_cost * distance[i, j] * actual_delivery
        
        # 惩罚成本
        gi_penalty_cost_scenario += penalty_cost_unassigned * shortage
    
    # 估算卡车成本（简化）
    estimated_truck_cost = truck_fixed_cost + truck_km_cost * 20  # 假设20km总距离
    
    gi_second_stage_scenario = gi_transport_cost_scenario + gi_penalty_cost_scenario + estimated_truck_cost
    gi_total_scenario = gi_first_stage_total + gi_second_stage_scenario
    
    print(f"\nG_I.PY 该场景成本:")
    print(f"  第二阶段运输成本: {gi_transport_cost_scenario:.2f}")
    print(f"  第二阶段惩罚成本: {gi_penalty_cost_scenario:.2f}")
    print(f"  第二阶段卡车成本: {estimated_truck_cost:.2f}")
    print(f"  总成本: {gi_total_scenario:.2f}")
    
    # SAA_G_R.PY 在该场景下的成本
    saa_gr_truck_cost_scenario = estimated_truck_cost  # 仅第二阶段卡车成本
    saa_gr_total_scenario = saa_gr_first_stage_total + saa_gr_truck_cost_scenario
    
    print(f"\nSAA_G_R.PY 该场景成本:")
    print(f"  第二阶段卡车成本: {saa_gr_truck_cost_scenario:.2f}")
    print(f"  总成本: {saa_gr_total_scenario:.2f}")
    
    print(f"\n5. 成本差异分析:")
    print("-" * 25)
    cost_difference = saa_gr_total_scenario - gi_total_scenario
    print(f"成本差异: {cost_difference:.2f}")
    print(f"差异百分比: {(cost_difference/gi_total_scenario)*100:.1f}%")
    
    if cost_difference > 0:
        print("SAA_G_R.PY 成本更高")
        print("主要原因: 第一阶段就计算了基于期望需求的运输成本")
    else:
        print("G_I.PY 成本更高")
        print("主要原因: 第二阶段包含了更多随机成本组件")
    
    print(f"\n6. 关键发现:")
    print("-" * 15)
    print("1. SAA_G_R.PY 将运输成本和惩罚成本归入第一阶段")
    print("2. G_I.PY 将运输成本和惩罚成本归入第二阶段")
    print("3. 这导致了不同的优化目标和解的质量")
    print("4. DRL vs Gurobi 的卡车成本计算也可能有差异")
    print("5. 实际需求处理逻辑的不同影响最终成本")

if __name__ == "__main__":
    analyze_cost_differences()
