#!/usr/bin/env python3
"""
实时内存监控工具
监控ALNS算法运行时的内存使用情况
"""

import psutil
import time
import os
import threading
from datetime import datetime

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self, check_interval=5):
        self.check_interval = check_interval
        self.monitoring = False
        self.monitor_thread = None
        self.process = psutil.Process(os.getpid())
        self.memory_history = []
        self.max_memory_mb = 0
        
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        print(f"[内存监控] 开始监控，检查间隔: {self.check_interval}秒")
        
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        print(f"[内存监控] 停止监控")
        
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取内存使用情况
                memory_info = self.process.memory_info()
                rss_mb = memory_info.rss / (1024 * 1024)
                vms_mb = memory_info.vms / (1024 * 1024)
                
                # 记录历史
                timestamp = datetime.now()
                self.memory_history.append({
                    'time': timestamp,
                    'rss_mb': rss_mb,
                    'vms_mb': vms_mb
                })
                
                # 更新最大内存使用
                if rss_mb > self.max_memory_mb:
                    self.max_memory_mb = rss_mb
                
                # 检查内存使用是否过高
                system_memory = psutil.virtual_memory()
                if rss_mb > 500:  # 超过500MB就开始警告
                    if rss_mb > 2000:  # 超过2GB时严重警告
                        print(f"[严重警告] 进程内存使用过高: {rss_mb:.1f} MB - 建议立即清理")
                    elif rss_mb > 1000:  # 超过1GB时警告
                        print(f"[内存警告] 进程内存使用过高: {rss_mb:.1f} MB")
                    else:  # 500MB-1GB时轻微警告
                        if hasattr(self, '_last_warning_time'):
                            current_time = time.time()
                            if current_time - self._last_warning_time > 30:  # 每30秒最多警告一次
                                print(f"[内存提示] 进程内存使用: {rss_mb:.1f} MB")
                                self._last_warning_time = current_time
                        else:
                            print(f"[内存提示] 进程内存使用: {rss_mb:.1f} MB")
                            self._last_warning_time = time.time()
                
                if system_memory.percent > 90:  # 系统内存使用超过90%
                    print(f"[系统警告] 系统内存使用率: {system_memory.percent:.1f}%")
                
                # 保持历史记录在合理范围内
                if len(self.memory_history) > 1000:
                    self.memory_history = self.memory_history[-500:]
                    
            except Exception as e:
                print(f"[内存监控] 监控出错: {e}")
                
            time.sleep(self.check_interval)
    
    def get_current_usage(self):
        """获取当前内存使用情况"""
        try:
            memory_info = self.process.memory_info()
            system_memory = psutil.virtual_memory()
            
            return {
                'process_rss_mb': memory_info.rss / (1024 * 1024),
                'process_vms_mb': memory_info.vms / (1024 * 1024),
                'system_total_gb': system_memory.total / (1024 * 1024 * 1024),
                'system_used_gb': system_memory.used / (1024 * 1024 * 1024),
                'system_percent': system_memory.percent,
                'system_available_gb': system_memory.available / (1024 * 1024 * 1024)
            }
        except Exception as e:
            print(f"[内存监控] 获取内存信息失败: {e}")
            return None
    
    def print_current_usage(self):
        """打印当前内存使用情况"""
        usage = self.get_current_usage()
        if usage:
            print(f"\n[内存状态] {datetime.now().strftime('%H:%M:%S')}")
            print(f"  进程内存: {usage['process_rss_mb']:.1f} MB (RSS), {usage['process_vms_mb']:.1f} MB (VMS)")
            print(f"  系统内存: {usage['system_used_gb']:.1f}/{usage['system_total_gb']:.1f} GB ({usage['system_percent']:.1f}%)")
            print(f"  可用内存: {usage['system_available_gb']:.1f} GB")
            print(f"  最大使用: {self.max_memory_mb:.1f} MB")
    
    def get_memory_summary(self):
        """获取内存使用摘要"""
        if not self.memory_history:
            return None
            
        rss_values = [entry['rss_mb'] for entry in self.memory_history]
        
        return {
            'max_memory_mb': max(rss_values),
            'min_memory_mb': min(rss_values),
            'avg_memory_mb': sum(rss_values) / len(rss_values),
            'current_memory_mb': rss_values[-1] if rss_values else 0,
            'samples_count': len(self.memory_history)
        }
    
    def print_summary(self):
        """打印内存使用摘要"""
        summary = self.get_memory_summary()
        if summary:
            print(f"\n[内存摘要]")
            print(f"  最大内存: {summary['max_memory_mb']:.1f} MB")
            print(f"  最小内存: {summary['min_memory_mb']:.1f} MB")
            print(f"  平均内存: {summary['avg_memory_mb']:.1f} MB")
            print(f"  当前内存: {summary['current_memory_mb']:.1f} MB")
            print(f"  监控样本: {summary['samples_count']} 个")

# 全局内存监控器实例
memory_monitor = MemoryMonitor(check_interval=10)  # 每10秒检查一次

def start_memory_monitoring():
    """启动内存监控"""
    memory_monitor.start_monitoring()

def stop_memory_monitoring():
    """停止内存监控"""
    memory_monitor.stop_monitoring()

def print_memory_status():
    """打印内存状态"""
    memory_monitor.print_current_usage()

def print_memory_summary():
    """打印内存摘要"""
    memory_monitor.print_summary()

if __name__ == "__main__":
    # 测试内存监控
    print("测试内存监控...")
    start_memory_monitoring()
    
    try:
        # 模拟一些内存使用
        data = []
        for i in range(100):
            data.append([0] * 10000)  # 创建一些数据
            time.sleep(0.1)
            
            if i % 20 == 0:
                print_memory_status()
                
    except KeyboardInterrupt:
        print("用户中断")
    finally:
        stop_memory_monitoring()
        print_memory_summary()
