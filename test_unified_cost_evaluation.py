#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一成本评估方法的效果

这个脚本用于验证ALNS修改后的统一成本评估方法是否能够：
1. 消除训练和验证阶段的评估不一致问题
2. 提供与g_i.py可比的精确评估结果
3. 确保成本分解的一致性
"""

import sys
import time
import numpy as np
from alns import StochasticDroneDeliveryOptimizerSAA, ALNS_Solver

def test_cost_evaluation_consistency():
    """测试成本评估的一致性"""
    print("=" * 60)
    print("测试统一成本评估方法")
    print("=" * 60)
    
    # 创建测试问题实例
    print("1. 创建测试问题实例...")
    optimizer = StochasticDroneDeliveryOptimizerSAA(
        num_customers=15,
        num_sites=4,
        max_trucks=1,
        random_seed=611
    )
    
    # 生成测试需求样本
    print("2. 生成测试需求样本...")
    test_samples = optimizer._generate_demand_samples(num_samples=20)
    
    # 创建ALNS求解器
    print("3. 创建ALNS求解器...")
    alns_solver = ALNS_Solver(
        problem_instance=optimizer,
        demand_samples=test_samples
    )
    
    # 创建测试解
    test_solution = {
        'y': {1: 1, 2: 1, 3: 0, 4: 0},  # 选择储物柜1和2
        'n': {1: 1, 2: 1, 3: 0, 4: 0}   # 每个选中的储物柜配置1架无人机
    }
    
    print("4. 测试不同评估模式的一致性...")
    
    # 测试训练模式评估
    print("\n--- 训练模式评估 ---")
    start_time = time.time()
    training_cost = alns_solver.calculate_objective_unified(
        test_solution, test_samples, evaluation_mode='training'
    )
    training_time = time.time() - start_time
    print(f"训练模式成本: {training_cost:.2f} 元/天")
    print(f"训练模式耗时: {training_time:.3f} 秒")
    
    # 测试一致启发式评估
    print("\n--- 一致启发式评估 ---")
    start_time = time.time()
    consistent_cost = alns_solver.calculate_objective_unified(
        test_solution, test_samples, evaluation_mode='consistent_heuristic'
    )
    consistent_time = time.time() - start_time
    print(f"一致启发式成本: {consistent_cost:.2f} 元/天")
    print(f"一致启发式耗时: {consistent_time:.3f} 秒")
    
    # 测试精确评估
    print("\n--- 精确评估模式 ---")
    start_time = time.time()
    exact_cost = alns_solver.calculate_objective_unified(
        test_solution, test_samples, evaluation_mode='exact'
    )
    exact_time = time.time() - start_time
    print(f"精确评估成本: {exact_cost:.2f} 元/天")
    print(f"精确评估耗时: {exact_time:.3f} 秒")
    
    # 分析结果
    print("\n5. 结果分析:")
    print(f"训练模式 vs 一致启发式差异: {abs(training_cost - consistent_cost):.2f} 元/天")
    print(f"一致启发式 vs 精确评估差异: {abs(consistent_cost - exact_cost):.2f} 元/天")
    print(f"训练模式 vs 精确评估差异: {abs(training_cost - exact_cost):.2f} 元/天")
    
    # 速度对比
    print(f"\n速度对比:")
    print(f"训练模式速度: {training_time:.3f}s")
    print(f"一致启发式速度: {consistent_time:.3f}s")
    print(f"精确评估速度: {exact_time:.3f}s")
    print(f"精确评估相对训练模式慢: {exact_time/training_time:.1f}x")
    
    return {
        'training_cost': training_cost,
        'consistent_cost': consistent_cost,
        'exact_cost': exact_cost,
        'training_time': training_time,
        'consistent_time': consistent_time,
        'exact_time': exact_time
    }

def test_cost_breakdown_validation():
    """测试成本分解验证功能"""
    print("\n" + "=" * 60)
    print("测试成本分解验证功能")
    print("=" * 60)
    
    # 创建测试问题实例
    optimizer = StochasticDroneDeliveryOptimizerSAA(
        num_customers=10,
        num_sites=3,
        max_trucks=1,
        random_seed=611
    )
    
    # 生成测试需求样本
    test_samples = optimizer._generate_demand_samples(num_samples=10)
    
    # 创建ALNS求解器，启用详细验证
    alns_solver = ALNS_Solver(
        problem_instance=optimizer,
        demand_samples=test_samples
    )
    alns_solver.cost_evaluation_config['verbose_validation'] = True
    
    # 创建测试解
    test_solution = {
        'y': {1: 1, 2: 1, 3: 0},
        'n': {1: 1, 2: 1, 3: 0}
    }
    
    print("1. 执行详细成本分析...")
    cost_analysis = alns_solver._detailed_cost_analysis(
        test_solution, test_samples, 'consistent_heuristic'
    )
    
    print("\n2. 测试成本分解验证...")
    # 测试一致启发式评估的成本分解
    consistent_cost = alns_solver.calculate_objective_unified(
        test_solution, test_samples, evaluation_mode='consistent_heuristic'
    )
    
    print(f"\n3. 成本分解验证结果:")
    print(f"统一评估成本: {consistent_cost:.2f} 元/天")
    print(f"详细分析预估: {cost_analysis['estimated_total']:.2f} 元/天")
    print(f"差异: {abs(consistent_cost - cost_analysis['estimated_total']):.2f} 元/天")
    
    return cost_analysis

def compare_with_legacy_methods():
    """与旧版评估方法对比"""
    print("\n" + "=" * 60)
    print("与旧版评估方法对比")
    print("=" * 60)
    
    # 创建测试问题实例
    optimizer = StochasticDroneDeliveryOptimizerSAA(
        num_customers=15,
        num_sites=4,
        max_trucks=1,
        random_seed=611
    )
    
    # 生成测试需求样本
    test_samples = optimizer._generate_demand_samples(num_samples=30)
    
    # 创建ALNS求解器
    alns_solver = ALNS_Solver(
        problem_instance=optimizer,
        demand_samples=test_samples
    )
    
    # 创建测试解
    test_solution = {
        'y': {1: 1, 2: 1, 3: 1, 4: 0},
        'n': {1: 1, 2: 1, 3: 1, 4: 0}
    }
    
    print("1. 新版统一评估方法...")
    start_time = time.time()
    unified_cost = alns_solver.calculate_objective_unified(
        test_solution, test_samples, evaluation_mode='training'
    )
    unified_time = time.time() - start_time
    print(f"统一评估成本: {unified_cost:.2f} 元/天 (耗时: {unified_time:.3f}s)")
    
    print("\n2. 旧版启发式评估方法...")
    start_time = time.time()
    try:
        legacy_cost = alns_solver._calculate_objective_heuristic(test_solution)
        legacy_time = time.time() - start_time
        print(f"旧版启发式成本: {legacy_cost:.2f} 元/天 (耗时: {legacy_time:.3f}s)")
        
        print(f"\n3. 对比结果:")
        print(f"成本差异: {abs(unified_cost - legacy_cost):.2f} 元/天")
        print(f"相对差异: {abs(unified_cost - legacy_cost)/legacy_cost*100:.2f}%")
        print(f"速度对比: 统一方法 {unified_time:.3f}s vs 旧版 {legacy_time:.3f}s")
        
    except Exception as e:
        print(f"旧版方法执行失败: {e}")
        print("这表明旧版方法存在实现问题")

def main():
    """主测试函数"""
    print("开始测试统一成本评估方法...")
    
    try:
        # 测试1: 成本评估一致性
        consistency_results = test_cost_evaluation_consistency()
        
        # 测试2: 成本分解验证
        breakdown_results = test_cost_breakdown_validation()
        
        # 测试3: 与旧版方法对比
        compare_with_legacy_methods()
        
        print("\n" + "=" * 60)
        print("测试总结")
        print("=" * 60)
        
        # 评估一致性
        training_exact_diff = abs(consistency_results['training_cost'] - consistency_results['exact_cost'])
        consistency_ratio = training_exact_diff / consistency_results['exact_cost'] * 100
        
        print(f"✅ 成本评估一致性测试完成")
        print(f"   训练模式与精确评估差异: {training_exact_diff:.2f} 元/天 ({consistency_ratio:.2f}%)")
        
        if consistency_ratio < 5.0:
            print(f"   ✅ 一致性良好 (差异 < 5%)")
        elif consistency_ratio < 15.0:
            print(f"   ⚠️ 一致性可接受 (差异 < 15%)")
        else:
            print(f"   ❌ 一致性较差 (差异 >= 15%)")
        
        print(f"✅ 成本分解验证测试完成")
        print(f"✅ 与旧版方法对比完成")
        
        print(f"\n🎯 主要改进:")
        print(f"   1. 统一了训练和验证阶段的成本计算方法")
        print(f"   2. 提供了精确评估和一致启发式评估两种模式")
        print(f"   3. 增强了成本分解验证机制")
        print(f"   4. 修复了卡车成本计算的实现问题")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
