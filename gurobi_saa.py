import gurobipy as gp
from gurobipy import GRB
import numpy as np
from typing import Dict, List, Tuple
import random
import time
import math

import logging
from clustering import generate_locker_sites_with_kmeans
from visualization import visualize_solution
from scipy.stats import norm, poisson  # 用于正态分布和泊松分布采样

# 设置随机种子以确保结果可重现
RANDOM_SEED = 603
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# SAA 参数 (与论文一致)
SAA_MAX_REPLICATIONS_M = 10  # 最大复制次数
SAA_MIN_REPLICATIONS_M = 3   # 最小复制次数
SAA_SAMPLES_K = 50           # 训练样本数量 N
SAA_SAMPLES_K_PRIME = 10000  # 验证样本数量 N' (论文标准)
SAA_CONFIDENCE_LEVEL_ALPHA = 0.1  # 置信水平 (90%置信区间)
SAA_GAP_TOLERANCE_PERCENT = 0.05  # Gap阈值 ε' = 5%
SAA_VARIANCE_TOLERANCE_PERCENT = 0.03  # 方差阈值 ε = 3%


class GurobiCVRPSolver:
    """使用Gurobi求解车辆路径问题(CVRP)的求解器"""

    def __init__(self, depot_coord: Tuple[float, float],
                 truck_capacity: float,
                 truck_fixed_cost: float,
                 truck_km_cost: float,
                 keep_temp_files: bool = False,
                 max_temp_files: int = 5,
                 make_plots: bool = True):
        """
        初始化Gurobi CVRP求解器

        Args:
            depot_coord: 仓库坐标 (x, y)
            truck_capacity: 卡车容量
            truck_fixed_cost: 每次派遣卡车的固定成本
            truck_km_cost: 卡车每公里成本
        """
        self.depot_coord = depot_coord
        self.truck_capacity = truck_capacity
        self.truck_fixed_cost = truck_fixed_cost
        self.truck_km_cost = truck_km_cost
        self.keep_temp_files = keep_temp_files
        self.max_temp_files = max_temp_files
        self.make_plots = make_plots

        print(f"  [Gurobi CVRP] 初始化Gurobi CVRP求解器: 仓库{depot_coord}, 容量{truck_capacity}, 固定成本{truck_fixed_cost}, 公里成本{truck_km_cost}")

    def solve(self, active_lockers_info: Dict[int, Dict[str, any]], return_route_info: bool = False):
        """
        使用Gurobi求解CVRP问题

        Args:
            active_lockers_info: 活跃储物柜信息字典
                格式: {locker_id: {'coord': (x, y), 'demand': demand_value}}
            return_route_info: 是否返回路线信息

        Returns:
            如果return_route_info=False: 总成本
            如果return_route_info=True: (总成本, 路线信息字典)
        """
        if not active_lockers_info:
            if return_route_info:
                return 0.0, None
            return 0.0

        try:
            # 提取储物柜信息
            locker_ids = list(active_lockers_info.keys())
            n_lockers = len(locker_ids)

            # 构建距离矩阵（包括仓库）
            # 节点0是仓库，节点1到n是储物柜
            n_nodes = n_lockers + 1
            distances = {}
            coords = [self.depot_coord]  # 仓库坐标
            demands = [0]  # 仓库需求为0

            # 添加储物柜坐标和需求
            for locker_id in locker_ids:
                info = active_lockers_info[locker_id]
                coords.append(info['coord'])
                demands.append(info['demand'])

            # 计算距离矩阵
            for i in range(n_nodes):
                for j in range(n_nodes):
                    if i != j:
                        x1, y1 = coords[i]
                        x2, y2 = coords[j]
                        dist = math.sqrt((x1 - x2)**2 + (y1 - y2)**2)
                        distances[i, j] = dist
                    else:
                        distances[i, j] = 0

            # 估算需要的卡车数量上界
            total_demand = sum(demands[1:])  # 排除仓库需求
            max_trucks = max(1, math.ceil(total_demand / self.truck_capacity))

            # 创建Gurobi模型
            model = gp.Model("CVRP")
            model.setParam('OutputFlag', 0)  # 关闭输出
            model.setParam('TimeLimit', 60)  # 设置时间限制为60秒

            # 决策变量
            # x[i,j,k]: 卡车k是否从节点i行驶到节点j
            x = {}
            for k in range(max_trucks):
                for i in range(n_nodes):
                    for j in range(n_nodes):
                        if i != j:
                            x[i, j, k] = model.addVar(vtype=GRB.BINARY, name=f"x_{i}_{j}_{k}")

            # y[k]: 是否使用卡车k
            y = {}
            for k in range(max_trucks):
                y[k] = model.addVar(vtype=GRB.BINARY, name=f"y_{k}")

            # u[i,k]: 节点i在卡车k路线中的访问顺序（用于消除子回路）
            u = {}
            for k in range(max_trucks):
                for i in range(1, n_nodes):  # 排除仓库
                    u[i, k] = model.addVar(vtype=GRB.CONTINUOUS, lb=1, ub=n_nodes-1, name=f"u_{i}_{k}")

            # 目标函数：最小化总成本
            obj = gp.quicksum(self.truck_fixed_cost * y[k] for k in range(max_trucks))
            obj += gp.quicksum(distances[i, j] * self.truck_km_cost * x[i, j, k]
                              for k in range(max_trucks) for i in range(n_nodes) for j in range(n_nodes) if i != j)
            model.setObjective(obj, GRB.MINIMIZE)

            # 约束条件
            # 1. 每个储物柜必须被恰好一辆卡车访问
            for i in range(1, n_nodes):
                model.addConstr(gp.quicksum(x[i, j, k] for k in range(max_trucks) for j in range(n_nodes) if i != j) == 1)

            # 2. 流平衡约束
            for k in range(max_trucks):
                for i in range(n_nodes):
                    model.addConstr(
                        gp.quicksum(x[i, j, k] for j in range(n_nodes) if i != j) ==
                        gp.quicksum(x[j, i, k] for j in range(n_nodes) if i != j)
                    )

            # 3. 每辆卡车最多从仓库出发一次
            for k in range(max_trucks):
                model.addConstr(gp.quicksum(x[0, j, k] for j in range(1, n_nodes)) <= y[k])

            # 4. 容量约束
            for k in range(max_trucks):
                model.addConstr(
                    gp.quicksum(demands[i] * gp.quicksum(x[i, j, k] for j in range(n_nodes) if i != j)
                               for i in range(1, n_nodes)) <= self.truck_capacity * y[k]
                )

            # 5. 子回路消除约束（Miller-Tucker-Zemlin）
            for k in range(max_trucks):
                for i in range(1, n_nodes):
                    for j in range(1, n_nodes):
                        if i != j:
                            model.addConstr(u[i, k] - u[j, k] + n_nodes * x[i, j, k] <= n_nodes - 1)

            # 求解模型
            model.optimize()

            if model.status == GRB.OPTIMAL or model.status == GRB.TIME_LIMIT:
                # 计算总成本
                total_distance = 0
                num_trucks_used = 0
                routes = []

                for k in range(max_trucks):
                    if y[k].x > 0.5:  # 如果使用了卡车k
                        num_trucks_used += 1
                        route = [0]  # 从仓库开始
                        current_node = 0
                        route_distance = 0  # 单条路线的距离

                        # 构建路线
                        while True:
                            next_node = None
                            for j in range(n_nodes):
                                if current_node != j and (current_node, j, k) in x and x[current_node, j, k].x > 0.5:
                                    next_node = j
                                    break

                            if next_node is None:
                                # 没有找到下一个节点，路线结束，回到仓库
                                if current_node != 0:  # 如果当前不在仓库
                                    segment_distance = distances[current_node, 0]
                                    route_distance += segment_distance
                                    print(f"    路线段: 节点{current_node} → 节点0(仓库), 距离: {segment_distance:.2f}")
                                route.append(0)  # 回到仓库
                                break
                            else:
                                # 找到下一个节点，移动到该节点
                                segment_distance = distances[current_node, next_node]
                                route_distance += segment_distance
                                if next_node == 0:
                                    print(f"    路线段: 节点{current_node} → 节点0(仓库), 距离: {segment_distance:.2f}")
                                    route.append(0)  # 回到仓库
                                    break
                                else:
                                    print(f"    路线段: 节点{current_node} → 节点{next_node}, 距离: {segment_distance:.2f}")
                                    route.append(next_node)
                                    current_node = next_node

                        total_distance += route_distance
                        print(f"    卡车{k+1}路线距离: {route_distance:.2f}, 路线: {route}")

                        # 转换节点索引为储物柜ID
                        route_lockers = []
                        route_coords = []
                        route_demand = 0
                        for node in route[1:-1]:  # 排除起始和结束的仓库
                            if node > 0:
                                locker_id = locker_ids[node - 1]
                                route_lockers.append(locker_id)
                                route_coords.append(active_lockers_info[locker_id]['coord'])
                                route_demand += active_lockers_info[locker_id]['demand']

                        if route_lockers:  # 只添加非空路线
                            routes.append({
                                'vehicle_id': k + 1,
                                'lockers': route_lockers,
                                'total_demand': route_demand,
                                'locker_coords': route_coords
                            })

                # 计算成本
                fixed_cost = num_trucks_used * self.truck_fixed_cost
                distance_cost = total_distance * self.truck_km_cost
                total_cost = fixed_cost + distance_cost

                print(f"  [Gurobi CVRP] 求解完成: 距离={total_distance:.2f}, 卡车数={num_trucks_used}, 固定成本={fixed_cost:.2f}, 距离成本={distance_cost:.2f}, 总成本={total_cost:.2f}")

                if return_route_info:
                    route_info = {
                        'num_trucks': num_trucks_used,
                        'total_distance': total_distance,
                        'fixed_cost': fixed_cost,
                        'distance_cost': distance_cost,
                        'routes': routes,
                        'depot_coord': self.depot_coord,
                        'truck_capacity': self.truck_capacity
                    }
                    return total_cost, route_info
                else:
                    return total_cost

            else:
                print(f"  [Gurobi CVRP] 求解失败，状态: {model.status}")
                # 使用简化估算
                total_demand = sum(info['demand'] for info in active_lockers_info.values())
                num_trucks = max(1, math.ceil(total_demand / self.truck_capacity))
                fallback_cost = num_trucks * self.truck_fixed_cost

                if return_route_info:
                    return fallback_cost, None
                else:
                    return fallback_cost

        except Exception as e:
            print(f"  [Gurobi CVRP] 求解出错: {str(e)}")
            # 使用简化估算作为回退
            total_demand = sum(info['demand'] for info in active_lockers_info.values())
            num_trucks = max(1, math.ceil(total_demand / self.truck_capacity))
            fallback_cost = num_trucks * self.truck_fixed_cost

            if return_route_info:
                return fallback_cost, None
            else:
                return fallback_cost


class DeterministicDroneDeliveryOptimizer:

    def __init__(self):
        # 模型相关
        self.model = None
        # 决策变量
        self.x_qty = {}  # xᵢⱼ: 客户i分配给储物柜j的需求量 (Z+)
        self.z = {}  # zᵢⱼ: 客户i是否分配给储物柜j (二进制, 如果xᵢⱼ > 0, 则zᵢⱼ = 1)
        self.y = {}  # yⱼ: 是否在站点j开设储物柜 (二进制)
        self.n = {}  # nⱼ: 储物柜j部署的无人机数量 (Z+)
        self.u = {}  # uᵢ: 客户i是否未被分配 (二进制, uᵢ = 1 当且仅当客户i没有被分配给任何储物柜; uᵢ = 0 当且仅当客户i至少被一个储物柜服务)

        # 基础数据 - 由set_parameters方法设置
        self.customers = []  # I: 客户集合
        self.sites = []  # J: 候选储物柜站点集合
        self.demand_deterministic = {}  # λᵢ: 客户i的需求 (订单/天)
        self.distance = {}  # dᵢⱼ: 客户i到储物柜站点j的距离
        self.locker_fixed_cost = {}  # cˡⱼ: 在站点j开设储物柜的固定成本
        self.Q_locker_capacity = {}  # Qⱼ: 储物柜j的最大服务能力 (订单/天)

        # 系统参数 - 由set_parameters方法设置
        self.drone_speed = None  # v: 无人机飞行速度 (公里/小时)
        self.loading_time = None  # t₀: 无人机装载时间 (小时)
        self.max_flight_distance = None  # d_max: 无人机最大飞行距离 (公里, 来自约束5)
        self.transport_unit_cost = None  # cᵈ: 无人机单位运输成本 (元/公里)
        self.drone_cost = None  # pᵈ: 单架无人机购置成本
        self.H_drone_working_hours_per_day = None  # H: 每架无人机每日工作小时数
        self.penalty_cost_unassigned = None  # cᴾ: 未分配客户单位需求的惩罚成本

        # 坐标信息 - 可选，用于可视化
        self.customer_coords = {}
        self.site_coords = {}

        # Gurobi CVRP求解器参数 - 由set_parameters方法设置
        self.depot_coord = None  # 仓库坐标
        self.truck_capacity = None  # 卡车容量
        self.truck_fixed_cost = None  # 卡车固定成本
        self.truck_km_cost = None  # 卡车每公里成本

        # Gurobi CVRP求解器实例
        self._gurobi_cvrp_solver = None
        self._gurobi_cvrp_solver_params = None
        self._gurobi_cvrp_solver_no_plots = None
        self._gurobi_cvrp_solver_no_plots_params = None

        # 用于Big-M约束的大数
        self.BIG_M = 1e6  # 一个足够大的常数

    def set_parameters(self,
                       customers: List,
                       sites: List,
                       demand_deterministic: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,  # 这是 cᴾ
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        # 设置必需参数
        self.customers = customers
        self.sites = sites
        self.demand_deterministic = demand_deterministic
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned  # cᴾ
        self.Q_locker_capacity = Q_locker_capacity

        # 设置可选参数
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        # 设置Big-M的值，可以根据最大需求量动态调整以获得更紧的界
        if self.demand_deterministic:
            max_demand_val = max(self.demand_deterministic.values()) if self.demand_deterministic else 0
            self.BIG_M = max_demand_val if max_demand_val > 0 else 1e6  # 确保BIG_M至少为1或更大
        else:
            self.BIG_M = 1e6

    def _get_gurobi_cvrp_solver(self, make_plots: bool = True):
        """获取或创建Gurobi CVRP求解器实例"""
        current_params = (self.depot_coord, self.truck_capacity, self.truck_fixed_cost, self.truck_km_cost)
        if make_plots:
            if self._gurobi_cvrp_solver is None or self._gurobi_cvrp_solver_params != current_params:
                self._gurobi_cvrp_solver = GurobiCVRPSolver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=True
                )
                self._gurobi_cvrp_solver_params = current_params
                print(f"  [Gurobi] 初始化Gurobi CVRP求解器(生成图片): 仓库{self.depot_coord}, 容量{self.truck_capacity}")
            return self._gurobi_cvrp_solver
        else:
            if self._gurobi_cvrp_solver_no_plots is None or self._gurobi_cvrp_solver_no_plots_params != current_params:
                self._gurobi_cvrp_solver_no_plots = GurobiCVRPSolver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=False
                )
                self._gurobi_cvrp_solver_no_plots_params = current_params
                print(f"  [Gurobi] 初始化Gurobi CVRP求解器(不生成图片): 仓库{self.depot_coord}, 容量{self.truck_capacity}")
            return self._gurobi_cvrp_solver_no_plots

    def calculate_truck_cost(self,
                             selected_lockers: List[int],
                             x_qty_solution_values: Dict[Tuple[int, int], float],
                             make_plots: bool = True,
                             return_route_info: bool = False):
        """
        计算给定选址和分配方案下的卡车运输成本
        Args:
            selected_lockers: 选中的储物柜列表
            x_qty_solution_values: 字典 {(customer_id, locker_id): quantity} 包含当前解中x_qty的值
            make_plots: 是否生成路线图
            return_route_info: 是否返回路线信息
        """
        if not selected_lockers:  # 如果没有选中的储物柜，则卡车成本为0
            return (0.0, None) if return_route_info else 0.0

        # print(f"  [calculate_truck_cost] 计算卡车成本: {len(selected_lockers)}个储物柜")
        try:
            gurobi_cvrp_solver = self._get_gurobi_cvrp_solver(make_plots=make_plots)

            # 计算每个选中储物柜的总需求量
            locker_total_demands = {locker_id: 0.0 for locker_id in selected_lockers}
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:  # 仅考虑分配给已选储物柜的需求
                    locker_total_demands[locker_id] += quantity

            # 构建活跃储物柜信息 (有需求且有坐标的储物柜)
            active_lockers_info = {}
            for locker_id in selected_lockers:
                demand_at_locker = locker_total_demands.get(locker_id, 0)
                if demand_at_locker > 1e-6:
                    if locker_id in self.site_coords:
                        # 检查储物柜需求是否超过单辆卡车容量 (Gurobi求解器内部也会处理，但提前检查可以避免无效调用)
                        if self.truck_capacity is not None and demand_at_locker > self.truck_capacity:
                            # print(f"    [calculate_truck_cost] 警告: 储物柜 {locker_id} 需求 {demand_at_locker} 超过单卡车容量 {self.truck_capacity}。Gurobi将尝试用多辆卡车或多趟。")
                            # Gurobi求解器应该能处理这种情况，所以不直接返回惩罚值。
                            pass  # Gurobi会处理
                        active_lockers_info[locker_id] = {
                            'coord': self.site_coords[locker_id],
                            'demand': demand_at_locker
                        }

            if active_lockers_info:  # 如果有活跃的储物柜需要服务
                truck_cost, route_info_gurobi = gurobi_cvrp_solver.solve(active_lockers_info, return_route_info=True)
                # print(f"    [calculate_truck_cost] Gurobi计算的卡车成本: {truck_cost:.2f}")
                return (truck_cost, route_info_gurobi) if return_route_info else truck_cost
            else:  # 没有活跃储物柜
                # print(f"    [calculate_truck_cost] 无活跃储物柜，卡车成本为0")
                return (0.0, None) if return_route_info else 0.0

        except Exception as e:  # Gurobi求解失败的回退方案
            print(f"  [calculate_truck_cost] Gurobi求解失败: {str(e)}, 使用简化估算")
            total_demand_overall = 0
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    total_demand_overall += quantity

            num_trucks = math.ceil(total_demand_overall / self.truck_capacity) if self.truck_capacity > 0 else 1
            simplified_cost = num_trucks * self.truck_fixed_cost  # 简化成本只考虑固定成本
            # print(f"    [calculate_truck_cost] 简化估算的卡车成本: {simplified_cost:.2f}")
            return (simplified_cost, None) if return_route_info else simplified_cost

    def _get_current_solution(self, x_qty_vals, z_vals, y_vals, u_vals):
        """从当前变量值中提取解决方案的结构化信息"""
        selected_lockers = [j for j in self.sites if y_vals[j] > 0.5]  # 选中的储物柜

        # customer_assignments_primary: 客户i分配到的储物柜列表 (基于zᵢⱼ=1, 现在一个客户可以分配给多个储物柜)
        customer_assignments_primary = {}
        # customer_assignment_quantities: 客户i分配给储物柜j的具体数量 (基于xᵢⱼ > 0)
        customer_assignment_quantities = {i: {} for i in self.customers}

        unassigned_customers_by_u = [i for i in self.customers if u_vals.get(i, 0) > 0.5]  # 根据uᵢ判断未分配的客户

        for i in self.customers:
            assigned_lockers_for_i = []  # 客户i分配到的储物柜列表
            for j in self.sites:  # 遍历所有可能的储物柜
                if z_vals.get((i, j), 0) > 0.5:  # 如果zᵢⱼ=1
                    assigned_lockers_for_i.append(j)

                qty = x_qty_vals.get((i, j), 0)  # 获取实际分配量
                if qty > 0.5:  # 使用0.5是为了处理浮点数精度问题 (Gurobi整数变量解也可能略有偏差)
                    customer_assignment_quantities[i][j] = round(qty)  # 四舍五入到整数

            if assigned_lockers_for_i:  # 如果客户有分配的储物柜
                customer_assignments_primary[i] = assigned_lockers_for_i  # 记录分配的储物柜列表

        # customer_service_modes 字段在当前模型下意义不大，因为分配了就是无人机
        customer_service_modes = {}
        return selected_lockers, customer_assignments_primary, customer_assignment_quantities, unassigned_customers_by_u, customer_service_modes

    def build_model(self):
        """构建Gurobi优化模型"""
        self.model = gp.Model("DroneLockerOptimization_ModelAligned")  # 模型名称
        self._create_variables()  # 创建决策变量
        self._set_objective()  # 设置目标函数
        self._add_constraints()  # 添加约束条件
        self.model.update()  # 更新模型以确保所有更改生效
        print(f"模型构建完成 - 统计信息: {self.model.NumVars}变量, {self.model.NumConstrs}约束")

    def _create_variables(self):
        """创建模型中的所有决策变量"""
        # xᵢⱼ: 客户i分配给储物柜j的需求量 (非负整数)
        for i in self.customers:
            for j in self.sites:
                self.x_qty[i, j] = self.model.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_qty_{i}_{j}")

        # zᵢⱼ: 客户i是否分配给储物柜j (二进制)
        for i in self.customers:
            for j in self.sites:
                self.z[i, j] = self.model.addVar(vtype=GRB.BINARY, name=f"z_{i}_{j}")

        # yⱼ: 是否在站点j开设储物柜 (二进制)
        for j in self.sites:
            self.y[j] = self.model.addVar(vtype=GRB.BINARY, name=f"y_{j}")

        # nⱼ: 储物柜j部署的无人机数量 (非负整数)
        for j in self.sites:
            self.n[j] = self.model.addVar(vtype=GRB.INTEGER, lb=0, name=f"n_{j}")

        # uᵢ: 客户i是否未被分配 (二进制)
        for i in self.customers:
            self.u[i] = self.model.addVar(vtype=GRB.BINARY, name=f"u_{i}")

    def _set_objective(self):
        """设置模型的目标函数"""
        # 目标函数组成部分:
        # 1. 储物柜开设固定成本: Σ cˡⱼ * yⱼ
        locker_cost = gp.quicksum(self.locker_fixed_cost[j] * self.y[j] for j in self.sites)

        # 2. 无人机往返运输成本: Σᵢ Σⱼ 2 * cᵈ * dᵢⱼ * xᵢⱼ (xᵢⱼ 是实际分配量 self.x_qty[i,j])
        transport_cost = gp.quicksum(
            2 * self.transport_unit_cost * self.distance[i, j] * self.x_qty[i, j]
            for i in self.customers for j in self.sites if (i, j) in self.distance
        )

        # 3. 无人机购置成本: Σ pᵈ * nⱼ
        drone_deployment_cost = gp.quicksum(self.drone_cost * self.n[j] for j in self.sites)

        # 4. 未分配客户需求的惩罚成本: Σᵢ cᴾ * (λᵢ - Σⱼ xᵢⱼ)
        unassigned_demand_penalty = gp.quicksum(
            self.penalty_cost_unassigned *
            (self.demand_deterministic[i] - gp.quicksum(self.x_qty[i, k] for k in self.sites if (i, k) in self.x_qty))
            for i in self.customers
        )

        # 5. 卡车运输成本 (通过回调函数动态计算)
        truck_cost_var = self.model.addVar(lb=0.0, name="truck_cost")  # 卡车成本变量
        self.model._truck_cost_var = truck_cost_var  # 存储变量以便回调函数访问
        self.model.addConstr(truck_cost_var >= 0, name="initial_truck_cost_lower_bound")  # 初始下界

        # 总成本 = 各部分成本之和
        total_cost = (locker_cost + transport_cost + drone_deployment_cost +
                      unassigned_demand_penalty + truck_cost_var)
        self.model.setObjective(total_cost, GRB.MINIMIZE)  # 最小化总成本

        # 初始化回调函数相关的缓存和计数器
        self.model._callback_count = 0
        self.model._solution_cache = {}  # 缓存解的卡车成本
        self.model._last_truck_cost = 0.0  # 记录上一次计算的卡车成本

        # 定义卡车成本回调函数 (Lazy Constraint Callback)
        def truck_cost_callback_for_x_qty(model, where):
            if where == GRB.Callback.MIPSOL:  # 当找到新的整数可行解时调用
                model._callback_count += 1

                # 获取当前整数解中的变量值
                x_qty_sol_vals = model.cbGetSolution(self.x_qty)
                y_sol_vals = model.cbGetSolution(self.y)

                selected_lockers_cb = [j_site for j_site in self.sites if y_sol_vals[j_site] > 0.5]  # 当前解选中的储物柜

                # 创建用于缓存的解的哈希键 (基于选中的储物柜和相关的x_qty值)
                # 只考虑分配量大于0.5 (避免浮点问题) 且分配给已选储物柜的x_qty值
                relevant_x_qty_tuple = tuple(sorted(
                    ((i_cust, j_lock), val)
                    for (i_cust, j_lock), val in x_qty_sol_vals.items()
                    if val > 0.5 and j_lock in selected_lockers_cb
                ))
                solution_key = (tuple(sorted(selected_lockers_cb)), relevant_x_qty_tuple)

                current_truck_cost_cb = 0.0
                if solution_key in model._solution_cache:  # 检查缓存
                    current_truck_cost_cb = model._solution_cache[solution_key]
                    # print(f"  [回调 {model._callback_count}] 使用缓存的卡车成本: {current_truck_cost_cb:.2f}")
                else:
                    # print(f"  [回调 {model._callback_count}] 计算新解的确定性卡车成本...")
                    # 调用calculate_truck_cost计算当前解的卡车成本 (回调中不生成图片)
                    current_truck_cost_cb = self.calculate_truck_cost(selected_lockers_cb, x_qty_sol_vals,
                                                                      make_plots=False)
                    model._solution_cache[solution_key] = current_truck_cost_cb  # 缓存结果
                    # print(f"  [回调 {model._callback_count}] 新计算的卡车成本: {current_truck_cost_cb:.2f}")

                # 添加 Lazy Constraint: truck_cost_var >= (刚计算得到的实际卡车成本)
                # 这会切掉当前解，除非目标函数中的truck_cost_var已经至少这么大
                model.cbLazy(model._truck_cost_var >= current_truck_cost_cb)
                model._last_truck_cost = current_truck_cost_cb  # 更新记录
                # print(f"  [回调 {model._callback_count}] 添加Lazy约束: truck_cost >= {current_truck_cost_cb:.2f}")

        self.model._callback = truck_cost_callback_for_x_qty  # 将回调函数注册到模型

    def _add_constraints(self):
        """添加模型的所有约束条件"""
        # 约束1: 至少开设一个储物柜: Σⱼ yⱼ ≥ 1
        self.model.addConstr(gp.quicksum(self.y[j] for j in self.sites) >= 1, "C1_AtLeastOneLocker")

        # 约束2 : 客户分配给储物柜的总需求不超过客户的总需求: Σⱼ xᵢⱼ ≤ λᵢ, ∀i
        for i in self.customers:
            self.model.addConstr(
                gp.quicksum(self.x_qty[i, j] for j in self.sites if (i, j) in self.x_qty) <= self.demand_deterministic[
                    i],
                name=f"C2_TotalAssignedQtyLimit_{i}"
            )


        # zᵢⱼ = 1 当且仅当 xᵢⱼ > 0
        for i in self.customers:
            for j in self.sites:
                # 如果 xᵢⱼ > 0, 则 zᵢⱼ 必须为 1.  实现方式: xᵢⱼ ≤ M*zᵢⱼ
                self.model.addConstr(self.x_qty[i, j] <= self.demand_deterministic[i] * self.z[i, j],
                                     name=f"Link_x_z_upper_{i}_{j}")
                # 如果 zᵢⱼ = 1, 则 xᵢⱼ 必须 > 0. 实现方式: xᵢⱼ ≥ zᵢⱼ
                self.model.addConstr(self.x_qty[i, j] >= self.z[i, j], name=f"Link_x_z_lower_{i}_{j}")

        # 新的u定义约束：允许一个客户由多个储物柜服务
        # Σⱼ zᵢⱼ ≤ M * (1 - uᵢ), ∀i：如果uᵢ=1(未分配)，则所有zᵢⱼ必须为0；
        # Σⱼ zᵢⱼ ≥ (1 - uᵢ), ∀i：如果uᵢ=0(已分配)，则至少有一个zᵢⱼ为1；
        # for i in self.customers:
        #     sum_z_i = gp.quicksum(self.z[i, j] for j in self.sites)
        #     # 约束1: Σⱼ zᵢⱼ ≤ M * (1 - uᵢ)
        #     self.model.addConstr(sum_z_i <= len(self.sites) * (1 - self.u[i]),
        #                          name=f"CustomerAssignmentUpperBound_{i}")
        #     # 约束2: Σⱼ zᵢⱼ ≥ (1 - uᵢ)
        #     self.model.addConstr(sum_z_i >= (1 - self.u[i]),
        #                          name=f"CustomerAssignmentLowerBound_{i}")

        # 约束3: 储物柜开设约束 (只有开设的储物柜才能分配客户): zᵢⱼ ≤ yⱼ, ∀i,j
        for i in self.customers:
            for j in self.sites:
                self.model.addConstr(self.z[i, j] <= self.y[j], name=f"C3_AssignIfOpen_{i}_{j}")

        # 约束4 : 储物柜服务约束 (开设的储物柜至少服务一个客户): Σᵢ zᵢⱼ ≥ yⱼ, ∀j
        for j in self.sites:
            self.model.addConstr(gp.quicksum(self.z[i, j] for i in self.customers) >= self.y[j],
                                 name=f"C4_OpenLockerServesOne_{j}")

        # 约束5 : 客户点分配受无人机飞行距离限制: 2*dᵢⱼ*zᵢⱼ ≤ d_max, ∀i ∈ I, j ∈ J
        for i in self.customers:
            for j in self.sites:
                if (i, j) in self.distance:
                    self.model.addConstr(
                        2 * self.distance[i, j] * self.z[i, j] <= self.max_flight_distance,
                        name=f"C5_FlightDistanceLimit_{i}_{j}"
                    )

        # 约束6 : 无人机部署约束 (开设的储物柜至少配备一架无人机): nⱼ ≥ yⱼ, ∀j
        for j in self.sites:
            self.model.addConstr(self.n[j] >= self.y[j], name=f"C6_MinOneDroneIfOpen_{j}")

        # 约束7 : 无人机服务能力约束: Σᵢ xᵢⱼ * (2dᵢⱼ/v + t₀) ≤ nⱼH, ∀j
        # 服务能力取决于实际运送的需求量 xᵢⱼ
        for j in self.sites:
            total_drone_hours_needed_j = gp.quicksum(
                self.x_qty[i, j] * ((2 * self.distance[i, j] / self.drone_speed) + self.loading_time)
                for i in self.customers if (i, j) in self.distance
            )
            drone_hours_supplied_j = self.n[j] * self.H_drone_working_hours_per_day
            self.model.addConstr(total_drone_hours_needed_j <= drone_hours_supplied_j,
                                 name=f"C7_DroneServiceCapacity_{j}")

        # 约束8 : 储物柜最大服务能力约束: Σᵢ xᵢⱼ ≤ Qⱼyⱼ, ∀j
        # 储物柜容量也与实际处理的需求量 xᵢⱼ 相关
        for j in self.sites:
            self.model.addConstr(
                gp.quicksum(self.x_qty[i, j] for i in self.customers if (i, j) in self.x_qty) <= self.Q_locker_capacity[
                    j] * self.y[j],
                name=f"C8_LockerOrderCapacity_{j}"
            )

    def solve(self, time_limit: int = 3600, mip_gap: float = 0.01):
        """求解构建好的Gurobi模型"""
        if self.model is None: raise ValueError("模型尚未构建 (Model not built).")
        print("\n配置Gurobi求解器参数...")
        self.model.setParam('OutputFlag', 1)  # 显示Gurobi输出
        self.model.setParam('LogToConsole', 1)  # 将日志输出到控制台
        logging.getLogger('gurobipy').setLevel(logging.WARNING)  # 减少Gurobi的冗余日志

        self.model.setParam('TimeLimit', time_limit)  # 时间限制
        self.model.setParam('MIPGap', mip_gap)  # MIP Gap
        self.model.setParam('MIPFocus', 2)  # MIP焦点 (1: 更侧重于找到可行解  0：平衡  2：更侧重于找到最优解)
        self.model.setParam('Threads', 0)  # 使用所有可用线程
        self.model.setParam('NodefileStart', 0.5)  # 当内存使用达到0.5GB时开始使用节点文件
        self.model.setParam('DisplayInterval', 10)  # 每10秒更新一次日志
        self.model.setParam('LogFile', 'gurobi_model_log.txt')  # 日志文件名
        self.model.setParam('LazyConstraints', 1)  # 启用Lazy Constraints (用于回调函数)

        print("开始求解模型...")
        self.model.optimize(self.model._callback)  # 传入回调函数进行优化

        print(f"优化完成，状态: {self.model.status}")
        if hasattr(self.model, '_callback_count'):  # 显示回调函数统计信息
            print(f"\n回调函数统计:")
            print(f"  总调用次数: {self.model._callback_count}")
            print(f"  缓存命中数 (不同解): {len(self.model._solution_cache)}")  # 缓存中的条目数代表不同解的数量
            print(f"  最后一次计算的卡车成本: {self.model._last_truck_cost:.2f}")

        # 根据求解状态打印结果
        if self.model.status == GRB.OPTIMAL:
            print("找到最优解!")
            self._print_solution()
        elif self.model.status == GRB.TIME_LIMIT:
            print("求解时间到达限制，返回当前最佳解。")
            if self.model.SolCount > 0:
                self._print_solution()
            else:
                print("时间限制内未找到可行解。")
        else:
            print(f"优化失败，状态码: {self.model.status}")
            if self.model.status == GRB.INFEASIBLE:  # 如果模型不可行
                print("模型不可行，尝试计算不可行子集 (IIS)...")
                self.model.computeIIS()  # 计算IIS
                self.model.write("model_infeasible.ilp")  # 将IIS写入文件
                print("IIS已写入 model_infeasible.ilp")
            if self.model.SolCount > 0:  # 如果有解，即使不是最优
                print("尽管未达到最优或满足特定终止条件，但找到了可行解:")
                self._print_solution()

    def _print_solution(self):
        """打印优化结果的详细信息"""
        if self.model.SolCount == 0: print("模型未找到解."); return

        # 获取解中的变量值
        x_qty_sol = {(i, j): self.x_qty[i, j].x for i in self.customers for j in self.sites if (i, j) in self.x_qty}
        z_sol = {(i, j): self.z[i, j].x for i in self.customers for j in self.sites if (i, j) in self.z}
        y_sol = {j: self.y[j].x for j in self.sites}
        u_sol = {i: self.u[i].x for i in self.customers}
        n_sol = {j: self.n[j].x for j in self.sites}

        selected_lockers_final = [j for j in self.sites if y_sol[j] > 0.5]  # 最终选中的储物柜

        # 重新获取和组织客户分配信息用于打印
        _, customer_assignments_primary_final, customer_assignment_quantities_final, unassigned_customers_by_u_final, _ = \
            self._get_current_solution(x_qty_sol, z_sol, y_sol, u_sol)

        print("  计算最终解的卡车成本 (可能生成路线图)...")
        # 为最终解计算卡车成本，并获取路线信息 (允许生成图片)
        final_truck_cost, route_info_final = self.calculate_truck_cost(
            selected_lockers_final,
            x_qty_sol,
            make_plots=True,
            return_route_info=True
        )

        # 计算目标函数各组成部分的最终值
        locker_cost_val = sum(self.locker_fixed_cost[j] * y_sol[j] for j in self.sites if y_sol[j] > 0.5)
        transport_cost_val = sum(
            2 * self.transport_unit_cost * self.distance[i, j] * x_qty_sol.get((i, j), 0)
            for i in self.customers for j in self.sites
            if (i, j) in self.distance and x_qty_sol.get((i, j), 0) > 0.5  # 只计算实际分配的量
        )
        drone_deployment_cost_val = sum(self.drone_cost * n_sol[j] for j in self.sites if y_sol[j] > 0.5)

        unassigned_demand_penalty_val = sum(
            self.penalty_cost_unassigned *
            max(0, (self.demand_deterministic[i] - sum(x_qty_sol.get((i, k), 0) for k in self.sites if
                                                       (i, k) in self.x_qty and x_qty_sol.get((i, k), 0) > 0.5)))
            for i in self.customers
        )  # 使用max(0, ...)确保惩罚不为负

        gurobi_obj_val = self.model.ObjVal  # Gurobi报告的目标函数值
        # 手动计算总成本 (使用最终计算的卡车成本)
        manual_total_cost = (locker_cost_val + transport_cost_val + drone_deployment_cost_val +
                             final_truck_cost + unassigned_demand_penalty_val)

        print(f"\n目标函数值 (Gurobi报告, 可能包含回调中未精确的truck_cost): {gurobi_obj_val:.2f}")
        print(f"目标函数值 (手动计算, 使用最终精确的truck_cost): {manual_total_cost:.2f}")
        print(f"  - 储物柜固定成本: {locker_cost_val:.2f}")
        print(f"  - 无人机运输成本 (基于实际分配量xᵢⱼ): {transport_cost_val:.2f}")
        print(f"  - 无人机部署成本: {drone_deployment_cost_val:.2f}")
        print(f"  - 卡车运输成本 (Gurobi最终计算): {final_truck_cost:.2f}")
        print(f"  - 未分配需求惩罚成本: {unassigned_demand_penalty_val:.2f}")

        print("\n选定的储物柜位置及无人机数量:")
        for j_site in selected_lockers_final:
            print(f"  位置 {j_site}: 配备 {round(n_sol[j_site])} 架无人机")

        print("\n客户分配详情 (基于实际分配量 xᵢⱼ > 0):")
        for j_site in selected_lockers_final:  # 遍历选中的储物柜
            print(f"  储物柜 {j_site}:")
            customers_served_by_j_list = []  # 服务于此储物柜的客户列表
            total_demand_at_j_qty_val = 0  # 此储物柜处理的总需求量

            # 收集分配到此储物柜的客户及其需求量
            for i_cust, assigned_lockers_for_cust in customer_assignment_quantities_final.items():
                qty_ij_val = assigned_lockers_for_cust.get(j_site, 0)
                if qty_ij_val > 0:
                    customers_served_by_j_list.append(f"客户{i_cust}(数量:{qty_ij_val})")
                    total_demand_at_j_qty_val += qty_ij_val

            if not customers_served_by_j_list:
                print("    - 未分配任何客户的需求至此储物柜")
                continue

            print(f"    - 分配的客户及数量: {', '.join(customers_served_by_j_list)}")
            max_cap_j_val = self.Q_locker_capacity.get(j_site, float('inf'))  # 获取储物柜容量
            cap_util_j_val = total_demand_at_j_qty_val / max_cap_j_val if max_cap_j_val > 0 else 0  # 计算容量利用率
            print(
                f"    - 此储物柜总服务需求量: {total_demand_at_j_qty_val:.2f} (最大容量: {max_cap_j_val}, 利用率: {cap_util_j_val * 100:.1f}%)")

            # 计算此储物柜的无人机工时需求
            actual_hours_needed_j_val = sum(
                x_qty_sol.get((i_cust, j_site), 0) * (
                            (2 * self.distance[i_cust, j_site] / self.drone_speed) + self.loading_time)
                for i_cust in self.customers if
                (i_cust, j_site) in self.distance and x_qty_sol.get((i_cust, j_site), 0) > 0.5
            )
            supplied_hours_j_val = n_sol[j_site] * self.H_drone_working_hours_per_day  # 提供的总工时
            drone_util_j_val = actual_hours_needed_j_val / supplied_hours_j_val if supplied_hours_j_val > 0 else 0  # 无人机利用率
            print(f"    - 无人机工时需求: {actual_hours_needed_j_val:.2f} 小时/天")
            print(
                f"    - 无人机提供工时: {round(n_sol[j_site])}架 * {self.H_drone_working_hours_per_day}小时/架 = {supplied_hours_j_val:.2f} (利用率: {drone_util_j_val * 100:.1f}%)")

        if unassigned_customers_by_u_final:  # 打印通过uᵢ标记为未分配的客户
            print(f"\n通过 uᵢ=1 标记为完全未分配的客户: {unassigned_customers_by_u_final}")
        else:
            print("\n所有客户均未通过 uᵢ=1 标记为完全未分配 (可能部分满足或完全满足).")

        # 检查客户需求的满足情况
        print("\n客户需求满足情况汇总:")
        num_fully_served = 0
        num_partially_served = 0
        num_not_served_at_all = 0
        for i_cust in self.customers:
            total_served_for_i_val = sum(customer_assignment_quantities_final.get(i_cust, {}).values())
            demand_i = self.demand_deterministic[i_cust]
            if abs(total_served_for_i_val - demand_i) < 1e-6:  # 完全满足
                num_fully_served += 1
            elif total_served_for_i_val > 1e-6:  # 部分满足
                num_partially_served += 1
                print(f"  客户 {i_cust}: 总需求 {demand_i}, 实际服务量 {total_served_for_i_val:.2f} (部分满足)")
            else:  # 完全未满足 (total_served_for_i_val < 1e-6)
                num_not_served_at_all += 1
                # print(f"  客户 {i_cust}: 总需求 {demand_i}, 实际服务量 0 (完全未满足)")

        print(f"  - 完全满足需求的客户数: {num_fully_served}")
        print(f"  - 部分满足需求的客户数: {num_partially_served}")
        print(f"  - 完全未得到服务的客户数: {num_not_served_at_all}")

        self._print_truck_routes(route_info_final)  # 打印卡车路线信息

    def _print_truck_routes(self, route_info):  # 打印卡车路线信息的辅助函数
        if not route_info: print("\n卡车路线信息: 无路线信息"); return
        print(f"\n卡车路线信息:")
        print(f"  总卡车数量 (Gurobi估计): {route_info.get('num_trucks', '未知')}")
        print(f"  总行驶距离: {route_info.get('total_distance', 0):.2f} km")
        print(f"  卡车固定成本部分: {route_info.get('fixed_cost', 0):.2f}")
        print(f"  卡车距离成本部分: {route_info.get('distance_cost', 0):.2f}")
        print(f"  仓库坐标: {route_info.get('depot_coord', '未知')}")
        print(f"  卡车容量: {route_info.get('truck_capacity', '未知')}")

        routes_list = route_info.get('routes', [])
        num_trucks_from_gurobi = route_info.get('num_trucks', 0)
        print(f"  Gurobi返回的路线数量: {len(routes_list)}")
        if len(routes_list) != num_trucks_from_gurobi and num_trucks_from_gurobi != '未知':
            print(
                f"  ⚠ 注意: Gurobi返回的路线数量({len(routes_list)})与Gurobi估计的卡车数量({num_trucks_from_gurobi})不一致。这可能是由于Gurobi的内部逻辑或近似。")

        if not routes_list: print("  无具体路线详情"); return
        print(f"\n详细路线:")
        for idx, route_detail in enumerate(routes_list):
            vehicle_id_val = route_detail.get('vehicle_id', idx)  # 如果没有vehicle_id，用索引代替
            lockers_on_route_list = route_detail.get('lockers', [])
            total_demand_on_route_val = route_detail.get('total_demand', 0)
            locker_coords_on_route_list = route_detail.get('locker_coords', [])

            print(f"  卡车 {vehicle_id_val}:")
            if lockers_on_route_list:
                print(f"    路线: 仓库 → {' → '.join(map(str, lockers_on_route_list))} → 仓库")
                print(f"    配送储物柜: {lockers_on_route_list}")
            else:
                print(f"    路线: 该卡车可能未使用或无停靠点")

            print(f"    此路线上总需求量: {total_demand_on_route_val:.2f} 订单")

            if locker_coords_on_route_list:
                coord_str_list = [f"储物柜{lockers_on_route_list[k_idx]}({coord[0]:.1f}, {coord[1]:.1f})"
                                  for k_idx, coord in enumerate(locker_coords_on_route_list)]
                print(f"    储物柜坐标: {', '.join(coord_str_list)}")

            truck_cap_val = route_info.get('truck_capacity', 1)
            utilization_val = (total_demand_on_route_val / truck_cap_val) * 100 if truck_cap_val > 0 else 0
            print(f"    此路线上容量利用率: {utilization_val:.1f}% ({total_demand_on_route_val:.2f}/{truck_cap_val})")
        if 'error' in route_info: print(f"\n  ⚠ Gurobi路线解析时出现错误: {route_info['error']}")

    def get_solution(self) -> Dict:  # 获取结构化的解决方案字典
        if self.model is None or self.model.status not in [GRB.OPTIMAL, GRB.TIME_LIMIT,
                                                           GRB.SUBOPTIMAL] or self.model.SolCount == 0:
            print("没有找到有效解或模型未优化。")
            return None

        # 获取解中的变量值
        x_qty_sol_final = {(i, j): self.x_qty[i, j].x for i in self.customers for j in self.sites if
                           (i, j) in self.x_qty}
        z_sol_final = {(i, j): self.z[i, j].x for i in self.customers for j in self.sites if (i, j) in self.z}
        y_sol_final = {j: self.y[j].x for j in self.sites}
        u_sol_final = {i: self.u[i].x for i in self.customers}
        n_sol_final = {j: self.n[j].x for j in self.sites}

        selected_lockers_list_final = [j for j in self.sites if y_sol_final[j] > 0.5]

        # 提取客户分配信息
        _, customer_assignments_primary_dict, customer_quantities_dict, unassigned_customers_list_by_u, _ = \
            self._get_current_solution(x_qty_sol_final, z_sol_final, y_sol_final, u_sol_final)

        # 计算最终卡车成本 (不生成图片，避免在get_solution中重复生成)
        final_truck_cost_val = self.calculate_truck_cost(selected_lockers_list_final, x_qty_sol_final, make_plots=False)

        # 计算各成本项
        locker_cost_val_final = sum(
            self.locker_fixed_cost[j] * y_sol_final[j] for j in self.sites if y_sol_final[j] > 0.5)
        transport_cost_val_final = sum(
            2 * self.transport_unit_cost * self.distance[i, j] * x_qty_sol_final.get((i, j), 0)
            for i in self.customers for j in self.sites if
            (i, j) in self.distance and x_qty_sol_final.get((i, j), 0) > 0.5
        )
        drone_deployment_cost_val_final = sum(
            self.drone_cost * n_sol_final[j] for j in self.sites if y_sol_final[j] > 0.5)
        unassigned_demand_penalty_val_final = sum(
            self.penalty_cost_unassigned *
            max(0, (self.demand_deterministic[i] - sum(x_qty_sol_final.get((i, k), 0) for k in self.sites if
                                                       (i, k) in self.x_qty and x_qty_sol_final.get((i, k), 0) > 0.5)))
            for i in self.customers
        )
        total_calculated_cost_final = (
                    locker_cost_val_final + transport_cost_val_final + drone_deployment_cost_val_final +
                    final_truck_cost_val + unassigned_demand_penalty_val_final)

        # 构建解决方案字典
        solution_dict = {
            'objective_value': self.model.ObjVal,  # Gurobi报告的目标值
            'calculated_total_cost': total_calculated_cost_final,  # 手动计算的总成本 (使用精确卡车成本)
            'locker_cost': locker_cost_val_final,
            'transport_cost': transport_cost_val_final,
            'drone_deployment_cost': drone_deployment_cost_val_final,
            'truck_cost': final_truck_cost_val,
            'unassigned_penalty_cost': unassigned_demand_penalty_val_final,
            'selected_lockers': {j: True for j in selected_lockers_list_final},  # 选中的储物柜
            'customer_assignments_primary': customer_assignments_primary_dict,  # 基于zᵢⱼ的主要分配
            'customer_assigned_quantities': customer_quantities_dict,  # 基于xᵢⱼ的实际分配量
            'unassigned_customers_by_u': unassigned_customers_list_by_u,  # 基于uᵢ标记的未分配客户
            'drone_allocations': {j: int(round(n_sol_final[j])) for j in selected_lockers_list_final if
                                  j in n_sol_final}  # 无人机分配
        }
        return solution_dict

    def visualize_solution(self, solution: Dict):  # 可视化解决方案
        if not solution: print("无解决方案可供可视化 (No solution to visualize)."); return

        # 为可视化准备一个简化的解决方案结构 (如果visualize_solution需要特定格式)
        temp_solution_for_viz = solution.copy()

        # 直接传递多储物柜分配信息，让可视化模块处理
        primary_assignments = solution.get('customer_assignments_primary', {})
        temp_solution_for_viz['customer_assignments'] = primary_assignments

        # 在确定性模型中，所有分配的客户都使用无人机配送（模式0）
        customer_service_modes = {}
        for customer_id in primary_assignments:
            customer_service_modes[customer_id] = 0  # 0表示无人机配送
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # 移除可能引起混淆或不被visualize_solution使用的键
        if 'customer_assignments_primary' in temp_solution_for_viz: del temp_solution_for_viz[
            'customer_assignments_primary']
        if 'customer_assigned_quantities' in temp_solution_for_viz: del temp_solution_for_viz[
            'customer_assigned_quantities']
        if 'unassigned_customers_by_u' in temp_solution_for_viz:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']
            del temp_solution_for_viz['unassigned_customers_by_u']

        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,  # 使用调整后的solution字典
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="确定性无人机配送网络规划 (支持多储物柜服务)"
        )
        if plt_fig:  # 如果visualize_solution返回一个matplotlib图形对象
            # plt_fig.show() # 在某些环境下，直接调用show可能需要matplotlib.pyplot as plt
            import matplotlib.pyplot as plt  # 确保导入
            plt.show()


class StochasticDroneDeliveryOptimizerSAA:
    """
    随机无人机配送网络优化器 - 使用SAA方法
    基于Gurobi求解器实现SAA算法
    """

    def __init__(self):
        self.customers = []
        self.sites = []
        self.expected_demand = {}
        self.distance = {}
        self.locker_fixed_cost = {}
        self.Q_locker_capacity = {}
        self.drone_speed = None
        self.loading_time = None
        self.max_flight_distance = None
        self.transport_unit_cost = None
        self.drone_cost = None
        self.H_drone_working_hours_per_day = None
        self.penalty_cost_unassigned = None
        self.customer_coords = {}
        self.site_coords = {}
        self.depot_coord = None
        self.truck_capacity = None
        self.truck_fixed_cost = None
        self.truck_km_cost = None
        self.BIG_M = 1e6

        # SAA 相关结果存储
        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = []

        # Gurobi CVRP求解器实例
        self._gurobi_cvrp_solver = None
        self._gurobi_cvrp_solver_params = None
        self._gurobi_cvrp_solver_no_plots = None
        self._gurobi_cvrp_solver_no_plots_params = None

    def set_parameters(self,
                       customers: List,
                       sites: List,
                       expected_demand: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        self.customers = customers
        self.sites = sites
        self.expected_demand = expected_demand
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned
        self.Q_locker_capacity = Q_locker_capacity
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        if self.expected_demand:
            max_expected_demand_val = max(self.expected_demand.values()) if self.expected_demand else 0
            self.BIG_M = max_expected_demand_val * 2 if max_expected_demand_val > 0 else 1e6
        else:
            self.BIG_M = 1e6

    def _get_gurobi_cvrp_solver(self, make_plots: bool = True):
        """获取或创建Gurobi CVRP求解器实例"""
        current_params = (self.depot_coord, self.truck_capacity, self.truck_fixed_cost, self.truck_km_cost)
        if make_plots:
            if self._gurobi_cvrp_solver is None or self._gurobi_cvrp_solver_params != current_params:
                self._gurobi_cvrp_solver = GurobiCVRPSolver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=True
                )
                self._gurobi_cvrp_solver_params = current_params
            return self._gurobi_cvrp_solver
        else:
            if self._gurobi_cvrp_solver_no_plots is None or self._gurobi_cvrp_solver_no_plots_params != current_params:
                self._gurobi_cvrp_solver_no_plots = GurobiCVRPSolver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=False
                )
                self._gurobi_cvrp_solver_no_plots_params = current_params
            return self._gurobi_cvrp_solver_no_plots

    def calculate_truck_cost(self,
                             selected_lockers: List[int],
                             x_qty_solution_values: Dict[Tuple[int, int], float],
                             make_plots: bool = True,
                             return_route_info: bool = False):
        """计算卡车运输成本"""
        if not selected_lockers:
            return (0.0, None) if return_route_info else 0.0

        try:
            gurobi_cvrp_solver = self._get_gurobi_cvrp_solver(make_plots=make_plots)
            if gurobi_cvrp_solver is None:
                return (0.0, None) if return_route_info else 0.0

            locker_total_demands = {locker_id: 0.0 for locker_id in selected_lockers}
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    locker_total_demands[locker_id] += quantity

            active_lockers_info = {}
            for locker_id in selected_lockers:
                demand_at_locker = locker_total_demands.get(locker_id, 0)
                if demand_at_locker > 1e-6:
                    if locker_id in self.site_coords:
                        active_lockers_info[locker_id] = {
                            'coord': self.site_coords[locker_id],
                            'demand': round(demand_at_locker)
                        }

            if active_lockers_info:
                truck_cost, route_info_gurobi = gurobi_cvrp_solver.solve(active_lockers_info, return_route_info=True)
                return (truck_cost, route_info_gurobi) if return_route_info else truck_cost
            else:
                return (0.0, None) if return_route_info else 0.0
        except Exception as e:
            total_demand_overall = 0
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    total_demand_overall += quantity
            num_trucks = math.ceil(total_demand_overall / self.truck_capacity) if self.truck_capacity > 0 else 1
            simplified_cost = num_trucks * self.truck_fixed_cost
            return (simplified_cost, None) if return_route_info else simplified_cost

    def _generate_demand_samples(self, num_samples: int) -> List[Dict[int, float]]:
        """
        生成需求场景样本，客户需求服从泊松分布
        泊松分布的参数λ等于期望需求值，天然产生非负整数
        """
        demand_scenarios = []
        for _ in range(num_samples):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]  # 泊松分布参数λ
                # 使用泊松分布生成需求，λ既是均值也是方差
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)  # 转换为float以保持一致性
            demand_scenarios.append(current_scenario_demand)
        return demand_scenarios

    def _build_saa_model_for_one_replication(self, demand_samples_k: List[Dict[int, float]]):
        """构建单次SAA复制的模型"""
        model = gp.Model(f"SAA_DroneLocker_Rep")
        num_scenarios = len(demand_samples_k)

        y_rep = {j: model.addVar(vtype=GRB.BINARY, name=f"y_{j}") for j in self.sites}
        n_rep = {j: model.addVar(vtype=GRB.INTEGER, lb=0, name=f"n_{j}") for j in self.sites}
        x_qty_rep = {}
        z_rep = {}
        u_rep = {}

        for k_idx in range(num_scenarios):
            for i in self.customers:
                u_rep[i, k_idx] = model.addVar(vtype=GRB.BINARY, name=f"u_{i}_{k_idx}")
                for j in self.sites:
                    x_qty_rep[i, j, k_idx] = model.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_qty_{i}_{j}_{k_idx}")
                    z_rep[i, j, k_idx] = model.addVar(vtype=GRB.BINARY, name=f"z_{i}_{j}_{k_idx}")

        locker_cost_fixed_part = gp.quicksum(self.locker_fixed_cost[j] * y_rep[j] for j in self.sites)
        drone_deployment_cost_fixed_part = gp.quicksum(self.drone_cost * n_rep[j] for j in self.sites)
        expected_second_stage_cost = gp.LinExpr()

        for k_idx in range(num_scenarios):
            demand_for_scenario_k = demand_samples_k[k_idx]
            transport_cost_k = gp.quicksum(
                2 * self.transport_unit_cost * self.distance[i, j] * x_qty_rep[i, j, k_idx]
                for i in self.customers for j in self.sites if (i, j) in self.distance
            )
            unassigned_demand_penalty_k = gp.quicksum(
                self.penalty_cost_unassigned *
                (demand_for_scenario_k[i] - gp.quicksum(x_qty_rep.get((i, site_k, k_idx),0) for site_k in self.sites))
                for i in self.customers
            )
            expected_second_stage_cost += (1.0 / num_scenarios) * (transport_cost_k + unassigned_demand_penalty_k)

        truck_cost_var_saa = model.addVar(lb=0.0, name="truck_cost_saa")
        model._truck_cost_var_saa = truck_cost_var_saa

        total_expected_cost = locker_cost_fixed_part + drone_deployment_cost_fixed_part + \
                              expected_second_stage_cost + truck_cost_var_saa
        model.setObjective(total_expected_cost, GRB.MINIMIZE)

        # 约束条件
        model.addConstr(gp.quicksum(y_rep[j] for j in self.sites) >= 1, "C1_AtLeastOneLocker_SAA")
        for j in self.sites:
            model.addConstr(n_rep[j] >= y_rep[j], name=f"C6_MinOneDroneIfOpen_SAA_{j}")

        for k_idx in range(num_scenarios):
            demand_for_scenario_k = demand_samples_k[k_idx]
            for i in self.customers:
                model.addConstr(
                    gp.quicksum(x_qty_rep.get((i, j, k_idx),0) for j in self.sites) <= demand_for_scenario_k[i],
                    name=f"C2_TotalAssignedQtyLimit_SAA_{i}_{k_idx}"
                )
                for j in self.sites:
                    model.addConstr(x_qty_rep[i, j, k_idx] <= demand_for_scenario_k[i] * z_rep[i, j, k_idx],
                                     name=f"Link_x_z_upper_SAA_{i}_{j}_{k_idx}")
                    model.addConstr(x_qty_rep[i, j, k_idx] >= z_rep[i, j, k_idx],
                                     name=f"Link_x_z_lower_SAA_{i}_{j}_{k_idx}")
                sum_z_i_k = gp.quicksum(z_rep[i, j, k_idx] for j in self.sites)
                model.addConstr(sum_z_i_k <= len(self.sites) * (1 - u_rep[i, k_idx]),
                                     name=f"CustomerAssignmentUpperBound_SAA_{i}_{k_idx}")
                model.addConstr(sum_z_i_k >= (1 - u_rep[i, k_idx]),
                                     name=f"CustomerAssignmentLowerBound_SAA_{i}_{k_idx}")
                for j in self.sites:
                    model.addConstr(z_rep[i, j, k_idx] <= y_rep[j], name=f"C3_AssignIfOpen_SAA_{i}_{j}_{k_idx}")
                    if (i, j) in self.distance:
                        model.addConstr(
                            2 * self.distance[i, j] * z_rep[i, j, k_idx] <= self.max_flight_distance,
                            name=f"C5_FlightDistanceLimit_SAA_{i}_{j}_{k_idx}"
                        )
            for j in self.sites:
                model.addConstr(gp.quicksum(z_rep[i, j, k_idx] for i in self.customers) >= y_rep[j],
                                     name=f"C4_OpenLockerServesOne_SAA_{j}_{k_idx}")
                total_drone_hours_needed_j_k = gp.quicksum(
                    x_qty_rep[i, j, k_idx] * ((2 * self.distance[i, j] / self.drone_speed) + self.loading_time)
                    for i in self.customers if (i, j) in self.distance
                )
                drone_hours_supplied_j = n_rep[j] * self.H_drone_working_hours_per_day
                model.addConstr(total_drone_hours_needed_j_k <= drone_hours_supplied_j,
                                     name=f"C7_DroneServiceCapacity_SAA_{j}_{k_idx}")
                model.addConstr(
                    gp.quicksum(x_qty_rep.get((i, j, k_idx),0) for i in self.customers) <= self.Q_locker_capacity[j] * y_rep[j],
                    name=f"C8_LockerOrderCapacity_SAA_{j}_{k_idx}"
                )

        model._callback_count_saa = 0
        model._solution_cache_saa = {}

        def truck_cost_callback_saa(model_cb, where):
            if where == GRB.Callback.MIPSOL:
                model_cb._callback_count_saa += 1
                y_sol_cb_vals = model_cb.cbGetSolution(y_rep)
                selected_lockers_cb_saa = [j_site for j_site in self.sites if y_sol_cb_vals[j_site] > 0.5]
                solution_key_saa = tuple(sorted(selected_lockers_cb_saa))

                current_truck_cost_cb_saa = 0.0
                if solution_key_saa in model_cb._solution_cache_saa:
                    current_truck_cost_cb_saa = model_cb._solution_cache_saa[solution_key_saa]
                else:
                    avg_x_qty_for_truck_calc = {}
                    if selected_lockers_cb_saa:
                        for i_c in self.customers:
                            for j_l in selected_lockers_cb_saa:
                                sum_qty_ij = 0
                                for k_s_idx in range(num_scenarios):
                                    sum_qty_ij += model_cb.cbGetSolution(x_qty_rep.get((i_c, j_l, k_s_idx),0.0))
                                avg_x_qty_for_truck_calc[i_c, j_l] = sum_qty_ij / num_scenarios if num_scenarios > 0 else 0

                    current_truck_cost_cb_saa = self.calculate_truck_cost(
                        selected_lockers_cb_saa,
                        avg_x_qty_for_truck_calc,
                        make_plots=False
                    )
                    model_cb._solution_cache_saa[solution_key_saa] = current_truck_cost_cb_saa
                model_cb.cbLazy(model_cb._truck_cost_var_saa >= current_truck_cost_cb_saa)

        model._callback_saa = truck_cost_callback_saa
        model.update()
        return model, y_rep, n_rep, x_qty_rep, z_rep, u_rep

    def solve_saa(self, time_limit_per_replication: int = 300, mip_gap_per_replication: float = 0.01):
        """求解SAA问题"""
        print(f"\n开始SAA优化，最多 {SAA_MAX_REPLICATIONS_M} 次复制，最少 {SAA_MIN_REPLICATIONS_M} 次...")
        print(f"终止条件: Gap阈值 < {SAA_GAP_TOLERANCE_PERCENT*100:.0f}% 或 方差阈值 < {SAA_VARIANCE_TOLERANCE_PERCENT*100:.0f}%")

        # 生成固定的验证样本集 (所有复制共用)
        print(f"生成 {SAA_SAMPLES_K_PRIME} 个固定验证样本 (所有复制共用)...")
        self.fixed_validation_samples = self._generate_demand_samples(num_samples=SAA_SAMPLES_K_PRIME)

        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = []

        best_solution_info = {'y': None, 'n': None, 'avg_obj_k_prime': float('inf'), 'replication_idx': -1, 'truck_cost_k_prime': 0.0}

        for m_rep in range(SAA_MAX_REPLICATIONS_M):
            print(f"\n--- SAA 复制 {m_rep + 1}/{SAA_MAX_REPLICATIONS_M} ---")
            demand_samples_for_k = self._generate_demand_samples(num_samples=SAA_SAMPLES_K)
            print(f"  已生成 {SAA_SAMPLES_K} 个需求场景用于求解。")

            saa_model_rep, y_vars, n_vars, _, _, _ = \
                self._build_saa_model_for_one_replication(demand_samples_for_k)

            saa_model_rep.setParam('TimeLimit', time_limit_per_replication)
            saa_model_rep.setParam('MIPGap', mip_gap_per_replication)
            saa_model_rep.setParam('OutputFlag', 0)
            saa_model_rep.setParam('LogToConsole', 0)
            saa_model_rep.setParam('LazyConstraints', 1)
            saa_model_rep.setParam('Threads', 0)

            print(f"  开始求解复制 {m_rep + 1} 的SAA模型...")
            solve_start_time_rep = time.time()
            saa_model_rep.optimize(saa_model_rep._callback_saa)
            solve_time_rep = time.time() - solve_start_time_rep
            print(f"  复制 {m_rep + 1} 求解完成，耗时: {solve_time_rep:.2f} 秒，状态: {saa_model_rep.status}")

            if saa_model_rep.SolCount > 0:
                obj_val_k = saa_model_rep.ObjVal
                self.saa_objective_values_k.append(obj_val_k)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K} 个样本上的目标值: {obj_val_k:.2f}")

                y_star_m = {j: y_vars[j].X for j in self.sites}
                n_star_m = {j: n_vars[j].X for j in self.sites}
                self.saa_solutions_first_stage.append({'y':y_star_m, 'n':n_star_m})

                print(f"  评估复制 {m_rep + 1} 的解在 {SAA_SAMPLES_K_PRIME} 个固定验证样本上的性能...")
                avg_obj_k_prime, avg_truck_cost_for_k_prime_eval = self._evaluate_solution_on_new_samples(y_star_m, n_star_m, self.fixed_validation_samples)
                self.saa_upper_bounds_k_prime.append(avg_obj_k_prime)
                self.saa_truck_costs_k_prime.append(avg_truck_cost_for_k_prime_eval)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均目标值 (UB估计): {avg_obj_k_prime:.2f}")
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均卡车成本: {avg_truck_cost_for_k_prime_eval:.2f}")

                if avg_obj_k_prime < best_solution_info['avg_obj_k_prime']:
                    best_solution_info['y'] = y_star_m
                    best_solution_info['n'] = n_star_m
                    best_solution_info['avg_obj_k_prime'] = avg_obj_k_prime
                    best_solution_info['replication_idx'] = m_rep
                    best_solution_info['truck_cost_k_prime'] = avg_truck_cost_for_k_prime_eval
            else:
                print(f"  复制 {m_rep + 1} 未能找到可行解。")
                self.saa_objective_values_k.append(float('inf'))
                self.saa_upper_bounds_k_prime.append(float('inf'))
                self.saa_truck_costs_k_prime.append(float('inf'))
                self.saa_solutions_first_stage.append(None)

            del saa_model_rep

            # 检查SAA终止条件 (在最少复制次数后)
            if m_rep + 1 >= SAA_MIN_REPLICATIONS_M:
                should_terminate, gap_info = self._check_saa_termination_criteria()
                if should_terminate:
                    print(f"\n✓ SAA终止条件满足，在第 {m_rep + 1} 次复制后停止")
                    print(f"  {gap_info}")
                    break
                else:
                    print(f"  当前SAA状态: {gap_info}")

        if not any(s is not None for s in self.saa_solutions_first_stage):
            print("\nSAA 未能产生任何有效解。")
            return None

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        avg_saa_obj_k = np.mean(valid_obj_k) if valid_obj_k else float('inf')
        std_saa_obj_k = np.std(valid_obj_k) if valid_obj_k else float('inf')
        avg_upper_bound = np.mean(valid_ub_k_prime) if valid_ub_k_prime else float('inf')
        std_upper_bound = np.std(valid_ub_k_prime) if valid_ub_k_prime else float('inf')

        statistical_lower_bound = avg_saa_obj_k
        statistical_upper_bound = avg_upper_bound

        actual_replications = len([s for s in self.saa_solutions_first_stage if s is not None])
        print(f"\n--- SAA 结果汇总 ({actual_replications} 次有效复制) ---")
        print(f"  平均目标值 (在 {SAA_SAMPLES_K} 个样本上，仅有效解): {avg_saa_obj_k:.2f} (标准差: {std_saa_obj_k:.2f})")
        print(f"  平均上界估计 (在 {SAA_SAMPLES_K_PRIME} 个样本上，仅有效解): {avg_upper_bound:.2f} (标准差: {std_upper_bound:.2f})")
        print(f"  统计下界 (LB_M): {statistical_lower_bound:.2f}")
        print(f"  统计上界 (UB_M): {statistical_upper_bound:.2f}")

        if best_solution_info['y'] is not None:
            print(f"\n最佳解来自复制 {best_solution_info['replication_idx'] + 1}，其在 {SAA_SAMPLES_K_PRIME} 个样本上的评估目标值为: {best_solution_info['avg_obj_k_prime']:.2f}")
            final_solution_saa = {
                'objective_value_k_prime_estimate': best_solution_info['avg_obj_k_prime'],
                'selected_lockers_y': best_solution_info['y'],
                'drone_allocations_n': best_solution_info['n'],
                'truck_cost_k_prime_estimate': best_solution_info['truck_cost_k_prime'],
            }
            print(f"  最佳解选定的储物柜 (y*): {[j for j, val in best_solution_info['y'].items() if val > 0.5]}")
            print(f"  最佳解无人机分配 (n*): {dict((j, round(val)) for j, val in best_solution_info['n'].items() if best_solution_info['y'].get(j,0) > 0.5)}")
            return final_solution_saa
        else:
            print("\nSAA 未能找到任何有效的最终解。")
            return None

    def _evaluate_solution_on_new_samples(self, y_star: Dict, n_star: Dict, demand_samples_k_prime: List[Dict[int,float]]):
        """在新样本上评估解的性能"""
        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        for k_prime_idx, demand_scenario_k_prime in enumerate(demand_samples_k_prime):
            model_sp = gp.Model(f"SP_eval_{k_prime_idx}")
            model_sp.setParam('OutputFlag', 0)
            model_sp.setParam('Threads', 1)

            x_sp = {}
            z_sp = {}
            u_sp = {i: model_sp.addVar(vtype=GRB.BINARY, name=f"u_sp_{i}") for i in self.customers}

            for i in self.customers:
                for j_site in selected_lockers_eval:
                    x_sp[i, j_site] = model_sp.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_sp_{i}_{j_site}")
                    z_sp[i, j_site] = model_sp.addVar(vtype=GRB.BINARY, name=f"z_sp_{i}_{j_site}")

            transport_cost_sp = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j_site), self.BIG_M) * x_sp.get((i, j_site),0)
                for i in self.customers for j_site in selected_lockers_eval
            )
            unassigned_penalty_sp = gp.quicksum(
                self.penalty_cost_unassigned *
                (demand_scenario_k_prime[i] - gp.quicksum(x_sp.get((i, site_k_sp),0) for site_k_sp in selected_lockers_eval))
                for i in self.customers
            )
            model_sp.setObjective(transport_cost_sp + unassigned_penalty_sp, GRB.MINIMIZE)

            for i in self.customers:
                model_sp.addConstr(gp.quicksum(x_sp.get((i,j_site),0) for j_site in selected_lockers_eval) <= demand_scenario_k_prime[i])
                for j_site in selected_lockers_eval:
                    model_sp.addConstr(x_sp[i, j_site] <= demand_scenario_k_prime[i] * z_sp[i, j_site])
                    model_sp.addConstr(x_sp[i, j_site] >= z_sp[i, j_site])
                sum_z_i_sp = gp.quicksum(z_sp.get((i,j_site),0) for j_site in selected_lockers_eval)
                model_sp.addConstr(sum_z_i_sp <= len(selected_lockers_eval) * (1 - u_sp[i]))
                model_sp.addConstr(sum_z_i_sp >= (1 - u_sp[i]))
                for j_site in selected_lockers_eval:
                    if (i, j_site) in self.distance:
                         model_sp.addConstr(2 * self.distance[i, j_site] * z_sp[i, j_site] <= self.max_flight_distance)
            for j_site in selected_lockers_eval:
                total_drone_hours_needed_j_sp = gp.quicksum(
                    x_sp.get((i,j_site),0) * ((2 * self.distance.get((i, j_site), self.BIG_M) / self.drone_speed) + self.loading_time)
                    for i in self.customers
                )
                model_sp.addConstr(total_drone_hours_needed_j_sp <= n_star.get(j_site,0) * self.H_drone_working_hours_per_day)
                model_sp.addConstr(gp.quicksum(x_sp.get((i,j_site),0) for i in self.customers) <= self.Q_locker_capacity[j_site])

            model_sp.optimize()
            second_stage_cost_k_prime = 0
            x_qty_sol_for_truck_k_prime = {}
            if model_sp.status == GRB.OPTIMAL:
                second_stage_cost_k_prime = model_sp.ObjVal
                for i_c_sp in self.customers:
                    for j_l_sp in selected_lockers_eval:
                        if (i_c_sp, j_l_sp) in x_sp:
                             x_qty_sol_for_truck_k_prime[i_c_sp, j_l_sp] = x_sp[i_c_sp, j_l_sp].X
            else:
                second_stage_cost_k_prime = self.BIG_M * sum(demand_scenario_k_prime.values())

            truck_cost_k_prime = self.calculate_truck_cost(selected_lockers_eval, x_qty_sol_for_truck_k_prime, make_plots=False)
            total_truck_cost_over_k_prime_samples += truck_cost_k_prime

            total_cost_for_scenario_k_prime = locker_cost_fixed + drone_deployment_cost_fixed + \
                                              second_stage_cost_k_prime + truck_cost_k_prime
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime
            del model_sp
        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        return avg_total_cost_k_prime, avg_truck_cost_k_prime

    def _check_saa_termination_criteria(self):
        """检查SAA终止条件"""
        import scipy.stats as stats

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        if len(valid_obj_k) < SAA_MIN_REPLICATIONS_M or len(valid_ub_k_prime) < SAA_MIN_REPLICATIONS_M:
            return False, f"有效解数量不足 ({len(valid_obj_k)}, {len(valid_ub_k_prime)} < {SAA_MIN_REPLICATIONS_M})"

        # 计算统计量
        avg_saa_obj_k = np.mean(valid_obj_k)  # LB_M (下界)
        std_saa_obj_k = np.std(valid_obj_k, ddof=1) if len(valid_obj_k) > 1 else 0
        avg_upper_bound = np.mean(valid_ub_k_prime)  # UB_M (上界)
        std_upper_bound = np.std(valid_ub_k_prime, ddof=1) if len(valid_ub_k_prime) > 1 else 0

        # SAA Gap计算 (按论文公式)
        saa_gap = avg_upper_bound - avg_saa_obj_k
        gap_percent = saa_gap / avg_upper_bound if avg_upper_bound > 0 else float('inf')  # Gap_{N,N'}(ŝ)

        # SAA Gap方差计算
        gap_variance = std_upper_bound ** 2  # δ²_{Gap}(ŝ)
        variance_percent = gap_variance / avg_upper_bound if avg_upper_bound > 0 else float('inf')

        # 置信区间计算 (使用t分布)
        n_replications = len(valid_obj_k)
        t_critical = stats.t.ppf(1 - SAA_CONFIDENCE_LEVEL_ALPHA/2, n_replications - 1)

        # 下界置信区间
        lb_margin = t_critical * std_saa_obj_k / np.sqrt(n_replications) if std_saa_obj_k > 0 else 0
        lb_confidence_interval = (avg_saa_obj_k - lb_margin, avg_saa_obj_k + lb_margin)

        # 上界置信区间
        ub_margin = t_critical * std_upper_bound / np.sqrt(n_replications) if std_upper_bound > 0 else 0
        ub_confidence_interval = (avg_upper_bound - ub_margin, avg_upper_bound + ub_margin)

        # 终止条件检查 (按论文标准)
        # 条件1: Gap阈值 ε' = 5%
        gap_condition = gap_percent <= SAA_GAP_TOLERANCE_PERCENT

        # 条件2: 方差阈值 ε = 3%
        variance_condition = variance_percent <= SAA_VARIANCE_TOLERANCE_PERCENT

        # 满足任一条件即可终止
        should_terminate = gap_condition or variance_condition

        # 如果gap为负，说明统计不稳定，不应该终止
        if saa_gap < 0:
            should_terminate = False

        gap_info = (f"Gap: {gap_percent:.2%} (阈值: {SAA_GAP_TOLERANCE_PERCENT:.0%}), "
                   f"方差: {variance_percent:.2%} (阈值: {SAA_VARIANCE_TOLERANCE_PERCENT:.0%}), "
                   f"LB: {avg_saa_obj_k:.2f}±{lb_margin:.2f}, "
                   f"UB: {avg_upper_bound:.2f}±{ub_margin:.2f}")

        return should_terminate, gap_info

    def _print_saa_solution(self, saa_solution_dict: Dict):
        """打印SAA解决方案的详细信息，包括客户分配和可视化"""
        if not saa_solution_dict:
            print("SAA 未找到有效解。")
            return

        print("\n" + "=" * 60 + "\nSAA 优化结果\n" + "=" * 60)
        print(f"  最终评估目标值 (在 {SAA_SAMPLES_K_PRIME} 个样本上): {saa_solution_dict.get('objective_value_k_prime_estimate', 'N/A'):.2f}")
        print(f"  估计的卡车成本 (在 {SAA_SAMPLES_K_PRIME} 个样本上): {saa_solution_dict.get('truck_cost_k_prime_estimate', 'N/A'):.2f}")

        y_star_final = saa_solution_dict.get('selected_lockers_y', {})
        n_star_final = saa_solution_dict.get('drone_allocations_n', {})
        selected_lockers_print = [j for j, val in y_star_final.items() if val > 0.5]
        print(f"  选定的储物柜站点 (y*): {selected_lockers_print}")
        print(f"  各站点无人机分配 (n*):")
        for j_site_p in selected_lockers_print:
            print(f"    位置 {j_site_p}: 配备 {round(n_star_final.get(j_site_p,0))} 架无人机")

        print("\n  客户分配 (基于期望需求和选定的y*, n*):")
        deterministic_alloc_model = gp.Model("FinalDeterministicAlloc")
        deterministic_alloc_model.setParam('OutputFlag', 0)
        deterministic_alloc_model.setParam('Threads', 1)

        x_det_alloc = {}
        z_det_alloc = {}
        u_det_alloc = {i: deterministic_alloc_model.addVar(vtype=GRB.BINARY) for i in self.customers}
        for i_cust_da in self.customers:
            for j_site_da in selected_lockers_print:
                x_det_alloc[i_cust_da, j_site_da] = deterministic_alloc_model.addVar(vtype=GRB.INTEGER, lb=0)
                z_det_alloc[i_cust_da, j_site_da] = deterministic_alloc_model.addVar(vtype=GRB.BINARY)

        transport_cost_da = gp.quicksum(
            2 * self.transport_unit_cost * self.distance.get((i,j), self.BIG_M) * x_det_alloc.get((i,j),0)
            for i in self.customers for j in selected_lockers_print
        )
        penalty_da = gp.quicksum(
            self.penalty_cost_unassigned * (self.expected_demand[i] - gp.quicksum(x_det_alloc.get((i,j),0) for j in selected_lockers_print))
            for i in self.customers
        )
        deterministic_alloc_model.setObjective(transport_cost_da + penalty_da, GRB.MINIMIZE)

        for i_cust_da in self.customers:
            deterministic_alloc_model.addConstr(gp.quicksum(x_det_alloc.get((i_cust_da, j),0) for j in selected_lockers_print) <= self.expected_demand[i_cust_da])
            for j_site_da in selected_lockers_print:
                deterministic_alloc_model.addConstr(x_det_alloc[i_cust_da, j_site_da] <= self.expected_demand[i_cust_da] * z_det_alloc[i_cust_da, j_site_da])
                deterministic_alloc_model.addConstr(x_det_alloc[i_cust_da, j_site_da] >= z_det_alloc[i_cust_da, j_site_da])
            sum_z_da = gp.quicksum(z_det_alloc.get((i_cust_da, j),0) for j in selected_lockers_print)
            deterministic_alloc_model.addConstr(sum_z_da <= len(selected_lockers_print) * (1 - u_det_alloc[i_cust_da]))
            deterministic_alloc_model.addConstr(sum_z_da >= (1 - u_det_alloc[i_cust_da]))
            for j_site_da in selected_lockers_print:
                 if (i_cust_da, j_site_da) in self.distance:
                     deterministic_alloc_model.addConstr(2 * self.distance[i_cust_da, j_site_da] * z_det_alloc[i_cust_da, j_site_da] <= self.max_flight_distance)
        for j_site_da in selected_lockers_print:
            hours_needed_da = gp.quicksum(
                x_det_alloc.get((i,j_site_da),0) * ((2*self.distance.get((i,j_site_da), self.BIG_M)/self.drone_speed) + self.loading_time)
                for i in self.customers
            )
            deterministic_alloc_model.addConstr(hours_needed_da <= n_star_final.get(j_site_da,0) * self.H_drone_working_hours_per_day)
            deterministic_alloc_model.addConstr(gp.quicksum(x_det_alloc.get((i,j_site_da),0) for i in self.customers) <= self.Q_locker_capacity[j_site_da])

        deterministic_alloc_model.optimize()

        customer_assignment_quantities_final_print = {i: {} for i in self.customers}
        customer_assignments_primary_final_print = {i: [] for i in self.customers}
        unassigned_customers_final_print = []

        if deterministic_alloc_model.status == GRB.OPTIMAL:
            for i_cust_fp in self.customers:
                is_assigned_fp = False
                for j_site_fp in selected_lockers_print:
                    qty_fp_var = x_det_alloc.get((i_cust_fp, j_site_fp), None)
                    z_fp_var = z_det_alloc.get((i_cust_fp, j_site_fp), None)
                    if qty_fp_var is not None and qty_fp_var.X > 0.5:
                        customer_assignment_quantities_final_print[i_cust_fp][j_site_fp] = round(qty_fp_var.X)
                        if z_fp_var is not None and z_fp_var.X > 0.5:
                           if j_site_fp not in customer_assignments_primary_final_print[i_cust_fp]: # 避免重复添加
                                customer_assignments_primary_final_print[i_cust_fp].append(j_site_fp)
                        is_assigned_fp = True

                u_fp_var = u_det_alloc.get(i_cust_fp, None)
                if u_fp_var is not None and u_fp_var.X > 0.5 and not is_assigned_fp :
                     unassigned_customers_final_print.append(i_cust_fp)

            total_assigned_demand = 0
            assigned_customers_count = 0
            unassigned_customers_count = 0

            for i_cust_p in self.customers:
                assignments_p = customer_assignment_quantities_final_print.get(i_cust_p, {})
                primary_p = customer_assignments_primary_final_print.get(i_cust_p, [])
                total_demand_p = self.expected_demand[i_cust_p]
                total_assigned_p = sum(assignments_p.values())

                if assignments_p:
                    status_p = "完全满足期望" if abs(total_assigned_p - total_demand_p) < 1e-5 else f"部分满足期望({total_assigned_p:.1f}/{total_demand_p})"
                    print(f"    客户 {i_cust_p}: {assignments_p} - {status_p} (分配到储物柜: {primary_p})")
                    total_assigned_demand += total_assigned_p
                    assigned_customers_count += 1
                elif i_cust_p in unassigned_customers_final_print or not primary_p : # 如果u=1或者没有任何分配
                    print(f"    客户 {i_cust_p}: 未分配 (期望需求: {total_demand_p})")
                    unassigned_customers_count += 1

            print(f"\n  分配汇总:")
            print(f"    已分配客户数: {assigned_customers_count}/{len(self.customers)}")
            print(f"    未分配客户数: {unassigned_customers_count}/{len(self.customers)}")
            print(f"    总分配需求量: {total_assigned_demand:.2f}")
            print(f"    总期望需求量: {sum(self.expected_demand.values()):.2f}")
            print(f"    需求满足率: {total_assigned_demand/sum(self.expected_demand.values())*100:.1f}%")
        else:
            print("    未能为最终(y*, n*)计算出代表性的确定性分配。")

        # 可视化部分
        if deterministic_alloc_model.status == GRB.OPTIMAL:
            viz_solution_saa = {
                'selected_lockers': {j:True for j in selected_lockers_print},
                'customer_assignments_primary': customer_assignments_primary_final_print,
                'unassigned_customers_by_u': unassigned_customers_final_print, # 使用u变量的结果
                'drone_allocations': {j: round(n_star_final.get(j,0)) for j in selected_lockers_print},
                'objective_value': saa_solution_dict.get('objective_value_k_prime_estimate', 0.0)  # 添加目标值用于可视化
            }
            print("\n  显示SAA最终解的可视化 (基于期望需求的分配)...")
            self.visualize_solution(viz_solution_saa)
        del deterministic_alloc_model

    def visualize_solution(self, solution: Dict):
        """可视化SAA解决方案，支持多储物柜分配"""
        if not solution:
            print("无解决方案可供可视化。")
            return

        temp_solution_for_viz = solution.copy()
        primary_assignments = solution.get('customer_assignments_primary', {})

        # 修改：支持多储物柜分配的可视化
        # 直接传递完整的储物柜列表给可视化函数
        customer_assignments_viz = {}

        for cust, lockers in primary_assignments.items():
            if lockers: # 如果有分配的储物柜
                # 直接传递储物柜列表，visualization.py会自动处理多储物柜连线
                customer_assignments_viz[cust] = lockers

        temp_solution_for_viz['customer_assignments'] = customer_assignments_viz

        customer_service_modes = {}
        for customer_id in customer_assignments_viz: # 使用调整后的键
            customer_service_modes[customer_id] = 0
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # unassigned_customers 可能需要从 solution['unassigned_customers_by_u'] 获取
        if 'unassigned_customers_by_u' in solution:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']

        # 移除可能引起混淆的旧键
        if 'customer_assignments_primary' in temp_solution_for_viz:
            del temp_solution_for_viz['customer_assignments_primary']
        if 'unassigned_customers_by_u' in temp_solution_for_viz and 'unassigned_customers' in temp_solution_for_viz :
             if temp_solution_for_viz['unassigned_customers_by_u'] == temp_solution_for_viz['unassigned_customers']:
                 del temp_solution_for_viz['unassigned_customers_by_u']

        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="SAA 无人机配送网络规划 (基于期望需求分配)"
        )
        if plt_fig:
            import matplotlib.pyplot as plt
            plt.show(block=False) # 使用 block=False 避免阻塞后续代码，如果需要交互则去掉
            plt.pause(1) # 暂停一下，确保图像显示


def create_deterministic_example_instance(
        num_customers: int = 15,
        num_sites: int = 6,
        use_kmeans_clustering: bool = False,
        demand_level: str = "medium",
        locker_cost_level: str = "medium",
        drone_cost_level: str = "medium",
        drone_transport_cost_level: str = "medium",
        use_generated_distances: bool = True,
        random_seed: int = RANDOM_SEED
):
    """创建示例问题的参数实例"""
    local_random = random.Random(random_seed)  # 使用局部随机数生成器以控制种子
    customers_list = list(range(1, num_customers + 1))
    sites_list = list(range(1, num_sites + 1))

    # 生成客户坐标
    customer_coords_dict = {i: (local_random.uniform(0, 15), local_random.uniform(0, 15)) for i in customers_list}

    # 生成储物柜站点坐标 (可选K-Means聚类)
    if use_kmeans_clustering:
        site_coords_dict = generate_locker_sites_with_kmeans(customer_coords_dict, num_sites, random_state=random_seed)
    else:
        site_coords_dict = {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in sites_list}

    # 生成客户确定性需求 (λᵢ)
    demand_params_dict = {"low": (1, 2), "medium": (2, 3), "high": (3, 4)}.get(demand_level, (2, 3))
    demand_deterministic_dict = {c: local_random.randint(demand_params_dict[0], demand_params_dict[1]) for c in
                                 customers_list}

    # 储物柜固定成本 (cˡⱼ)
    # 适当降低储物柜固定成本，使模型更愿意开设多个储物柜
    locker_base_cost_val = {"low": 6000, "medium": 8000, "high": 12000}.get(locker_cost_level, 8000)  # 降低成本
    locker_fixed_cost_dict = {s: locker_base_cost_val for s in sites_list}

    # 无人机购置成本 (pᵈ)
    drone_cost_val_param = {"low": 2500, "medium": 3500, "high": 4500}.get(drone_cost_level, 3500)  # 调整了成本示例

    # 无人机运输单位成本 (cᵈ)
    transport_unit_cost_val_param = {"low": 0.008, "medium": 0.015, "high": 0.025}.get(drone_transport_cost_level,
                                                                                       0.015)  # 调整了成本示例

    # 系统参数
    drone_speed_param = 50.0
    loading_time_param = 300.0 / 3600.0
    max_flight_distance_param = 20.0
    H_drone_working_hours_per_day_param = 8.0

    # 未分配单位需求的惩罚成本 (cᴾ)
    penalty_cost_unassigned_param = 1000.0  # 提高到1000元/订单，使惩罚成本非常高

    # 储物柜最大服务能力 (Qⱼ, 订单/天)
    # 增加储物柜容量以适应更多客户需求
    avg_locker_capacity_val = 30
    Q_locker_capacity_dict = {s: avg_locker_capacity_val for s in sites_list}

    # DRL求解器相关参数 (卡车运输)
    depot_coord_param = (0, 0)  # 仓库坐标
    truck_capacity_param = 80  # 卡车容量 (订单)
    truck_fixed_cost_param = 1000  # 卡车固定成本 (元/车次或天)
    truck_km_cost_param = 0.5  # 卡车每公里成本 (元/公里)

    # 生成距离矩阵 dᵢⱼ
    distance_matrix_dict = {}
    if use_generated_distances:  # 基于坐标计算欧氏距离
        for c_id_val_dist, c_coord_val_dist in customer_coords_dict.items():
            for s_id_val_dist, s_coord_val_dist in site_coords_dict.items():
                dist_val = math.sqrt(
                    (c_coord_val_dist[0] - s_coord_val_dist[0]) ** 2 + (c_coord_val_dist[1] - s_coord_val_dist[1]) ** 2)
                distance_matrix_dict[c_id_val_dist, s_id_val_dist] = round(dist_val, 2)
    else:  # 随机生成一些距离 (不常用，通常基于坐标)
        for c_id_val_dist_rand in customers_list:
            for s_id_val_dist_rand in sites_list:
                # 粗略估计一个基础距离，然后加随机扰动
                base_dist_val = math.sqrt(
                    (customer_coords_dict[c_id_val_dist_rand][0] - site_coords_dict[s_id_val_dist_rand][0]) ** 2 + \
                    (customer_coords_dict[c_id_val_dist_rand][1] - site_coords_dict[s_id_val_dist_rand][1]) ** 2)
                distance_matrix_dict[c_id_val_dist_rand, s_id_val_dist_rand] = round(
                    local_random.uniform(base_dist_val * 0.8, base_dist_val * 1.2), 1)

    # 返回参数字典
    return {
        'customers': customers_list, 'sites': sites_list,
        'demand_deterministic': demand_deterministic_dict,
        'distance_matrix': distance_matrix_dict, 'drone_speed': drone_speed_param,
        'loading_time': loading_time_param, 'max_flight_distance': max_flight_distance_param,
        'locker_fixed_cost': locker_fixed_cost_dict, 'transport_unit_cost': transport_unit_cost_val_param,
        'drone_cost': drone_cost_val_param, 'H_drone_working_hours_per_day': H_drone_working_hours_per_day_param,
        'penalty_cost_unassigned': penalty_cost_unassigned_param,
        'Q_locker_capacity': Q_locker_capacity_dict, 'customer_coords': customer_coords_dict,
        'site_coords': site_coords_dict, 'depot_coord': depot_coord_param,
        'truck_capacity': truck_capacity_param, 'truck_fixed_cost': truck_fixed_cost_param,
        'truck_km_cost': truck_km_cost_param
    }


# 主程序入口
if __name__ == "__main__":
    start_time_main = time.time()  # 记录主程序开始时间
    print(f"设置全局随机种子: {RANDOM_SEED}")

    # set_drl_log_level(logging.WARNING)  # 注释掉DRL相关设置
    print("使用Gurobi求解器替代DRL求解CVRP问题")

    print("\n创建随机需求的示例数据 (使用期望需求)...")
    # 创建一个规模较小的问题实例，以便快速测试
    stochastic_data_instance = create_deterministic_example_instance(
        demand_level="high", locker_cost_level="medium", drone_cost_level="medium",
        drone_transport_cost_level="medium", use_generated_distances=True,
        num_customers=20,  # 客户数量
        num_sites=3,  # 候选储物柜站点数量
        use_kmeans_clustering=True,
        random_seed=RANDOM_SEED
    )
    # 将确定性需求转换为期望需求
    stochastic_data_instance['expected_demand'] = stochastic_data_instance.pop('demand_deterministic')
    print("随机需求数据 (期望值) 已创建。")

    # 显示一些创建的数据信息
    print("\n所有客户期望需求 (λᵢ_bar):")
    for customer_id_saa, demand_val_saa in stochastic_data_instance['expected_demand'].items():
        print(f"  客户 {customer_id_saa}: {demand_val_saa} 订单/天 (期望)")
    total_expected_demand_val_saa = sum(stochastic_data_instance['expected_demand'].values())
    print(f"总期望需求: {total_expected_demand_val_saa} 订单/天")

    print("\n" + "=" * 60 + "\n求解带随机需求的无人机配送网络设计问题 (SAA)\n" + "=" * 60)
    solve_start_time_saa = time.time()

    optimizer_saa = StochasticDroneDeliveryOptimizerSAA()
    optimizer_saa.set_parameters(**stochastic_data_instance)

    final_saa_solution = optimizer_saa.solve_saa(
        time_limit_per_replication=60,  # 减小时间限制以便快速测试
        mip_gap_per_replication=0.01     # 增大MIP Gap以便快速测试
    )

    solve_time_saa = time.time() - solve_start_time_saa

    if final_saa_solution:
        optimizer_saa._print_saa_solution(final_saa_solution)
        print(f"\nSAA模型求解总耗时: {solve_time_saa:.2f} 秒")
    else:
        print("\n⚠ SAA未能找到可行解或优化失败。")

    total_time_with_saa = time.time() - start_time_main
    print(f"\n包含SAA的总运行时间: {total_time_with_saa:.2f} 秒")

    print("测试完成。如果图像窗口仍然打开，请手动关闭。")