#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成本差异分析脚本
分析3.py (ALNS) 和 g_i.py (精确方法) 之间的成本计算差异
"""

import numpy as np
import random
import math
from collections import defaultdict

# 设置随机种子确保可重现性
RANDOM_SEED = 606
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

def analyze_cost_differences():
    """
    分析两种方法的成本计算差异
    """
    print("=" * 80)
    print("成本差异分析：3.py (ALNS) vs g_i.py (精确方法)")
    print("=" * 80)
    
    # 模拟相同的问题参数
    customers = list(range(1, 21))  # 20个客户
    sites = [1, 2, 3]  # 3个储物柜站点
    
    # 模拟解决方案（基于输出结果）
    # 3.py的解：储物柜[1, 2, 3], 无人机{1: 2, 2: 2, 3: 2}
    alns_solution = {
        'y': {1: 1, 2: 1, 3: 1},
        'n': {1: 2, 2: 2, 3: 2}
    }
    
    # 成本参数（基于create_deterministic_example_instance）
    locker_daily_cost = 41.10  # 日均固定成本
    drone_daily_cost = 10.96   # 日均固定成本
    transport_unit_cost = 0.01  # 运输单位成本
    penalty_cost_unassigned = 1000.0  # 未分配惩罚成本
    truck_fixed_cost = 100
    truck_km_cost = 0.5
    
    print("\n1. 第一阶段成本计算 (两种方法相同)")
    print("-" * 50)
    
    # 第一阶段成本
    selected_lockers = [j for j, val in alns_solution['y'].items() if val > 0.5]
    locker_cost = sum(locker_daily_cost for j in selected_lockers)
    drone_cost = sum(drone_daily_cost * alns_solution['n'][j] for j in selected_lockers)
    first_stage_cost = locker_cost + drone_cost
    
    print(f"储物柜固定成本: {locker_cost:.2f} (3个储物柜 × {locker_daily_cost:.2f})")
    print(f"无人机固定成本: {drone_cost:.2f} (6架无人机 × {drone_daily_cost:.2f})")
    print(f"第一阶段总成本: {first_stage_cost:.2f}")
    
    print("\n2. 第二阶段成本计算差异")
    print("-" * 50)
    
    print("\n2.1 样本使用差异:")
    print(f"  - 3.py (ALNS): 使用15个随机样本进行快速评估")
    print(f"  - g_i.py (精确): 使用全部40个样本进行完整评估")
    print(f"  影响: 样本数量差异可能导致期望成本估计不同")
    
    print("\n2.2 客户分配算法差异:")
    print(f"  - 3.py (ALNS): 使用FastAssignmentSolver启发式算法")
    print(f"  - g_i.py (精确): 使用Gurobi MIP精确求解器")
    print(f"  影响: 分配质量差异直接影响运输成本和惩罚成本")
    
    print("\n2.3 容量约束处理差异:")
    print(f"  - 3.py (ALNS): 使用软约束+惩罚函数处理容量违反")
    print(f"  - g_i.py (精确): 使用硬约束严格限制容量")
    print(f"  影响: 3.py可能允许轻微超载但增加惩罚成本")
    
    print("\n2.4 卡车成本计算差异:")
    print(f"  - 3.py (ALNS): 使用简化估算 (基于需求量和距离)")
    print(f"  - g_i.py (精确): 使用完整CVRP求解器优化路径")
    print(f"  影响: 卡车成本估算精度差异")
    
    print("\n3. 具体成本差异分析")
    print("-" * 50)
    
    # 基于输出结果的成本分析
    alns_total = 145.13  # 3.py的结果
    exact_total = 141.19  # g_i.py的结果
    difference = alns_total - exact_total
    
    print(f"3.py (ALNS) 总成本: {alns_total:.2f}")
    print(f"g_i.py (精确) 总成本: {exact_total:.2f}")
    print(f"成本差异: {difference:.2f} ({difference/exact_total*100:.1f}%)")
    
    print("\n4. 差异原因分析")
    print("-" * 50)
    
    print("可能的差异来源:")
    print("1. 启发式vs精确算法:")
    print("   - ALNS使用启发式客户分配，可能不是最优解")
    print("   - 精确方法使用MIP求解器找到最优分配")
    
    print("\n2. 样本数量差异:")
    print("   - ALNS只使用15个样本，统计误差较大")
    print("   - 精确方法使用40个样本，统计更稳定")
    
    print("\n3. 容量惩罚机制:")
    print("   - ALNS包含容量违反惩罚，增加了额外成本")
    print("   - 精确方法严格满足容量约束")
    
    print("\n4. 卡车成本估算:")
    print("   - ALNS使用简化估算，可能不够精确")
    print("   - 精确方法使用CVRP优化，路径更优")
    
    print("\n5. 建议改进方案")
    print("-" * 50)
    
    print("为了减少成本差异，可以考虑:")
    print("1. 增加ALNS的样本数量 (从15增加到40)")
    print("2. 改进FastAssignmentSolver的分配质量")
    print("3. 调整容量惩罚权重，使其更接近硬约束")
    print("4. 使用更精确的卡车成本估算方法")
    print("5. 在ALNS最终解上运行精确评估进行验证")
    
    return {
        'first_stage_cost': first_stage_cost,
        'alns_total': alns_total,
        'exact_total': exact_total,
        'difference': difference,
        'difference_percent': difference/exact_total*100
    }

if __name__ == "__main__":
    results = analyze_cost_differences()
    print(f"\n分析完成。成本差异: {results['difference']:.2f} ({results['difference_percent']:.1f}%)")
