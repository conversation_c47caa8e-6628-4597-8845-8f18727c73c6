import numpy as np
from typing import Dict, List, Tuple, Any
import random
import time
import math
from collections import defaultdict
import copy

# 尝试导入Numba进行性能优化
try:
    import numba
    from numba import jit, types
    NUMBA_AVAILABLE = True
    print("✅ Numba可用，将启用高性能编译优化")
except ImportError:
    NUMBA_AVAILABLE = False
    print("⚠️ Numba不可用，使用纯Python实现（性能较慢）")
try:
    from drl import DRL_CVRP_Solver, set_drl_log_level
    from clustering import generate_locker_sites_with_kmeans
    from visualization import visualize_solution
    DRL_AVAILABLE = True
except ImportError:
    print("警告: drl, clustering, 或 visualization 模块未找到。DRL和可视化功能将不可用。")
    DRL_AVAILABLE = False
    # 提供这些类的虚拟实现，以避免 NameError
    class DRL_CVRP_Solver:
        def __init__(self, *args, **kwargs): pass
        def solve(self, *args, **kwargs): return 0.0, None # 返回一个默认值
    def set_drl_log_level(level): pass
    def generate_locker_sites_with_kmeans(customer_coords, num_sites, random_state):
        print("警告: K-Means聚类不可用，将随机生成储物柜站点。")
        local_random = random.Random(random_state)
        return {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in range(1, num_sites + 1)}
    def visualize_solution(*args, **kwargs):
        print("警告: 可视化功能不可用。")
        return None


import logging # logging 应该在顶部导入

# 设置随机种子以确保结果可重现
RANDOM_SEED = 607
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# SAA 参数 (调整以提高统计稳定性)
SAA_MAX_REPLICATIONS_M = 10  # 最大复制次数
SAA_MIN_REPLICATIONS_M = 2   # 最小复制次数 (设为最小值，主要依靠终止条件)
SAA_SAMPLES_K = 40          # 训练样本数量 N (减少以提高求解效率)
SAA_SAMPLES_K_PRIME = 2000   # 验证样本数量 N' (保持不变，使用流式处理)
SAA_CONFIDENCE_LEVEL_ALPHA = 0.1  # 置信水平 (90%置信区间)
SAA_GAP_TOLERANCE_PERCENT = 0.03  # Gap阈值 ε' = 3%
SAA_VARIANCE_TOLERANCE_PERCENT = 0.05  # 方差阈值 ε = 5%

# 内存优化参数
MEMORY_BATCH_SIZE = 100      # 验证样本批处理大小
MAX_CACHE_SIZE = 1000        # 最大缓存条目数
ENABLE_MEMORY_OPTIMIZATION = True  # 启用内存优化
ENABLE_MEMORY_MONITORING = True    # 启用内存监控

def get_memory_usage():
    """获取当前内存使用情况（MB）"""
    try:
        import psutil
        import os
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # 转换为MB
    except ImportError:
        return 0  # 如果psutil不可用，返回0

def log_memory_usage(context=""):
    """记录内存使用情况"""
    if ENABLE_MEMORY_MONITORING:
        memory_mb = get_memory_usage()
        if memory_mb > 0:
            print(f"  [内存监控] {context}: {memory_mb:.1f} MB")
        return memory_mb
    return 0

# ============================================================================
# 优化后的Adaptive模式配置
# ============================================================================
#
# 核心特性：
# 1. 智能无人机调优算子：系统测试1-4架无人机的所有组合，选择成本最低配置
# 2. 减少无人机破坏算子：积极探索较少无人机的配置空间
# 3. 自适应第二阶段求解器：精确评估用Gurobi，启发式评估用贪心算法
# 4. 优化的初始解生成：从最少无人机配置开始，避免过度配置
#
# 性能表现：
# - 解质量：与Gurobi精确解几乎相同 (141.16 vs 141.18)
# - 求解速度：比Gurobi快约2倍 (135秒 vs 282秒)
# - 无人机配置：找到最优的每储物柜1架配置
# ============================================================================

USE_EXACT_SECOND_STAGE_SOLVER = "adaptive"  # 自适应选择：精确评估时用Gurobi，启发式评估时用贪心
USE_NUMBA_OPTIMIZATION = NUMBA_AVAILABLE  # 是否使用Numba优化

# ============================================================================
# Numba优化的核心算法
# ============================================================================

if NUMBA_AVAILABLE:
    @jit(nopython=True, cache=True)
    def numba_regret_assignment(customer_demands, locker_capacities, drone_capacities,
                               distance_matrix, transport_cost, customer_to_lockers,
                               num_customers, num_lockers):
        """
        Numba优化的后悔值分配算法

        参数:
        - customer_demands: 客户需求数组 [num_customers]
        - locker_capacities: 储物柜容量数组 [num_lockers]
        - drone_capacities: 无人机容量数组 [num_lockers]
        - distance_matrix: 距离矩阵 [num_customers, num_lockers]
        - transport_cost: 运输单位成本
        - customer_to_lockers: 客户可达储物柜矩阵 [num_customers, num_lockers] (0/1)
        - num_customers, num_lockers: 数量

        返回:
        - assignment: 分配矩阵 [num_customers, num_lockers]
        """
        # 初始化分配矩阵
        assignment = np.zeros((num_customers, num_lockers), dtype=np.float64)

        # 剩余容量
        rem_locker_caps = locker_capacities.copy()
        rem_drone_caps = drone_capacities.copy()

        # 剩余需求
        rem_demands = customer_demands.copy()

        # 主循环：直到所有需求分配完或无法继续分配
        max_iterations = min(50, num_customers * 3)  # 限制最大迭代次数，防止无限循环
        iteration = 0

        while iteration < max_iterations:
            iteration += 1
            best_customer = -1
            best_locker = -1
            best_regret = -1.0
            best_amount = 0.0

            # 为每个有剩余需求的客户计算后悔值
            for i in range(num_customers):
                if rem_demands[i] <= 1e-6:
                    continue

                # 找到该客户的最优和次优选择
                best_cost = 1e9
                second_best_cost = 1e9
                best_j = -1

                for j in range(num_lockers):
                    if customer_to_lockers[i, j] == 0:  # 不可达
                        continue

                    # 检查剩余容量
                    available_cap = min(rem_locker_caps[j], rem_drone_caps[j])
                    if available_cap <= 1e-6:
                        continue

                    # 计算成本
                    cost = 2.0 * transport_cost * distance_matrix[i, j]

                    if cost < best_cost:
                        second_best_cost = best_cost
                        best_cost = cost
                        best_j = j
                    elif cost < second_best_cost:
                        second_best_cost = cost

                # 计算后悔值
                if best_j >= 0:
                    if second_best_cost >= 1e8:  # 只有一个选择
                        regret = 1e6  # 高优先级
                    else:
                        regret = second_best_cost - best_cost

                    # 选择后悔值最大的客户
                    if regret > best_regret:
                        best_regret = regret
                        best_customer = i
                        best_locker = best_j
                        available_cap = min(rem_locker_caps[best_j], rem_drone_caps[best_j])
                        best_amount = min(rem_demands[i], available_cap)

            # 如果没有找到可分配的客户，退出
            if best_customer < 0 or best_amount <= 1e-6:
                break

            # 执行分配
            assignment[best_customer, best_locker] += best_amount
            rem_locker_caps[best_locker] -= best_amount
            rem_drone_caps[best_locker] -= best_amount
            rem_demands[best_customer] -= best_amount

            # 检查是否所有需求都已分配
            total_remaining = 0.0
            for i in range(num_customers):
                total_remaining += rem_demands[i]
            if total_remaining <= 1e-6:
                break

        return assignment

# ============================================================================
# 简化评估策略说明：
#
# 核心评估函数: _calculate_objective_heuristic 只调用 _estimate_service_costs
# 这个函数不涉及任何Gurobi调用，速度很快
#
# 精确评估函数: calculate_objective_cached 保持不变，使用Gurobi或启发式求解器
# 但只在ALNS找到潜在的全局最优解时才被调用一次，用于更新best_obj
#
# 预期效果：大幅减少Gurobi调用次数，显著提高求解效率
# ============================================================================

# ---------------------------------------------------------------------------
# create_deterministic_example_instance 函数 (全局作用域)
# ---------------------------------------------------------------------------
def create_deterministic_example_instance(
        num_customers: int = 15,
        num_sites: int = 6,
        use_kmeans_clustering: bool = False,
        demand_level: str = "medium",
        locker_cost_level: str = "medium",
        drone_cost_level: str = "medium",
        drone_transport_cost_level: str = "medium",
        use_generated_distances: bool = True,
        random_seed: int = RANDOM_SEED,
        # 新增年化成本计算参数
        annual_interest_rate: float = 0.04,  # IR: 年利率 4%
        equipment_life_years: int = 10,      # T_life: 设备生命周期 10年
        operating_days_per_year: int = 365   # D_year: 年运营天数
):
    """
    创建示例问题的参数实例 (用于确定性或期望值)

    修正版：使用年化成本计算，确保时间单位一致性
    - 储物柜和无人机成本从一次性投资转换为日均固定成本
    - 所有成本项统一使用日成本单位
    """
    local_random = random.Random(random_seed)
    customers_list = list(range(1, num_customers + 1))
    sites_list = list(range(1, num_sites + 1))

    customer_coords_dict = {i: (local_random.uniform(0, 15), local_random.uniform(0, 15)) for i in customers_list}

    if use_kmeans_clustering and DRL_AVAILABLE: # K-Means依赖clustering模块
        site_coords_dict = generate_locker_sites_with_kmeans(customer_coords_dict, num_sites, random_state=random_seed)
    else:
        site_coords_dict = {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in sites_list}

    demand_params_dict = {"low": (2, 4), "medium": (4, 6), "high": (6, 8)}.get(demand_level, (2, 3))
    demand_dict_for_instance = {c: local_random.randint(demand_params_dict[0], demand_params_dict[1]) for c in customers_list}

    # === 修正版成本计算：统一时间单位为日成本 ===

    # 第1步：计算资本回收因子 (Capital Recovery Factor)
    IR = annual_interest_rate
    T_life = equipment_life_years
    capital_recovery_factor = (IR * (1 + IR)**T_life) / ((1 + IR)**T_life - 1)

    print(f"  成本计算参数:")
    print(f"    年利率 (IR): {IR*100:.1f}%")
    print(f"    设备生命周期 (T_life): {T_life}年")
    print(f"    资本回收因子: {capital_recovery_factor:.6f}")
    print(f"    年运营天数: {operating_days_per_year}天")

    # 第2步：储物柜初始建设成本 -> 日均固定成本
    locker_initial_cost_val = {"low": 10000, "medium": 15000, "high": 20000}.get(locker_cost_level, 10000)
    locker_annual_cost = locker_initial_cost_val * capital_recovery_factor  # c_l^a
    locker_daily_cost = locker_annual_cost / operating_days_per_year        # c_l^daily
    locker_fixed_cost_dict = {s: locker_daily_cost for s in sites_list}

    print(f"  储物柜成本转换:")
    print(f"    初始建设成本: {locker_initial_cost_val:,.0f}元")
    print(f"    年化成本: {locker_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {locker_daily_cost:.2f}元/天")

    # 第3步：无人机初始采购成本 -> 日均固定成本
    drone_initial_cost_val = {"low": 3000, "medium": 4000, "high": 5000}.get(drone_cost_level, 3000)
    drone_annual_cost = drone_initial_cost_val * capital_recovery_factor     # c_d^a
    drone_daily_cost = drone_annual_cost / operating_days_per_year           # c_d^daily
    drone_cost_val_param = drone_daily_cost

    print(f"  无人机成本转换:")
    print(f"    初始采购成本: {drone_initial_cost_val:,.0f}元")
    print(f"    年化成本: {drone_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {drone_daily_cost:.2f}元/天")

    # 无人机运输单位成本
    transport_unit_cost_val_param = {"low": 0.01, "medium": 0.02, "high": 0.03}.get(drone_transport_cost_level, 0.01)

    drone_speed_param = 50.0
    loading_time_param = 300.0 / 3600.0
    max_flight_distance_param = 15.0
    H_drone_working_hours_per_day_param = 8.0
    penalty_cost_unassigned_param = 40.0  # 高惩罚成本促使系统分配更多客户
    avg_locker_capacity_val = 30
    Q_locker_capacity_dict = {s: avg_locker_capacity_val for s in sites_list}

    depot_coord_param = (0, 0)
    truck_capacity_param = 90
    truck_fixed_cost_param = 100
    truck_km_cost_param = 0.5

    distance_matrix_dict = {}
    if use_generated_distances:
        for c_id_val_dist, c_coord_val_dist in customer_coords_dict.items():
            for s_id_val_dist, s_coord_val_dist in site_coords_dict.items():
                dist_val = math.sqrt(
                    (c_coord_val_dist[0] - s_coord_val_dist[0]) ** 2 + (c_coord_val_dist[1] - s_coord_val_dist[1]) ** 2)
                distance_matrix_dict[c_id_val_dist, s_id_val_dist] = round(dist_val, 2)
    else:
        for c_id_val_dist_rand in customers_list:
            for s_id_val_dist_rand in sites_list:
                base_dist_val = math.sqrt(
                    (customer_coords_dict[c_id_val_dist_rand][0] - site_coords_dict[s_id_val_dist_rand][0]) ** 2 + \
                    (customer_coords_dict[c_id_val_dist_rand][1] - site_coords_dict[s_id_val_dist_rand][1]) ** 2)
                distance_matrix_dict[c_id_val_dist_rand, s_id_val_dist_rand] = round(
                    local_random.uniform(base_dist_val * 0.8, base_dist_val * 1.2), 1)

    # === 成本单位统一性验证 ===
    print(f"\n  成本单位统一性检查:")
    print(f"    储物柜固定成本: {locker_daily_cost:.2f} 元/天")
    print(f"    无人机固定成本: {drone_daily_cost:.2f} 元/天")
    print(f"    无人机运输成本: {transport_unit_cost_val_param:.3f} 元/公里 (按实际运输量)")
    print(f"    卡车固定成本: {truck_fixed_cost_param} 元/天")
    print(f"    卡车运输成本: {truck_km_cost_param} 元/公里 (按实际运输量)")
    print(f"    ✓ 所有固定成本已统一为日成本单位")
    print(f"    ✓ 运输成本保持按实际使用量计费")

    return {
        'customers': customers_list, 'sites': sites_list,
        'demand_deterministic': demand_dict_for_instance,
        'distance_matrix': distance_matrix_dict, 'drone_speed': drone_speed_param,
        'loading_time': loading_time_param, 'max_flight_distance': max_flight_distance_param,
        'locker_fixed_cost': locker_fixed_cost_dict, 'transport_unit_cost': transport_unit_cost_val_param,
        'drone_cost': drone_cost_val_param, 'H_drone_working_hours_per_day': H_drone_working_hours_per_day_param,
        'penalty_cost_unassigned': penalty_cost_unassigned_param,
        'Q_locker_capacity': Q_locker_capacity_dict, 'customer_coords': customer_coords_dict,
        'site_coords': site_coords_dict, 'depot_coord': depot_coord_param,
        'truck_capacity': truck_capacity_param, 'truck_fixed_cost': truck_fixed_cost_param,
        'truck_km_cost': truck_km_cost_param
    }

# ---------------------------------------------------------------------------
# ALNS 统一求解器类定义
# ---------------------------------------------------------------------------
#
# 架构说明：
# 1. ALNS_Solver: 统一求解器，直接处理SAA生成的确定性两阶段问题
#    - 优化第一阶段决策：储物柜选址(y)和无人机配置(n)
#    - 通过快速启发式算法处理第二阶段客户分配
#    - 目标函数：在K个给定需求样本下的期望总成本
#
# 2. FastAssignmentSolver: 快速客户分配求解器
#    - 仅用于第二阶段子问题的快速求解，使用贪心启发式算法
#    - 不是独立的ALNS，而是ALNS_Solver的辅助工具
#
# 这种设计避免了"两个ALNS"的问题，ALNS作为统一求解器处理整个SAA问题。
# ---------------------------------------------------------------------------
class ALNS_Solver:
    """
    ALNS (Adaptive Large Neighborhood Search) 两阶段随机规划求解器
    正确实现两阶段决策时序：需求实现前vs需求实现后

    解表示（修正版）：
    solution = {
        'y': {j: 0/1},           # 第一阶段：储物柜选址决策（需求实现前）
        'n': {j: int},           # 第一阶段：无人机配置决策（需求实现前）
    }

    注意：客户分配决策x不包含在解中，因为它们是第二阶段决策，
    需要在需求实现后根据具体场景动态优化。

    ALNS算子只操作第一阶段决策变量：
    - 破坏算子：移除/修改储物柜选址和无人机配置
    - 修复算子：重新配置储物柜选址和无人机数量
    - 目标函数：给定第一阶段决策，通过求解多个第二阶段子问题计算期望总成本

    这正确实现了两阶段随机规划的时序逻辑。
    """

    def __init__(self, problem_instance, demand_samples, alns_config=None):
        self.problem = problem_instance
        self.demand_samples = demand_samples
        self.num_scenarios = len(demand_samples)

        # 获取客户数量用于自适应参数设置
        num_customers = len(self.problem.customers)

        # ALNS参数配置（Adaptive模式优化版）
        default_config = {
            # 核心ALNS参数
            'initial_temperature': 100,
            'cooling_rate': 0.995,
            'min_temperature': 0.01,
            'max_iterations': 500,
            'max_iterations_without_improvement': 10,

            # 评估策略参数
            'full_evaluation_frequency': 50,  # 定期精确评估频率
            'significant_improvement_threshold': 10.0,  # 显著改进阈值（5%）
            'use_delta_evaluation': True,  # 启用增量评估

            # 局部搜索参数
            'use_local_search_in_alns': True,  # 是否在ALNS内部使用局部搜索
            'local_search_frequency': 1,  # 局部搜索频率（每N次迭代进行一次）
            'local_search_neighborhoods': ['N1_Drone', 'N2_AddDrop', 'N3_Swap'],  # 启用的邻域类型
            'local_search_strategy': 'first_improvement',  # 搜索策略: 'first_improvement' 或 'best_improvement'

            # 权重更新参数
            'weight_update_coefficient': 0.1,
            'weight_update_frequency': 100,
            'use_score_based_weights': True,

            # 分数奖励机制
            'score_new_best': 5,
            'score_better': 3,
            'score_accepted': 1,
        }

        # 【修改1】容量违反惩罚权重（自适应调整）
        self.capacity_penalty_weight = 5000  # 初始容量违反惩罚因子
        self.min_penalty_weight = 1000       # 惩罚权重下限
        self.max_penalty_weight = 50000      # 惩罚权重上限
        self.penalty_adjustment_factor = 1.05 # 权重调整因子（温和调整，配合平滑惩罚函数）



        # 合并用户配置
        self.config = default_config.copy()
        if alns_config:
            self.config.update(alns_config)

        # 移除服务覆盖率约束，与saa_g_r.py保持一致

        # 解缓存机制，避免重复计算相同解的目标值
        self.solution_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
        self.precise_evaluations_count = 0  # 精确评估次数计数器

        # 【新增】DRL卡车成本缓存
        self.drl_truck_cost_cache = {}
        self.drl_cache_hits = 0
        self.drl_cache_misses = 0

        # 【新增】批量求解缓存
        self.batch_solve_cache = {}
        self.batch_cache_hits = 0
        self.batch_cache_misses = 0

        # 【简化】移除复杂的性能统计
        self.heuristic_eval_count = 0

        # 局部搜索统计
        self.local_search_calls = 0
        self.local_search_improvements = 0
        self.local_search_neighborhood_stats = {
            'N1_Drone': {'calls': 0, 'improvements': 0},
            'N2_AddDrop': {'calls': 0, 'improvements': 0},
            'N3_Swap': {'calls': 0, 'improvements': 0}
        }

        # 【新增】启发式评估动态校准参数
        self.z_score = 0.15  # 【修复】降低风险调整系数初始值，减少需求过度膨胀
        self.congestion_weight = 0.5  # 【修复】降低拥堵成本权重，减少运输成本高估
        self.calibration_data = []  # 存储校准数据 [(heuristic_score, exact_score), ...]
        self.last_calibration_iteration = 0  # 上次校准的迭代次数
        self.calibration_frequency = 50  # 校准频率（每50次迭代校准一次）

        # 【修复】在初始化时就创建FastAssignmentSolver，避免运行时重复创建
        print("  初始化FastAssignmentSolver...")

        # 【新增】将ALNS实例保存到problem对象中，以便FastAssignmentSolver可以访问
        self.problem.alns_instance = self

        if not hasattr(self.problem, 'fast_solver') or self.problem.fast_solver is None:
            self.problem.fast_solver = FastAssignmentSolver(self.problem)
        print("  FastAssignmentSolver初始化完成")

        # 【新增】警告抑制机制
        self.warning_counts = {}  # 记录每种警告的出现次数
        self.max_warnings_per_type = 5  # 每种警告最多显示5次



        # 移除强化破坏模式相关变量

        # ALNS算子：操作第一阶段决策变量(y, n)
        # 破坏算子：移除/修改储物柜选址和无人机配置
        self.destroy_operators = [
            self.drone_reduction_removal,    # 减少无人机破坏算子（新增，高优先级）
            self.random_locker_removal,      # 随机移除储物柜
            self.worst_locker_removal,       # 移除效益最差的储物柜
            self.related_locker_removal,     # 移除相关储物柜
            self.drone_adjustment_removal,   # 调整无人机配置
            self.cluster_removal,            # 簇移除算子
            self.zone_removal,               # 区域移除算子
            self.radical_restructure         # 激进重构算子
        ]

        # 修复算子：重新配置储物柜选址和无人机数量
        self.repair_operators = [
            self.smart_drone_tuner,              # 智能无人机调优算子（最高优先级）
            self.simulation_based_greedy_insertion,  # 【新增】基于仿真的贪心插入（推荐）
            self.greedy_locker_insertion,        # 传统贪心插入储物柜（保留作为备用）
            self.regret_insertion,               # 后悔值插入储物柜
            self.drone_optimization              # 优化无人机配置
        ]

        # 初始化算子权重（给关键算子更高权重）
        self.destroy_weights = {op.__name__: 1.0 for op in self.destroy_operators}
        self.repair_weights = {op.__name__: 1.0 for op in self.repair_operators}
        # 给智能无人机调优算子设置最高初始权重
        self.repair_weights['smart_drone_tuner'] = 5.0
        # 给新的基于仿真的贪心插入算子设置较高权重
        self.repair_weights['simulation_based_greedy_insertion'] = 3.0
        # 给减少无人机破坏算子设置较高权重
        self.destroy_weights['drone_reduction_removal'] = 3.0

        # 算子使用统计
        self.destroy_usage = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_usage = {op.__name__: 0 for op in self.repair_operators}

        # 算子成功统计（传统方式）
        self.destroy_success = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_success = {op.__name__: 0 for op in self.repair_operators}

        # 算子分数统计（新的基于分数的方式）
        self.destroy_scores = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_scores = {op.__name__: 0 for op in self.repair_operators}

        # 历史最优解记录
        self.historical_best_obj = float('inf')

    def _calculate_initial_temperature(self, initial_solution):
        """
        自动计算初始温度，使得接受一个平均恶化程度的解的概率为预设值
        """
        print(f"  正在自动计算初始温度...")

        sample_size = self.config['auto_temp_sample_size']
        acceptance_prob = self.config['auto_temp_acceptance_prob']

        # 计算初始解的目标值（使用快速评估保持一致性）
        initial_obj = self._calculate_objective_heuristic(initial_solution, 0)

        # 生成邻域解样本并计算目标值差异
        deltas = []
        valid_neighbors = 0
        feasible_neighbors = 0

        for i in range(sample_size):
            try:
                # 随机选择破坏和修复算子
                destroy_op = random.choice(self.destroy_operators)
                repair_op = random.choice(self.repair_operators)

                # 生成邻域解
                temp_solution = destroy_op(copy.deepcopy(initial_solution))
                neighbor_solution = repair_op(temp_solution, 0)

                if neighbor_solution is not None:
                    valid_neighbors += 1
                    if self._is_feasible(neighbor_solution):
                        feasible_neighbors += 1
                        neighbor_obj = self._calculate_objective_heuristic(neighbor_solution, 0)
                        delta = neighbor_obj - initial_obj
                        if delta > 0:  # 只考虑恶化的解
                            deltas.append(delta)
                        elif delta < 0:  # 改进的解也记录（用于调试）
                            deltas.append(abs(delta))  # 使用绝对值
            except Exception as e:
                continue

        print(f"  温度计算统计: 尝试{sample_size}次, 有效邻域{valid_neighbors}个, 可行邻域{feasible_neighbors}个, 有效差值{len(deltas)}个")

        if not deltas:
            print(f"  无法生成有效的邻域解样本，使用默认初始温度: 1000.0")
            return 1000.0

        # 计算平均恶化程度
        avg_delta = sum(deltas) / len(deltas)

        # 根据公式 p = exp(-avg_delta / T0) 计算初始温度
        # T0 = -avg_delta / ln(p)
        if acceptance_prob > 0 and acceptance_prob < 1:
            initial_temp = -avg_delta / math.log(acceptance_prob)
        else:
            initial_temp = avg_delta  # 回退值

        print(f"  自动计算的初始温度: {initial_temp:.2f} (基于{len(deltas)}个样本，平均恶化: {avg_delta:.2f})")
        return max(initial_temp, 1.0)  # 确保温度至少为1.0

    def solve(self, initial_solution=None, time_limit=300):
        """
        ALNS统一求解器主循环

        求解SAA生成的确定性两阶段随机规划问题：
        - 优化第一阶段决策：储物柜选址(y)和无人机配置(n)
        - 第二阶段客户分配通过快速启发式算法处理
        - 目标函数：在K个给定需求样本下的期望总成本

        Args:
            initial_solution: 初始第一阶段解 {'y': {}, 'n': {}}
            time_limit: 时间限制（秒）

        Returns:
            Dict: 最优第一阶段解
        """
        print(f"  开始ALNS求解 (Adaptive模式)，时间限制: {time_limit}秒")
        print(f"  核心参数: 最大迭代{self.config['max_iterations']}, 精确评估阈值{self.config['significant_improvement_threshold']:.1f}%")
        print(f"  第二阶段求解器: Adaptive (精确评估用Gurobi，启发式评估用贪心算法)")

        start_time = time.time()

        # 生成初始解
        if initial_solution is None:
            current_solution = self.create_initial_solution()
        else:
            current_solution = copy.deepcopy(initial_solution)

        if current_solution is None:
            print("  无法生成初始解")
            return None

        # 【关键修改】使用快速评估函数计算初始目标值，用于迭代
        current_obj = self._calculate_objective_heuristic(current_solution, 0)
        # 使用精确评估函数计算真实的最优值
        best_obj = self.calculate_objective_cached(current_solution)
        best_solution = copy.deepcopy(current_solution)
        self.historical_best_obj = best_obj

        print(f"  初始解目标值(精确): {best_obj:.2f}, (启发式): {current_obj:.2f}")

        # 设置历史最优解
        self.historical_best_obj = best_obj

        # 使用论文中的固定初始温度
        temperature = self.config['initial_temperature']  # T₀ = 100
        print(f"  使用论文设置的初始温度: {temperature}")

        # ALNS主循环
        iteration = 0
        iterations_without_improvement = 0

        # 主循环（改进的终止条件）
        max_iterations = self.config['max_iterations']  # 减少到5000
        quality_convergence_count = 0  # 解质量收敛计数
        last_best_obj = float('inf')

        while (iteration < max_iterations and
               iterations_without_improvement < self.config['max_iterations_without_improvement'] and
               time.time() - start_time < time_limit):  # 简化终止条件，专注速度

            iteration += 1

            # 注意：early_termination_threshold 已经通过 while 循环条件处理
            # self.config['max_iterations_without_improvement'] = 10 已经是合适的阈值

            # 选择破坏和修复算子（移除强化破坏模式）
            destroy_op = self._select_operator(self.destroy_operators, self.destroy_weights)

            # 选择修复算子
            repair_op = self._select_operator(self.repair_operators, self.repair_weights)

            # 记录算子使用
            self.destroy_usage[destroy_op.__name__] += 1
            self.repair_usage[repair_op.__name__] += 1

            # 【调试】添加算子执行进度输出 - 减少输出频率
            if iteration % 50 == 0 or iteration <= 1:  # 只在第1次迭代和每50次迭代输出一次
                print(f"    迭代 {iteration}: 执行 {destroy_op.__name__} -> {repair_op.__name__}")

            # 生成新解
            try:
                temp_solution = destroy_op(copy.deepcopy(current_solution))
                new_solution = repair_op(temp_solution, iteration)

                if new_solution is None or not self._is_feasible(new_solution):
                    continue

                # 【新增】对修复后的解进行局部搜索优化
                if (self.config['use_local_search_in_alns'] and
                    iteration % self.config['local_search_frequency'] == 0):
                    improved_solution = self._local_search_improvement(new_solution, iteration)
                    if improved_solution is not None:
                        new_solution = improved_solution

                # 【核心修改】使用超快速评估函数进行解比较
                new_obj = self._calculate_objective_heuristic(new_solution, iteration)

                # 标准ALNS接受准则：模拟退火
                accept = False
                delta = new_obj - current_obj

                # 1. 改进的解总是接受
                if delta < 0:
                    accept = True
                # 2. 模拟退火接受较差解
                elif temperature > 0 and random.random() < math.exp(-delta / temperature):
                    accept = True

                if accept:
                    current_solution = new_solution
                    current_obj = new_obj

                    # 记录算子成功（传统方式）
                    self.destroy_success[destroy_op.__name__] += 1
                    self.repair_success[repair_op.__name__] += 1

                    # 基于分数的奖励机制
                    score = 0

                    # 【关键改进】只在启发式评估显示潜在更优解时才触发精确评估
                    if new_obj < best_obj:
                        print(f"    迭代 {iteration}: 启发式发现潜在更优解 ({new_obj:.2f} < {best_obj:.2f})，进行精确验证...")
                        full_obj = self.calculate_objective_cached(new_solution)
                        self.precise_evaluations_count += 1
                        print(f"    精确评估结果: {full_obj:.2f} (第{self.precise_evaluations_count}次精确评估)")

                        # 【新增】添加校准数据
                        self._add_calibration_data(new_obj, full_obj)

                        if full_obj < best_obj:
                            best_solution = copy.deepcopy(new_solution)
                            best_obj = full_obj
                            iterations_without_improvement = 0

                            # 判断是否为历史最优解
                            if full_obj < self.historical_best_obj:
                                self.historical_best_obj = full_obj
                                score = self.config['score_new_best']  # 找到历史最优解
                                print(f"  迭代 {iteration}: 找到历史最优解，精确目标值: {best_obj:.2f}")
                                # 重置质量收敛计数
                                quality_convergence_count = 0
                                last_best_obj = best_obj
                            else:
                                score = self.config['score_better']  # 找到比当前解好的解
                                print(f"  迭代 {iteration}: 找到更优解，精确目标值: {best_obj:.2f}")
                                # 重置质量收敛计数
                                quality_convergence_count = 0
                                last_best_obj = best_obj
                        else:
                            iterations_without_improvement += 1
                            score = self.config['score_accepted']  # 被接受但未改进全局最优的解
                            print(f"    精确验证：启发式过于乐观，实际目标值 {full_obj:.2f} >= {best_obj:.2f}")
                    elif new_obj < current_obj:
                        score = self.config['score_better']  # 找到比当前解好的解
                        iterations_without_improvement += 1
                    else:
                        score = self.config['score_accepted']  # 被接受的较差解
                        iterations_without_improvement += 1

                    # 更新算子分数
                    if self.config['use_score_based_weights']:
                        self.destroy_scores[destroy_op.__name__] += score
                        self.repair_scores[repair_op.__name__] += score
                else:
                    iterations_without_improvement += 1

                # 检查解质量收敛（如果没有找到更好解）
                if abs(best_obj - last_best_obj) < 0.01:  # 解质量变化很小
                    quality_convergence_count += 1
                else:
                    quality_convergence_count = 0
                    last_best_obj = best_obj

                # 降温
                temperature *= self.config['cooling_rate']

                # 定期更新算子权重
                if iteration % self.config['weight_update_frequency'] == 0:
                    self._update_operator_weights()

                # 【新增】定期校准启发式评估参数
                if iteration - self.last_calibration_iteration >= self.calibration_frequency:
                    self._calibrate_heuristic_parameters(iteration)
                    self.last_calibration_iteration = iteration

                # 【移除】定期精确评估，改为只在发现潜在更优解时精确评估

                # 【简化】移除复杂的容量惩罚权重调整

            except Exception as e:
                print(f"  迭代 {iteration} 出错: {str(e)}")
                import traceback
                print(f"  错误详情: {traceback.format_exc()}")
                continue

        solve_time = time.time() - start_time

        # 确定终止原因
        termination_reason = []
        if iteration >= max_iterations:
            termination_reason.append(f"达到最大迭代次数({max_iterations})")
        if temperature <= self.config['min_temperature']:
            termination_reason.append(f"温度降至阈值({self.config['min_temperature']})")
        if iterations_without_improvement >= self.config['max_iterations_without_improvement']:
            termination_reason.append(f"连续{self.config['max_iterations_without_improvement']}次无改进")
        if quality_convergence_count >= 200:
            termination_reason.append(f"解质量收敛(连续{quality_convergence_count}次变化<0.01)")
        if time.time() - start_time >= time_limit:
            termination_reason.append(f"达到时间限制({time_limit}秒)")

        print(f"  ALNS求解完成，耗时: {solve_time:.2f}秒")
        print(f"  总迭代次数: {iteration}")
        print(f"  最终温度: {temperature:.4f}")
        print(f"  连续无改进次数: {iterations_without_improvement}")
        print(f"  终止原因: {' & '.join(termination_reason) if termination_reason else '未知'}")

        # 局部搜索统计
        if self.local_search_calls > 0:
            improvement_rate = (self.local_search_improvements / self.local_search_calls) * 100
            print(f"  局部搜索统计: {self.local_search_improvements}/{self.local_search_calls} 次改进 ({improvement_rate:.1f}%)")

            # 详细邻域统计
            for neighborhood, stats in self.local_search_neighborhood_stats.items():
                if stats['calls'] > 0:
                    neighborhood_rate = (stats['improvements'] / stats['calls']) * 100
                    print(f"    {neighborhood}: {stats['improvements']}/{stats['calls']} 次改进 ({neighborhood_rate:.1f}%)")

        # 使用精确评估验证最终解
        if best_solution is not None:
            print(f"  验证最终解质量...")
            exact_obj = self.problem.calculate_objective(best_solution, self.demand_samples)
            print(f"  启发式目标值: {best_obj:.2f}")
            print(f"  精确目标值: {exact_obj:.2f}")
            print(f"  误差: {abs(exact_obj - best_obj):.2f} ({abs(exact_obj - best_obj)/exact_obj*100:.1f}%)")

        # 输出详细的缓存统计信息
        total_evaluations = self.cache_hits + self.cache_misses
        if total_evaluations > 0:
            cache_hit_rate = self.cache_hits / total_evaluations * 100
            print(f"  解缓存统计: {self.cache_hits} 命中, {self.cache_misses} 未命中, 命中率: {cache_hit_rate:.1f}%")
            print(f"  缓存大小: {len(getattr(self, 'solution_cache', {}))}")
            if hasattr(self, 'precise_evaluations_count'):
                print(f"  精确评估次数: {self.precise_evaluations_count}")
        else:
            print(f"  警告：未进行任何精确评估，缓存未被使用")

        # 输出关键统计信息
        if hasattr(self, 'heuristic_eval_count') and self.heuristic_eval_count > 0:
            print(f"  启发式评估次数: {self.heuristic_eval_count}")

        if hasattr(self, 'capacity_penalty_weight'):
            print(f"  容量惩罚权重: {self.capacity_penalty_weight:.0f}")



        return best_solution





    def _calculate_feasibility_penalty(self, solution):
        """计算解可行性惩罚 - 与saa_g_r.py约束一致"""
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        penalty = 0

        # 检查是否至少有一个储物柜 (对应saa_g_r.py的C1约束)
        if not selected_lockers:
            return 10000  # 适度惩罚

        # 检查无人机配置合理性 (对应saa_g_r.py的C6约束: n_j >= y_j)
        for j in selected_lockers:
            if n_star.get(j, 0) < 1:  # 开放储物柜至少需要1架无人机
                penalty += 1000

        return penalty



    def _local_search_improvement(self, solution, iteration=0):
        """
        【ALNS集成版】增强局部搜索改进

        专为ALNS内部使用设计，进行多邻域局部优化：
        - N1: 无人机数量微调（最快速）
        - N2: 储物柜开关操作（Add/Drop）
        - N3: 储物柜交换操作（Swap）
        - 支持First/Best Improvement策略
        """
        try:
            self.local_search_calls += 1
            current_solution = solution
            current_obj = self._calculate_objective_heuristic(solution, iteration)

            # 定义所有可用邻域
            all_neighborhoods = [
                ('N1_Drone', self._search_drone_optimization_neighborhood),
                ('N2_AddDrop', self._search_single_locker_neighborhood),
                ('N3_Swap', self._search_swap_locker_neighborhood)
            ]

            # 根据配置筛选启用的邻域
            enabled_neighborhoods = [
                (name, func) for name, func in all_neighborhoods
                if name in self.config['local_search_neighborhoods']
            ]

            if not enabled_neighborhoods:
                return None

            # 根据搜索策略执行
            if self.config['local_search_strategy'] == 'first_improvement':
                return self._first_improvement_search(enabled_neighborhoods, current_solution, current_obj, iteration)
            else:  # best_improvement
                return self._best_improvement_search(enabled_neighborhoods, current_solution, current_obj, iteration)

        except Exception as e:
            return None

    def _first_improvement_search(self, neighborhoods, current_solution, current_obj, iteration):
        """First Improvement策略：找到第一个改进即返回"""
        for neighborhood_name, search_func in neighborhoods:
            # 记录邻域调用统计
            self.local_search_neighborhood_stats[neighborhood_name]['calls'] += 1

            improved_solution, improved_obj = search_func(current_solution, current_obj, iteration)

            if improved_solution is not None and improved_obj < current_obj:
                self.local_search_improvements += 1
                self.local_search_neighborhood_stats[neighborhood_name]['improvements'] += 1
                if iteration % 100 == 0:  # 每100次迭代记录一次日志
                    improvement_pct = ((current_obj - improved_obj) / current_obj) * 100
                    print(f"    局部搜索{neighborhood_name}改进: {current_obj:.2f} → {improved_obj:.2f} (-{improvement_pct:.1f}%)")
                return improved_solution
        return None

    def _best_improvement_search(self, neighborhoods, current_solution, current_obj, iteration):
        """Best Improvement策略：尝试所有邻域，返回最佳改进"""
        best_solution = None
        best_obj = current_obj
        best_neighborhood = None

        for neighborhood_name, search_func in neighborhoods:
            # 记录邻域调用统计
            self.local_search_neighborhood_stats[neighborhood_name]['calls'] += 1

            improved_solution, improved_obj = search_func(current_solution, current_obj, iteration)

            if improved_solution is not None and improved_obj < best_obj:
                best_solution = improved_solution
                best_obj = improved_obj
                best_neighborhood = neighborhood_name

        if best_solution is not None:
            self.local_search_improvements += 1
            self.local_search_neighborhood_stats[best_neighborhood]['improvements'] += 1
            if iteration % 100 == 0:  # 每100次迭代记录一次日志
                improvement_pct = ((current_obj - best_obj) / current_obj) * 100
                print(f"    局部搜索{best_neighborhood}改进: {current_obj:.2f} → {best_obj:.2f} (-{improvement_pct:.1f}%)")
            return best_solution

        return None

    def _search_drone_optimization_neighborhood(self, solution, current_obj, iteration=0):
        """
        N1: 无人机优化邻域搜索
        保持储物柜位置不变，仅微调每个开放储物柜的无人机数量（±1架）
        这是最快的操作，优先搜索
        """
        best_solution = None
        best_obj = current_obj

        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        for j in open_lockers:
            current_drones = solution['n'][j]

            # 尝试增加1架无人机（最多8架）
            if current_drones < 8:
                temp_solution = copy.deepcopy(solution)
                temp_solution['n'][j] = current_drones + 1

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

            # 尝试减少1架无人机（最少1架）
            if current_drones > 1:
                temp_solution = copy.deepcopy(solution)
                temp_solution['n'][j] = current_drones - 1

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

        return best_solution, best_obj

    def _search_single_locker_neighborhood(self, solution, current_obj, iteration=0):
        """
        N2: 智能单储物柜操作邻域搜索
        Add: 尝试开启一个当前关闭的储物柜（智能选择无人机数量）
        Drop: 尝试关闭一个当前开启的储物柜
        """
        best_solution = None
        best_obj = current_obj

        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        closed_lockers = [j for j in self.problem.sites if j not in open_lockers]

        # Add操作：智能开启关闭的储物柜
        for j in closed_lockers:
            # 估算该储物柜的需求，智能配置无人机数量
            estimated_demand = self._estimate_locker_demand(j, solution)
            recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

            # 尝试推荐数量及其邻近值（±1）
            drone_candidates = [recommended_drones]
            if recommended_drones > 1:
                drone_candidates.append(recommended_drones - 1)
            if recommended_drones < 5:
                drone_candidates.append(recommended_drones + 1)

            for num_drones in drone_candidates:
                temp_solution = copy.deepcopy(solution)
                temp_solution['y'][j] = 1
                temp_solution['n'][j] = num_drones

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

        # Drop操作：尝试关闭开启的储物柜（至少保留1个）
        if len(open_lockers) > 1:
            for j in open_lockers:
                temp_solution = copy.deepcopy(solution)
                temp_solution['y'][j] = 0
                temp_solution['n'][j] = 0

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

        return best_solution, best_obj

    def _search_swap_locker_neighborhood(self, solution, current_obj, iteration=0):
        """
        N3: 智能交换储物柜邻域搜索
        Swap: 关闭一个已开启的储物柜，同时开启一个当前关闭的储物柜
        保持储物柜总数不变，但改变布局，并智能调整无人机配置
        """
        best_solution = None
        best_obj = current_obj

        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        closed_lockers = [j for j in self.problem.sites if j not in open_lockers]

        # 限制搜索范围以提高效率（如果储物柜太多）
        max_swaps = min(len(open_lockers) * len(closed_lockers), 20)  # 最多尝试20个交换
        swap_count = 0

        # 尝试交换组合
        for close_j in open_lockers:
            for open_j in closed_lockers:
                if swap_count >= max_swaps:
                    break

                temp_solution = copy.deepcopy(solution)

                # 关闭一个储物柜
                temp_solution['y'][close_j] = 0
                temp_solution['n'][close_j] = 0

                # 开启另一个储物柜，智能配置无人机数量
                temp_solution['y'][open_j] = 1

                # 估算新储物柜的需求，智能配置无人机
                estimated_demand = self._estimate_locker_demand(open_j, temp_solution)
                recommended_drones = self._calculate_recommended_drones(open_j, estimated_demand)

                # 尝试推荐数量和原配置
                original_drones = solution['n'][close_j]
                drone_candidates = [recommended_drones, original_drones]

                for num_drones in set(drone_candidates):  # 去重
                    test_solution = copy.deepcopy(temp_solution)
                    test_solution['n'][open_j] = num_drones

                    if self._is_feasible(test_solution):
                        temp_obj = self._calculate_objective_heuristic(test_solution, iteration)
                        if temp_obj < best_obj:
                            best_solution = test_solution
                            best_obj = temp_obj

                swap_count += 1

            if swap_count >= max_swaps:
                break

        return best_solution, best_obj

    def smart_drone_tuner(self, partial_solution, iteration=0):
        """
        智能无人机调优修复算子 - 核心修复算子，最高优先级

        算法流程：
        1. 首先用贪心方法补全储物柜选址（确保至少有一个储物柜）
        2. 对所有开放的储物柜，系统地测试无人机数量1-5的所有组合
        3. 计算每种组合的启发式成本，选择成本最低的配置
        4. 这确保每次修复都能得到最优的无人机配置

        Args:
            partial_solution: 被破坏的解（可能缺少储物柜或无人机配置不当）
            iteration: 当前迭代次数

        Returns:
            完整的高质量解，包含最优的储物柜选址和无人机配置
        """
        try:
            # 第1步：确保有基本的储物柜配置
            current_solution = copy.deepcopy(partial_solution)

            # 检查是否有开放的储物柜，如果没有则贪心添加
            open_lockers = [j for j, val in current_solution['y'].items() if val > 0.5]
            if not open_lockers:
                # 使用贪心方法添加至少一个储物柜
                current_solution = self._greedy_add_initial_lockers(current_solution, iteration)
                open_lockers = [j for j, val in current_solution['y'].items() if val > 0.5]

                if not open_lockers:  # 如果仍然没有储物柜，返回None
                    return None

            # 第2步：智能无人机配置优化
            best_solution = None
            best_obj = float('inf')

            # 为每个开放的储物柜测试不同的无人机数量组合
            max_drones_per_locker = 4  # 每个储物柜最多测试4架无人机

            # 生成所有可能的无人机配置组合
            drone_combinations = self._generate_drone_combinations(open_lockers, max_drones_per_locker)

            # 优先测试较少无人机的组合（通常成本更低）
            drone_combinations = sorted(drone_combinations, key=lambda x: sum(x.values()))

            # 限制组合数量以控制计算时间，但确保包含最少无人机的组合
            if len(drone_combinations) > 50:
                # 保留前50个组合（无人机数量最少的）
                drone_combinations = drone_combinations[:50]

            # 测试每种无人机配置
            for idx, drone_config in enumerate(drone_combinations):
                test_solution = copy.deepcopy(current_solution)

                # 应用无人机配置
                for j in open_lockers:
                    test_solution['n'][j] = drone_config[j]

                # 确保未开放的储物柜无人机数量为0
                for j in self.problem.sites:
                    if j not in open_lockers:
                        test_solution['n'][j] = 0

                # 检查可行性
                if self._is_feasible(test_solution):
                    # 【优化】统一使用快速启发式评估，避免精确评估开销
                    obj_value = self._calculate_objective_heuristic(test_solution, iteration)

                    if obj_value < best_obj:
                        best_obj = obj_value
                        best_solution = copy.deepcopy(test_solution)

                        # 如果找到了显著更好的解，记录日志
                        total_drones = sum(drone_config.values())
                        if iteration % 100 == 0:  # 每100次迭代输出一次
                            print(f"    智能调优发现更优配置: {drone_config}, 总无人机: {total_drones}, 启发式成本: {obj_value:.2f}")

            # 第3步：如果找到了改进的解，返回；否则返回当前解
            if best_solution is not None:
                return best_solution
            else:
                # 如果所有组合都不可行，至少确保当前解的基本可行性
                return self._ensure_basic_feasibility(current_solution)

        except Exception as e:
            # 如果出错，尝试返回一个基本可行解
            try:
                return self._ensure_basic_feasibility(partial_solution)
            except:
                return None

    def _generate_drone_combinations(self, open_lockers, max_drones_per_locker):
        """
        生成所有可能的无人机配置组合

        Args:
            open_lockers: 开放的储物柜列表
            max_drones_per_locker: 每个储物柜最大无人机数量

        Returns:
            所有可能的无人机配置组合列表
        """
        import itertools

        combinations = []

        # 为每个储物柜生成可能的无人机数量（1到max_drones_per_locker）
        drone_options = list(range(1, max_drones_per_locker + 1))

        # 生成所有组合
        for combo in itertools.product(drone_options, repeat=len(open_lockers)):
            drone_config = {open_lockers[i]: combo[i] for i in range(len(open_lockers))}
            combinations.append(drone_config)

        return combinations

    def _greedy_add_initial_lockers(self, solution, iteration=0):
        """
        贪心方法添加初始储物柜（如果解中没有储物柜）
        """
        # 计算每个候选储物柜的效益（基于距离和需求）
        locker_scores = {}
        total_expected_demand = sum(self.problem.expected_demand.values())

        for j in self.problem.sites:
            if solution['y'].get(j, 0) < 0.5:  # 未开放的储物柜
                # 计算该储物柜的服务效益
                service_score = 0
                for i in self.problem.customers:
                    if (i, j) in self.problem.distance:
                        distance = self.problem.distance[i, j]
                        demand = self.problem.expected_demand.get(i, 0)
                        # 效益 = 需求量 / (距离 + 1)，距离越近、需求越大效益越高
                        service_score += demand / (distance + 1)

                # 考虑储物柜固定成本
                locker_cost = self.problem.locker_fixed_cost.get(j, 0)
                locker_scores[j] = service_score / (locker_cost + 1)  # 效益成本比

        # 选择效益最高的储物柜
        if locker_scores:
            best_locker = max(locker_scores.keys(), key=lambda x: locker_scores[x])
            solution['y'][best_locker] = 1
            solution['n'][best_locker] = 1  # 默认配置1架无人机

        return solution

    def _ensure_basic_feasibility(self, solution):
        """
        确保解的基本可行性
        """
        # 确保至少有一个储物柜
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if not open_lockers:
            # 随机选择一个储物柜
            if self.problem.sites:
                j = random.choice(self.problem.sites)
                solution['y'][j] = 1
                solution['n'][j] = 1
                open_lockers = [j]

        # 确保每个开放的储物柜至少有1架无人机
        for j in open_lockers:
            if solution['n'].get(j, 0) < 1:
                solution['n'][j] = 1

        # 确保未开放的储物柜无人机数量为0
        for j in self.problem.sites:
            if j not in open_lockers:
                solution['n'][j] = 0

        return solution

    def drone_reduction_removal(self, solution):
        """
        减少无人机破坏算子 - 专门探索较少无人机的配置

        策略：
        1. 随机选择一些开放的储物柜
        2. 将它们的无人机数量减少到1架（最小值）
        3. 这样可以强制算法探索更经济的无人机配置

        Args:
            solution: 当前解

        Returns:
            被破坏的解（部分储物柜的无人机数量被减少）
        """
        try:
            destroyed_solution = copy.deepcopy(solution)
            open_lockers = [j for j, val in destroyed_solution['y'].items() if val > 0.5]

            if not open_lockers:
                return destroyed_solution

            # 选择要减少无人机的储物柜数量（30%-70%的开放储物柜）
            num_to_reduce = max(1, random.randint(
                max(1, int(len(open_lockers) * 0.3)),
                max(1, int(len(open_lockers) * 0.7))
            ))

            # 随机选择要减少无人机的储物柜
            lockers_to_reduce = random.sample(open_lockers, min(num_to_reduce, len(open_lockers)))

            # 将选中的储物柜的无人机数量减少到1架
            for j in lockers_to_reduce:
                destroyed_solution['n'][j] = 1  # 设置为最小值1架

            return destroyed_solution

        except Exception as e:
            return solution

    def _adjust_capacity_penalty_weight(self, solution):
        """
        【简化】移除复杂的自适应惩罚权重调整
        容量约束已由分配算法确保，不需要动态调整
        """
        pass

    def _perform_periodic_exact_evaluation(self, current_solution, iteration):
        """
        【新增】定期精确评估，提高搜索质量和一致性

        在ALNS迭代过程中定期使用精确评估来：
        1. 校正启发式评估的偏差
        2. 提供更准确的解质量信息
        3. 改善搜索方向
        """
        try:
            # 使用精确评估计算当前解的真实目标值
            exact_obj = self.calculate_objective_cached(current_solution)
            heuristic_obj = self._calculate_objective_heuristic(current_solution, iteration)

            # 计算评估偏差
            bias = abs(exact_obj - heuristic_obj) / max(exact_obj, 1e-6)

            # 只在偏差较大时输出信息（避免过多输出）
            if bias > 0.05 and iteration % (self.config['full_evaluation_frequency'] * 5) == 0:
                print(f"    迭代 {iteration}: 精确评估校正 - 启发式: {heuristic_obj:.2f}, 精确: {exact_obj:.2f}, 偏差: {bias:.1%}")

            # 记录评估一致性统计
            if not hasattr(self, 'evaluation_biases'):
                self.evaluation_biases = []
            self.evaluation_biases.append(bias)

        except Exception as e:
            # 如果精确评估失败，不影响主流程
            pass

    def _solution_to_key(self, solution):
        """
        将解转换为可用作字典键的字符串
        """
        # 更精确的键生成：包含所有储物柜的状态和无人机数量
        y_items = tuple(sorted((j, int(val > 0.5)) for j, val in solution['y'].items()))
        n_items = tuple(sorted((j, int(val)) for j, val in solution['n'].items() if solution['y'].get(j, 0) > 0.5))
        return (y_items, n_items)



    def calculate_objective_cached(self, solution):
        """
        带缓存的目标函数计算，正确实现两阶段结构
        """
        # 生成解的键（只基于第一阶段决策变量）
        solution_key = self._solution_to_key(solution)

        # 检查缓存
        if solution_key in self.solution_cache:
            self.cache_hits += 1
            return self.solution_cache[solution_key]

        # 缓存未命中，计算目标值
        self.cache_misses += 1
        # 【优化】减少缓存未命中输出 - 只显示前3次
        if self.cache_misses <= 3:
            open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
            print(f"    缓存未命中 #{self.cache_misses}: 储物柜{open_lockers}")

        # 使用两阶段目标函数：给定第一阶段决策，求解第二阶段期望成本
        obj_value = self.calculate_objective_two_stage(solution, self.demand_samples)

        # 存入缓存
        self.solution_cache[solution_key] = obj_value

        return obj_value

    def calculate_objective_two_stage(self, solution, demand_samples_k):
        """
        正确的两阶段目标函数计算

        给定第一阶段决策(y, n)，对每个需求场景求解第二阶段子问题，
        然后计算期望总成本。

        Args:
            solution: 第一阶段解 {'y': {}, 'n': {}}
            demand_samples_k: K个需求场景

        Returns:
            float: 期望总成本
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            return float('inf')  # 无效解

        # 第一阶段成本（here-and-now decisions）
        first_stage_cost = (sum(self.problem.locker_fixed_cost[j] for j in selected_lockers) +
                           sum(self.problem.drone_cost * n_star.get(j, 0) for j in selected_lockers))

        # 第二阶段期望成本（wait-and-see decisions）
        total_second_stage_costs = 0
        batch_active_lockers_info = []

        try:
            # 对每个需求场景求解第二阶段子问题
            for k_idx, demand_scenario in enumerate(demand_samples_k):
                # 求解第二阶段客户分配子问题
                optimal_assignment = self._solve_second_stage_subproblem(
                    y_star, n_star, selected_lockers, demand_scenario
                )

                # 计算该场景下的第二阶段成本
                transport_cost_k, penalty_cost_k, locker_demands = self._calculate_second_stage_costs(
                    optimal_assignment, demand_scenario, selected_lockers
                )

                # 准备卡车成本计算数据
                active_info = {j: {'coord': self.problem.site_coords[j], 'demand': round(demand)}
                              for j, demand in locker_demands.items() if demand > 1e-6}
                batch_active_lockers_info.append(active_info)

                total_second_stage_costs += (transport_cost_k + penalty_cost_k)

            # 批量计算卡车成本
            batch_truck_costs = self.problem.calculate_truck_cost_batch(batch_active_lockers_info)
            avg_truck_cost = sum(batch_truck_costs) / len(batch_truck_costs) if batch_truck_costs else 0

            # 计算期望第二阶段成本
            avg_second_stage_cost = total_second_stage_costs / len(demand_samples_k)

            return first_stage_cost + avg_second_stage_cost + avg_truck_cost

        except Exception as e:
            print(f"    两阶段目标函数计算失败: {str(e)}")
            return float('inf')

    def _solve_second_stage_subproblem(self, y_star, n_star, selected_lockers, demand_scenario, use_exact=None):
        """
        求解第二阶段客户分配子问题

        给定第一阶段决策和具体需求场景，求解最优客户分配

        Args:
            use_exact: None(自动选择), True(强制精确), False(强制启发式)
        """
        # 【改进】自适应第二阶段算法选择
        should_use_exact = self._should_use_exact_solver(use_exact)

        if should_use_exact:
            # 使用精确求解器（Gurobi MIP）
            try:
                return self.problem._solve_assignment_with_gurobi(
                    y_star, n_star, selected_lockers, demand_scenario
                )
            except Exception as e:
                print(f"    精确求解器失败，回退到贪心算法: {e}")
                # 回退到贪心算法
                if not hasattr(self.problem, 'fast_solver') or self.problem.fast_solver is None:
                    self.problem.fast_solver = FastAssignmentSolver(self.problem)
                return self.problem.fast_solver.solve_assignment_heuristic(
                    y_star, n_star, selected_lockers, demand_scenario
                )
        else:
            # 使用贪心启发式算法
            if not hasattr(self.problem, 'fast_solver') or self.problem.fast_solver is None:
                self.problem.fast_solver = FastAssignmentSolver(self.problem)
            return self.problem.fast_solver.solve_assignment_heuristic(
                y_star, n_star, selected_lockers, demand_scenario
            )

    def _should_use_exact_solver(self, use_exact=None):
        """
        【简化】简单的求解器选择策略
        """
        if use_exact is not None:
            return use_exact

        # 简化策略：默认使用启发式求解器，只在明确要求时使用精确求解器
        return False

    def _calculate_second_stage_costs(self, assignment, demand_scenario, selected_lockers):
        """
        计算第二阶段成本：运输成本和惩罚成本
        """
        transport_cost = 0
        locker_demands = {j: 0 for j in selected_lockers}

        # 计算运输成本和储物柜需求
        for (customer, locker), quantity in assignment.items():
            if quantity > 0:
                distance = self.problem.distance.get((customer, locker), 0)
                transport_cost += 2 * self.problem.transport_unit_cost * distance * quantity
                locker_demands[locker] += quantity

        # 计算惩罚成本（与g_i.py保持一致的精确方法）
        penalty_cost = 0
        for customer_id, demand in demand_scenario.items():
            customer_assigned = sum(assignment.get((customer_id, locker), 0) for locker in selected_lockers)
            shortage = max(0, demand - customer_assigned)
            penalty_cost += self.problem.penalty_cost_unassigned * shortage

        return transport_cost, penalty_cost, locker_demands



    def _calculate_objective_heuristic(self, solution, iteration=0):
        """
        【优化版】启发式目标函数：引入风险评估的快速估算

        核心改进:
        1. 使用风险调整后的需求 (mean + z*std_dev) 来评估，更好地反映需求波动风险。
        2. 估算服务成本时，引入"拥堵成本"，平滑地惩罚接近饱和的容量。
        3. 使用基于坐标的TSP近似来估算卡车成本，比简单计数更准确。

        这个版本仍然很快，因为它避免了场景循环，但其结果与真实随机环境下的成本更相关。
        """
        self.heuristic_eval_count += 1

        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            # 如果没有储物柜，成本是所有期望需求都未满足的惩罚
            total_expected_demand = sum(self.problem.expected_demand.values())
            return total_expected_demand * self.problem.penalty_cost_unassigned

        # 1. 第一阶段成本（精确）
        first_stage_cost = (sum(self.problem.locker_fixed_cost.get(j, 0) for j in selected_lockers) +
                           sum(self.problem.drone_cost * n_star.get(j, 0) for j in selected_lockers))

        # 2. 【核心改进】使用风险调整后的需求进行服务成本估算
        # 【修复】不使用ceil()，保持浮点数精度以便参数调整生效
        risk_adjusted_demand = {
            i: demand + self.z_score * math.sqrt(demand)  # 保持浮点数，不向上取整
            for i, demand in self.problem.expected_demand.items()
        }

        transport_cost, penalty_cost, assigned_demand_to_locker = self._estimate_service_costs(
            solution, risk_adjusted_demand
        )

        # 3. 【核心改进】使用更精确的卡车成本快速估算
        truck_cost = self._estimate_truck_cost_fast_v2(assigned_demand_to_locker)

        # 4. 总成本
        total_cost = first_stage_cost + transport_cost + penalty_cost + truck_cost

        return total_cost

    def _estimate_truck_cost_fast(self, assigned_demand_to_locker):
        """
        【新增】超快速卡车成本估算，避免DRL调用

        基于储物柜需求分布进行简化估算，不调用DRL求解器
        """
        if not assigned_demand_to_locker:
            return 0.0

        # 计算总需求和活跃储物柜数量
        total_demand = sum(assigned_demand_to_locker.values())
        active_lockers = len([d for d in assigned_demand_to_locker.values() if d > 0.5])

        if total_demand <= 0 or active_lockers == 0:
            return 0.0

        # 简化估算：基于需求量和储物柜数量
        # 卡车数量 = ceil(总需求 / 卡车容量)
        num_trucks = math.ceil(total_demand / self.problem.truck_capacity) if self.problem.truck_capacity > 0 else 1

        # 距离估算：假设平均每个储物柜距离仓库5公里，储物柜间距离3公里
        avg_depot_distance = 5.0
        avg_inter_locker_distance = 3.0

        # 简化TSP路径长度估算
        if active_lockers == 1:
            estimated_distance = avg_depot_distance * 2  # 往返
        else:
            # 多储物柜：仓库往返 + 储物柜间连接
            estimated_distance = avg_depot_distance * 2 + avg_inter_locker_distance * (active_lockers - 1)

        # 总卡车成本 = 固定成本 + 距离成本
        truck_cost = (num_trucks * self.problem.truck_fixed_cost) + (estimated_distance * self.problem.truck_km_cost)

        return truck_cost

    def _estimate_truck_cost_fast_v2(self, assigned_demand_to_locker: Dict) -> float:
        """
        【优化版】快速卡车成本估算，使用坐标信息进行TSP近似

        比原版更准确，但仍然比调用DRL快几个数量级。
        """
        if not assigned_demand_to_locker:
            return 0.0

        active_lockers_coords = {
            j: self.problem.site_coords[j]
            for j in assigned_demand_to_locker
            if assigned_demand_to_locker[j] > 1e-6 and j in self.problem.site_coords
        }

        if not active_lockers_coords:
            return 0.0

        # a. 计算卡车固定成本 (基于总需求)
        total_demand = sum(assigned_demand_to_locker.values())
        num_trucks = math.ceil(total_demand / self.problem.truck_capacity) if self.problem.truck_capacity > 0 else 1
        truck_fixed_cost = num_trucks * self.problem.truck_fixed_cost

        # b. 估算卡车路线成本
        # 使用一个简单的TSP近似：最近邻插入法
        depot_coord = self.problem.depot_coord
        unvisited = list(active_lockers_coords.keys())

        # 从仓库开始
        tour = [0] # 0 代表仓库
        estimated_distance = 0

        current_loc_id = 0 # Start at depot

        while unvisited:
            # 找到离当前位置最近的未访问储物柜
            next_loc_id = -1
            min_dist = float('inf')
            for loc_id in unvisited:
                dist = self.problem.truck_distances.get((current_loc_id, loc_id), float('inf'))
                if dist < min_dist:
                    min_dist = dist
                    next_loc_id = loc_id

            if next_loc_id != -1:
                estimated_distance += min_dist
                current_loc_id = next_loc_id
                tour.append(current_loc_id)
                unvisited.remove(current_loc_id)
            else: # 无法找到下一个点
                break

        # 加上返回仓库的距离
        if tour[-1] != 0: # 如果最后一个点不是仓库
             estimated_distance += self.problem.truck_distances.get((tour[-1], 0), 0)

        truck_variable_cost = estimated_distance * self.problem.truck_km_cost

        return truck_fixed_cost + truck_variable_cost

    def _calibrate_heuristic_parameters(self, iteration):
        """
        【新增】动态校准启发式评估参数

        根据历史的启发式评估与精确评估的对比结果，动态调整参数以提高相关性
        """
        if len(self.calibration_data) < 5:  # 需要至少5个数据点
            return

        # 计算当前相关性
        heuristic_scores = [data[0] for data in self.calibration_data]
        exact_scores = [data[1] for data in self.calibration_data]

        if len(set(heuristic_scores)) < 2 or len(set(exact_scores)) < 2:
            return  # 数据变化不足，无法计算相关性

        try:
            from scipy.stats import pearsonr
            correlation, _ = pearsonr(heuristic_scores, exact_scores)
        except:
            # 如果scipy不可用，使用简单的相关性估算
            import numpy as np
            correlation = np.corrcoef(heuristic_scores, exact_scores)[0, 1]

        # 计算平均相对误差
        relative_errors = []
        for h, e in zip(heuristic_scores, exact_scores):
            if e > 0:
                relative_errors.append(abs(h - e) / e)

        avg_relative_error = sum(relative_errors) / len(relative_errors) if relative_errors else 0

        # 【修改】降低校准触发阈值，更积极地调整参数
        if correlation < 0.9 or avg_relative_error > 0.1:  # 相关性不够高或误差超过10%
            # 分析启发式评估与精确评估的系统性偏差
            heuristic_higher = sum(1 for h, e in zip(heuristic_scores, exact_scores) if h > e)
            exact_higher = sum(1 for h, e in zip(heuristic_scores, exact_scores) if h < e)

            # 确定偏差方向
            bias_direction = "high" if heuristic_higher > exact_higher else "low"

            # 根据偏差方向调整参数
            if bias_direction == "high":  # 启发式评估倾向于高估
                # 降低风险系数和拥堵权重
                adjustment = 0.9 if avg_relative_error > 0.15 else 0.95
                self.z_score *= adjustment
                self.congestion_weight *= adjustment
            else:  # 启发式评估倾向于低估
                # 增加风险系数和拥堵权重
                adjustment = 1.1 if avg_relative_error > 0.15 else 1.05
                self.z_score *= adjustment
                self.congestion_weight *= adjustment

            # 限制参数范围
            self.z_score = max(0.1, min(0.5, self.z_score))
            self.congestion_weight = max(0.5, min(2.0, self.congestion_weight))

            # 输出校准信息
            print(f"    [校准] 迭代{iteration}: 相关性={correlation:.3f}, 误差={avg_relative_error:.3f}, 偏差方向={bias_direction}")
            print(f"    [校准] 调整参数: z_score={self.z_score:.3f}, 拥堵权重={self.congestion_weight:.3f}")

        # 清理旧数据，保持最近的数据
        if len(self.calibration_data) > 20:
            self.calibration_data = self.calibration_data[-15:]  # 保留最近15个数据点

    def _add_calibration_data(self, heuristic_score, exact_score):
        """添加校准数据点"""
        self.calibration_data.append((heuristic_score, exact_score))

    def _stratified_sample_selection(self, k_small, iteration):
        """
        【简化】使用简单的随机抽样策略
        """
        total_samples = len(self.demand_samples)
        k_small = min(k_small, total_samples)

        if k_small >= total_samples:
            return list(range(total_samples))

        # 简单随机抽样
        return random.sample(range(total_samples), k_small)

    def _get_drl_cache_key(self, active_lockers_info):
        """
        【新增】生成DRL缓存键
        """
        # 创建一个基于储物柜ID和需求的缓存键
        items = []
        for locker_id in sorted(active_lockers_info.keys()):
            info = active_lockers_info[locker_id]
            demand = info.get('demand', 0)
            # 将需求四舍五入到整数，减少缓存键的数量
            demand_rounded = round(demand)
            items.append((locker_id, demand_rounded))
        return tuple(items)

    def _get_batch_cache_key(self, batch_active_lockers_info):
        """
        【新增】生成批量求解缓存键
        """
        # 为整个批量求解生成缓存键
        batch_items = []
        for scenario in batch_active_lockers_info:
            scenario_key = self._get_drl_cache_key(scenario)
            batch_items.append(scenario_key)
        # 排序确保相同内容的不同顺序产生相同的键
        return tuple(sorted(batch_items))



    def _calculate_capacity_penalty(self, locker_demands, n_star, selected_lockers):
        """
        【修复】检查容量违反情况（应该始终为0，因为分配算法已确保容量约束）
        """
        capacity_violations = []

        for j in selected_lockers:
            demand_on_j = locker_demands.get(j, 0)

            # 物理容量检查
            locker_cap = self.problem.Q_locker_capacity.get(j, 0)
            if locker_cap > 0 and demand_on_j > locker_cap + 1e-6:
                violation = demand_on_j - locker_cap
                capacity_violations.append(f"储物柜{j}超载{violation:.2f}")

            # 无人机容量检查
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                drone_cap = self.problem.fast_solver._get_drone_capacity_fast(j, n_star.get(j, 0))
                if drone_cap > 0 and demand_on_j > drone_cap + 1e-6:
                    violation = demand_on_j - drone_cap
                    capacity_violations.append(f"储物柜{j}无人机超载{violation:.2f}")

        # 如果发现容量违反，这是一个bug，应该报告
        if capacity_violations:
            print(f"          [警告] 发现容量违反: {capacity_violations}")
            # 返回0，因为这应该不会发生
            return 0.0

        # 正常情况下，容量违反惩罚应该为0
        return 0.0





    def _fallback_truck_cost_estimation(self, active_lockers_info):
        """
        DRL不可用时的回退卡车成本估算方法
        """
        if not active_lockers_info:
            return 0

        # 使用原来的TSP近似方法作为回退
        total_demand = sum(info['demand'] for info in active_lockers_info.values())
        coords = [self.problem.depot_coord] + [info['coord'] for info in active_lockers_info.values()]

        # 【修复】更合理的TSP距离估算
        if len(coords) > 1:
            # 计算从仓库到各储物柜的距离
            depot = coords[0]
            locker_coords = coords[1:]

            # 简化的TSP估算：仓库到最远储物柜的往返距离
            max_distance = 0
            for coord in locker_coords:
                distance = math.sqrt((coord[0] - depot[0])**2 + (coord[1] - depot[1])**2)
                max_distance = max(max_distance, distance)

            # TSP路径估算：往返最远点 + 储物柜间的连接成本
            if len(locker_coords) == 1:
                estimated_distance = max_distance * 2  # 简单往返
            else:
                # 多个储物柜：往返 + 储物柜间连接
                estimated_distance = max_distance * 2 + max_distance * 0.5 * (len(locker_coords) - 1)
        else:
            estimated_distance = 0

        # 组合成本
        num_trucks = math.ceil(total_demand / self.problem.truck_capacity) if self.problem.truck_capacity > 0 else 0
        return (num_trucks * self.problem.truck_fixed_cost) + (estimated_distance * self.problem.truck_km_cost)



    def _estimate_service_costs(self, solution, demand_to_evaluate: Dict):
        """
        【优化版】估算服务成本：运输成本 + 惩罚成本

        核心改进:
        - 接受一个需求字典 (可以是期望需求或风险调整后需求) 作为输入。
        - 在分配过程中增加"拥堵成本"，当储物柜容量利用率过高时，成本会非线性增加。
        - 分配逻辑采用更优的后悔值思想。
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = {j for j, val in y_star.items() if val > 0.5}

        if not selected_lockers:
            unmet_demand = sum(demand_to_evaluate.values())
            return 0, unmet_demand * self.problem.penalty_cost_unassigned, {}

        # 1. 估算每个储物柜的有效服务能力 (容量和无人机运力的较小者)
        # FastAssignmentSolver应该已经在ALNS初始化时创建了

        effective_capacities = {
            j: math.floor(min(self.problem.Q_locker_capacity.get(j, 0),
                             self.problem.fast_solver._get_drone_capacity_fast(j, n_star.get(j, 0))))
            for j in selected_lockers
        }
        initial_capacities = effective_capacities.copy() # 保存初始容量用于计算拥堵

        # 2. 模拟一个考虑竞争的后悔值分配
        remaining_demands = demand_to_evaluate.copy()
        assignment = defaultdict(float)

        # 主循环：直到所有需求分配完或无法继续分配
        # 【改进】大幅增加最大迭代次数，支持需求拆分的充分分配
        max_iterations = min(500, len(remaining_demands) * 10)  # 从100增加到500，从5倍增加到10倍
        iteration_count = 0
        consecutive_no_progress = 0  # 连续无进展计数器

        # 【优化】按需求量排序，优先分配大需求客户
        sorted_customers = sorted(remaining_demands.items(), key=lambda x: x[1], reverse=True)

        # 【优化】第一轮：先尝试贪心分配大需求客户
        for i, demand in sorted_customers:
            if demand <= 1e-6:
                continue

            # 找到最近的有容量的储物柜
            best_j = -1
            min_dist = float('inf')
            for j in selected_lockers:
                if effective_capacities[j] >= demand and self.problem.distance.get((i, j), float('inf')) <= self.problem.max_flight_distance:
                    dist = self.problem.distance.get((i, j), float('inf'))
                    if dist < min_dist:
                        min_dist = dist
                        best_j = j

            # 如果找到合适的储物柜，直接分配
            if best_j >= 0:
                assignment[(i, best_j)] += demand
                effective_capacities[best_j] -= demand
                remaining_demands[i] = 0

        # 移除已完全分配的客户
        remaining_demands = {i: d for i, d in remaining_demands.items() if d > 1e-6}

        # 第二轮：使用后悔值算法分配剩余需求
        while any(d > 1e-6 for d in remaining_demands.values()) and iteration_count < max_iterations:
            iteration_count += 1
            best_customer = -1
            best_locker = -1
            max_regret = -1.0

            # 为每个有剩余需求的客户计算后悔值
            customers_with_demand = [i for i, d in remaining_demands.items() if d > 1e-6]
            if not customers_with_demand:
                break

            for i in customers_with_demand:
                # 找到该客户的成本最低和次低的两个选项
                options = []
                # 使用 fast_solver 预计算的可达储物柜
                for j in self.problem.fast_solver.reachable_lockers.get(i, []):
                    if j in selected_lockers and effective_capacities.get(j, 0) > 1e-6:
                        # 成本 = 运输成本 + 拥堵成本
                        transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance.get((i, j), float('inf'))

                        # 【修复】改进拥堵成本计算 - 使用更平滑的函数
                        utilization = 1.0 - (effective_capacities[j] / max(1e-6, initial_capacities[j]))

                        # 只有当利用率超过70%时才开始增加拥堵成本，避免低利用率时的过度惩罚
                        if utilization > 0.7:
                            # 使用线性增长而非二次方增长，更接近实际情况
                            congestion_factor = (utilization - 0.7) / 0.3  # 归一化到[0,1]范围
                            congestion_penalty = transport_cost * congestion_factor * self.congestion_weight
                        else:
                            congestion_penalty = 0.0

                        total_cost = transport_cost + congestion_penalty
                        options.append({'locker': j, 'cost': total_cost})

                if not options:
                    continue

                options.sort(key=lambda x: x['cost'])
                best_option = options[0]

                if len(options) > 1:
                    second_best_option = options[1]
                    regret = second_best_option['cost'] - best_option['cost']
                else:
                    regret = float('inf') # 只有一个选择，后悔值无穷大，优先分配

                if regret > max_regret:
                    max_regret = regret
                    best_customer = i
                    best_locker = best_option['locker']

            if best_customer == -1: # 没有可分配的了
                consecutive_no_progress += 1
                if consecutive_no_progress >= 3:  # 连续3次无进展就退出
                    break
                continue  # 【修复】直接跳到下一次循环，避免访问 best_customer = -1
            else:
                consecutive_no_progress = 0  # 重置无进展计数器

            # 执行分配 - 【修复】支持浮点数需求，但确保容量为整数
            assignable_amount = min(
                remaining_demands[best_customer],
                math.floor(effective_capacities[best_locker])  # 容量向下取整
            )

            if assignable_amount >= 0.01:  # 降低最小阈值，允许更小的分配
                assignment[(best_customer, best_locker)] += assignable_amount

                # 更新状态
                remaining_demands[best_customer] -= assignable_amount
                effective_capacities[best_locker] -= assignable_amount

                # 如果客户需求已基本满足，从字典中移除
                if remaining_demands[best_customer] < 0.01:
                    del remaining_demands[best_customer]

        # 3. 计算总成本
        total_transport_cost = 0
        assigned_demand_to_locker = defaultdict(float)
        for (i, j), quantity in assignment.items():
            if quantity > 0:
                total_transport_cost += 2 * self.problem.transport_unit_cost * self.problem.distance.get((i, j), 0) * quantity
                assigned_demand_to_locker[j] += quantity

        # 【优化】只在真正达到最大迭代次数且还有未分配需求时才输出警告
        if iteration_count >= max_iterations and any(d > 1e-6 for d in remaining_demands.values()):
            unassigned_total = sum(d for d in remaining_demands.values() if d > 1e-6)
            if unassigned_total > 1.0:  # 只有当未分配需求大于1.0时才警告，忽略小数点精度问题
                # 计算未分配需求占总需求的百分比
                total_demand = sum(demand_to_evaluate.values())
                unassigned_percent = (unassigned_total / total_demand) * 100 if total_demand > 0 else 0

                # 只有当未分配比例超过5%时才输出警告
                if unassigned_percent > 5.0:
                    # 【新增】使用警告抑制机制
                    warning_key = f"assignment_max_iter_{unassigned_percent:.0f}%"
                    if warning_key not in self.warning_counts:
                        self.warning_counts[warning_key] = 0

                    if self.warning_counts[warning_key] < self.max_warnings_per_type:
                        self.warning_counts[warning_key] += 1
                        remaining_warnings = self.max_warnings_per_type - self.warning_counts[warning_key]
                        if remaining_warnings > 0:
                            print(f"    [警告] 分配算法达到最大迭代次数 {max_iterations}，未分配需求: {unassigned_total:.1f}/{total_demand:.1f} ({unassigned_percent:.1f}%) [还将显示{remaining_warnings}次]")
                        else:
                            print(f"    [警告] 分配算法达到最大迭代次数 {max_iterations}，未分配需求: {unassigned_total:.1f}/{total_demand:.1f} ({unassigned_percent:.1f}%) [后续同类警告将被抑制]")

                    # 【新增】尝试强制分配剩余需求
                    if hasattr(self, 'heuristic_eval_count') and self.heuristic_eval_count % 50 == 0:
                        print(f"    [修复] 尝试强制分配剩余需求...")
                        # 按容量排序储物柜
                        sorted_lockers = sorted([(j, effective_capacities[j]) for j in selected_lockers],
                                               key=lambda x: x[1], reverse=True)

                        # 按需求量排序客户
                        sorted_customers = sorted([(i, d) for i, d in remaining_demands.items() if d > 1e-6],
                                                key=lambda x: x[1], reverse=True)

                        # 尝试分配
                        for i, demand in sorted_customers:
                            for j, capacity in sorted_lockers:
                                if capacity > 0 and self.problem.distance.get((i, j), float('inf')) <= self.problem.max_flight_distance:
                                    assign_amount = min(demand, capacity)
                                    if assign_amount > 0:
                                        assignment[(i, j)] += assign_amount
                                        effective_capacities[j] -= assign_amount
                                        remaining_demands[i] -= assign_amount
                                        demand -= assign_amount
                                        capacity -= assign_amount
                                    if demand <= 1e-6:
                                        break

        # 4. 计算最终的惩罚成本
        total_penalty_cost = sum(d * self.problem.penalty_cost_unassigned for d in remaining_demands.values())

        # 【调试】添加容量分析 - 【修复】支持浮点数显示，减少输出频率
        if hasattr(self, 'heuristic_eval_count') and self.heuristic_eval_count <= 1:  # 只在前1次评估时输出
            total_demand = sum(demand_to_evaluate.values())
            total_assigned = sum(assigned_demand_to_locker.values())
            total_unassigned = sum(remaining_demands.values())
            total_capacity = sum(effective_capacities.values()) + sum(assigned_demand_to_locker.values())
            print(f"    [调试] 启发式分配: 总需求{total_demand:.1f}, 已分配{total_assigned:.1f}, 未分配{total_unassigned:.1f}")
            print(f"    [调试] 总容量{total_capacity:.1f}, 惩罚成本{total_penalty_cost:.1f}, 迭代次数{iteration_count}")

            # 【新增】详细容量分析 - 只在第一次评估时输出，避免过多日志
            if total_unassigned > 5.0 and hasattr(self, 'heuristic_eval_count') and self.heuristic_eval_count == 1:
                print(f"    [调试] 储物柜容量详情:")
                for j in selected_lockers:
                    initial_cap = math.floor(min(self.problem.Q_locker_capacity.get(j, 0),
                                               self.problem.fast_solver._get_drone_capacity_fast(j, solution['n'].get(j, 0))))
                    remaining_cap = effective_capacities.get(j, 0)
                    assigned_cap = assigned_demand_to_locker.get(j, 0)
                    print(f"      储物柜{j}: 初始{initial_cap:.1f}, 已分配{assigned_cap:.1f}, 剩余{remaining_cap:.1f}")

                print(f"    [调试] 未分配客户详情:")
                unassigned_customers = [(i, demand) for i, demand in remaining_demands.items() if demand > 1e-6]
                for i, demand in unassigned_customers[:5]:  # 只显示前5个未分配客户
                    reachable = [j for j in self.problem.fast_solver.reachable_lockers.get(i, []) if j in selected_lockers]
                    available_caps = [effective_capacities.get(j, 0) for j in reachable]
                    print(f"      客户{i}: 需求{demand:.1f}, 可达储物柜{reachable}, 可用容量{available_caps}")
                if len(unassigned_customers) > 5:
                    print(f"      ... 还有{len(unassigned_customers)-5}个未分配客户")

        return total_transport_cost, total_penalty_cost, assigned_demand_to_locker

    def _is_feasible(self, solution):
        """
        检查解是否满足基本约束条件（简化版本，避免过于严格）
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 1. 至少开设一个储物柜
        if not selected_lockers:
            return False

        # 2. 每个开放的储物柜至少配置一架无人机
        for j in selected_lockers:
            if n_star.get(j, 0) < 1:
                return False

        # 3. 基本合理性检查：无人机数量不能过多
        for j in selected_lockers:
            if n_star.get(j, 0) > 10:  # 限制最大无人机数量
                return False

        return True

    def create_initial_solution(self, return_multiple=False):
        """
        生成第一阶段初始解（修正版两阶段结构）

        返回格式：
        solution = {
            'y': {j: 0/1},                    # 第一阶段：储物柜选址
            'n': {j: num_drones},             # 第一阶段：无人机配置
        }

        注意：不包含客户分配决策x，因为它们是第二阶段决策，
        需要在需求实现后根据具体场景动态优化。

        Args:
            return_multiple: 如果为True，返回多个候选解列表；否则返回最佳解
        """
        try:
            # 调试信息已移除以减少冗余输出

            # 生成多个候选解
            candidates = []

            # 【修正版】使用总成本最小化策略
            try:
                print(f"  使用总成本最小化策略生成初始解...")
                solution = self._create_cost_benefit_solution_dynamic()
                if solution and self._is_feasible(solution):
                    obj = self._calculate_objective_heuristic(solution, 0)
                    candidates.append({
                        'solution': solution,
                        'objective': obj,
                        'strategy': 'total_cost_minimization'
                    })
                    print(f"    成功生成解，目标值: {obj:.2f}")
            except Exception as e:
                print(f"    总成本最小化策略失败: {str(e)}")
                # 如果新策略失败，回退到旧策略
                try:
                    print(f"    回退到动态成本效益策略...")
                    solution = self._create_cost_benefit_solution_dynamic()
                    if solution and self._is_feasible(solution):
                        obj = self._calculate_objective_heuristic(solution, 0)
                        candidates.append({
                            'solution': solution,
                            'objective': obj,
                            'strategy': 'dynamic_cost_benefit_fallback'
                        })
                        print(f"    回退策略成功，目标值: {obj:.2f}")
                except Exception as e2:
                    print(f"    回退策略也失败: {str(e2)}")

            # 备用策略：简单解
            if not candidates:
                try:
                    fallback_solution = self._create_fallback_solution()
                    if fallback_solution and self._is_feasible(fallback_solution):
                        obj = self._calculate_objective_heuristic(fallback_solution, 0)
                        candidates.append({
                            'solution': fallback_solution,
                            'objective': obj,
                            'strategy': 'fallback'
                        })
                        print(f"    备用解目标值: {obj:.2f}")
                except Exception as e2:
                    print(f"    备用策略也失败: {str(e2)}")

            # 如果没有生成任何候选解，使用回退解
            if not candidates:
                fallback = self._create_fallback_solution()
                if fallback:
                    candidates.append({
                        'solution': fallback,
                        'objective': self._calculate_objective_heuristic(fallback, 0),
                        'strategy': 'fallback'
                    })

            if not candidates:
                return [] if return_multiple else None

            # 按目标值排序
            candidates.sort(key=lambda x: x['objective'])

            if return_multiple:
                # 返回前3-5个不同的候选解，确保多样性
                diverse_candidates = []
                for candidate in candidates[:8]:  # 从前8个中选择
                    # 检查是否与已选择的解足够不同
                    is_diverse = True
                    for selected in diverse_candidates:
                        if self._solutions_too_similar(candidate['solution'], selected['solution']):
                            is_diverse = False
                            break

                    if is_diverse:
                        diverse_candidates.append(candidate)
                        if len(diverse_candidates) >= 5:  # 最多返回5个
                            break

                print(f"  生成了 {len(diverse_candidates)} 个多样化候选解:")
                for i, candidate in enumerate(diverse_candidates):
                    print(f"    候选解{i+1}: {candidate['strategy']}, 目标值: {candidate['objective']:.2f}")

                return [c['solution'] for c in diverse_candidates]
            else:
                # 返回最佳解
                return candidates[0]['solution']

        except Exception as e:
            print(f"  初始解生成失败: {e}")
            fallback = self._create_fallback_solution()
            return [fallback] if return_multiple and fallback else fallback

    def _solutions_too_similar(self, solution1, solution2, threshold=0.7):
        """
        检查两个解是否过于相似

        Args:
            solution1, solution2: 要比较的解
            threshold: 相似度阈值，超过此值认为过于相似
        """
        if not solution1 or not solution2:
            return False

        # 比较储物柜选择的相似度
        lockers1 = set(j for j, val in solution1['y'].items() if val > 0.5)
        lockers2 = set(j for j, val in solution2['y'].items() if val > 0.5)

        if not lockers1 or not lockers2:
            return False

        # 计算Jaccard相似度
        intersection = len(lockers1.intersection(lockers2))
        union = len(lockers1.union(lockers2))
        jaccard_similarity = intersection / union if union > 0 else 0

        # 如果储物柜选择相似度过高，认为解过于相似
        return jaccard_similarity > threshold

    def _create_greedy_solution(self):
        """基于贪心策略的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化所有储物柜为关闭状态
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算每个储物柜的"吸引力"分数
        locker_scores = {}
        for j in self.problem.sites:
            score = 0
            reachable_customers = 0
            total_expected_demand = 0

            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers += 1
                        # 距离越近，分数越高
                        distance_score = 1.0 / (1.0 + self.problem.distance[i, j])
                        demand_score = self.problem.expected_demand[i]
                        score += distance_score * demand_score
                        total_expected_demand += self.problem.expected_demand[i]

            # 考虑储物柜固定成本
            if reachable_customers > 0:
                cost_penalty = self.problem.locker_fixed_cost[j] / 10000.0  # 归一化
                locker_scores[j] = score - cost_penalty
            else:
                locker_scores[j] = -float('inf')  # 无法服务任何客户

        # 贪心选择储物柜，确保满足服务能力要求
        sorted_lockers = sorted(locker_scores.items(), key=lambda x: x[1], reverse=True)

        # 计算总需求
        total_demand = sum(self.problem.expected_demand.values())

        # 选择足够的储物柜以避免过高的惩罚成本
        if sorted_lockers and sorted_lockers[0][1] > -float('inf'):
            # 更积极的初始选择：选择更多储物柜以确保服务覆盖
            positive_score_lockers = [s for s in sorted_lockers if s[1] > 0]
            num_to_select = min(max(4, len(positive_score_lockers) // 2), 8)  # 选择4-8个储物柜
            num_to_select = max(1, num_to_select)  # 至少选择1个

            # 确保不超过可用储物柜数量
            num_to_select = min(num_to_select, len(sorted_lockers))

            for i in range(num_to_select):
                if i < len(sorted_lockers):  # 双重检查索引有效性
                    j = sorted_lockers[i][0]
                    solution['y'][j] = 1

                # 估算需要的无人机数量
                estimated_demand = 0
                for customer in self.problem.customers:
                    if (customer, j) in self.problem.distance:
                        flight_distance = 2 * self.problem.distance[customer, j]
                        if flight_distance <= self.problem.max_flight_distance:
                            # 简单分配：按距离权重分配需求
                            weight = 1.0 / (1.0 + self.problem.distance[customer, j])
                            estimated_demand += self.problem.expected_demand[customer] * weight / num_to_select

                # 计算所需无人机数量（更精确的计算）
                if estimated_demand > 0:
                    # 计算该储物柜的平均服务距离
                    total_distance = 0
                    reachable_count = 0
                    for customer in self.problem.customers:
                        if (customer, j) in self.problem.distance:
                            flight_distance = 2 * self.problem.distance[customer, j]
                            if flight_distance <= self.problem.max_flight_distance:
                                total_distance += self.problem.distance[customer, j]
                                reachable_count += 1

                    if reachable_count > 0:
                        avg_distance = total_distance / reachable_count
                        avg_service_time = (2 * avg_distance / self.problem.drone_speed) + self.problem.loading_time
                        total_time_needed = estimated_demand * avg_service_time
                        drones_needed = math.ceil(total_time_needed / self.problem.H_drone_working_hours_per_day)
                        # 确保有足够的无人机运力，考虑服务能力要求
                        min_drones = max(1, math.ceil(estimated_demand / (self.problem.H_drone_working_hours_per_day / avg_service_time)))
                        # 适当增加无人机配置以提供服务缓冲
                        recommended_drones = max(min_drones, math.ceil(drones_needed * 1.5))  # 增加50%缓冲
                        solution['n'][j] = min(recommended_drones, 15)  # 增加上限到15架
                    else:
                        solution['n'][j] = 2  # 如果无法计算，默认2架
                else:
                    solution['n'][j] = 1  # 至少1架无人机

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution



    def _create_coverage_based_solution(self):
        """基于服务覆盖率的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算每个储物柜的覆盖能力
        coverage_scores = {}
        for j in self.problem.sites:
            covered_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        covered_demand += self.problem.expected_demand[i]
            coverage_scores[j] = covered_demand

        # 选择覆盖能力最强的储物柜
        sorted_by_coverage = sorted(coverage_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前5个储物柜，并智能配置无人机
        for i in range(min(5, len(sorted_by_coverage))):
            j = sorted_by_coverage[i][0]
            if sorted_by_coverage[i][1] > 0:  # 确保有覆盖能力
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution

    def _create_balanced_solution(self):
        """平衡成本和覆盖的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算成本效益比
        efficiency_scores = {}
        for j in self.problem.sites:
            covered_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        covered_demand += self.problem.expected_demand[i]

            if covered_demand > 0:
                # 效益 = 覆盖需求 / (固定成本 + 无人机成本)
                total_cost = self.problem.locker_fixed_cost[j] + self.problem.drone_cost
                efficiency_scores[j] = covered_demand / total_cost
            else:
                efficiency_scores[j] = 0

        # 选择效益最高的储物柜
        sorted_by_efficiency = sorted(efficiency_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前5个储物柜，智能配置无人机
        for i in range(min(5, len(sorted_by_efficiency))):
            j = sorted_by_efficiency[i][0]
            if sorted_by_efficiency[i][1] > 0:
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution

    def _create_saa_inspired_solution(self):
        """基于saa_g_r.py最优解的启发式初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 基于saa_g_r.py的最优解模式：选择储物柜[1,2,4,5,6]
        optimal_pattern = [1, 2, 4, 5, 6]
        for j in optimal_pattern:
            if j in self.problem.sites:
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量，而非固定1架
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution





    def _create_cost_benefit_solution_dynamic(self):
        """
        【改进版】智能容量规划的初始解生成

        核心改进：
        1. 根据总需求预先估算最少需要的储物柜数量
        2. 确保初始解有足够的容量避免巨大惩罚成本
        3. 基于需求密度和容量需求智能选择储物柜
        """
        print("    [智能容量规划] 生成初始解...")

        # 第1步：计算总需求和容量需求
        total_expected_demand = sum(self.problem.expected_demand.values())
        avg_locker_capacity = sum(self.problem.Q_locker_capacity.values()) / len(self.problem.Q_locker_capacity) if self.problem.Q_locker_capacity else 30

        # 估算最少需要的储物柜数量（考虑70%容量利用率，更保守）
        min_lockers_needed = math.ceil(total_expected_demand / (avg_locker_capacity * 0.7))

        # 设置初始储物柜数量（更积极地开设储物柜）
        max_available = len(self.problem.sites)
        target_lockers = min(max(min_lockers_needed, 5), min(max_available, 15))  # 至少5个，最多15个

        print(f"      总期望需求: {total_expected_demand:.1f}")
        print(f"      平均储物柜容量: {avg_locker_capacity:.1f}")
        print(f"      估算最少储物柜: {min_lockers_needed}")
        print(f"      目标储物柜数量: {target_lockers}")

        # 第2步：计算每个储物柜的综合评分（需求密度 + 容量效率）
        locker_scores = {}
        if self.problem.expected_demand and self.problem.distance:
            for j in self.problem.sites:
                # 计算可达需求量
                reachable_demand = 0
                total_weighted_demand = 0
                for i in self.problem.customers:
                    if (i, j) in self.problem.distance:
                        distance = self.problem.distance[i, j]
                        if distance <= self.problem.max_flight_distance:
                            demand = self.problem.expected_demand.get(i, 0)
                            reachable_demand += demand
                            # 距离越近，权重越高
                            weight = 1.0 / (distance + 1.0)
                            total_weighted_demand += demand * weight

                # 计算容量利用率评分
                locker_capacity = self.problem.Q_locker_capacity.get(j, avg_locker_capacity)
                capacity_utilization = min(reachable_demand / locker_capacity, 1.0) if locker_capacity > 0 else 0

                # 综合评分：加权需求密度 + 容量利用率
                locker_scores[j] = total_weighted_demand * 0.7 + capacity_utilization * 100 * 0.3

            # 选择评分最高的储物柜
            sorted_lockers = sorted(locker_scores.items(), key=lambda x: x[1], reverse=True)
            selected_lockers = [j for j, score in sorted_lockers[:target_lockers] if score > 0]
        else:
            # 回退策略：选择前几个储物柜
            selected_lockers = self.problem.sites[:target_lockers]

        # 确保至少选择了一个储物柜
        if not selected_lockers:
            selected_lockers = [self.problem.sites[0]] if self.problem.sites else []

        # 第3步：构建解并智能配置无人机
        solution = {'y': {j: 0 for j in self.problem.sites}, 'n': {j: 0 for j in self.problem.sites}}
        for j in selected_lockers:
            solution['y'][j] = 1
            # 智能配置无人机数量
            estimated_demand = self._estimate_locker_demand(j, solution)
            recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
            solution['n'][j] = recommended_drones

        print(f"      选择储物柜: {selected_lockers}")
        print(f"      无人机配置: {[solution['n'][j] for j in selected_lockers]}")

        # 第4步：验证容量充足性
        total_capacity = sum(self.problem.Q_locker_capacity.get(j, 0) for j in selected_lockers)
        capacity_ratio = total_capacity / total_expected_demand if total_expected_demand > 0 else 1.0
        print(f"      总容量: {total_capacity:.1f}, 容量比率: {capacity_ratio:.2f}")

        if capacity_ratio < 0.8:
            print(f"      ⚠️ 警告：容量可能不足（建议比率≥0.8），当前{capacity_ratio:.2f}")

        # 如果容量严重不足，尝试增加更多储物柜
        if capacity_ratio < 0.6 and len(selected_lockers) < max_available:
            additional_needed = math.ceil((total_expected_demand * 0.8 - total_capacity) / avg_locker_capacity)
            additional_available = max_available - len(selected_lockers)
            additional_to_add = min(additional_needed, additional_available, 5)  # 最多再加5个

            if additional_to_add > 0:
                # 从剩余储物柜中选择评分最高的
                remaining_lockers = [j for j in self.problem.sites if j not in selected_lockers]
                if remaining_lockers and locker_scores:
                    remaining_scores = [(j, locker_scores.get(j, 0)) for j in remaining_lockers]
                    remaining_scores.sort(key=lambda x: x[1], reverse=True)

                    for j, score in remaining_scores[:additional_to_add]:
                        selected_lockers.append(j)
                        solution['y'][j] = 1
                        estimated_demand = self._estimate_locker_demand(j, solution)
                        recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                        solution['n'][j] = recommended_drones

                    # 重新计算容量
                    total_capacity = sum(self.problem.Q_locker_capacity.get(j, 0) for j in selected_lockers)
                    capacity_ratio = total_capacity / total_expected_demand if total_expected_demand > 0 else 1.0
                    print(f"      增加{additional_to_add}个储物柜后: 总容量{total_capacity:.1f}, 容量比率{capacity_ratio:.2f}")

        return solution

    def _debug_compare_locker_configurations(self, base_solution):
        """
        调试函数：比较不同储物柜数量配置的成本，找出为什么倾向于选择更多储物柜
        """
        print(f"    [调试] 比较不同储物柜配置的成本...")

        # 获取所有可用储物柜
        all_lockers = self.problem.sites
        base_selected = [j for j, val in base_solution['y'].items() if val > 0.5]

        # 测试不同数量的储物柜配置（智能配置无人机）
        configurations_to_test = []

        # 1. 测试只用前2个最优储物柜
        if len(all_lockers) >= 2:
            config_2 = {'y': {}, 'n': {}}
            open_lockers_2 = all_lockers[:2]
            for j in all_lockers:
                config_2['y'][j] = 1 if j in open_lockers_2 else 0
                config_2['n'][j] = 0

            # 为这两个储物柜智能配置无人机
            for j in open_lockers_2:
                est_demand = self._estimate_locker_demand(j, config_2)
                config_2['n'][j] = self._calculate_recommended_drones(j, est_demand)
            configurations_to_test.append(("前2个储物柜", config_2))

        # 2. 测试只用前3个最优储物柜
        if len(all_lockers) >= 3:
            config_3 = {'y': {}, 'n': {}}
            open_lockers_3 = all_lockers[:3]
            for j in all_lockers:
                config_3['y'][j] = 1 if j in open_lockers_3 else 0
                config_3['n'][j] = 0

            # 为这三个储物柜智能配置无人机
            for j in open_lockers_3:
                est_demand = self._estimate_locker_demand(j, config_3)
                config_3['n'][j] = self._calculate_recommended_drones(j, est_demand)
            configurations_to_test.append(("前3个储物柜", config_3))

        # 3. 测试当前解
        configurations_to_test.append(("当前解", base_solution))

        # 4. 测试全部储物柜
        if len(all_lockers) >= 4:
            config_all = {'y': {}, 'n': {}}
            for j in all_lockers:
                config_all['y'][j] = 1
                config_all['n'][j] = 0

            # 为所有储物柜智能配置无人机
            for j in all_lockers:
                est_demand = self._estimate_locker_demand(j, config_all)
                config_all['n'][j] = self._calculate_recommended_drones(j, est_demand)
            configurations_to_test.append(("全部储物柜", config_all))

        print(f"      配置对比:")
        for name, config in configurations_to_test:
            try:
                if self._is_feasible(config):
                    # 使用精确评估获得真实成本
                    exact_cost = self.calculate_objective_cached(config)
                    heuristic_cost = self._calculate_objective_heuristic(config, 0)

                    selected = [j for j, val in config['y'].items() if val > 0.5]
                    drones = {j: config['n'][j] for j in selected}

                    print(f"        {name}: 储物柜{selected}, 无人机{drones}")
                    print(f"          精确成本: {exact_cost:.2f}, 启发式成本: {heuristic_cost:.2f}, 差异: {abs(exact_cost-heuristic_cost):.2f}")
                else:
                    print(f"        {name}: 不可行")
            except Exception as e:
                print(f"        {name}: 评估失败 - {e}")

        print(f"    [调试] 配置对比完成")

    def simulation_based_greedy_insertion(self, solution, iteration=0):
        """
        【新增】基于仿真的贪心插入修复算子

        使用与初始解生成相同的逻辑：通过比较总成本来决定是否插入储物柜
        这确保了修复算子与初始解生成策略的一致性
        """
        current_cost = self._calculate_objective_heuristic(solution, iteration)

        # 找到所有关闭的储物柜作为候选
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return solution  # 所有储物柜都已开启

        best_improvement = 0
        best_locker_to_add = None
        best_temp_solution = None

        # 遍历所有候选储物柜，找到能最大程度降低总成本的选择
        for locker_to_add in closed_lockers:
            # 创建临时解
            temp_solution = copy.deepcopy(solution)
            temp_solution['y'][locker_to_add] = 1

            # 智能配置无人机
            estimated_demand = self._estimate_locker_demand(locker_to_add, temp_solution)
            recommended_drones = self._calculate_recommended_drones(locker_to_add, estimated_demand)
            temp_solution['n'][locker_to_add] = recommended_drones

            # 评估新配置的总成本
            try:
                temp_cost = self._calculate_objective_heuristic(temp_solution, iteration)
                improvement = current_cost - temp_cost

                # 寻找最大的改进
                if improvement > best_improvement:
                    best_improvement = improvement
                    best_locker_to_add = locker_to_add
                    best_temp_solution = temp_solution

            except Exception as e:
                continue  # 如果评估失败，跳过这个候选

        # 如果找到了有益的插入，执行它
        if best_locker_to_add is not None and best_improvement > 0:
            solution['y'][best_locker_to_add] = 1
            estimated_demand = self._estimate_locker_demand(best_locker_to_add, solution)
            recommended_drones = self._calculate_recommended_drones(best_locker_to_add, estimated_demand)
            solution['n'][best_locker_to_add] = recommended_drones

            if iteration <= 5:  # 只在前几次迭代输出调试信息
                print(f"    [修复] 插入储物柜{best_locker_to_add}，成本降低{best_improvement:.2f}")

        return solution

    def _analyze_current_demand_samples(self):
        """
        分析当前复制的需求样本特征，为初始解生成提供指导
        """
        if not self.demand_samples:
            # 如果没有样本，使用期望需求
            total_demand = sum(self.problem.expected_demand.values())
            return {
                'total_demand': total_demand,
                'demand_variance': 0.0,
                'high_demand_ratio': 0.5,
                'demand_distribution': {i: self.problem.expected_demand[i] for i in self.problem.customers}
            }

        # 计算样本统计
        sample_totals = []
        customer_demands = {i: [] for i in self.problem.customers}

        for sample in self.demand_samples:
            sample_total = sum(sample.values())
            sample_totals.append(sample_total)

            for customer_i in self.problem.customers:
                customer_demands[customer_i].append(sample.get(customer_i, 0))

        # 计算统计指标
        import numpy as np
        total_demand = np.mean(sample_totals)
        demand_variance = np.var(sample_totals)

        # 计算每个客户的平均需求
        avg_customer_demands = {}
        for customer_i in self.problem.customers:
            avg_customer_demands[customer_i] = np.mean(customer_demands[customer_i])

        # 计算高需求客户比例（需求高于平均值的客户）
        avg_demand_per_customer = total_demand / len(self.problem.customers)
        high_demand_customers = sum(1 for demand in avg_customer_demands.values()
                                   if demand > avg_demand_per_customer)
        high_demand_ratio = high_demand_customers / len(self.problem.customers)

        return {
            'total_demand': total_demand,
            'demand_variance': demand_variance,
            'high_demand_ratio': high_demand_ratio,
            'demand_distribution': avg_customer_demands
        }

    def _prioritize_lockers_by_demand_samples(self, candidate_lockers, sample_demand_stats):
        """
        根据当前需求样本特征对候选储物柜进行优先级排序
        引入随机性以避免每次复制产生相同结果
        """
        import random

        # 计算每个储物柜对当前需求分布的适应性
        locker_scores = {}
        demand_distribution = sample_demand_stats['demand_distribution']

        for j in candidate_lockers:
            score = 0.0
            reachable_demand = 0.0

            # 基于实际需求样本计算储物柜的价值
            for i in self.problem.customers:
                if (i, j) in self.problem.distance and \
                   2 * self.problem.distance[i, j] <= self.problem.max_flight_distance:
                    # 使用当前样本的平均需求而不是期望需求
                    customer_demand = demand_distribution.get(i, 0)
                    distance_factor = 1.0 / (1.0 + self.problem.distance[i, j])
                    score += customer_demand * distance_factor
                    reachable_demand += customer_demand

            # 考虑储物柜成本
            locker_cost = self.problem.locker_fixed_cost.get(j, 0) + self.problem.drone_cost
            if locker_cost > 0:
                score = score / locker_cost

            locker_scores[j] = score

        # 按分数排序，但引入随机性
        sorted_lockers = sorted(candidate_lockers, key=lambda j: locker_scores.get(j, 0), reverse=True)

        # 【关键】引入基于需求方差的随机性
        # 需求方差越大，随机性越强
        variance_factor = min(1.0, sample_demand_stats['demand_variance'] / 100.0)
        randomness_strength = 0.3 * variance_factor  # 最多30%的随机性

        if randomness_strength > 0.1 and len(sorted_lockers) > 1:
            # 对前几个储物柜进行轻微的随机重排
            top_k = min(3, len(sorted_lockers))
            top_lockers = sorted_lockers[:top_k]
            rest_lockers = sorted_lockers[top_k:]

            # 随机打乱前k个储物柜的顺序
            if random.random() < randomness_strength:
                random.shuffle(top_lockers)
                print(f"    [随机性] 基于需求方差{sample_demand_stats['demand_variance']:.2f}，"
                      f"对前{top_k}个储物柜进行随机重排")

            return top_lockers + rest_lockers

        return sorted_lockers

    def _get_adaptive_decision_threshold(self, sample_demand_stats, iteration):
        """
        根据需求样本特征和迭代次数计算自适应决策阈值
        """
        base_threshold = 0.0  # 基础阈值

        # 根据需求方差调整：方差大时更保守
        variance_factor = sample_demand_stats['demand_variance'] / 100.0
        variance_adjustment = min(5.0, variance_factor * 2.0)

        # 根据总需求调整：需求高时更积极开启储物柜
        total_demand = sample_demand_stats['total_demand']
        expected_total = sum(self.problem.expected_demand.values())
        demand_ratio = total_demand / expected_total if expected_total > 0 else 1.0

        if demand_ratio > 1.1:  # 高需求场景
            demand_adjustment = -2.0  # 更积极
        elif demand_ratio < 0.9:  # 低需求场景
            demand_adjustment = 3.0   # 更保守
        else:
            demand_adjustment = 0.0

        # 根据迭代次数调整：后期迭代更保守
        iteration_adjustment = iteration * 1.0

        final_threshold = base_threshold + variance_adjustment + demand_adjustment + iteration_adjustment
        return max(0.0, final_threshold)

    def _is_improvement_statistically_significant(self, current_solution, new_solution, confidence_level=0.95):
        """
        【高级方法】通过多次评估检验成本改进是否统计显著

        Args:
            current_solution: 当前解
            new_solution: 新解
            confidence_level: 置信水平 (默认95%)

        Returns:
            tuple: (is_significant, mean_improvement, confidence_interval)
        """
        import numpy as np
        from scipy import stats

        # 进行多次独立评估
        n_evaluations = 10
        current_costs = []
        new_costs = []

        for _ in range(n_evaluations):
            # 使用不同的随机种子进行评估
            current_cost = self._calculate_objective_heuristic(current_solution, 0)
            new_cost = self._calculate_objective_heuristic(new_solution, 0)

            current_costs.append(current_cost)
            new_costs.append(new_cost)

        # 计算成本差异
        improvements = [current - new for current, new in zip(current_costs, new_costs)]

        if len(improvements) < 2:
            return False, 0.0, (0.0, 0.0)

        # 计算统计量
        mean_improvement = np.mean(improvements)
        std_improvement = np.std(improvements, ddof=1)

        # 单样本t检验：H0: 改进 <= 0, H1: 改进 > 0
        if std_improvement == 0:
            # 如果标准差为0，直接基于均值判断
            return mean_improvement > 0, mean_improvement, (mean_improvement, mean_improvement)

        t_stat = mean_improvement / (std_improvement / np.sqrt(n_evaluations))
        p_value = 1 - stats.t.cdf(t_stat, df=n_evaluations-1)  # 单侧检验

        # 计算置信区间
        alpha = 1 - confidence_level
        t_critical = stats.t.ppf(1 - alpha/2, df=n_evaluations-1)
        margin_error = t_critical * (std_improvement / np.sqrt(n_evaluations))
        ci_lower = mean_improvement - margin_error
        ci_upper = mean_improvement + margin_error

        # 判断是否显著
        is_significant = p_value < (1 - confidence_level) and ci_lower > 0

        return is_significant, mean_improvement, (ci_lower, ci_upper)

    def _get_cost_breakdown(self, solution):
        """
        获取解的详细成本分解，用于调试
        【修正版】直接使用启发式评估的内部计算，确保一致性
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            # 空解：只有惩罚成本
            if hasattr(self, 'demand_samples') and self.demand_samples:
                sample_totals = [sum(sample.values()) for sample in self.demand_samples]
                avg_total_demand = sum(sample_totals) / len(sample_totals)
            else:
                avg_total_demand = sum(self.problem.expected_demand.values())

            penalty_cost = avg_total_demand * self.problem.penalty_cost_unassigned
            return {
                'first_stage': 0.0,
                'second_stage': penalty_cost,
                'truck_cost': 0.0,
                'total': penalty_cost
            }

        # 【关键修正】直接调用启发式评估的内部逻辑
        # 第一阶段成本
        first_stage_cost = (sum(self.problem.locker_fixed_cost.get(j, 0) for j in selected_lockers) +
                           sum(self.problem.drone_cost * n_star.get(j, 0) for j in selected_lockers))

        # 使用与启发式评估相同的逻辑计算第二阶段成本
        try:
            # 使用启发式评估的采样策略
            k_small = min(15, len(self.demand_samples))
            sample_indices = list(range(k_small))

            total_second_stage_costs = []
            total_truck_costs = []

            for idx in sample_indices:
                demand_scenario = self.demand_samples[idx]

                # 使用与启发式评估相同的分配方法
                assignment = self._solve_second_stage_subproblem(y_star, n_star, selected_lockers, demand_scenario)

                # 计算运输和惩罚成本
                transport_cost = 0.0
                penalty_cost = 0.0

                for i in self.problem.customers:
                    demand_i = demand_scenario.get(i, 0)
                    assigned_total = sum(assignment.get((i, j), 0) for j in selected_lockers)

                    # 运输成本
                    for j in selected_lockers:
                        if (i, j) in assignment and assignment[i, j] > 0:
                            distance = self.problem.distance.get((i, j), 0)
                            transport_cost += assignment[i, j] * distance * self.problem.transport_unit_cost * 2

                    # 惩罚成本
                    if assigned_total < demand_i:
                        penalty_cost += (demand_i - assigned_total) * self.problem.penalty_cost_unassigned

                second_stage_cost = transport_cost + penalty_cost
                total_second_stage_costs.append(second_stage_cost)

                # 【修正】使用DRL精确计算卡车成本，与ALNS保持一致
                if DRL_AVAILABLE:
                    # 构建active_lockers_info用于DRL求解
                    active_lockers_info = {}
                    total_assignment_demand = 0

                    for j in selected_lockers:
                        estimated_demand = 0
                        for i in self.problem.customers:
                            if (i, j) in assignment and assignment[i, j] > 0:
                                estimated_demand += assignment[i, j]
                                total_assignment_demand += assignment[i, j]

                        if estimated_demand > 0.5:  # 只包含有需求的储物柜
                            active_lockers_info[j] = {
                                'coord': self.problem.site_coords[j],
                                'demand': round(estimated_demand)
                            }

                    # 【简化】移除调试输出

                    if active_lockers_info:
                        # 【新增】DRL缓存机制
                        cache_key = self._get_drl_cache_key(active_lockers_info)
                        if cache_key in self.drl_truck_cost_cache:
                            truck_cost = self.drl_truck_cost_cache[cache_key]
                            self.drl_cache_hits += 1
                        else:
                            truck_cost = self.problem.calculate_truck_cost([], {}, make_plots=False,
                                                                        active_lockers_info_override=active_lockers_info)
                            self.drl_truck_cost_cache[cache_key] = truck_cost
                            self.drl_cache_misses += 1

                        # 【修复】确保total_drl_demand变量总是被定义
                        total_drl_demand = sum(info['demand'] for info in active_lockers_info.values())

                        # 【简化】移除DRL调试输出

                        # 【简化】移除理论对比调试输出

                        # 【修复】移除异常检测逻辑，直接使用DRL结果
                        # DRL计算结果已经是合理的（105-115元），不需要回退到简化估算
                        # 简化估算会产生错误的高成本（586元等）
                        pass  # 直接使用DRL的truck_cost结果
                    else:
                        truck_cost = 0.0
                else:
                    # DRL不可用时直接报错，不使用不准确的简化估算
                    if active_lockers_info:
                        raise RuntimeError(f"DRL不可用但需要计算卡车成本，储物柜信息: {active_lockers_info}")
                    else:
                        truck_cost = 0.0

                total_truck_costs.append(truck_cost)

            avg_second_stage = sum(total_second_stage_costs) / len(total_second_stage_costs) if total_second_stage_costs else 0
            avg_truck_cost = sum(total_truck_costs) / len(total_truck_costs) if total_truck_costs else 0

        except Exception as e:
            # 【修复】如果计算失败，直接报错，不使用不准确的比例分解
            print(f"          [错误] _get_cost_breakdown计算失败: {e}")
            raise RuntimeError(f"成本分解计算失败，无法继续: {e}")

        return {
            'first_stage': first_stage_cost,
            'second_stage': avg_second_stage,
            'truck_cost': avg_truck_cost,
            'total': first_stage_cost + avg_second_stage + avg_truck_cost
        }

    def _estimate_truck_cost_simple(self, selected_lockers, demand_scenario):
        """
        【修正版】更准确的卡车成本估算，参考g_i.py的结果
        """
        if not selected_lockers:
            return 0.0

        num_lockers = len(selected_lockers)
        total_demand = sum(demand_scenario.values())

        # 【修正】基于g_i.py的实际结果校准估算公式
        # g_i.py: 3个储物柜，48需求 → 117.11元卡车成本
        # 分析：固定成本100 + 路径成本约17元

        base_cost = self.problem.truck_fixed_cost  # 100元固定成本

        # 【关键修正】路径成本应该考虑TSP效应，不是线性增长
        if num_lockers == 1:
            route_cost = 5.0   # 单点往返，路径成本很低
        elif num_lockers == 2:
            route_cost = 10.0  # 两点路径
        elif num_lockers == 3:
            route_cost = 17.0  # 三点路径（参考g_i.py）
        elif num_lockers == 4:
            route_cost = 25.0  # 四点路径，增长放缓
        else:
            route_cost = 25.0 + (num_lockers - 4) * 5.0  # 更多储物柜时增长更慢

        # 需求相关成本：每单位需求的运输成本
        # 【修正】降低需求系数，避免高需求场景下成本过高
        demand_cost = total_demand * 0.2  # 从0.5降到0.2

        total_cost = base_cost + route_cost + demand_cost

        # 【调试】输出详细计算过程
        if hasattr(self, '_debug_truck_cost') and self._debug_truck_cost:
            print(f"          卡车成本详细: 固定{base_cost} + 路径{route_cost} + 需求{demand_cost:.1f} = {total_cost:.1f}")

        return total_cost

    def _create_fallback_solution(self):
        """备用简单第一阶段解（修正版两阶段结构）"""
        # 初始化第一阶段解结构
        solution = {'y': {}, 'n': {}}

        # 检查是否有可用的储物柜站点
        if not self.problem.sites:
            print("  警告: 没有可用的储物柜站点")
            return None

        # 选择第一个储物柜站点作为备用解
        first_site = self.problem.sites[0]

        # 初始化储物柜选址和无人机配置（从最少无人机开始）
        for j in self.problem.sites:
            solution['y'][j] = 1 if j == first_site else 0
            solution['n'][j] = 1 if j == first_site else 0  # 从1架无人机开始

        return solution

    def _select_operator(self, operators, weights):
        """
        根据权重随机选择算子
        """
        total_weight = sum(weights[op.__name__] for op in operators)
        if total_weight <= 0:
            return random.choice(operators)

        rand_val = random.uniform(0, total_weight)
        cumulative_weight = 0

        for op in operators:
            cumulative_weight += weights[op.__name__]
            if rand_val <= cumulative_weight:
                return op

        return operators[-1]  # 回退

    def _update_operator_weights(self):
        """
        根据算子的成功率或分数更新权重
        """
        if self.config['use_score_based_weights']:
            # 基于分数的权重更新
            self._update_weights_by_score()
        else:
            # 传统的基于成功率的权重更新
            self._update_weights_by_success_rate()

    def _reset_operator_weights(self):
        """
        重置算子权重和统计信息，用于重启机制
        """
        print(f"      重置算子权重和统计信息...")

        # 重置权重为初始值
        self.destroy_weights = {op.__name__: 1.0 for op in self.destroy_operators}
        self.repair_weights = {op.__name__: 1.0 for op in self.repair_operators}

        # 重置使用统计
        self.destroy_usage = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_usage = {op.__name__: 0 for op in self.repair_operators}

        # 重置成功统计
        self.destroy_success = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_success = {op.__name__: 0 for op in self.repair_operators}

        # 重置分数统计
        self.destroy_scores = {op.__name__: 0.0 for op in self.destroy_operators}
        self.repair_scores = {op.__name__: 0.0 for op in self.repair_operators}

    def _update_weights_by_score(self):
        """
        基于论文公式的权重更新策略：ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
        其中：ω为权重，μ为更新系数，π为算子得分，β为算子使用次数
        """
        mu = self.config['weight_update_coefficient']  # μ = 0.1

        # 更新破坏算子权重
        for op in self.destroy_operators:
            op_name = op.__name__
            if self.destroy_usage[op_name] > 0:
                # π/β：平均得分
                avg_score = self.destroy_scores[op_name] / self.destroy_usage[op_name]
                # ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
                self.destroy_weights[op_name] = (self.destroy_weights[op_name] * (1 - mu) +
                                               mu * avg_score)
            self.destroy_weights[op_name] = max(0.1, self.destroy_weights[op_name])  # 最小权重

        # 更新修复算子权重
        for op in self.repair_operators:
            op_name = op.__name__
            if self.repair_usage[op_name] > 0:
                # π/β：平均得分
                avg_score = self.repair_scores[op_name] / self.repair_usage[op_name]
                # ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
                self.repair_weights[op_name] = (self.repair_weights[op_name] * (1 - mu) +
                                              mu * avg_score)
            self.repair_weights[op_name] = max(0.1, self.repair_weights[op_name])  # 最小权重

    def _update_weights_by_success_rate(self):
        """
        传统的基于成功率的权重更新策略
        """
        decay = self.config['weight_decay']

        # 更新破坏算子权重
        for op in self.destroy_operators:
            op_name = op.__name__
            if self.destroy_usage[op_name] > 0:
                success_rate = self.destroy_success[op_name] / self.destroy_usage[op_name]
                self.destroy_weights[op_name] = (self.destroy_weights[op_name] * decay +
                                               success_rate * (1 - decay))
            self.destroy_weights[op_name] = max(0.1, self.destroy_weights[op_name])  # 最小权重

        # 更新修复算子权重
        for op in self.repair_operators:
            op_name = op.__name__
            if self.repair_usage[op_name] > 0:
                success_rate = self.repair_success[op_name] / self.repair_usage[op_name]
                self.repair_weights[op_name] = (self.repair_weights[op_name] * decay +
                                              success_rate * (1 - decay))
            self.repair_weights[op_name] = max(0.1, self.repair_weights[op_name])  # 最小权重

    # ===== 破坏算子 =====

    def random_locker_removal(self, solution, iteration=0):
        """
        随机移除储物柜（修正版：只操作第一阶段决策变量）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution  # 至少保留一个储物柜

        # 移除数量：1到一半的储物柜
        num_to_remove = random.randint(1, max(1, len(open_lockers) // 2))
        lockers_to_remove = random.sample(open_lockers, num_to_remove)

        for j in lockers_to_remove:
            # 只移除第一阶段决策：储物柜选址和无人机配置
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def worst_locker_removal(self, solution, iteration=0):
        """
        移除贡献最小的储物柜
        贡献度 = 该储物柜对整体解质量的贡献，通过移除前后的目标函数差值计算
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution

        # 计算每个储物柜的真实贡献度（移除该储物柜后的成本增加）
        current_obj = self._calculate_objective_heuristic(solution, iteration)
        locker_contributions = {}

        for j in open_lockers:
            # 创建移除储物柜j的临时解
            temp_solution = copy.deepcopy(solution)
            temp_solution['y'][j] = 0
            temp_solution['n'][j] = 0

            # 计算移除后的目标函数值
            temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)

            # 贡献度 = 移除后的成本增加（越小说明贡献越小）
            contribution = temp_obj - current_obj
            locker_contributions[j] = contribution

        # 移除贡献最小的储物柜（即移除后成本增加最少的）
        sorted_lockers = sorted(locker_contributions.items(), key=lambda x: x[1])
        num_to_remove = random.randint(1, max(1, len(open_lockers) // 3))  # 最多移除1/3

        for i in range(min(num_to_remove, len(sorted_lockers))):
            j = sorted_lockers[i][0]
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def related_locker_removal(self, solution, iteration=0):
        """
        【修改3】移除相关储物柜（增强版：综合地理位置和客户服务重叠度）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution

        seed_locker = random.choice(open_lockers)

        # 计算每个储物柜和种子储物柜的相关性
        relatedness = {}

        # 1. 预计算每个客户到所有开放储物柜的成本列表
        customer_service_options = {}
        for i in self.problem.customers:
            options = []
            for j in open_lockers:
                 if (i, j) in self.problem.distance and \
                    2 * self.problem.distance[i, j] <= self.problem.max_flight_distance:
                     cost = self.problem.distance[i, j]  # 简化成本为距离
                     options.append((j, cost))
            options.sort(key=lambda x: x[1])
            customer_service_options[i] = [opt[0] for opt in options]

        for j in open_lockers:
            if j == seed_locker:
                continue

            # a. 地理位置相关性 (权重 w1)
            if j in self.problem.site_coords and seed_locker in self.problem.site_coords:
                coord1 = self.problem.site_coords[seed_locker]
                coord2 = self.problem.site_coords[j]
                # 归一化距离
                geo_dist = math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
                max_dist = 20  # 假设场景最大距离
                geo_relatedness = max(0, 1 - (geo_dist / max_dist))
            else:
                geo_relatedness = 0

            # b. 客户服务重叠度 (权重 w2)
            shared_customers = 0
            for i in self.problem.customers:
                # 如果两个储物柜都是某客户的前2优选择，则认为它们共享该客户
                top_options = customer_service_options.get(i, [])[:2]
                if seed_locker in top_options and j in top_options:
                    shared_customers += 1

            service_relatedness = shared_customers / len(self.problem.customers) if self.problem.customers else 0

            # 综合相关性
            w1, w2 = 0.5, 0.5
            relatedness[j] = w1 * geo_relatedness + w2 * service_relatedness

        # 按相关性从高到低排序，移除最相关的几个
        if relatedness:
            sorted_by_relatedness = sorted(relatedness.items(), key=lambda x: x[1], reverse=True)
            num_to_remove = random.randint(1, max(1, len(open_lockers) // 3))

            for i in range(min(num_to_remove, len(sorted_by_relatedness))):
                j_to_remove = sorted_by_relatedness[i][0]
                new_solution['y'][j_to_remove] = 0
                new_solution['n'][j_to_remove] = 0

        return new_solution

    def drone_adjustment_removal(self, solution, iteration=0):
        """
        调整无人机配置（减少无人机数量）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        # 随机选择几个储物柜    减少无人机
        num_to_adjust = random.randint(1, max(1, len(open_lockers)))
        lockers_to_adjust = random.sample(open_lockers, num_to_adjust)

        for j in lockers_to_adjust:
            current_drones = solution['n'][j]
            if current_drones > 1:
                reduction = random.randint(1, max(1, int(current_drones // 2)))
                new_solution['n'][j] = max(1, current_drones - reduction)

        return new_solution

    def cluster_removal(self, solution, iteration=0):
        """
        簇移除：基于客户分配关系，移除功能上相似的储物柜。
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 2:
            return new_solution

        # 1. 估算每个客户主要由哪个储物柜服务
        customer_main_locker = {}
        for i in self.problem.customers:
            best_j = -1
            min_dist = float('inf')
            for j in open_lockers:
                if (i, j) in self.problem.distance:
                    dist = self.problem.distance[i, j]
                    if dist < min_dist:
                        min_dist = dist
                        best_j = j
            if best_j != -1:
                customer_main_locker[i] = best_j

        # 2. 随机选择一个种子储物柜
        seed_locker = random.choice(open_lockers)

        # 3. 找到与种子储物柜服务相似客户群体的其他储物柜
        seed_customers = {i for i, j in customer_main_locker.items() if j == seed_locker}
        if not seed_customers:
            return new_solution  # 种子储物柜没服务客户，无法形成簇

        cluster_to_remove = {seed_locker}
        for j in open_lockers:
            if j != seed_locker:
                other_customers = {i for i, l in customer_main_locker.items() if l == j}
                # 计算Jaccard相似度
                intersection = len(seed_customers.intersection(other_customers))
                union = len(seed_customers.union(other_customers))
                if union > 0 and (intersection / union) > 0.2:  # 相似度阈值
                    cluster_to_remove.add(j)

        # 4. 移除整个簇（但至少保留一个储物柜）
        if len(open_lockers) - len(cluster_to_remove) < 1:
            # 如果要移除所有，则只移除一部分
            cluster_to_remove = set(random.sample(list(cluster_to_remove), len(cluster_to_remove) - 1))

        for j in cluster_to_remove:
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def zone_removal(self, solution, iteration=0):
        """
        区域移除算子：基于地理位置的大范围破坏
        """
        try:
            y_star = solution['y']
            n_star = solution['n']
            open_lockers = [j for j, val in y_star.items() if val > 0.5]

            if len(open_lockers) <= 1:
                return solution

            # 随机选择一个中心储物柜
            center_j = random.choice(open_lockers)

            # 计算所有储物柜到中心的距离
            distances = []
            for j in open_lockers:
                if j != center_j:
                    # 使用客户作为中介计算储物柜间的"服务距离"
                    min_dist = float('inf')
                    for i in self.problem.customers:
                        if ((center_j, i) in self.problem.distance and
                            (j, i) in self.problem.distance):
                            dist = abs(self.problem.distance[center_j, i] - self.problem.distance[j, i])
                            min_dist = min(min_dist, dist)

                    if min_dist < float('inf'):
                        distances.append((j, min_dist))

            if not distances:
                return solution

            # 按距离排序，移除最近的几个储物柜（形成一个"空白区域"）
            distances.sort(key=lambda x: x[1])
            removal_count = min(len(distances), max(1, len(open_lockers) // 3))

            new_solution = copy.deepcopy(solution)
            new_solution['y'][center_j] = 0  # 移除中心
            new_solution['n'][center_j] = 0

            for i in range(removal_count):
                j = distances[i][0]
                new_solution['y'][j] = 0
                new_solution['n'][j] = 0

            return new_solution

        except Exception as e:
            return solution

    def radical_restructure(self, solution, iteration=0):
        """
        激进重构算子：大幅度改变解的结构，专门用于跳出局部最优
        """
        try:
            y_star = solution['y']
            n_star = solution['n']
            open_lockers = [j for j, val in y_star.items() if val > 0.5]

            if len(open_lockers) <= 2:
                return solution

            new_solution = copy.deepcopy(solution)

            # 策略1: 随机关闭50-70%的储物柜
            if random.random() < 0.4:
                removal_ratio = random.uniform(0.5, 0.7)
                removal_count = max(1, int(len(open_lockers) * removal_ratio))
                lockers_to_remove = random.sample(open_lockers, removal_count)

                for j in lockers_to_remove:
                    new_solution['y'][j] = 0
                    new_solution['n'][j] = 0

            # 策略2: 【改进】智能重新配置无人机分布
            elif random.random() < 0.7:
                # 保持储物柜选择，但智能重新分配无人机
                for j in open_lockers:
                    # 重新估算该储物柜的需求和推荐无人机数量
                    estimated_demand = self._estimate_locker_demand(j, new_solution)
                    recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

                    # 添加一些随机扰动以增加多样性
                    perturbation = random.randint(-1, 2)  # -1, 0, 1, 2的随机扰动
                    final_drones = max(1, recommended_drones + perturbation)
                    new_solution['n'][j] = min(final_drones, 8)  # 限制最大值

            # 策略3: 混合策略 - 部分关闭 + 重新配置
            else:
                # 关闭30-50%的储物柜
                removal_ratio = random.uniform(0.3, 0.5)
                removal_count = max(1, int(len(open_lockers) * removal_ratio))
                lockers_to_remove = random.sample(open_lockers, removal_count)

                for j in lockers_to_remove:
                    new_solution['y'][j] = 0
                    new_solution['n'][j] = 0

                # 【改进】对剩余储物柜智能重新配置无人机
                remaining_lockers = [j for j in open_lockers if j not in lockers_to_remove]
                for j in remaining_lockers:
                    # 重新估算需求和推荐无人机数量
                    estimated_demand = self._estimate_locker_demand(j, new_solution)
                    recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

                    # 添加随机扰动
                    perturbation = random.randint(-1, 2)
                    final_drones = max(1, recommended_drones + perturbation)
                    new_solution['n'][j] = min(final_drones, 8)

            return new_solution

        except Exception as e:
            return solution

    # ===== 修复算子 =====

    def greedy_locker_insertion(self, solution, iteration=0):
        """
        贪心插入储物柜（修正版：只操作第一阶段决策变量）
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 简化评估：选择能服务最多客户的储物柜
        best_locker = None
        best_drones = 1
        max_reachable_customers = 0

        for j in closed_lockers:
            # 计算该储物柜能服务的客户数量
            reachable_customers = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers += 1

            if reachable_customers > max_reachable_customers:
                max_reachable_customers = reachable_customers
                best_locker = j
                # 根据可服务客户数量估算无人机需求
                best_drones = min(3, max(1, reachable_customers // 10))

        # 插入最佳储物柜（只修改第一阶段决策）
        if best_locker is not None:
            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

        return new_solution

    def _estimate_insertion_delta(self, solution, locker_j, num_drones):
        """
        快速估算插入储物柜j的成本变化（增量评估）
        """
        # 1. 增加的固定成本
        delta_cost = self.problem.locker_fixed_cost[locker_j] + self.problem.drone_cost * num_drones

        # 2. 估算由于新储物柜的加入而减少的运输成本和惩罚成本
        cost_reduction = 0

        # 计算当前解中每个客户的最佳服务成本
        current_customer_costs = {}
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        for i in self.problem.customers:
            min_cost = float('inf')
            for j in open_lockers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, j]
                        min_cost = min(min_cost, transport_cost)

            if min_cost == float('inf'):
                # 客户无法被服务，使用惩罚成本
                current_customer_costs[i] = self.problem.penalty_cost_unassigned
            else:
                current_customer_costs[i] = min_cost

        # 计算新储物柜能为每个客户提供的服务成本
        for i in self.problem.customers:
            if (i, locker_j) in self.problem.distance:
                flight_distance = 2 * self.problem.distance[i, locker_j]
                if flight_distance <= self.problem.max_flight_distance:
                    new_transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, locker_j]

                    # 如果新储物柜能提供更好的服务，计算成本减少
                    if new_transport_cost < current_customer_costs[i]:
                        # 简化：假设客户需求按期望值分配
                        expected_demand = self.problem.expected_demand[i]
                        cost_reduction += (current_customer_costs[i] - new_transport_cost) * expected_demand

        return delta_cost - cost_reduction

    def regret_insertion(self, solution, iteration=0):
        """
        后悔值插入法（支持增量成本估算）
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 计算每个储物柜的插入成本和后悔值
        insertion_costs = {}

        if self.config['use_delta_evaluation']:
            # 使用增量成本估算
            for j in closed_lockers:
                costs = []
                for num_drones in range(1, 4):
                    try:
                        delta_cost = self._estimate_insertion_delta(solution, j, num_drones)
                        costs.append((delta_cost, num_drones))
                    except:
                        costs.append((float('inf'), num_drones))

                # 排序找到最好和第二好的插入位置
                costs.sort()
                if len(costs) >= 2:
                    best_cost, best_drones = costs[0]
                    second_best_cost, _ = costs[1]
                    regret = second_best_cost - best_cost
                    insertion_costs[j] = (regret, best_cost, best_drones)
                elif len(costs) == 1:
                    best_cost, best_drones = costs[0]
                    insertion_costs[j] = (0, best_cost, best_drones)
        else:
            # 使用快速启发式评估
            current_obj = self._calculate_objective_heuristic(solution, iteration)

            for j in closed_lockers:
                costs = []
                for num_drones in range(1, 4):
                    temp_solution = copy.deepcopy(solution)
                    temp_solution['y'][j] = 1
                    temp_solution['n'][j] = num_drones

                    try:
                        temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                        cost = temp_obj - current_obj
                        costs.append((cost, num_drones))
                    except:
                        costs.append((float('inf'), num_drones))

                # 排序找到最好和第二好的插入位置
                costs.sort()
                if len(costs) >= 2:
                    best_cost, best_drones = costs[0]
                    second_best_cost, _ = costs[1]
                    regret = second_best_cost - best_cost
                    insertion_costs[j] = (regret, best_cost, best_drones)
                elif len(costs) == 1:
                    best_cost, best_drones = costs[0]
                    insertion_costs[j] = (0, best_cost, best_drones)

        # 选择后悔值最大的储物柜插入
        if insertion_costs:
            best_locker = max(insertion_costs.keys(),
                            key=lambda x: insertion_costs[x][0])
            _, _, best_drones = insertion_costs[best_locker]

            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

        return new_solution

    def drone_optimization(self, solution, iteration=0):
        """
        智能无人机配置优化：基于需求估算合理的无人机数量
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if not open_lockers:
            return new_solution

        # 对每个开放的储物柜智能优化无人机数量
        for j in open_lockers:
            # 1. 估算该储物柜的服务需求
            estimated_demand = self._estimate_locker_demand(j, solution)

            # 2. 基于需求计算推荐的无人机数量
            recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

            # 3. 在推荐值附近搜索最优配置
            best_drones = solution['n'][j]
            best_obj = self._calculate_objective_heuristic(solution, iteration)

            # 搜索范围：推荐值 ± 2，但至少包含1-5的基本范围
            search_range = set(range(1, 6))  # 基本范围
            search_range.update(range(max(1, recommended_drones - 2), recommended_drones + 3))  # 推荐值附近
            search_range = sorted(search_range)

            for num_drones in search_range:
                if num_drones == solution['n'][j]:
                    continue

                temp_solution = copy.deepcopy(solution)
                temp_solution['n'][j] = num_drones

                try:
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_obj = temp_obj
                        best_drones = num_drones
                except:
                    continue

            new_solution['n'][j] = best_drones

        return new_solution

    def _estimate_locker_demand(self, locker_j, solution):
        """
        估算储物柜j的服务需求量
        """
        # 获取该储物柜可以服务的客户
        if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
            # 使用预计算的可达性数据
            reachable_customers = []
            for i in self.problem.customers:
                if locker_j in self.problem.fast_solver.reachable_lockers.get(i, []):
                    reachable_customers.append(i)
        else:
            # 回退到距离计算
            reachable_customers = []
            for i in self.problem.customers:
                if (i, locker_j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, locker_j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers.append(i)

        if not reachable_customers:
            return 0

        # 估算该储物柜的潜在服务需求
        # 考虑与其他储物柜的竞争，使用保守估算
        selected_lockers = {j for j, val in solution['y'].items() if val > 0.5}
        total_potential_demand = sum(self.problem.expected_demand[i] for i in reachable_customers)

        # 计算竞争因子：该储物柜在所有可达储物柜中的"吸引力"
        competition_factor = 1.0
        if len(selected_lockers) > 1:
            # 简化的竞争模型：假设需求在可达储物柜间均匀分配
            avg_competitors = 0
            for i in reachable_customers:
                if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                    competitors = len([j for j in self.problem.fast_solver.reachable_lockers.get(i, []) if j in selected_lockers])
                else:
                    competitors = len([j for j in selected_lockers
                                     if (i, j) in self.problem.distance and
                                     2 * self.problem.distance[i, j] <= self.problem.max_flight_distance])
                avg_competitors += max(1, competitors)

            competition_factor = len(reachable_customers) / max(1, avg_competitors)

        estimated_demand = total_potential_demand * competition_factor
        return max(0, estimated_demand)

    def _calculate_recommended_drones(self, locker_j, estimated_demand):
        """
        基于估算需求计算推荐的无人机数量（优化版：倾向于较少无人机）
        """
        if estimated_demand <= 1e-6:
            return 1  # 至少1架

        # 计算平均服务时间
        if hasattr(self.problem, 'fast_solver') and hasattr(self.problem.fast_solver, 'locker_avg_service_time'):
            avg_service_time = self.problem.fast_solver.locker_avg_service_time.get(locker_j, 0.5)
        else:
            # 回退到简化估算
            avg_service_time = 0.5  # 假设平均服务时间为0.5小时

        if avg_service_time <= 0:
            return 1

        # 计算所需的总工作时间
        total_work_hours = estimated_demand * avg_service_time

        # 计算所需的无人机数量（不添加缓冲，使用精确计算）
        required_drones = total_work_hours / self.problem.H_drone_working_hours_per_day

        # 使用更保守的策略：只有在明显需要时才增加无人机
        if required_drones <= 1.0:
            recommended_drones = 1
        elif required_drones <= 1.8:  # 给1架无人机更多机会
            recommended_drones = 1  # 优先尝试1架
        else:
            recommended_drones = math.ceil(required_drones)

        # 限制在合理范围内，但优先较少的配置
        return max(1, min(recommended_drones, 4))  # 降低最大值从8到4

# ---------------------------------------------------------------------------
# 快速客户分配求解器（第二阶段子问题辅助工具）
# ---------------------------------------------------------------------------
class FastAssignmentSolver:
    """
    快速客户分配求解器

    这不是一个独立的ALNS算法，而是ALNS_Solver的辅助工具，
    专门用于快速求解第二阶段客户分配子问题。

    当ALNS_Solver优化第一阶段决策(y, n)时，需要评估每个候选解
    在多个需求场景下的成本，这就需要快速求解大量的客户分配子问题。

    该类提供快速贪心启发式算法。
    """

    def __init__(self, problem_instance):
        self.problem = problem_instance

        # 预计算优化数据结构
        self._precompute_efficiency_data()
        self._precompute_drone_capacities()

        # 【新增】预计算Numba优化所需的数据结构
        if USE_NUMBA_OPTIMIZATION:
            self._precompute_numba_data()

        # 缓存相关
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0



    def _precompute_efficiency_data(self):
        """
        预计算客户-储物柜的效率信息，避免重复计算
        """
        self.customer_locker_efficiency = {}
        self.reachable_lockers = {}

        for i in self.problem.customers:
            self.reachable_lockers[i] = []
            self.customer_locker_efficiency[i] = {}

            for j in self.problem.sites:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        # 成本效率 = 1 / (运输成本 + 距离惩罚)
                        transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, j]
                        efficiency = 1.0 / (transport_cost + self.problem.distance[i, j])
                        self.customer_locker_efficiency[i][j] = efficiency
                        self.reachable_lockers[i].append(j)

            # 预排序：按效率从高到低
            self.reachable_lockers[i].sort(
                key=lambda j: self.customer_locker_efficiency[i][j],
                reverse=True
            )

    def _precompute_drone_capacities(self):
        """
        预计算每个储物柜的无人机运力相关数据
        """
        self.locker_avg_service_time = {}
        self.locker_reachable_customers = {}

        for j in self.problem.sites:
            total_distance = 0
            reachable_customers = 0
            self.locker_reachable_customers[j] = []

            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        total_distance += self.problem.distance[i, j]
                        reachable_customers += 1
                        self.locker_reachable_customers[j].append(i)

            if reachable_customers > 0:
                avg_distance = total_distance / reachable_customers
                self.locker_avg_service_time[j] = (2 * avg_distance / self.problem.drone_speed) + self.problem.loading_time
            else:
                self.locker_avg_service_time[j] = float('inf')

    def solve_assignment_heuristic(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        【改进版】使用基于后悔值的贪心启发式，快速求解客户分配问题

        核心思想：优先处理那些"选择最少"或"不选这个就亏大了"的客户，
        这正是"后悔值插入法"（Regret Insertion）的精髓。
        """
        # 快速检查：如果没有选中的储物柜，直接返回空分配
        if not selected_lockers:
            return {(i, j): 0.0 for i in self.problem.customers for j in self.problem.sites}

        # 【新增】Numba优化分支
        if USE_NUMBA_OPTIMIZATION and hasattr(self, 'distance_matrix'):
            return self._solve_with_numba(y_star, n_star, selected_lockers, demand_scenario)
        else:
            # 使用原始Python实现
            return self._solve_with_python(y_star, n_star, selected_lockers, demand_scenario)

    def _create_cache_key(self, selected_lockers, n_star, demand_scenario):
        """
        创建缓存键
        """
        # 创建一个基于输入参数的哈希键
        lockers_tuple = tuple(sorted(selected_lockers))
        drones_tuple = tuple(n_star.get(j, 0) for j in selected_lockers)
        demand_tuple = tuple(round(demand_scenario.get(i, 0), 2) for i in sorted(self.problem.customers))
        return (lockers_tuple, drones_tuple, demand_tuple)

    def _get_drone_capacity_fast(self, locker_j, num_drones):
        """
        快速计算储物柜j的无人机运力（使用预计算数据）
        """
        if num_drones <= 0:
            return 0

        avg_service_time = self.locker_avg_service_time.get(locker_j, float('inf'))
        if avg_service_time == float('inf'):
            return 0

        # 每架无人机的日运力 - 【修复】确保返回整数
        single_drone_capacity = self.problem.H_drone_working_hours_per_day / avg_service_time
        return math.floor(num_drones * single_drone_capacity)

    def _precompute_numba_data(self):
        """
        预计算Numba优化所需的数据结构 - 内存优化版本
        """
        if not NUMBA_AVAILABLE or not ENABLE_MEMORY_OPTIMIZATION:
            return

        # 客户和储物柜数量
        self.num_customers = len(self.problem.customers)
        self.num_sites = len(self.problem.sites)

        # 【内存优化】检查问题规模，大规模问题禁用Numba
        total_elements = self.num_customers * self.num_sites
        if total_elements > 50000:  # 超过5万个元素时禁用
            print(f"  ⚠️ 问题规模过大({self.num_customers}×{self.num_sites}={total_elements})，禁用Numba优化以节省内存")
            self.numba_disabled = True
            return

        self.numba_disabled = False

        # 客户ID到索引的映射
        self.customer_to_idx = {cust_id: idx for idx, cust_id in enumerate(self.problem.customers)}
        self.site_to_idx = {site_id: idx for idx, site_id in enumerate(self.problem.sites)}

        # 【内存优化】使用稀疏存储距离矩阵
        self.sparse_distance_data = {}
        self.reachable_pairs = []

        for (i, j), dist in self.problem.distance.items():
            if i in self.customer_to_idx and j in self.site_to_idx:
                cust_idx = self.customer_to_idx[i]
                site_idx = self.site_to_idx[j]
                self.sparse_distance_data[(cust_idx, site_idx)] = dist
                self.reachable_pairs.append((cust_idx, site_idx))

        # 【内存优化】只在需要时创建完整矩阵
        self.distance_matrix = None
        self.reachability_matrix = None

        # 储物柜容量数组
        self.locker_capacities = np.array([
            self.problem.Q_locker_capacity.get(site_id, 0.0)
            for site_id in self.problem.sites
        ], dtype=np.float64)

        print(f"  ✅ Numba数据结构预计算完成(稀疏模式): {self.num_customers}客户 × {self.num_sites}站点, {len(self.sparse_distance_data)}个有效距离")

    def _solve_with_numba(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        使用Numba优化的高性能分配算法 - 内存优化版本
        """
        if not NUMBA_AVAILABLE or getattr(self, 'numba_disabled', False):
            # 回退到原始算法
            return self._solve_with_python(y_star, n_star, selected_lockers, demand_scenario)

        # 【内存优化】动态创建所需的矩阵，用完即释放
        try:
            # 1. 客户需求数组
            customer_demands = np.zeros(self.num_customers, dtype=np.float64)
            for cust_id, demand in demand_scenario.items():
                if cust_id in self.customer_to_idx:
                    cust_idx = self.customer_to_idx[cust_id]
                    customer_demands[cust_idx] = demand

            # 2. 选中储物柜的容量数组
            selected_locker_capacities = np.zeros(self.num_sites, dtype=np.float64)
            selected_drone_capacities = np.zeros(self.num_sites, dtype=np.float64)

            for site_id in self.problem.sites:
                site_idx = self.site_to_idx[site_id]
                if site_id in selected_lockers:
                    # 储物柜容量
                    selected_locker_capacities[site_idx] = self.problem.Q_locker_capacity.get(site_id, 0.0)
                    # 无人机容量
                    num_drones = n_star.get(site_id, 0)
                    selected_drone_capacities[site_idx] = self._get_drone_capacity_fast(site_id, num_drones)

            # 3. 【内存优化】动态创建距离和可达性矩阵
            distance_matrix = self._create_distance_matrix_on_demand()
            reachability_matrix = self._create_reachability_matrix_on_demand()

            # 4. 调用Numba优化算法
            assignment_matrix = numba_regret_assignment(
                customer_demands=customer_demands,
                locker_capacities=selected_locker_capacities,
                drone_capacities=selected_drone_capacities,
                distance_matrix=distance_matrix,
                transport_cost=self.problem.transport_unit_cost,
                customer_to_lockers=reachability_matrix,
                num_customers=self.num_customers,
                num_lockers=self.num_sites
            )

            # 5. 转换回字典格式
            result = {}
            for i in self.problem.customers:
                for j in selected_lockers:
                    if i in self.customer_to_idx and j in self.site_to_idx:
                        cust_idx = self.customer_to_idx[i]
                        site_idx = self.site_to_idx[j]
                        result[(i, j)] = assignment_matrix[cust_idx, site_idx]
                    else:
                        result[(i, j)] = 0.0

            # 6. 【内存优化】立即释放大型数组
            del distance_matrix, reachability_matrix, assignment_matrix
            del customer_demands, selected_locker_capacities, selected_drone_capacities

            return result

        except MemoryError:
            print("  ⚠️ Numba算法内存不足，回退到Python实现")
            return self._solve_with_python(y_star, n_star, selected_lockers, demand_scenario)

    def _create_distance_matrix_on_demand(self):
        """
        【内存优化】按需创建距离矩阵
        """
        if hasattr(self, 'distance_matrix') and self.distance_matrix is not None:
            return self.distance_matrix

        distance_matrix = np.full((self.num_customers, self.num_sites), 1e9, dtype=np.float64)
        for (cust_idx, site_idx), dist in self.sparse_distance_data.items():
            distance_matrix[cust_idx, site_idx] = dist
        return distance_matrix

    def _create_reachability_matrix_on_demand(self):
        """
        【内存优化】按需创建可达性矩阵
        """
        if hasattr(self, 'reachability_matrix') and self.reachability_matrix is not None:
            return self.reachability_matrix

        reachability_matrix = np.zeros((self.num_customers, self.num_sites), dtype=np.int32)
        for cust_idx, site_idx in self.reachable_pairs:
            reachability_matrix[cust_idx, site_idx] = 1
        return reachability_matrix

    def _solve_with_python(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        原始Python实现（作为Numba的回退方案）
        """
        # 缓存检查
        if hasattr(self.problem, 'use_assignment_cache') and self.problem.use_assignment_cache:
            cache_key = self._create_cache_key(selected_lockers, n_star, demand_scenario)
            if cache_key in self.cache:
                self.cache_hits += 1
                return self.cache[cache_key].copy()
            self.cache_misses += 1

        selected_lockers_set = set(selected_lockers)
        assignment = defaultdict(float)

        # 预计算储物柜的【当前剩余】容量和无人机运力
        rem_locker_caps = {j: self.problem.Q_locker_capacity.get(j, 0) for j in selected_lockers}
        rem_drone_caps = {j: self._get_drone_capacity_fast(j, n_star.get(j, 0)) for j in selected_lockers}

        # 【简化】移除容量调试输出

        # 找出所有需要分配的客户及其需求
        unassigned_demands = {i: demand for i, demand in demand_scenario.items() if demand > 1e-6}

        # 主循环：直到所有客户需求都被分配或无法再分配
        # 【改进】大幅增加最大迭代次数，支持需求拆分的充分分配
        max_iterations = min(500, len(unassigned_demands) * 10)  # 从50增加到500，从3倍增加到10倍
        iteration_count = 0
        consecutive_no_progress = 0

        while unassigned_demands and iteration_count < max_iterations:
            iteration_count += 1
            customer_regrets = {}

            # 1. 为每个【未完全分配的】客户计算"后悔值"
            for i, demand in unassigned_demands.items():
                costs = []
                # 找到服务该客户成本最低和次低的两个储物柜选项
                for j in self.reachable_lockers[i]:
                    if j in selected_lockers_set:
                        # 检查是否有剩余容量
                        available_cap = min(rem_locker_caps.get(j, 0), rem_drone_caps.get(j, 0))
                        if available_cap > 1e-6:
                            cost = 2 * self.problem.transport_unit_cost * self.problem.distance.get((i, j), float('inf'))
                            costs.append({'cost': cost, 'locker': j})

                if not costs:
                    continue  # 该客户已无法被服务

                costs.sort(key=lambda x: x['cost'])

                best_cost = costs[0]['cost']
                # 如果只有一个选项，后悔值设为无穷大，必须优先处理
                second_best_cost = costs[1]['cost'] if len(costs) > 1 else float('inf')

                regret_value = second_best_cost - best_cost
                customer_regrets[i] = {'regret': regret_value, 'best_option': costs[0]}

            if not customer_regrets:
                consecutive_no_progress += 1
                if consecutive_no_progress >= 3:  # 连续3次无进展就尝试激进分配
                    # 【新增】激进分配策略：直接按需求量排序分配
                    if self._try_aggressive_assignment(unassigned_demands, selected_lockers, rem_locker_caps, rem_drone_caps, assignment):
                        consecutive_no_progress = 0  # 重置计数器
                        continue
                    else:
                        break  # 激进分配也无效，退出
                continue
            else:
                consecutive_no_progress = 0  # 重置无进展计数器

            # 2. 选择后悔值最大的客户进行分配
            customer_to_assign = max(customer_regrets, key=lambda i: customer_regrets[i]['regret'])

            # 3. 将该客户分配给其最优选择
            best_option = customer_regrets[customer_to_assign]['best_option']
            best_locker = best_option['locker']
            demand_to_assign = unassigned_demands[customer_to_assign]

            # 计算实际可分配量
            available_cap = min(rem_locker_caps.get(best_locker, 0), rem_drone_caps.get(best_locker, 0))
            assigned_amount = min(demand_to_assign, available_cap)

            if assigned_amount > 0.01:  # 降低最小阈值，允许更小的分配
                assignment[(customer_to_assign, best_locker)] += assigned_amount

                # 更新储物柜剩余容量
                rem_locker_caps[best_locker] -= assigned_amount
                rem_drone_caps[best_locker] -= assigned_amount

                # 更新客户剩余需求
                unassigned_demands[customer_to_assign] -= assigned_amount
                if unassigned_demands[customer_to_assign] < 0.01:
                    del unassigned_demands[customer_to_assign]
            else:
                # 如果最优选择都无法分配，则认为该客户暂时无法分配
                del unassigned_demands[customer_to_assign]

        # 转换为标准格式（包含所有客户-储物柜对）
        result = {}
        for i in self.problem.customers:
            for j in selected_lockers:
                result[(i, j)] = assignment.get((i, j), 0.0)

        # 【优化】检查是否因为达到最大迭代次数而退出
        if iteration_count >= max_iterations and unassigned_demands:
            unassigned_total = sum(unassigned_demands.values())
            if unassigned_total > 1.0:  # 只有当未分配需求大于1.0时才警告
                # 计算总需求和未分配比例
                total_demand = sum(demand_scenario.values())
                unassigned_percent = (unassigned_total / total_demand) * 100 if total_demand > 0 else 0

                # 只有当未分配比例超过5%时才输出警告
                if unassigned_percent > 5.0:
                    # 【新增】使用警告抑制机制（如果ALNS实例可用）
                    if hasattr(self, 'problem') and hasattr(self.problem, 'alns_instance') and hasattr(self.problem.alns_instance, 'warning_counts'):
                        warning_key = f"python_assignment_max_iter_{unassigned_percent:.0f}%"
                        alns = self.problem.alns_instance
                        if warning_key not in alns.warning_counts:
                            alns.warning_counts[warning_key] = 0

                        if alns.warning_counts[warning_key] < alns.max_warnings_per_type:
                            alns.warning_counts[warning_key] += 1
                            remaining_warnings = alns.max_warnings_per_type - alns.warning_counts[warning_key]
                            if remaining_warnings > 0:
                                print(f"    [警告] Python分配算法达到最大迭代次数 {max_iterations}，未分配需求: {unassigned_total:.1f}/{total_demand:.1f} ({unassigned_percent:.1f}%) [还将显示{remaining_warnings}次]")
                            else:
                                print(f"    [警告] Python分配算法达到最大迭代次数 {max_iterations}，未分配需求: {unassigned_total:.1f}/{total_demand:.1f} ({unassigned_percent:.1f}%) [后续同类警告将被抑制]")
                    else:
                        print(f"    [警告] Python分配算法达到最大迭代次数 {max_iterations}，未分配需求: {unassigned_total:.1f}/{total_demand:.1f} ({unassigned_percent:.1f}%)")

        # 【内存优化】智能缓存管理
        if hasattr(self.problem, 'use_assignment_cache') and self.problem.use_assignment_cache:
            if 'cache_key' in locals():
                self.cache[cache_key] = result.copy()
                # 【内存优化】更积极的缓存大小限制
                if len(self.cache) > MAX_CACHE_SIZE:
                    # 删除最旧的一半缓存
                    keys_to_remove = list(self.cache.keys())[:MAX_CACHE_SIZE//2]
                    for key in keys_to_remove:
                        del self.cache[key]
                    # 强制垃圾回收
                    import gc
                    gc.collect()

        return result

    def _try_aggressive_assignment(self, unassigned_demands, selected_lockers, rem_locker_caps, rem_drone_caps, assignment):
        """
        激进分配策略：当后悔值方法效率低下时使用

        策略：
        1. 按需求量从大到小排序客户
        2. 为每个客户找到最近的有容量的储物柜
        3. 尽可能多地分配需求

        返回：是否成功分配了任何需求
        """
        initial_unassigned_total = sum(unassigned_demands.values())
        if initial_unassigned_total <= 1e-6:
            return False

        # 按需求量从大到小排序
        sorted_customers = sorted(unassigned_demands.items(), key=lambda x: x[1], reverse=True)

        assigned_any = False
        for customer, demand in sorted_customers:
            if demand <= 1e-6:
                continue

            # 找到所有可达的储物柜，按距离排序
            reachable_lockers = []
            for j in selected_lockers:
                if j in self.reachable_lockers.get(customer, []):
                    available_cap = min(rem_locker_caps.get(j, 0), rem_drone_caps.get(j, 0))
                    if available_cap > 1e-6:
                        distance = self.problem.distance.get((customer, j), float('inf'))
                        reachable_lockers.append((j, available_cap, distance))

            # 按距离排序
            reachable_lockers.sort(key=lambda x: x[2])

            # 尽可能多地分配给最近的储物柜
            remaining_demand = demand
            for locker, available_cap, distance in reachable_lockers:
                if remaining_demand <= 1e-6:
                    break

                assign_amount = min(remaining_demand, available_cap)
                if assign_amount > 0.01:
                    assignment[(customer, locker)] += assign_amount
                    rem_locker_caps[locker] -= assign_amount
                    rem_drone_caps[locker] -= assign_amount
                    remaining_demand -= assign_amount
                    assigned_any = True

            # 更新未分配需求
            if remaining_demand < 0.01:
                del unassigned_demands[customer]
            else:
                unassigned_demands[customer] = remaining_demand

        return assigned_any

# ---------------------------------------------------------------------------
# StochasticDroneDeliveryOptimizerSAA 类的定义
# ---------------------------------------------------------------------------
class StochasticDroneDeliveryOptimizerSAA:

    def __init__(self):
        self.customers = []
        self.sites = []
        self.expected_demand = {}
        self.distance = {}
        self.locker_fixed_cost = {}
        self.Q_locker_capacity = {}
        self.drone_speed = None
        self.loading_time = None
        self.max_flight_distance = None
        self.transport_unit_cost = None
        self.drone_cost = None
        self.H_drone_working_hours_per_day = None
        self.penalty_cost_unassigned = None
        self.service_time_per_unit = None  # 添加缺失的属性
        self.customer_coords = {}
        self.site_coords = {}
        self.depot_coord = None
        self.truck_capacity = None
        self.truck_fixed_cost = None
        self.truck_km_cost = None
        self.truck_distances = {}  # 卡车距离矩阵
        self.BIG_M = 1e6  # 用于Big-M约束的大数

        # SAA 相关结果存储
        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = [] # 存储每次复制在K'个样本上的平均卡车成本
        self.best_solution_validation_costs = []  # 存储最佳解在每个验证场景上的成本

        # 【内存优化】批量求解缓存 - 限制大小
        self.batch_solve_cache = {}
        self.batch_cache_hits = 0
        self.batch_cache_misses = 0
        self.max_batch_cache_size = MAX_CACHE_SIZE // 2  # 批量缓存使用一半的缓存空间

        # 第二阶段求解器性能统计
        self.second_stage_solver_stats = {
            'total_scenarios': 0,
            'exact_solver_success': 0,
            'exact_solver_failure': 0,
            'heuristic_solver_used': 0,
            'avg_exact_solve_time': 0,
            'avg_heuristic_solve_time': 0,
            'exact_solve_times': [],
            'heuristic_solve_times': []
        }

        # DRL求解器实例 (每个优化器实例共享，如果参数不变)
        self._drl_solver = None
        self._drl_solver_params = None
        self._drl_solver_no_plots = None
        self._drl_solver_no_plots_params = None





        # 快速启发式求解器
        self.fast_solver = None
        self.use_assignment_cache = True  # 是否使用分配结果缓存
        self.assignment_cache = {}  # 缓存分配结果


    def set_parameters(self,
                       customers: List,
                       sites: List,
                       expected_demand: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        self.customers = customers
        self.sites = sites
        self.expected_demand = expected_demand
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned
        self.Q_locker_capacity = Q_locker_capacity
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        # 计算平均服务时间（用于容量约束）
        self._calculate_service_time_per_unit()

        # 计算卡车距离矩阵（仓库到储物柜，储物柜间距离）
        self._build_truck_distance_matrix()

        # 设置Big-M值（与saa_g_r.py一致）
        if self.expected_demand:
            max_expected_demand_val = max(self.expected_demand.values()) if self.expected_demand else 0
            self.BIG_M = max_expected_demand_val * 2 if max_expected_demand_val > 0 else 1e6
        else:
            self.BIG_M = 1e6

    def _calculate_service_time_per_unit(self):
        """
        计算平均单位服务时间，用于无人机容量约束
        基于所有客户-储物柜对的平均距离计算
        """
        if not self.distance or not self.drone_speed or self.loading_time is None:
            # 如果缺少必要参数，使用默认值
            self.service_time_per_unit = 0.5  # 默认0.5小时/单位
            return

        total_service_time = 0
        count = 0

        # 计算所有可达客户-储物柜对的平均服务时间
        for (i, j), distance in self.distance.items():
            if i in self.customers and j in self.sites:
                flight_distance = 2 * distance  # 往返距离
                if flight_distance <= self.max_flight_distance:
                    service_time = (flight_distance / self.drone_speed) + self.loading_time
                    total_service_time += service_time
                    count += 1

        if count > 0:
            self.service_time_per_unit = total_service_time / count
        else:
            # 如果没有可达的客户-储物柜对，使用基于平均距离的估算
            if self.distance:
                avg_distance = sum(self.distance.values()) / len(self.distance)
                self.service_time_per_unit = (2 * avg_distance / self.drone_speed) + self.loading_time
            else:
                self.service_time_per_unit = 0.5  # 默认值

    def _build_truck_distance_matrix(self):
        """构建卡车距离矩阵，包括仓库到储物柜和储物柜间的距离"""
        self.truck_distances = {}

        if not self.depot_coord or not self.site_coords:
            return

        # 仓库到储物柜的距离
        for j in self.sites:
            if j in self.site_coords:
                dist = math.sqrt((self.depot_coord[0] - self.site_coords[j][0])**2 +
                               (self.depot_coord[1] - self.site_coords[j][1])**2)
                self.truck_distances[(0, j)] = dist
                self.truck_distances[(j, 0)] = dist

        # 储物柜间的距离
        for i in self.sites:
            for j in self.sites:
                if i != j and i in self.site_coords and j in self.site_coords:
                    dist = math.sqrt((self.site_coords[i][0] - self.site_coords[j][0])**2 +
                                   (self.site_coords[i][1] - self.site_coords[j][1])**2)
                    self.truck_distances[(i, j)] = dist

    def _get_drl_solver(self, make_plots: bool = True):
        if not DRL_AVAILABLE: return None # 如果DRL不可用，返回None

        current_params = (self.depot_coord, self.truck_capacity, self.truck_fixed_cost, self.truck_km_cost)
        if make_plots:
            if self._drl_solver is None or self._drl_solver_params != current_params:
                set_drl_log_level(logging.ERROR)
                self._drl_solver = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=True
                )
                self._drl_solver_params = current_params
            return self._drl_solver
        else:
            if self._drl_solver_no_plots is None or self._drl_solver_no_plots_params != current_params:
                set_drl_log_level(logging.ERROR)
                self._drl_solver_no_plots = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=False
                )
                self._drl_solver_no_plots_params = current_params
            return self._drl_solver_no_plots

    def calculate_truck_cost(self,
                             selected_lockers: List[int] = None,
                             x_qty_solution_values: Dict[Tuple[int, int], float] = None,
                             make_plots: bool = True,
                             return_route_info: bool = False,
                             active_lockers_info_override: Dict[int, Dict[str, Any]] = None):
        if not DRL_AVAILABLE or self.truck_capacity is None or self.truck_fixed_cost is None or self.truck_km_cost is None:
            # print("  [calculate_truck_cost] DRL不可用或卡车参数未设置，卡车成本返回0。")
            return (0.0, None) if return_route_info else 0.0

        # 如果提供了override参数，直接使用它
        if active_lockers_info_override is not None:
            if not active_lockers_info_override:
                return (0.0, None) if return_route_info else 0.0
            try:
                drl_solver = self._get_drl_solver(make_plots=make_plots)
                if drl_solver is None:
                    return (0.0, None) if return_route_info else 0.0

                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info_override, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            except Exception as e:
                # 使用简化估算
                total_demand = sum(info['demand'] for info in active_lockers_info_override.values())
                num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost

        # 原有逻辑：从selected_lockers和x_qty_solution_values构建active_lockers_info
        # 在修正的模型中，这个逻辑主要用于向后兼容
        if selected_lockers is None or not selected_lockers:
            return (0.0, None) if return_route_info else 0.0

        if x_qty_solution_values is None:
            # 如果没有客户分配信息，返回0成本
            return (0.0, None) if return_route_info else 0.0

        try:
            drl_solver = self._get_drl_solver(make_plots=make_plots)
            if drl_solver is None: return (0.0, None) if return_route_info else 0.0 # Double check

            locker_total_demands = {locker_id: 0.0 for locker_id in selected_lockers}
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    locker_total_demands[locker_id] += quantity
            active_lockers_info = {}
            for locker_id in selected_lockers:
                demand_at_locker = locker_total_demands.get(locker_id, 0)
                if demand_at_locker > 1e-6:
                    if locker_id in self.site_coords:
                        active_lockers_info[locker_id] = {
                            'coord': self.site_coords[locker_id],
                            'demand': round(demand_at_locker)  # 四舍五入为整数
                        }
            if active_lockers_info:
                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            else:
                return (0.0, None) if return_route_info else 0.0
        except Exception as e:
            # print(f"  [calculate_truck_cost] DRL求解失败: {str(e)}, 使用简化估算")
            if x_qty_solution_values:
                total_demand_overall = 0
                for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                    if locker_id in selected_lockers and quantity > 1e-6:
                        total_demand_overall += quantity
                num_trucks = math.ceil(total_demand_overall / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost
            else:
                return (0.0, None) if return_route_info else 0.0

    def _generate_demand_samples(self, num_samples: int) -> List[Dict[int, float]]:
        """
        生成需求场景样本，客户需求服从泊松分布
        泊松分布的参数λ等于期望需求值，天然产生非负整数
        """
        demand_scenarios = []
        for _ in range(num_samples):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]  # 泊松分布参数λ
                # 使用泊松分布生成需求，λ既是均值也是方差
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)  # 转换为float以保持一致性
            demand_scenarios.append(current_scenario_demand)
        return demand_scenarios



    def calculate_objective(self, solution, demand_samples_k):
        """
        计算第一阶段解的目标函数值（修正版两阶段结构）

        现在解只包含第一阶段决策变量：
        - y: 储物柜选址
        - n: 无人机配置

        给定第一阶段决策，对每个需求场景求解第二阶段子问题，计算期望总成本。
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            return float('inf')  # 无效解

        # 1. 计算第一阶段成本
        first_stage_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers) + \
                          sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)

        # 2. 对每个需求场景求解第二阶段子问题，计算期望成本
        total_second_stage_costs = 0
        batch_active_lockers_info = []

        try:
            # 对每个需求场景求解第二阶段客户分配子问题
            for k_idx, demand_scenario in enumerate(demand_samples_k):
                # 求解第二阶段最优客户分配
                optimal_assignment = self._solve_optimal_assignment_for_scenario(
                    y_star, n_star, selected_lockers, demand_scenario
                )

                # 计算该场景下的第二阶段成本
                transport_cost_k = sum(2 * self.transport_unit_cost * self.distance[i, j] * quantity
                                      for (i, j), quantity in optimal_assignment.items()
                                      if (i, j) in self.distance)

                # 计算惩罚成本
                total_assigned = sum(optimal_assignment.values())
                total_demand = sum(demand_scenario.values())
                penalty_cost_k = self.penalty_cost_unassigned * max(0, total_demand - total_assigned)

                # 准备卡车成本计算数据
                locker_demands = {j: sum(optimal_assignment.get((i, j), 0) for i in self.customers)
                                for j in selected_lockers}
                active_info = {j: {'coord': self.site_coords[j], 'demand': round(demand)}
                              for j, demand in locker_demands.items() if demand > 1e-6}
                batch_active_lockers_info.append(active_info)

                # 累加第二阶段成本（不含卡车成本）
                total_second_stage_costs += (transport_cost_k + penalty_cost_k)

            # 批量计算卡车成本
            batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)
            avg_truck_cost = sum(batch_truck_costs) / len(batch_truck_costs) if batch_truck_costs else 0

            # 计算期望值
            avg_second_stage_cost = total_second_stage_costs / len(demand_samples_k)

            # 返回总期望成本
            return first_stage_cost + avg_second_stage_cost + avg_truck_cost

        except Exception as e:
            # 如果计算失败，返回一个很大的值
            print(f"    两阶段目标函数计算失败: {str(e)}")
            return float('inf')

    def solve_saa_with_alns(self, time_limit_per_replication: int = 300):
        """
        使用ALNS统一求解器求解SAA问题

        SAA方法论：
        1. 对每个复制m，生成K个需求样本
        2. 使用ALNS求解确定性的两阶段问题：
           - 第一阶段：储物柜选址和无人机配置（ALNS优化）
           - 第二阶段：客户分配（快速启发式求解）
        3. 在固定的K'个验证样本上评估解质量
        4. 计算统计下界和上界，检查收敛条件

        ALNS在这里是统一的求解器，不是分阶段的算法。
        """
        print(f"\n开始SAA优化（使用ALNS），最多 {SAA_MAX_REPLICATIONS_M} 次复制...")
        print(f"SAA终止条件 (必须同时满足):")
        print(f"  1. 相对差距阈值: Gap/UB ≤ {SAA_GAP_TOLERANCE_PERCENT*100:.0f}% 且 Gap ≥ 0")
        print(f"  2. 方差阈值: δ²_Gap/UB ≤ {SAA_VARIANCE_TOLERANCE_PERCENT*100:.0f}%")
        print(f"  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)")
        print(f"  最少需要 {SAA_MIN_REPLICATIONS_M} 次有效复制")

        # 【内存优化】流式验证样本生成 - 不预先生成所有样本
        print(f"准备流式生成 {SAA_SAMPLES_K_PRIME} 个验证样本 (分批处理以节省内存)...")

        # 保存随机种子状态用于一致性验证样本生成
        self.validation_random_seed = RANDOM_SEED + 1000
        print(f"  验证样本将使用种子 {self.validation_random_seed} 确保一致性")

        # 【内存优化】移除固定验证样本的调试信息，因为现在使用流式处理
        # 调试信息已移除以减少冗余输出

        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = []

        best_solution_info = {'y': None, 'n': None, 'avg_obj_k_prime': float('inf'), 'replication_idx': -1, 'truck_cost_k_prime': 0.0}

        log_memory_usage("SAA算法初始化完成")

        for m_rep in range(SAA_MAX_REPLICATIONS_M):
            print(f"\n--- SAA 复制 {m_rep + 1}/{SAA_MAX_REPLICATIONS_M} (使用ALNS) ---")
            demand_samples_for_k = self._generate_demand_samples(num_samples=SAA_SAMPLES_K)
            print(f"  已生成 {SAA_SAMPLES_K} 个需求场景用于求解。")

            # 创建并运行ALNS求解器
            print(f"  开始ALNS求解复制 {m_rep + 1}...")
            alns_solver = ALNS_Solver(problem_instance=self, demand_samples=demand_samples_for_k)

            solve_start_time_rep = time.time()
            best_first_stage_solution = alns_solver.solve(time_limit=time_limit_per_replication)
            solve_time_rep = time.time() - solve_start_time_rep

            if best_first_stage_solution is not None:
                # 获取目标值（下界估计）
                obj_val_k = self.calculate_objective(best_first_stage_solution, demand_samples_for_k)
                self.saa_objective_values_k.append(obj_val_k)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K} 个样本上的目标值: {obj_val_k:.2f}")

                y_star_m = best_first_stage_solution['y']
                n_star_m = best_first_stage_solution['n']

                # 详细的调试信息已移除以减少冗余输出

                self.saa_solutions_first_stage.append({
                    'y': y_star_m,
                    'n': n_star_m
                })

                print(f"  评估复制 {m_rep + 1} 的解在 {SAA_SAMPLES_K_PRIME} 个验证样本上的性能...")
                eval_start_time = time.time()
                self._current_replication = m_rep + 1  # 设置当前复制编号用于分析
                # 传递完整的第一阶段解
                first_stage_solution = self.saa_solutions_first_stage[-1]
                # 【内存优化】使用流式评估替代固定样本
                avg_obj_k_prime, avg_truck_cost_for_k_prime_eval, scenario_costs = self._evaluate_solution_streaming(first_stage_solution, SAA_SAMPLES_K_PRIME)
                eval_time = time.time() - eval_start_time
                self.saa_upper_bounds_k_prime.append(avg_obj_k_prime)
                self.saa_truck_costs_k_prime.append(avg_truck_cost_for_k_prime_eval)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均目标值 (UB估计): {avg_obj_k_prime:.2f}")
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均卡车成本: {avg_truck_cost_for_k_prime_eval:.2f}")
                print(f"  时间分析: ALNS求解 {solve_time_rep:.2f}s, 验证评估 {eval_time:.2f}s, 总计 {solve_time_rep + eval_time:.2f}s")

                if avg_obj_k_prime < best_solution_info['avg_obj_k_prime']:
                    best_solution_info.update(first_stage_solution)  # 保存完整的第一阶段解
                    best_solution_info['avg_obj_k_prime'] = avg_obj_k_prime
                    best_solution_info['replication_idx'] = m_rep
                    best_solution_info['truck_cost_k_prime'] = avg_truck_cost_for_k_prime_eval
                    # 记录最佳解在每个验证场景上的成本
                    self.best_solution_validation_costs = scenario_costs
            else:
                print(f"  复制 {m_rep + 1} ALNS未能找到可行解。")
                self.saa_objective_values_k.append(float('inf'))
                self.saa_upper_bounds_k_prime.append(float('inf'))
                self.saa_truck_costs_k_prime.append(float('inf'))
                self.saa_solutions_first_stage.append(None)

            # 检查SAA终止条件 (从第2次复制开始检查)
            if m_rep + 1 >= SAA_MIN_REPLICATIONS_M:
                should_terminate, gap_info = self._check_saa_termination_criteria()
                if should_terminate:
                    print(f"\n✓ SAA终止条件满足，在第 {m_rep + 1} 次复制后停止")
                    print(f"  {gap_info}")
                    break
                else:
                    print(f"  当前SAA状态: {gap_info}")
            else:
                # 即使在最小复制次数之前，也显示当前状态（如果有足够的数据）
                if len([s for s in self.saa_solutions_first_stage if s is not None]) >= 2:
                    _, gap_info = self._check_saa_termination_criteria()
                    print(f"  当前SAA状态: {gap_info}")

        # 处理结果（与原来的solve_saa方法相同的逻辑）
        if not any(s is not None for s in self.saa_solutions_first_stage):
            print("\nSAA 未能产生任何有效解。")
            return None

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 正确的SAA下界：M次复制的训练目标值的平均
        statistical_lower_bound = np.mean(valid_obj_k) if valid_obj_k else float('inf')
        std_lower_bound = np.std(valid_obj_k) if valid_obj_k else float('inf')

        # 正确的SAA上界：最佳解在验证样本上的成本（不是平均）
        statistical_upper_bound = best_solution_info['avg_obj_k_prime'] if best_solution_info['y'] is not None else float('inf')

        # 所有复制验证成本的统计信息（仅用于分析）
        avg_all_validation_costs = np.mean(valid_ub_k_prime) if valid_ub_k_prime else float('inf')
        std_all_validation_costs = np.std(valid_ub_k_prime) if valid_ub_k_prime else float('inf')

        actual_replications = len([s for s in self.saa_solutions_first_stage if s is not None])
        print(f"\n📊 SAA 统计结果汇总 ({actual_replications} 次有效复制，使用ALNS)")
        print(f"=" * 60)
        print(f"  下界估计 cost_N^m: {statistical_lower_bound:.2f} 元/天")
        print(f"    ↳ 计算方法: 前{actual_replications}次复制的小样本优化成本的算术平均")
        print(f"    ↳ 含义: 系统真实期望成本的下界估计")
        print(f"    - 下界标准差: {std_lower_bound:.2f}")
        print(f"    - 下界方差 δ²(cost_N): {std_lower_bound**2/(actual_replications*(actual_replications-1)) if actual_replications > 1 else 0:.4f}")
        print(f"  上界估计 cost_{SAA_SAMPLES_K_PRIME}(ŝ): {statistical_upper_bound:.2f} 元/天")
        print(f"    ↳ 计算方法: 最佳解在{SAA_SAMPLES_K_PRIME}个大样本场景下的平均成本")
        print(f"    ↳ 含义: 最佳方案长期运营的期望日均成本")
        print(f"  SAA Gap: {statistical_upper_bound - statistical_lower_bound:.2f} 元/天 ({((statistical_upper_bound - statistical_lower_bound)/statistical_upper_bound*100):.1f}%)")
        print(f"  所有复制验证成本统计: {avg_all_validation_costs:.2f} ± {std_all_validation_costs:.2f} 元/天")

        if best_solution_info.get('y') is not None:
            # 计算最佳解的成本分解信息
            selected_lockers = [j for j, val in best_solution_info['y'].items() if val > 0.5]
            locker_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers)
            drone_cost = sum(self.drone_cost * best_solution_info['n'].get(j, 0) for j in selected_lockers)
            truck_cost = best_solution_info.get('truck_cost_k_prime', 0)
            total_cost = best_solution_info['avg_obj_k_prime']

            print(f"\n🏆 最佳解详情 (来自复制 {best_solution_info['replication_idx'] + 1})")
            print(f"  总成本: {total_cost:.2f} 元/天")
            print(f"  开放储物柜: {len(selected_lockers)} 个")

            # 计算正确的成本分解 - 进行实际的成本分解计算
            other_costs_temp = total_cost - locker_cost - drone_cost - truck_cost

            # 【内存优化】实际计算无人机运输成本和惩罚成本
            drone_transport_temp = 0
            penalty_temp = 0

            # 使用流式方法计算成本分解
            sample_count = min(100, SAA_SAMPLES_K_PRIME)  # 使用较少样本进行快速估算
            total_transport = 0
            total_penalty = 0

            # 生成少量样本进行成本分解
            cost_breakdown_samples = self._generate_validation_samples_batch(0, sample_count)

            for scenario in cost_breakdown_samples:
                # 求解该场景的客户分配
                assignment = self.fast_solver.solve_assignment_heuristic(
                    best_solution_info['y'], best_solution_info['n'], selected_lockers, scenario
                )

                # 计算该场景的成本
                scenario_transport = 0
                scenario_penalty = 0

                for i in self.customers:
                    actual_demand = scenario[i]
                    total_assigned = sum(assignment.get((i, j), 0) for j in selected_lockers)
                    shortage = max(0, actual_demand - total_assigned)
                    scenario_penalty += self.penalty_cost_unassigned * shortage

                    # 计算无人机运输成本
                    for j in selected_lockers:
                        assigned_qty = assignment.get((i, j), 0)
                        if assigned_qty > 1e-6 and (i, j) in self.distance:
                            scenario_transport += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

                total_transport += scenario_transport
                total_penalty += scenario_penalty

            drone_transport_temp = total_transport / sample_count
            penalty_temp = total_penalty / sample_count

            # 立即释放样本数据
            del cost_breakdown_samples

            drone_total_temp = drone_cost + drone_transport_temp
            print(f"  成本构成: 储物柜 {locker_cost:.2f} + 无人机(部署+运输) {drone_total_temp:.2f} + 卡车(固定+运输) {truck_cost:.2f} + 惩罚 {penalty_temp:.2f}")

            # ==========================================================
            # 【智能最终评估】根据当前配置决定是否需要额外的精确评估
            # ==========================================================
            global USE_EXACT_SECOND_STAGE_SOLVER  # 声明全局变量

            if USE_EXACT_SECOND_STAGE_SOLVER == "adaptive":
                print("\n[最终验证] 验证自适应求解器的精确性...")

                # 临时强制使用精确求解器进行验证
                original_solver_setting = USE_EXACT_SECOND_STAGE_SOLVER
                USE_EXACT_SECOND_STAGE_SOLVER = True

                try:
                    # 【内存优化】进行强制精确评估 - 使用流式处理
                    precise_avg_obj, precise_truck_cost, _ = self._evaluate_solution_streaming(
                        best_solution_info, SAA_SAMPLES_K_PRIME
                    )

                    # 计算与验证阶段评估的差异
                    validation_obj = best_solution_info['avg_obj_k_prime']
                    difference = abs(precise_avg_obj - validation_obj)
                    relative_diff = difference / validation_obj * 100

                    print(f"[最终验证] 强制精确评估: {precise_avg_obj:.2f}")
                    print(f"[最终验证] 验证阶段评估: {validation_obj:.2f}")
                    print(f"[最终验证] 评估差异: {difference:.2f} ({relative_diff:.2f}%)")

                    if relative_diff < 1.0:  # 差异小于1%
                        print(f"[最终验证] ✓ 自适应求解器工作正常，但使用更精确的强制精确评估结果")
                        final_solution_saa = {
                            'objective_value_k_prime_estimate': precise_avg_obj,  # 使用更精确的结果
                            'selected_lockers_y': best_solution_info['y'],
                            'drone_allocations_n': best_solution_info['n'],
                            'truck_cost_k_prime_estimate': precise_truck_cost,
                            'precise_evaluation_used': True,  # 标记使用了精确评估
                        }
                    else:
                        print(f"[最终验证] ⚠ 发现评估差异较大，使用强制精确评估结果")
                        final_solution_saa = {
                            'objective_value_k_prime_estimate': precise_avg_obj,
                            'selected_lockers_y': best_solution_info['y'],
                            'drone_allocations_n': best_solution_info['n'],
                            'truck_cost_k_prime_estimate': precise_truck_cost,
                            'precise_evaluation_used': True,  # 标记使用了精确评估
                        }

                except Exception as e:
                    print(f"[最终验证] 强制精确评估失败: {e}")
                    print("[最终验证] 使用验证阶段结果作为最终结果")
                    final_solution_saa = {
                        'objective_value_k_prime_estimate': best_solution_info['avg_obj_k_prime'],
                        'selected_lockers_y': best_solution_info['y'],
                        'drone_allocations_n': best_solution_info['n'],
                        'truck_cost_k_prime_estimate': best_solution_info['truck_cost_k_prime'],
                    }
                finally:
                    # 恢复全局开关
                    USE_EXACT_SECOND_STAGE_SOLVER = original_solver_setting
            else:
                # 非自适应模式，在最后进行一次精确评估以获得准确结果
                print(f"\n[最终验证] 当前求解器模式: {USE_EXACT_SECOND_STAGE_SOLVER}")
                print(f"[最终验证] 进行一次精确评估以获得准确的最终目标值...")

                # 临时启用精确求解器进行最终评估
                original_solver_setting = USE_EXACT_SECOND_STAGE_SOLVER
                USE_EXACT_SECOND_STAGE_SOLVER = True

                try:
                    # 【内存优化】进行精确的最终评估 - 使用流式处理
                    precise_avg_obj, precise_truck_cost, _ = self._evaluate_solution_streaming(
                        best_solution_info, SAA_SAMPLES_K_PRIME
                    )

                    # 显示评估对比
                    heuristic_obj = best_solution_info['avg_obj_k_prime']
                    difference = abs(precise_avg_obj - heuristic_obj)
                    relative_diff = difference / precise_avg_obj * 100

                    print(f"[最终验证] 启发式评估: {heuristic_obj:.2f}")
                    print(f"[最终验证] 精确评估: {precise_avg_obj:.2f}")
                    print(f"[最终验证] 评估差异: {difference:.2f} ({relative_diff:.2f}%)")

                    # 使用精确评估的结果作为最终结果
                    final_solution_saa = {
                        'objective_value_k_prime_estimate': precise_avg_obj,
                        'selected_lockers_y': best_solution_info['y'],
                        'drone_allocations_n': best_solution_info['n'],
                        'truck_cost_k_prime_estimate': precise_truck_cost,
                        'precise_evaluation_used': True,  # 标记使用了精确评估
                    }

                    print(f"\n[最终结果] 使用精确评估结果作为最终目标值: {precise_avg_obj:.2f}")

                finally:
                    # 恢复原始设置
                    USE_EXACT_SECOND_STAGE_SOLVER = original_solver_setting
            # ==========================================================

            print(f"  最佳解选定的储物柜 (y*): {[j for j, val in best_solution_info['y'].items() if val > 0.5]}")
            print(f"  最佳解无人机分配 (n*): {dict((j, round(val)) for j, val in best_solution_info['n'].items() if best_solution_info['y'].get(j,0) > 0.5)}")
            log_memory_usage("SAA算法完成")
            return final_solution_saa
        else:
            print("\nSAA 未能找到任何有效的最终解。")
            log_memory_usage("SAA算法结束(无解)")
            return None


    def _print_detailed_cost_breakdown(self, saa_solution_dict):
        """打印详细的成本构成分析 - 1.py ALNS版本"""
        print(f"\n  详细成本构成分析 (1.py - ALNS算法):")
        print(f"  " + "-" * 50)

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        locker_fixed_cost = sum(self.locker_fixed_cost[j] * y_star.get(j, 0) for j in selected_lockers)
        drone_deployment_cost = sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)
        first_stage_total = locker_fixed_cost + drone_deployment_cost

        print(f"  第一阶段成本 (here-and-now): {first_stage_total:.2f}")
        print(f"    - 储物柜固定成本: {locker_fixed_cost:.2f}")
        print(f"    - 无人机部署成本: {drone_deployment_cost:.2f}")

        # 【内存优化】使用流式方法计算第二阶段期望成本
        print(f"  正在计算第二阶段期望成本 (wait-and-see)...")
        sample_count = min(200, SAA_SAMPLES_K_PRIME)  # 使用适量样本进行成本分解
        sample_scenarios = self._generate_validation_samples_batch(0, sample_count)
        print(f"  使用 {sample_count} 个验证样本进行成本分解计算")
        total_transport_cost = 0
        total_penalty_cost = 0
        total_truck_cost = 0

        for scenario_idx, scenario in enumerate(sample_scenarios):
            # 为该场景求解最优分配
            optimal_assignment = self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, scenario)

            # 计算该场景下的成本
            total_shortage = 0
            scenario_transport_cost = 0

            for i in self.customers:
                actual_demand = scenario[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage += shortage

                # 计算无人机运输成本
                for j in selected_lockers:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        scenario_transport_cost += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            scenario_penalty_cost = self.penalty_cost_unassigned * total_shortage

            total_transport_cost += scenario_transport_cost
            total_penalty_cost += scenario_penalty_cost

        # 计算第二阶段期望成本
        avg_transport_cost = total_transport_cost / sample_count
        avg_penalty_cost = total_penalty_cost / sample_count

        # 【内存优化】立即释放样本数据
        del sample_scenarios
        truck_cost_estimate = saa_solution_dict.get('truck_cost_k_prime_estimate', 0)

        second_stage_total = avg_transport_cost + avg_penalty_cost + truck_cost_estimate

        print(f"  第二阶段期望成本 (wait-and-see): {second_stage_total:.2f}")
        print(f"    - 无人机运输成本 (随实际需求变化): {avg_transport_cost:.2f}")
        print(f"    - 未分配惩罚成本 (随需求不确定性): {avg_penalty_cost:.2f}")
        print(f"    - 卡车运输成本 (随储物柜需求变化): {truck_cost_estimate:.2f}")

        total_cost = first_stage_total + second_stage_total
        print(f"  总成本 (重新计算): {total_cost:.2f}")

        # 显示与SAA主评估的差异（用于调试）
        main_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        difference = abs(total_cost - main_objective)
        print(f"  SAA主评估目标值: {main_objective:.2f}")
        print(f"  差异: {difference:.2f} ({difference/main_objective*100:.1f}%)")
        print(f"  注意: 应以SAA主评估目标值为准，此处重新计算仅用于成本构成分析")

        # 成本占比分析（基于SAA主评估目标值）
        if main_objective > 0:
            print(f"\n  成本占比分析 (基于SAA主评估目标值):")
            print(f"    - 第一阶段占比: {first_stage_total/main_objective*100:.1f}%")
            print(f"    - 第二阶段占比: {(main_objective-first_stage_total)/main_objective*100:.1f}%")
            print(f"    - 储物柜固定成本占比: {locker_fixed_cost/main_objective*100:.1f}%")
            print(f"    - 无人机成本(部署+运输)占比: {(drone_deployment_cost + avg_transport_cost)/main_objective*100:.1f}%")
            print(f"    - 卡车成本(固定+运输)占比: {truck_cost_estimate/main_objective*100:.1f}%")
            print(f"    - 惩罚成本占比: {avg_penalty_cost/main_objective*100:.1f}%")

            # 第二阶段求解器性能分析已移除以减少冗余输出



    def _evaluate_solution_on_new_samples_corrected(self, first_stage_solution: Dict, demand_samples_k_prime: List[Dict[int,float]]):
        """
        修正版：评估完整第一阶段解在新样本上的性能
        """
        import time
        eval_start_time = time.time()

        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        # 记录每个验证场景的总成本，用于计算Var(UB)
        scenario_total_costs = []

        # 提取第一阶段解
        y_star = first_stage_solution['y']
        n_star = first_stage_solution['n']

        # 第一阶段固定成本
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        first_stage_cost = locker_cost_fixed + drone_deployment_cost_fixed

        # 准备批量卡车成本计算的数据
        batch_active_lockers_info = []

        # 时间统计
        assignment_start_time = time.time()

        # 使用串行求解所有场景的客户分配问题

        all_optimal_assignments = self._solve_assignments_sequential(y_star, n_star, selected_lockers_eval, demand_samples_k_prime)

        # 根据所有最优分配结果计算卡车运输需求
        for k_prime_idx, optimal_assignment in enumerate(all_optimal_assignments):
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 调试信息已移除以减少冗余输出

            # 根据最优分配计算每个储物柜的实际需求量
            active_lockers_info_k_prime = {}

            for j_locker in selected_lockers_eval:
                total_actual_demand_j = sum(optimal_assignment.get((i, j_locker), 0) for i in self.customers)
                if total_actual_demand_j > 1e-6:
                    active_lockers_info_k_prime[j_locker] = {
                        'coord': self.site_coords[j_locker],
                        'demand': round(total_actual_demand_j)
                    }

            batch_active_lockers_info.append(active_lockers_info_k_prime)

        assignment_time = time.time() - assignment_start_time

        # 使用DRL批量求解计算所有场景的卡车成本（仅第一次复制显示详细信息）
        truck_cost_start_time = time.time()
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  使用DRL批量求解计算 {num_scenarios_k_prime} 个验证场景的卡车成本...")
        batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)
        truck_cost_time = time.time() - truck_cost_start_time

        # 计算总成本并记录每个场景的成本
        cost_calc_start_time = time.time()
        for k_prime_idx in range(num_scenarios_k_prime):
            truck_cost_k_prime = batch_truck_costs[k_prime_idx]
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 使用缓存的最优分配结果，避免重复计算
            optimal_assignment = all_optimal_assignments[k_prime_idx]

            # 计算该场景下的第二阶段成本
            # 1. 无人机运输成本（基于最优分配）
            transport_cost_k_prime = 0
            total_shortage_k_prime = 0

            for i in self.customers:
                actual_demand = demand_scenario_k_prime[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers_eval)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage_k_prime += shortage

                # 计算无人机运输成本
                for j in selected_lockers_eval:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        transport_cost_k_prime += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            penalty_cost_k_prime = self.penalty_cost_unassigned * total_shortage_k_prime

            # 3. 卡车运输成本
            total_truck_cost_over_k_prime_samples += truck_cost_k_prime

            # 总成本 = 第一阶段成本 + 第二阶段成本
            total_cost_for_scenario_k_prime = (first_stage_cost + transport_cost_k_prime +
                                              penalty_cost_k_prime + truck_cost_k_prime)
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime

            # 记录每个场景的总成本
            scenario_total_costs.append(total_cost_for_scenario_k_prime)

        cost_calc_time = time.time() - cost_calc_start_time
        total_eval_time = time.time() - eval_start_time

        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')

        # 详细时间分析已移除以减少冗余输出

        # 返回平均成本、卡车成本和每个场景的成本列表
        return avg_total_cost_k_prime, avg_truck_cost_k_prime, scenario_total_costs

    def _evaluate_solution_streaming(self, first_stage_solution: Dict, num_validation_samples: int):
        """
        【内存优化】流式评估解在验证样本上的性能 - 分批处理避免内存溢出
        """
        import time
        eval_start_time = time.time()

        total_cost_accumulator = 0.0
        total_truck_cost_accumulator = 0.0
        scenario_costs = []

        # 提取第一阶段解
        y_star = first_stage_solution['y']
        n_star = first_stage_solution['n']
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（固定）
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        first_stage_cost = locker_cost_fixed + drone_deployment_cost_fixed

        print(f"  使用流式评估处理 {num_validation_samples} 个验证样本，批大小: {MEMORY_BATCH_SIZE}")
        log_memory_usage("流式评估开始")

        # 【内存优化】分批处理验证样本
        num_batches = (num_validation_samples + MEMORY_BATCH_SIZE - 1) // MEMORY_BATCH_SIZE

        for batch_idx in range(num_batches):
            batch_start = batch_idx * MEMORY_BATCH_SIZE
            batch_end = min(batch_start + MEMORY_BATCH_SIZE, num_validation_samples)
            batch_size = batch_end - batch_start

            if batch_idx == 0:  # 只在第一批显示详细信息
                print(f"  处理批次 {batch_idx + 1}/{num_batches}: 样本 {batch_start + 1}-{batch_end}")

            # 【内存优化】为当前批次生成验证样本
            batch_validation_samples = self._generate_validation_samples_batch(batch_start, batch_size)

            # 处理当前批次
            batch_total_cost, batch_truck_cost, batch_scenario_costs = self._process_validation_batch(
                y_star, n_star, selected_lockers_eval, first_stage_cost, batch_validation_samples
            )

            # 累积结果
            total_cost_accumulator += batch_total_cost
            total_truck_cost_accumulator += batch_truck_cost
            scenario_costs.extend(batch_scenario_costs)

            # 【内存优化】立即释放批次数据
            del batch_validation_samples, batch_scenario_costs

            # 【内存优化】定期垃圾回收
            if (batch_idx + 1) % 5 == 0:  # 每5个批次进行一次垃圾回收
                import gc
                gc.collect()

        # 计算平均值
        avg_total_cost = total_cost_accumulator / num_validation_samples
        avg_truck_cost = total_truck_cost_accumulator / num_validation_samples

        eval_time = time.time() - eval_start_time
        print(f"  流式评估完成，耗时: {eval_time:.2f}秒，处理了 {num_batches} 个批次")
        log_memory_usage("流式评估完成")

        return avg_total_cost, avg_truck_cost, scenario_costs

    def _generate_validation_samples_batch(self, batch_start: int, batch_size: int):
        """
        【内存优化】生成指定批次的验证样本
        """
        # 设置确定性随机状态以确保一致性
        temp_random_state = random.getstate()
        temp_np_state = np.random.get_state()

        # 跳转到指定批次的随机状态
        random.seed(self.validation_random_seed)
        np.random.seed(self.validation_random_seed)

        # 跳过前面的样本（模拟生成但不保存）
        for _ in range(batch_start):
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]
                np.random.poisson(lam=lambda_param)

        # 生成当前批次的样本
        batch_samples = []
        for _ in range(batch_size):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)
            batch_samples.append(current_scenario_demand)

        # 恢复原始随机状态
        random.setstate(temp_random_state)
        np.random.set_state(temp_np_state)

        return batch_samples

    def _process_validation_batch(self, y_star, n_star, selected_lockers, first_stage_cost, batch_samples):
        """
        【内存优化】处理验证样本批次
        """
        batch_total_cost = 0.0
        batch_truck_cost = 0.0
        batch_scenario_costs = []

        # 准备批量卡车成本计算的数据
        batch_active_lockers_info = []

        # 串行求解所有场景的客户分配问题
        all_optimal_assignments = self._solve_assignments_sequential(y_star, n_star, selected_lockers, batch_samples)

        # 根据所有最优分配结果计算卡车运输需求
        for k_idx, optimal_assignment in enumerate(all_optimal_assignments):
            demand_scenario = batch_samples[k_idx]

            # 根据最优分配计算每个储物柜的实际需求量
            active_lockers_info = {}
            for j_locker in selected_lockers:
                total_actual_demand_j = sum(optimal_assignment.get((i, j_locker), 0) for i in self.customers)
                if total_actual_demand_j > 1e-6:
                    active_lockers_info[j_locker] = {
                        'coord': self.site_coords[j_locker],
                        'demand': round(total_actual_demand_j)
                    }
            batch_active_lockers_info.append(active_lockers_info)

        # 批量计算卡车成本
        batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)

        # 计算每个场景的总成本
        for k_idx in range(len(batch_samples)):
            truck_cost_k = batch_truck_costs[k_idx]
            demand_scenario = batch_samples[k_idx]
            optimal_assignment = all_optimal_assignments[k_idx]

            # 计算该场景下的第二阶段成本
            transport_cost_k = 0
            total_shortage_k = 0

            for i in self.customers:
                actual_demand = demand_scenario[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage_k += shortage

                # 计算无人机运输成本
                for j in selected_lockers:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        transport_cost_k += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 未分配惩罚成本
            penalty_cost_k = self.penalty_cost_unassigned * total_shortage_k

            # 总成本
            total_cost_for_scenario = first_stage_cost + transport_cost_k + penalty_cost_k + truck_cost_k

            batch_total_cost += total_cost_for_scenario
            batch_truck_cost += truck_cost_k
            batch_scenario_costs.append(total_cost_for_scenario)

        return batch_total_cost, batch_truck_cost, batch_scenario_costs

    def _calculate_precise_cost_breakdown(self, y_star, n_star, selected_lockers):
        """
        使用精确评估方法计算成本分解，确保与最终目标值一致

        Args:
            y_star: 储物柜选址决策
            n_star: 无人机配置决策
            selected_lockers: 选定的储物柜列表

        Returns:
            tuple: (drone_transport_cost, penalty_cost)
        """
        # 【内存优化】使用流式方法进行精确成本分解
        sample_count = min(500, SAA_SAMPLES_K_PRIME)  # 使用适量样本进行精确分解
        print(f"  使用精确评估方法计算成本分解（基于{sample_count}个验证样本）...")

        # 临时设置为精确求解器模式
        global USE_EXACT_SECOND_STAGE_SOLVER
        original_solver_setting = USE_EXACT_SECOND_STAGE_SOLVER
        USE_EXACT_SECOND_STAGE_SOLVER = True

        try:
            total_transport_cost = 0
            total_penalty_cost = 0

            # 【内存优化】生成验证样本
            validation_samples = self._generate_validation_samples_batch(0, sample_count)

            # 使用与精确评估相同的方法计算成本
            all_optimal_assignments = self._solve_assignments_sequential(
                y_star, n_star, selected_lockers, validation_samples
            )

            for k_idx, scenario in enumerate(validation_samples):
                optimal_assignment = all_optimal_assignments[k_idx]

                # 计算该场景下的运输成本和惩罚成本
                scenario_transport_cost = 0
                total_shortage = 0

                for i in self.customers:
                    actual_demand = scenario[i]
                    total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers)
                    shortage = max(0, actual_demand - total_assigned)
                    total_shortage += shortage

                    # 计算无人机运输成本
                    for j in selected_lockers:
                        assigned_qty = optimal_assignment.get((i, j), 0)
                        if assigned_qty > 1e-6 and (i, j) in self.distance:
                            scenario_transport_cost += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

                # 未分配惩罚成本
                scenario_penalty_cost = self.penalty_cost_unassigned * total_shortage

                total_transport_cost += scenario_transport_cost
                total_penalty_cost += scenario_penalty_cost

                # 显示前几个场景的详细信息
                if k_idx < 3:
                    total_demand_scenario = sum(scenario.values())
                    total_assigned_scenario = sum(optimal_assignment.values())
                    print(f"    场景{k_idx+1}: 总需求={total_demand_scenario:.1f}, 总分配={total_assigned_scenario:.1f}, 短缺={total_shortage:.1f}, 惩罚={scenario_penalty_cost:.2f}")

            # 计算平均成本
            avg_transport_cost = total_transport_cost / sample_count
            avg_penalty_cost = total_penalty_cost / sample_count

            print(f"  精确方法重新计算结果: 无人机运输成本={avg_transport_cost:.2f}, 惩罚成本={avg_penalty_cost:.2f}")

            # 【内存优化】立即释放样本数据
            del validation_samples, all_optimal_assignments

            return avg_transport_cost, avg_penalty_cost

        finally:
            # 恢复原始设置
            USE_EXACT_SECOND_STAGE_SOLVER = original_solver_setting

    def _solve_assignments_sequential(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_samples: List[Dict[int, float]]) -> List[Dict]:
        """
        使用串行求解所有场景的客户分配问题
        在adaptive模式下，验证阶段只对部分场景使用精确求解器
        """
        all_assignments = []

        # 检查是否在验证阶段（通过场景数量判断）
        is_validation_phase = len(demand_samples) > 100  # 验证阶段通常有2000个场景

        # 调试：检查_current_replication状态
        current_rep = getattr(self, '_current_replication', None)
        if current_rep == 1:
            if is_validation_phase and USE_EXACT_SECOND_STAGE_SOLVER == "adaptive":
                validation_exact_ratio = 0.1  # 10%的场景使用精确求解器
                exact_count = int(len(demand_samples) * validation_exact_ratio)
                print(f"  验证阶段：{len(demand_samples)}个场景，其中{exact_count}个({validation_exact_ratio*100:.0f}%)使用精确求解器")
            else:
                print(f"  使用串行求解")

        # 在adaptive模式的验证阶段，只对部分场景使用精确求解器
        if is_validation_phase and USE_EXACT_SECOND_STAGE_SOLVER == "adaptive":
            validation_exact_ratio = 0.1  # 10%的场景使用精确求解器
            exact_count = int(len(demand_samples) * validation_exact_ratio)
            # 均匀采样需要精确评估的场景索引
            exact_indices = set(range(0, len(demand_samples), max(1, len(demand_samples) // exact_count)))
        else:
            exact_indices = set()

        for k_idx, demand_scenario in enumerate(demand_samples):
            try:
                # 决定是否使用精确求解器
                if is_validation_phase and USE_EXACT_SECOND_STAGE_SOLVER == "adaptive":
                    use_exact = k_idx in exact_indices
                else:
                    use_exact = None  # 让_should_use_exact_solver自动决定

                assignment = self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, demand_scenario, use_exact=use_exact)
                all_assignments.append(assignment)
            except Exception as e:
                if hasattr(self, '_current_replication') and self._current_replication == 1:
                    print(f"  场景 {k_idx+1} 串行求解失败: {str(e)}")
                # 使用默认分配（全部为0）
                default_assignment = {(i, j): 0.0 for i in self.customers for j in selected_lockers}
                all_assignments.append(default_assignment)

        return all_assignments

    def _solve_optimal_assignment_for_scenario(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_scenario: Dict[int, float], use_exact: bool = None) -> Dict:
        """
        为给定的需求场景求解最优客户分配
        根据USE_EXACT_SECOND_STAGE_SOLVER配置或use_exact参数选择求解器
        """
        import time

        # 更新总场景计数
        self.second_stage_solver_stats['total_scenarios'] += 1

        # 决定使用哪种求解器
        if use_exact is not None:
            should_use_exact = use_exact
        elif USE_EXACT_SECOND_STAGE_SOLVER == "adaptive":
            # 在adaptive模式下，根据调用上下文决定
            should_use_exact = False  # 默认使用启发式求解器
            import inspect
            frame = inspect.currentframe()
            try:
                # 检查调用栈中是否包含精确评估相关的函数
                for i in range(10):  # 检查最近10层调用栈
                    frame = frame.f_back
                    if frame is None:
                        break
                    func_name = frame.f_code.co_name
                    if func_name in ['calculate_objective_cached', 'calculate_objective_two_stage']:
                        should_use_exact = True
                        break
            finally:
                del frame
        elif USE_EXACT_SECOND_STAGE_SOLVER == True:
            should_use_exact = True
        else:
            should_use_exact = False

        # 【修复】遵循配置参数选择求解器
        if should_use_exact:
            # 使用精确求解器（Gurobi MIP）
            try:
                start_time = time.time()
                result = self._solve_assignment_with_gurobi(y_star, n_star, selected_lockers, demand_scenario)
                solve_time = time.time() - start_time

                # 记录成功统计
                self.second_stage_solver_stats['exact_solver_success'] += 1
                self.second_stage_solver_stats['exact_solve_times'].append(solve_time)
                return result
            except Exception as e:
                # 记录失败统计
                self.second_stage_solver_stats['exact_solver_failure'] += 1

                # 回退到贪心算法
                if not hasattr(self, 'fast_solver') or self.fast_solver is None:
                    self.fast_solver = FastAssignmentSolver(self)

                start_time = time.time()
                result = self.fast_solver.solve_assignment_heuristic(y_star, n_star, selected_lockers, demand_scenario)
                solve_time = time.time() - start_time

                self.second_stage_solver_stats['heuristic_solver_used'] += 1
                self.second_stage_solver_stats['heuristic_solve_times'].append(solve_time)
                return result
        else:
            # 使用快速启发式求解器
            if not hasattr(self, 'fast_solver') or self.fast_solver is None:
                self.fast_solver = FastAssignmentSolver(self)

            start_time = time.time()
            result = self.fast_solver.solve_assignment_heuristic(y_star, n_star, selected_lockers, demand_scenario)
            solve_time = time.time() - start_time

            self.second_stage_solver_stats['heuristic_solver_used'] += 1
            self.second_stage_solver_stats['heuristic_solve_times'].append(solve_time)
            return result

    def _solve_assignment_with_gurobi(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_scenario: Dict[int, float]) -> Dict:
        """
        使用Gurobi精确求解器求解客户分配问题（与saa_g_r.py保持一致）
        """
        import gurobipy as gp
        from gurobipy import GRB

        try:
            model_scenario = gp.Model("ScenarioAssignment")
            model_scenario.setParam('OutputFlag', 0)
            model_scenario.setParam('Threads', 1)
            model_scenario.setParam('TimeLimit', 30)  # 与g_i.py保持一致的时间限制

            # 决策变量：实际配送量
            x_scenario = {}
            for i in self.customers:
                for j in selected_lockers:
                    x_scenario[i, j] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_{i}_{j}")

            # 短缺变量
            shortage_scenario = {}
            for i in self.customers:
                shortage_scenario[i] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"shortage_{i}")

            # 目标函数：最小化运输成本和惩罚成本（与saa_g_r.py一致）
            transport_cost = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), self.BIG_M) * x_scenario[i, j]
                for i in self.customers for j in selected_lockers
            )
            penalty_cost = gp.quicksum(
                self.penalty_cost_unassigned * shortage_scenario[i]
                for i in self.customers
            )
            model_scenario.setObjective(transport_cost + penalty_cost, GRB.MINIMIZE)

            # 约束条件（与saa_g_r.py保持一致）
            for i in self.customers:
                # 短缺量定义（与saa_g_r.py一致）
                model_scenario.addConstr(
                    shortage_scenario[i] == demand_scenario[i] - gp.quicksum(x_scenario[i, j] for j in selected_lockers),
                    name=f"ShortageDefinition_{i}"
                )

                for j in selected_lockers:
                    # 实际配送量不超过实际需求（与saa_g_r.py一致）
                    model_scenario.addConstr(
                        x_scenario[i, j] <= demand_scenario[i],
                        name=f"DeliveryLimit_{i}_{j}"
                    )

                    # 飞行距离限制（与saa_g_r.py一致）
                    if (i, j) in self.distance:
                        # 使用 Big-M 方法：2*distance*x <= max_flight_distance*demand
                        model_scenario.addConstr(
                            2 * self.distance[i, j] * x_scenario[i, j] <= self.max_flight_distance * demand_scenario[i],
                            name=f"FlightDistance_{i}_{j}"
                        )

            # 储物柜容量约束
            for j in selected_lockers:
                model_scenario.addConstr(
                    gp.quicksum(x_scenario[i, j] for i in self.customers) <= self.Q_locker_capacity[j],
                    name=f"locker_capacity_{j}"
                )

            # 无人机服务能力约束（与saa_g_r.py一致）
            for j in selected_lockers:
                total_service_time = gp.quicksum(
                    x_scenario[i, j] * ((2 * self.distance.get((i, j), self.BIG_M) / self.drone_speed) + self.loading_time)
                    for i in self.customers
                )
                available_hours = n_star.get(j, 0) * self.H_drone_working_hours_per_day
                model_scenario.addConstr(
                    total_service_time <= available_hours,
                    name=f"drone_capacity_{j}"
                )

            model_scenario.optimize()

            if model_scenario.SolCount > 0:
                assignment = {(i, j): x_scenario[i, j].X for i in self.customers for j in selected_lockers}
                return assignment
            else:
                # 如果无解，返回空分配
                return {(i, j): 0.0 for i in self.customers for j in selected_lockers}

        except Exception as e:
            # 求解失败，抛出异常让上层处理
            raise e





    def _analyze_truck_cost_details_corrected(self, y_solution, n_solution, validation_samples, replication, first_stage_assignment, sample_active_lockers_info):
        """
        修正版的卡车成本详细分析
        """
        print(f"  === 复制 {replication} 卡车成本详细分析（修正版两阶段） ===")

        # 统计开放的储物柜
        open_lockers = [site for site in self.sites if y_solution.get(site, 0) > 0.5]
        print(f"  开放储物柜数量: {len(open_lockers)}")
        print(f"  开放储物柜ID: {open_lockers}")

        # 显示第一阶段客户分配
        print(f"  第一阶段客户分配（基于期望需求）:")
        for i in self.customers:
            assignments = []
            for j in open_lockers:
                qty = first_stage_assignment.get((i, j), 0)
                if qty > 0.5:
                    assignments.append(f"储物柜{j}({qty:.1f})")
            if assignments:
                print(f"    客户{i}: {', '.join(assignments)} (期望需求: {self.expected_demand[i]})")

        # 分析前几个验证场景
        print(f"  前{len(sample_active_lockers_info)}个验证场景的卡车需求:")
        for i, active_lockers_info in enumerate(sample_active_lockers_info):
            print(f"  --- 场景 {i+1} ---")
            if active_lockers_info:
                total_demand = sum(info['demand'] for info in active_lockers_info.values())
                print(f"    总需求: {total_demand:.1f}")
                for locker_id, info in active_lockers_info.items():
                    print(f"    储物柜{locker_id}: 需求{info['demand']:.1f}, 坐标{info['coord']}")
            else:
                print(f"    无卡车运输需求")

        print(f"  ================================")

    def _debug_solution_analysis(self, replication, y_solution, n_solution, x_qty_solution, z_solution, u_solution):
        """
        详细调试解的分配情况（修正版：不依赖第一阶段客户分配）
        """
        print(f"\n  🔍 复制 {replication} 解的详细调试分析:")
        print(f"  " + "=" * 60)

        # 1. 储物柜开放情况
        open_lockers = [j for j, val in y_solution.items() if val > 0.5]
        print(f"  开放储物柜: {open_lockers}")

        # 2. 无人机配置情况
        drone_config = {j: round(n_solution.get(j, 0)) for j in open_lockers}
        print(f"  无人机配置: {drone_config}")

        # 3. 第一阶段成本分析
        print(f"  第一阶段成本分析:")
        locker_cost = sum(self.locker_fixed_cost[j] * y_solution.get(j, 0) for j in open_lockers)
        drone_cost = sum(self.drone_cost * n_solution.get(j, 0) for j in open_lockers)
        first_stage_cost = locker_cost + drone_cost

        print(f"    储物柜固定成本: {locker_cost:.2f}")
        print(f"    无人机部署成本: {drone_cost:.2f}")
        print(f"    第一阶段总成本: {first_stage_cost:.2f}")

        # 4. 储物柜资源配置分析
        print(f"  储物柜资源配置:")
        for j in open_lockers:
            capacity_j = self.Q_locker_capacity[j]
            drone_count = n_solution.get(j, 0)
            available_hours = drone_count * self.H_drone_working_hours_per_day

            # 距离约束检查
            reachable_customers = []
            unreachable_customers = []
            for i in self.customers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        reachable_customers.append(i)
                    else:
                        unreachable_customers.append(i)

            print(f"    储物柜{j}: 容量{capacity_j}, 无人机{drone_count}架, 可用时间{available_hours:.1f}h")
            print(f"      可达客户: {len(reachable_customers)}/{len(self.customers)}")
            if unreachable_customers:
                print(f"      不可达客户: {unreachable_customers}")

        print(f"  注意: 在修正的两阶段模型中，客户分配在第二阶段根据实际需求动态优化")

        # 添加惩罚成本分析
        self._analyze_penalty_cost_causes(y_solution, n_solution)

    def _analyze_penalty_cost_causes(self, y_solution, n_solution):
        """
        分析惩罚成本高的原因
        """
        print(f"\n  🔍 惩罚成本分析:")
        print(f"  " + "=" * 50)

        open_lockers = [j for j, val in y_solution.items() if val > 0.5]
        total_expected_demand = sum(self.expected_demand.values())

        print(f"  总期望需求: {total_expected_demand:.1f}")
        print(f"  惩罚成本单价: {self.penalty_cost_unassigned:.0f} 元/单位")
        print(f"  开放储物柜: {open_lockers}")

        # 分析每个客户的可达性
        unreachable_customers = []
        reachable_customers = []
        total_unreachable_demand = 0

        for i in self.customers:
            customer_reachable = False
            min_distance = float('inf')

            for j in open_lockers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        customer_reachable = True
                        min_distance = min(min_distance, self.distance[i, j])

            if not customer_reachable:
                unreachable_customers.append(i)
                total_unreachable_demand += self.expected_demand[i]
            else:
                reachable_customers.append((i, min_distance))

        print(f"\n  距离约束分析:")
        print(f"    最大飞行距离: {self.max_flight_distance:.1f} km")
        print(f"    不可达客户数: {len(unreachable_customers)}/{len(self.customers)}")
        print(f"    不可达需求量: {total_unreachable_demand:.1f}/{total_expected_demand:.1f} ({total_unreachable_demand/total_expected_demand*100:.1f}%)")

        if unreachable_customers:
            print(f"    不可达客户: {unreachable_customers[:10]}{'...' if len(unreachable_customers) > 10 else ''}")

        # 分析容量约束
        print(f"\n  容量约束分析:")
        total_locker_capacity = sum(self.Q_locker_capacity[j] for j in open_lockers)
        total_drone_capacity = 0

        for j in open_lockers:
            locker_capacity = self.Q_locker_capacity[j]
            drone_count = n_solution.get(j, 0)

            # 计算无人机运力
            reachable_demand_for_j = 0
            avg_distance_j = 0
            reachable_count_j = 0

            for i in self.customers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        reachable_demand_for_j += self.expected_demand[i]
                        avg_distance_j += self.distance[i, j]
                        reachable_count_j += 1

            if reachable_count_j > 0:
                avg_distance_j /= reachable_count_j
                avg_service_time = (2 * avg_distance_j / self.drone_speed) + self.loading_time
                drone_capacity_j = drone_count * self.H_drone_working_hours_per_day / avg_service_time
            else:
                drone_capacity_j = 0

            total_drone_capacity += drone_capacity_j

            print(f"    储物柜{j}: 容量{locker_capacity}, 无人机{drone_count}架, 运力{drone_capacity_j:.1f}")

        print(f"    总储物柜容量: {total_locker_capacity:.1f}")
        print(f"    总无人机运力: {total_drone_capacity:.1f}")
        print(f"    可达需求量: {total_expected_demand - total_unreachable_demand:.1f}")

        # 分析瓶颈
        effective_capacity = min(total_locker_capacity, total_drone_capacity)
        reachable_demand = total_expected_demand - total_unreachable_demand

        print(f"\n  瓶颈分析:")
        print(f"    有效服务容量: {effective_capacity:.1f}")
        print(f"    可达需求量: {reachable_demand:.1f}")

        if effective_capacity < reachable_demand:
            shortage = reachable_demand - effective_capacity
            print(f"    容量不足: {shortage:.1f} ({shortage/reachable_demand*100:.1f}%)")
            print(f"    容量不足惩罚成本: {shortage * self.penalty_cost_unassigned:.0f} 元")

        total_shortage = total_unreachable_demand + max(0, reachable_demand - effective_capacity)
        total_penalty = total_shortage * self.penalty_cost_unassigned

        print(f"\n  总惩罚成本预估:")
        print(f"    距离约束导致: {total_unreachable_demand * self.penalty_cost_unassigned:.0f} 元")
        print(f"    容量约束导致: {max(0, reachable_demand - effective_capacity) * self.penalty_cost_unassigned:.0f} 元")
        print(f"    总惩罚成本: {total_penalty:.0f} 元")
        print(f"    占总成本比例: {total_penalty/(total_penalty + 49500 + 2028):.1%}")



    def calculate_truck_cost_batch(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        批量计算卡车成本 - 智能分组批量求解

        将具有相同储物柜配置的场景分组进行批量求解，
        以最大化DRL批量求解的效率。

        Args:
            batch_active_lockers_info: 批量活跃储物柜信息列表

        Returns:
            卡车成本列表
        """
        if not DRL_AVAILABLE:
            print("  DRL不可用，使用简化估算计算批量卡车成本")
            return self._calculate_simplified_batch_costs(batch_active_lockers_info)

        if not batch_active_lockers_info:
            return []

        try:
            # 【新增】生成批量求解缓存键
            batch_cache_key = self._get_batch_cache_key(batch_active_lockers_info)
            if batch_cache_key in self.batch_solve_cache:
                self.batch_cache_hits += 1
                if hasattr(self, '_current_replication') and self._current_replication == 1:
                    print(f"  批量求解缓存命中，跳过重复计算")
                return self.batch_solve_cache[batch_cache_key]

            self.batch_cache_misses += 1

            # 按储物柜配置分组
            groups = {}
            for i, scenario in enumerate(batch_active_lockers_info):
                locker_ids = tuple(sorted(scenario.keys()))
                if locker_ids not in groups:
                    groups[locker_ids] = []
                groups[locker_ids].append((i, scenario))

            if hasattr(self, '_current_replication') and self._current_replication == 1:
                print(f"  将 {len(batch_active_lockers_info)} 个场景分为 {len(groups)} 组进行批量求解")

            # 初始化结果列表
            batch_costs = [0.0] * len(batch_active_lockers_info)
            drl_solver = self._get_drl_solver(make_plots=False)

            # 对每组进行批量求解
            for group_idx, (locker_ids, scenarios) in enumerate(groups.items()):
                scenario_indices = [idx for idx, _ in scenarios]
                scenario_data = [data for _, data in scenarios]

                if len(scenario_data) > 1:
                    # 批量求解
                    group_costs = drl_solver.solve_batch(scenario_data, return_route_info=False)
                else:
                    # 单个求解
                    group_costs = [drl_solver.solve(scenario_data[0], return_route_info=False)]

                # 将结果放回原始位置
                for i, cost in enumerate(group_costs):
                    batch_costs[scenario_indices[i]] = cost

            # 【优化】减少批量求解输出 - 只在第一次复制的前3次输出
            if (hasattr(self, '_current_replication') and self._current_replication == 1 and
                hasattr(self, '_batch_solve_count') and self._batch_solve_count <= 3):
                if not hasattr(self, '_batch_solve_count'):
                    self._batch_solve_count = 0
                self._batch_solve_count += 1
                print(f"  分组批量求解成功，平均卡车成本: {sum(batch_costs)/len(batch_costs):.2f}")

            # 【内存优化】保存到批量求解缓存，并管理缓存大小
            self.batch_solve_cache[batch_cache_key] = batch_costs

            # 【内存优化】限制批量缓存大小
            if len(self.batch_solve_cache) > self.max_batch_cache_size:
                # 删除最旧的一半缓存
                keys_to_remove = list(self.batch_solve_cache.keys())[:self.max_batch_cache_size//2]
                for key in keys_to_remove:
                    del self.batch_solve_cache[key]
                import gc
                gc.collect()

            return batch_costs
        except Exception as e:
            print(f"  DRL批量求解失败: {str(e)}，回退到逐个求解")
            return self._fallback_individual_solving(batch_active_lockers_info)





    def _calculate_simplified_batch_costs(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        简化估算批量成本计算
        """
        batch_costs = []
        for active_lockers_info in batch_active_lockers_info:
            batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _calculate_simplified_cost(self, active_lockers_info: Dict[int, Dict[str, Any]]) -> float:
        """
        单个场景的简化成本估算
        """
        if not active_lockers_info:
            return 0.0

        total_demand = sum(info['demand'] for info in active_lockers_info.values())
        num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
        return num_trucks * self.truck_fixed_cost

    def _fallback_individual_solving(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        回退到逐个求解的方法
        """
        batch_costs = []
        for i, active_lockers_info in enumerate(batch_active_lockers_info):
            try:
                cost = self.calculate_truck_cost([], {}, make_plots=False, active_lockers_info_override=active_lockers_info)
                batch_costs.append(cost)
            except Exception as e2:
                print(f"  场景 {i+1} 求解失败: {str(e2)}，使用简化估算")
                batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _check_saa_termination_criteria(self):
        """
        检查SAA终止条件
        返回: (should_terminate: bool, gap_info: str)
        """
        import scipy.stats as stats

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 至少需要2次有效复制才能计算统计量
        if len(valid_obj_k) < 2 or len(valid_ub_k_prime) < 2:
            return False, f"有效解数量不足 ({len(valid_obj_k)}, {len(valid_ub_k_prime)} < 2)，需要至少2次有效复制"

        # 找到当前最佳解的信息
        best_validation_cost = float('inf')
        for i, ub_val in enumerate(self.saa_upper_bounds_k_prime):
            if ub_val != float('inf') and ub_val < best_validation_cost:
                best_validation_cost = ub_val

        # 计算正确的SAA统计量
        m = len(valid_obj_k)  # 当前迭代次数
        statistical_lower_bound = np.mean(valid_obj_k)  # cost_N^m = (1/m) * Σ cost_N^{m'}
        statistical_upper_bound = best_validation_cost  # UB_{N'}(ŝ) (最佳解的验证成本)

        # SAA Gap计算 (按正确的论文公式)
        saa_gap = statistical_upper_bound - statistical_lower_bound
        gap_percent = saa_gap / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 计算下界方差：δ²(cost_N) = (1/[m(m-1)]) * Σ(cost_N^{m'} - cost_N^m)²
        if m > 1:
            variance_sum = sum((cost_val - statistical_lower_bound)**2 for cost_val in valid_obj_k)
            var_lower_bound = variance_sum / (m * (m - 1))
            std_lower_bound = np.sqrt(var_lower_bound)
        else:
            var_lower_bound = 0
            std_lower_bound = 0

        # 计算上界方差：δ²_{N'}(ŝ) = (1/[N'(N'-1)]) * Σ(Q(ŝ,ξ_n) - cost_{N'}(ŝ))²
        # 这里简化处理，假设每个复制的验证样本数量相同
        N_prime = SAA_SAMPLES_K_PRIME
        if len(valid_ub_k_prime) > 1:
            # 使用最佳解对应的上界值计算方差
            best_ub_index = valid_ub_k_prime.index(best_validation_cost)
            # 简化计算：使用所有上界值的方差除以验证样本数量
            var_upper_bound = np.var(valid_ub_k_prime, ddof=1) / N_prime
        else:
            var_upper_bound = 0

        # Gap的总方差：δ²_Gap(ŝ) = δ²(cost_N) + δ²_{N'}(ŝ)
        gap_variance = var_lower_bound + var_upper_bound
        gap_std = np.sqrt(gap_variance) if gap_variance > 0 else 0
        variance_percent = gap_variance / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 置信区间计算 (使用t分布)
        n_replications = len(valid_obj_k)
        if n_replications > 1:
            t_critical = stats.t.ppf(1 - SAA_CONFIDENCE_LEVEL_ALPHA/2, n_replications - 1)
            # 下界置信区间
            lb_margin = t_critical * std_lower_bound / np.sqrt(n_replications) if std_lower_bound > 0 else 0
        else:
            lb_margin = 0

        # SAA终止条件检查 (必须同时满足两个条件)
        # 条件1: 相对差距阈值 Gap_{N,N'}(ŝ) / cost_{N'}(ŝ) ≤ ε (如 ε = 3%)
        relative_gap_condition = (gap_percent <= SAA_GAP_TOLERANCE_PERCENT and saa_gap >= 0)

        # 条件2: 方差阈值 δ²_Gap(ŝ) / cost_{N'}(ŝ) ≤ ε' (如 ε' = 5%)
        variance_condition = variance_percent <= SAA_VARIANCE_TOLERANCE_PERCENT

        # 主要终止条件：必须同时满足相对差距阈值和方差阈值
        should_terminate = relative_gap_condition and variance_condition

        # 如果gap为负，说明统计估计不稳定，不应该终止
        if saa_gap < 0:
            should_terminate = False

        # 计算下界变异系数
        cv_lower_bound = std_lower_bound / statistical_lower_bound if statistical_lower_bound > 0 else float('inf')

        # 构建详细的状态信息
        gap_condition_status = "✓" if relative_gap_condition else "✗"
        variance_condition_status = "✓" if variance_condition else "✗"

        gap_info = (f"相对差距: {gap_percent:.2%} {gap_condition_status} (阈值: ≤{SAA_GAP_TOLERANCE_PERCENT:.0%}), "
                   f"方差比例: {variance_percent:.2%} {variance_condition_status} (阈值: ≤{SAA_VARIANCE_TOLERANCE_PERCENT:.0%}), "
                   f"LB(m={m}): {statistical_lower_bound:.2f}, "
                   f"UB: {statistical_upper_bound:.2f}, "
                   f"Gap: {saa_gap:.2f}, "
                   f"δ²_Gap: {gap_variance:.4f}")

        return should_terminate, gap_info



    def _print_saa_solution(self, saa_solution_dict: Dict):
        if not saa_solution_dict:
            print("SAA 未找到有效解。")
            return

        print("\n" + "=" * 60 + "\nSAA 优化结果 (ALNS算法)\n" + "=" * 60)

        # 提取解决方案信息
        y_star_final = saa_solution_dict.get('selected_lockers_y', {})
        n_star_final = saa_solution_dict.get('drone_allocations_n', {})
        selected_lockers_print = [j for j, val in y_star_final.items() if val > 0.5]
        total_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        truck_cost = saa_solution_dict.get('truck_cost_k_prime_estimate', 0)

        # 计算成本分解 - 需要从other_costs中分离出无人机运输成本和惩罚成本
        locker_fixed_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers_print)
        drone_deployment_cost = sum(self.drone_cost * n_star_final.get(j, 0) for j in selected_lockers_print)

        # 计算无人机运输成本和惩罚成本（从第二阶段成本中分离）
        # other_costs = 无人机运输成本 + 惩罚成本
        other_costs = total_objective - locker_fixed_cost - drone_deployment_cost - truck_cost

        # 为了正确分解，我们需要重新计算无人机运输成本和惩罚成本
        # 使用与成本分解函数相同的逻辑
        drone_transport_cost = 0
        penalty_cost = 0

        # 【修复】使用与最终目标值相同的评估方法进行成本分解
        print(f"  正在使用精确评估方法重新计算成本分解...")

        # 检查是否使用了精确评估结果
        if 'precise_evaluation_used' in saa_solution_dict and saa_solution_dict['precise_evaluation_used']:
            print(f"  检测到使用了精确评估结果，将使用精确方法进行成本分解")
            # 使用精确评估方法重新计算成本分解
            drone_transport_cost, penalty_cost = self._calculate_precise_cost_breakdown(
                y_star_final, n_star_final, selected_lockers_print
            )
        else:
            print(f"  使用启发式方法进行成本分解")
            # 使用启发式方法计算成本分解
            # 【内存优化】使用流式方法计算成本分解
            total_transport = 0
            total_penalty = 0

            sample_count = min(500, SAA_SAMPLES_K_PRIME)  # 限制样本数量以提高速度
            cost_breakdown_samples = self._generate_validation_samples_batch(0, sample_count)
            for idx, scenario in enumerate(cost_breakdown_samples):
                # 求解该场景的客户分配
                assignment = self.fast_solver.solve_assignment_heuristic(
                    y_star_final, n_star_final, selected_lockers_print, scenario
                )

                # 计算该场景的无人机运输成本
                scenario_transport = 0
                scenario_penalty = 0
                total_demand_scenario = sum(scenario.values())
                total_assigned_scenario = 0

                for i in self.customers:
                    actual_demand = scenario[i]
                    total_assigned = sum(assignment.get((i, j), 0) for j in selected_lockers_print)
                    total_assigned_scenario += total_assigned
                    shortage = max(0, actual_demand - total_assigned)
                    scenario_penalty += self.penalty_cost_unassigned * shortage

                    # 计算无人机运输成本
                    for j in selected_lockers_print:
                        assigned_qty = assignment.get((i, j), 0)
                        if assigned_qty > 1e-6 and (i, j) in self.distance:
                            scenario_transport += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

                # 调试信息：显示前几个场景的详细信息
                if idx < 3:
                    total_shortage_scenario = total_demand_scenario - total_assigned_scenario
                    print(f"    场景{idx+1}: 总需求={total_demand_scenario:.1f}, 总分配={total_assigned_scenario:.1f}, 短缺={total_shortage_scenario:.1f}, 惩罚={scenario_penalty:.2f}")

                total_transport += scenario_transport
                total_penalty += scenario_penalty

            drone_transport_cost = total_transport / sample_count
            penalty_cost = total_penalty / sample_count

            print(f"  启发式重新计算结果: 无人机运输成本={drone_transport_cost:.2f}, 惩罚成本={penalty_cost:.2f}")

            # 【内存优化】立即释放样本数据
            del cost_breakdown_samples

        # 重新计算分类后的成本
        drone_total_cost = drone_deployment_cost + drone_transport_cost  # 无人机成本（部署+运输）
        truck_total_cost = truck_cost  # 卡车成本（固定+运输）
        penalty_total_cost = penalty_cost  # 惩罚成本

        # 主要结果展示
        print(f"  📊 核心指标:")
        print(f"    开放储物柜数量: {len(selected_lockers_print)} 个")
        print(f"    总成本: {total_objective:.2f} 元/天")
        print(f"    无人机成本(部署+运输): {drone_total_cost:.2f} 元/天 ({drone_total_cost/total_objective*100:.1f}%)")
        print(f"    卡车成本(固定+运输): {truck_total_cost:.2f} 元/天 ({truck_total_cost/total_objective*100:.1f}%)")

        print(f"\n  💰 详细成本分解:")
        print(f"    储物柜固定成本: {locker_fixed_cost:.2f} 元/天 ({locker_fixed_cost/total_objective*100:.1f}%)")
        print(f"    无人机成本(部署+运输): {drone_total_cost:.2f} 元/天 ({drone_total_cost/total_objective*100:.1f}%)")
        print(f"    卡车成本(固定+运输): {truck_total_cost:.2f} 元/天 ({truck_total_cost/total_objective*100:.1f}%)")
        print(f"    其他成本(惩罚): {penalty_total_cost:.2f} 元/天 ({penalty_total_cost/total_objective*100:.1f}%)")

        print(f"\n  🏪 储物柜配置:")
        print(f"    选定站点: {selected_lockers_print}")
        total_drones = 0
        for j_site_p in selected_lockers_print:
            drones = round(n_star_final.get(j_site_p,0))
            total_drones += drones
            print(f"    位置 {j_site_p}: {drones} 架无人机")
        print(f"    无人机总数: {total_drones} 架")

        print(f"\n  📈 运营指标:")
        print(f"    总期望需求量: {sum(self.expected_demand.values()):.2f} 订单/天")
        print(f"    平均每储物柜服务: {sum(self.expected_demand.values())/len(selected_lockers_print):.2f} 订单/天")
        print(f"    平均每无人机服务: {sum(self.expected_demand.values())/total_drones:.2f} 订单/天" if total_drones > 0 else "    平均每无人机服务: N/A")

        print("\n  📝 模型说明:")
        print("    第一阶段决策：储物柜选址和无人机配置（已确定）")
        print("    第二阶段决策：根据实际需求场景动态优化客户分配和配送")
        print("    成本为考虑需求不确定性后的期望日均成本")

        # 详细的客户分配方案分析已移除以减少冗余输出

        if DRL_AVAILABLE:
            viz_solution_saa = {
                'selected_lockers': {j:True for j in selected_lockers_print},
                'customer_assignments_primary': {},  # 空的，因为客户分配在第二阶段
                'unassigned_customers_by_u': list(self.customers),  # 所有客户都在第二阶段分配
                'drone_allocations': {j: round(n_star_final.get(j,0)) for j in selected_lockers_print},
                'objective_value': saa_solution_dict.get('objective_value_k_prime_estimate', 0.0)
            }
            print("\n  显示SAA最终解的可视化:")
            print("  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）")
            print("  ↳ 客户分配将在第二阶段根据实际需求场景动态优化")
            print("  ↳ 上述成本是考虑需求不确定性后的期望值")
            self.visualize_solution(viz_solution_saa)

    def _print_detailed_customer_assignment_analysis(self, saa_solution_dict: Dict, selected_lockers: List[int]):
        """
        输出详细的客户分配方案分析
        """
        print(f"\n  详细客户分配方案分析:")
        print(f"  ============================================================")

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        # 使用第一阶段分配（基于期望需求）进行分析
        first_stage_assignment = self._solve_first_stage_assignment_for_analysis(y_star, n_star, selected_lockers)

        if first_stage_assignment:
            print(f"  基于期望需求的第一阶段客户分配:")

            # 按储物柜分组显示分配
            for j in selected_lockers:
                assigned_customers = []
                total_assigned_demand = 0

                for i in self.customers:
                    assigned_qty = first_stage_assignment.get((i, j), 0)
                    if assigned_qty > 0.1:  # 避免浮点数精度问题
                        assigned_customers.append((i, assigned_qty))
                        total_assigned_demand += assigned_qty

                print(f"    储物柜 {j}:")
                print(f"      总分配需求: {total_assigned_demand:.1f}")
                print(f"      容量利用率: {total_assigned_demand/self.Q_locker_capacity[j]*100:.1f}%")
                print(f"      分配的客户: {len(assigned_customers)} 个")

                if assigned_customers:
                    print(f"      详细分配:")
                    for customer_id, qty in sorted(assigned_customers):
                        expected_demand = self.expected_demand[customer_id]
                        allocation_rate = qty / expected_demand * 100 if expected_demand > 0 else 0
                        distance = self.distance.get((customer_id, j), 0)
                        print(f"        客户 {customer_id}: {qty:.1f}/{expected_demand:.1f} ({allocation_rate:.1f}%), 距离: {distance:.1f}km")
                else:
                    print(f"      无客户分配")
                print()

            # 分析未分配的客户
            unassigned_customers = []
            for i in self.customers:
                total_assigned = sum(first_stage_assignment.get((i, j), 0) for j in selected_lockers)
                expected_demand = self.expected_demand[i]
                if total_assigned < expected_demand - 0.1:
                    shortage = expected_demand - total_assigned
                    unassigned_customers.append((i, shortage, expected_demand))

            if unassigned_customers:
                print(f"  未完全分配的客户 ({len(unassigned_customers)} 个):")
                total_unassigned_demand = 0
                for customer_id, shortage, expected_demand in sorted(unassigned_customers):
                    print(f"    客户 {customer_id}: 短缺 {shortage:.1f}/{expected_demand:.1f} ({shortage/expected_demand*100:.1f}%)")
                    total_unassigned_demand += shortage
                print(f"  总未分配需求: {total_unassigned_demand:.1f}")
                print(f"  未分配比例: {total_unassigned_demand/sum(self.expected_demand.values())*100:.1f}%")
            else:
                print(f"  ✓ 所有客户的期望需求都已完全分配")

        else:
            print(f"  ⚠ 无法获取第一阶段客户分配信息")

    def _solve_first_stage_assignment_for_analysis(self, y_star: Dict, n_star: Dict, selected_lockers: List[int]) -> Dict:
        """
        为分析目的求解第一阶段的客户分配问题（基于期望需求）
        """
        try:
            import gurobipy as gp
            from gurobipy import GRB

            model_fs = gp.Model("FirstStageAssignmentAnalysis")
            model_fs.setParam('OutputFlag', 0)
            model_fs.setParam('Threads', 1)

            # 决策变量
            x_fs = {}
            for i in self.customers:
                for j in selected_lockers:
                    x_fs[i, j] = model_fs.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"x_fs_{i}_{j}")

            # 目标函数：最小化运输成本和惩罚成本
            transport_cost_fs = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), self.BIG_M) * x_fs[i, j]
                for i in self.customers for j in selected_lockers
            )
            penalty_cost_fs = gp.quicksum(
                self.penalty_cost_unassigned * (self.expected_demand[i] - gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers))
                for i in self.customers
            )
            model_fs.setObjective(transport_cost_fs + penalty_cost_fs, GRB.MINIMIZE)

            # 约束
            for i in self.customers:
                # 分配量不超过期望需求
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers) <= self.expected_demand[i])
                for j in selected_lockers:
                    # 飞行距离限制
                    if (i, j) in self.distance:
                        model_fs.addConstr(2 * self.distance[i, j] * x_fs[i, j] <= self.max_flight_distance * self.expected_demand[i])

            for j in selected_lockers:
                # 无人机服务能力约束
                total_hours_needed_j = gp.quicksum(
                    x_fs.get((i, j), 0) * ((2 * self.distance.get((i, j), self.BIG_M) / self.drone_speed) + self.loading_time)
                    for i in self.customers
                )
                model_fs.addConstr(total_hours_needed_j <= n_star.get(j, 0) * self.H_drone_working_hours_per_day)
                # 储物柜容量约束
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for i in self.customers) <= self.Q_locker_capacity[j])

            model_fs.optimize()

            if model_fs.status == GRB.OPTIMAL:
                assignment = {}
                for i in self.customers:
                    for j in selected_lockers:
                        if (i, j) in x_fs:
                            assignment[i, j] = x_fs[i, j].X
                del model_fs
                return assignment
            else:
                del model_fs
                return None

        except Exception as e:
            print(f"  第一阶段分配分析失败: {str(e)}")
            return None

    def _print_second_stage_solver_analysis(self):
        """
        输出第二阶段求解器性能分析
        """
        stats = self.second_stage_solver_stats

        if stats['total_scenarios'] == 0:
            print(f"\n  第二阶段求解器性能分析:")
            print(f"    无求解统计数据")
            return

        print(f"\n  第二阶段求解器性能分析:")
        print(f"  ============================================================")
        print(f"  求解器配置: {'精确求解器 (Gurobi MIP)' if USE_EXACT_SECOND_STAGE_SOLVER else '启发式算法 (FastAssignmentSolver)'}")
        print(f"  总验证场景数: {stats['total_scenarios']}")

        if USE_EXACT_SECOND_STAGE_SOLVER:
            # 精确求解器模式的统计
            success_rate = stats['exact_solver_success'] / stats['total_scenarios'] * 100
            failure_rate = stats['exact_solver_failure'] / stats['total_scenarios'] * 100

            print(f"  精确求解器成功: {stats['exact_solver_success']} ({success_rate:.1f}%)")
            print(f"  精确求解器失败: {stats['exact_solver_failure']} ({failure_rate:.1f}%)")

            if stats['exact_solve_times']:
                avg_exact_time = sum(stats['exact_solve_times']) / len(stats['exact_solve_times'])
                print(f"  平均精确求解时间: {avg_exact_time:.3f} 秒")
                print(f"  精确求解时间范围: {min(stats['exact_solve_times']):.3f} - {max(stats['exact_solve_times']):.3f} 秒")

            if stats['heuristic_solve_times']:
                avg_heuristic_time = sum(stats['heuristic_solve_times']) / len(stats['heuristic_solve_times'])
                print(f"  回退启发式求解: {len(stats['heuristic_solve_times'])} 次")
                print(f"  平均启发式求解时间: {avg_heuristic_time:.3f} 秒")
        else:
            # 启发式求解器模式的统计
            print(f"  启发式求解: {stats['heuristic_solver_used']} (100.0%)")

            if stats['heuristic_solve_times']:
                avg_heuristic_time = sum(stats['heuristic_solve_times']) / len(stats['heuristic_solve_times'])
                print(f"  平均启发式求解时间: {avg_heuristic_time:.3f} 秒")
                print(f"  启发式求解时间范围: {min(stats['heuristic_solve_times']):.3f} - {max(stats['heuristic_solve_times']):.3f} 秒")

        # 性能对比提示
        if USE_EXACT_SECOND_STAGE_SOLVER:
            if stats['exact_solver_failure'] > 0:
                print(f"  ⚠ 注意: {stats['exact_solver_failure']} 个场景精确求解失败，可能影响解质量")
            else:
                print(f"  ✓ 所有场景都使用精确求解器，解质量最优")
        else:
            print(f"  ⚠ 使用启发式算法可能导致次优解，建议与精确求解器结果对比")

    def visualize_solution(self, solution: Dict):
        if not DRL_AVAILABLE: return
        if not solution: print("无解决方案可供可视化。"); return
        temp_solution_for_viz = solution.copy()
        primary_assignments = solution.get('customer_assignments_primary', {})

        # 修改：支持多储物柜分配的可视化
        # 直接传递完整的储物柜列表给可视化函数
        customer_assignments_viz = {}
        for cust, lockers in primary_assignments.items():
            if lockers: # 如果有分配的储物柜
                # 直接传递储物柜列表，visualization.py会自动处理多储物柜连线
                customer_assignments_viz[cust] = lockers

        temp_solution_for_viz['customer_assignments'] = customer_assignments_viz


        customer_service_modes = {}
        for customer_id in customer_assignments_viz: # 使用调整后的键
            customer_service_modes[customer_id] = 0
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # unassigned_customers 可能需要从 solution['unassigned_customers_by_u'] 获取
        if 'unassigned_customers_by_u' in solution:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']


        # 移除可能引起混淆的旧键
        if 'customer_assignments_primary' in temp_solution_for_viz: del temp_solution_for_viz['customer_assignments_primary']
        if 'unassigned_customers_by_u' in temp_solution_for_viz and 'unassigned_customers' in temp_solution_for_viz :
             if temp_solution_for_viz['unassigned_customers_by_u'] == temp_solution_for_viz['unassigned_customers']:
                 del temp_solution_for_viz['unassigned_customers_by_u']


        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="SAA 无人机配送网络规划 (基于期望需求分配)"
        )
        if plt_fig:
            import matplotlib.pyplot as plt
            plt.show(block=False) # 使用 block=False 避免阻塞后续代码，如果需要交互则去掉
            plt.pause(1) # 暂停一下，确保图像显示

    def _get_batch_cache_key(self, batch_active_lockers_info):
        """
        【新增】生成批量求解缓存键
        """
        # 为整个批量求解生成缓存键
        batch_items = []
        for scenario in batch_active_lockers_info:
            scenario_key = self._get_drl_cache_key(scenario)
            batch_items.append(scenario_key)
        # 排序确保相同内容的不同顺序产生相同的键
        return tuple(sorted(batch_items))

    def _get_drl_cache_key(self, active_lockers_info):
        """
        【新增】生成DRL缓存键
        """
        # 创建一个基于储物柜ID和需求的缓存键
        items = []
        for locker_id in sorted(active_lockers_info.keys()):
            info = active_lockers_info[locker_id]
            demand = info.get('demand', 0)
            # 将需求四舍五入到整数，减少缓存键的数量
            demand_rounded = round(demand)
            items.append((locker_id, demand_rounded))
        return tuple(items)

# --- 主程序入口 ---
if __name__ == "__main__":
    start_time_main = time.time()
    print(f"设置全局随机种子: {RANDOM_SEED}")
    if DRL_AVAILABLE:
        set_drl_log_level(logging.ERROR)
        print("DRL日志级别已设置为ERROR（屏蔽WARNING信息）")
    else:
        print("DRL模块不可用，相关功能将跳过。")


    print("\n创建随机需求的示例数据 (使用期望需求)...")
    print("=" * 60)
    print("成本计算方法改进：统一时间单位")
    print("=" * 60)
    print("修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用")
    print("修正后方案：使用资本回收因子将所有固定成本统一为日成本单位")
    print("优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差")
    print("=" * 60)

    stochastic_data_instance = create_deterministic_example_instance(
        demand_level="low",
        locker_cost_level="medium",
        drone_cost_level="medium",
        drone_transport_cost_level="medium",
        use_generated_distances=True,
        num_customers=15, # 与g_i.py保持一致
        num_sites=3,    #  与g_i.py保持一致z
        use_kmeans_clustering=True,
        random_seed=RANDOM_SEED,
        # 年化成本参数
        annual_interest_rate=0.04,    # 4% 年利率
        equipment_life_years=10,      # 10年设备生命周期
        operating_days_per_year=365   # 365天年运营天数
    )
    stochastic_data_instance['expected_demand'] = stochastic_data_instance.pop('demand_deterministic')
    print("随机需求数据 (期望值) 已创建。")

    print("\n所有客户期望需求 (λᵢ_bar):")
    for customer_id_main, demand_val_main in stochastic_data_instance['expected_demand'].items():
        print(f"  客户 {customer_id_main}: {demand_val_main} 订单/天 (期望)")
    total_expected_demand_val_main = sum(stochastic_data_instance['expected_demand'].values())
    print(f"总期望需求: {total_expected_demand_val_main} 订单/天")

    # 调试信息已移除以减少冗余输出


    print("\n" + "=" * 60 + "\n求解带随机需求的无人机配送网络设计问题 (SAA)\n" + "=" * 60)
    solve_start_time_saa_main = time.time()

    optimizer_saa_main = StochasticDroneDeliveryOptimizerSAA()
    optimizer_saa_main.set_parameters(**stochastic_data_instance)

    # 使用ALNS方法求解SAA问题
    print("使用ALNS方法求解SAA问题...")
    final_saa_solution = optimizer_saa_main.solve_saa_with_alns(
        time_limit_per_replication=120  # ALNS时间限制
    )

    solve_time_saa_main = time.time() - solve_start_time_saa_main

    if final_saa_solution:
        optimizer_saa_main._print_saa_solution(final_saa_solution)
        print(f"\nSAA模型求解总耗时: {solve_time_saa_main:.2f} 秒")
    else:
        print("\n⚠ SAA未能找到可行解或优化失败。")

    total_time_main = time.time() - start_time_main
    print(f"\n总运行时间: {total_time_main:.2f} 秒")
    if DRL_AVAILABLE:
        print("测试完成。如果图像窗口仍然打开，请手动关闭。")
