import gurobipy as gp
from gurobipy import GRB
import numpy as np
from typing import Dict, List, Tuple
import random
import time
import math

from drl import DRL_CVRP_Solver, set_drl_log_level
import logging
from clustering import generate_locker_sites_with_kmeans
from visualization import visualize_solution

# 设置随机种子以确保结果可重现
RANDOM_SEED = 603
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)


class DeterministicDroneDeliveryOptimizer:

    def __init__(self):
        # 模型相关
        self.model = None
        # 决策变量
        self.x_qty = {}  # xᵢⱼ: 客户i分配给储物柜j的需求量 (Z+)
        self.z = {}  # zᵢⱼ: 客户i是否分配给储物柜j (二进制, 如果xᵢⱼ > 0, 则zᵢⱼ = 1)
        self.y = {}  # yⱼ: 是否在站点j开设储物柜 (二进制)
        self.n = {}  # nⱼ: 储物柜j部署的无人机数量 (Z+)
        self.u = {}  # uᵢ: 客户i是否未被分配 (二进制, uᵢ = 1 当且仅当客户i没有被分配给任何储物柜; uᵢ = 0 当且仅当客户i至少被一个储物柜服务)

        # 基础数据 - 由set_parameters方法设置
        self.customers = []  # I: 客户集合
        self.sites = []  # J: 候选储物柜站点集合
        self.demand_deterministic = {}  # λᵢ: 客户i的需求 (订单/天)
        self.distance = {}  # dᵢⱼ: 客户i到储物柜站点j的距离
        self.locker_fixed_cost = {}  # cˡⱼ: 在站点j开设储物柜的固定成本
        self.Q_locker_capacity = {}  # Qⱼ: 储物柜j的最大服务能力 (订单/天)

        # 系统参数 - 由set_parameters方法设置
        self.drone_speed = None  # v: 无人机飞行速度 (公里/小时)
        self.loading_time = None  # t₀: 无人机装载时间 (小时)
        self.max_flight_distance = None  # d_max: 无人机最大飞行距离 (公里, 来自约束5)
        self.transport_unit_cost = None  # cᵈ: 无人机单位运输成本 (元/公里)
        self.drone_cost = None  # pᵈ: 单架无人机购置成本
        self.H_drone_working_hours_per_day = None  # H: 每架无人机每日工作小时数
        self.penalty_cost_unassigned = None  # cᴾ: 未分配客户单位需求的惩罚成本

        # 坐标信息 - 可选，用于可视化
        self.customer_coords = {}
        self.site_coords = {}

        # DRL求解器参数 - 由set_parameters方法设置
        self.depot_coord = None  # 仓库坐标
        self.truck_capacity = None  # 卡车容量
        self.truck_fixed_cost = None  # 卡车固定成本
        self.truck_km_cost = None  # 卡车每公里成本

        # DRL求解器实例
        self._drl_solver = None
        self._drl_solver_params = None
        self._drl_solver_no_plots = None
        self._drl_solver_no_plots_params = None

        # 用于Big-M约束的大数
        self.BIG_M = 1e6  # 一个足够大的常数

    def set_parameters(self,
                       customers: List,
                       sites: List,
                       demand_deterministic: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,  # 这是 cᴾ
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        # 设置必需参数
        self.customers = customers
        self.sites = sites
        self.demand_deterministic = demand_deterministic
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned  # cᴾ
        self.Q_locker_capacity = Q_locker_capacity

        # 设置可选参数
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        # 设置Big-M的值，可以根据最大需求量动态调整以获得更紧的界
        if self.demand_deterministic:
            max_demand_val = max(self.demand_deterministic.values()) if self.demand_deterministic else 0
            self.BIG_M = max_demand_val if max_demand_val > 0 else 1e6  # 确保BIG_M至少为1或更大
        else:
            self.BIG_M = 1e6

    def _get_drl_solver(self, make_plots: bool = True):
        """获取或创建DRL求解器实例"""
        current_params = (self.depot_coord, self.truck_capacity, self.truck_fixed_cost, self.truck_km_cost)
        if make_plots:
            if self._drl_solver is None or self._drl_solver_params != current_params:
                set_drl_log_level(logging.WARNING)  # 设置DRL日志级别
                self._drl_solver = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=True
                )
                self._drl_solver_params = current_params
                print(f"  [DRL] 初始化DRL求解器(生成图片): 仓库{self.depot_coord}, 容量{self.truck_capacity}")
            return self._drl_solver
        else:
            if self._drl_solver_no_plots is None or self._drl_solver_no_plots_params != current_params:
                set_drl_log_level(logging.WARNING)  # 设置DRL日志级别
                self._drl_solver_no_plots = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=False
                )
                self._drl_solver_no_plots_params = current_params
                print(f"  [DRL] 初始化DRL求解器(不生成图片): 仓库{self.depot_coord}, 容量{self.truck_capacity}")
            return self._drl_solver_no_plots

    def calculate_truck_cost(self,
                             selected_lockers: List[int],
                             x_qty_solution_values: Dict[Tuple[int, int], float],
                             make_plots: bool = True,
                             return_route_info: bool = False):
        """
        计算给定选址和分配方案下的卡车运输成本
        Args:
            selected_lockers: 选中的储物柜列表
            x_qty_solution_values: 字典 {(customer_id, locker_id): quantity} 包含当前解中x_qty的值
            make_plots: 是否生成路线图
            return_route_info: 是否返回路线信息
        """
        if not selected_lockers:  # 如果没有选中的储物柜，则卡车成本为0
            return (0.0, None) if return_route_info else 0.0

        # print(f"  [calculate_truck_cost] 计算卡车成本: {len(selected_lockers)}个储物柜")
        try:
            drl_solver = self._get_drl_solver(make_plots=make_plots)

            # 计算每个选中储物柜的总需求量
            locker_total_demands = {locker_id: 0.0 for locker_id in selected_lockers}
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:  # 仅考虑分配给已选储物柜的需求
                    locker_total_demands[locker_id] += quantity

            # 构建活跃储物柜信息 (有需求且有坐标的储物柜)
            active_lockers_info = {}
            for locker_id in selected_lockers:
                demand_at_locker = locker_total_demands.get(locker_id, 0)
                if demand_at_locker > 1e-6:
                    if locker_id in self.site_coords:
                        # 检查储物柜需求是否超过单辆卡车容量 (DRL求解器内部也会处理，但提前检查可以避免无效调用)
                        if self.truck_capacity is not None and demand_at_locker > self.truck_capacity:
                            # print(f"    [calculate_truck_cost] 警告: 储物柜 {locker_id} 需求 {demand_at_locker} 超过单卡车容量 {self.truck_capacity}。DRL将尝试用多辆卡车或多趟。")
                            # DRL求解器应该能处理这种情况，所以不直接返回惩罚值。
                            pass  # DRL会处理
                        active_lockers_info[locker_id] = {
                            'coord': self.site_coords[locker_id],
                            'demand': demand_at_locker
                        }

            if active_lockers_info:  # 如果有活跃的储物柜需要服务
                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info, return_route_info=True)
                # print(f"    [calculate_truck_cost] DRL计算的卡车成本: {truck_cost:.2f}")
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            else:  # 没有活跃储物柜
                # print(f"    [calculate_truck_cost] 无活跃储物柜，卡车成本为0")
                return (0.0, None) if return_route_info else 0.0

        except Exception as e:  # DRL求解失败的回退方案
            print(f"  [calculate_truck_cost] DRL求解失败: {str(e)}, 使用简化估算")
            total_demand_overall = 0
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    total_demand_overall += quantity

            num_trucks = math.ceil(total_demand_overall / self.truck_capacity) if self.truck_capacity > 0 else 1
            simplified_cost = num_trucks * self.truck_fixed_cost  # 简化成本只考虑固定成本
            # print(f"    [calculate_truck_cost] 简化估算的卡车成本: {simplified_cost:.2f}")
            return (simplified_cost, None) if return_route_info else simplified_cost

    def _get_current_solution(self, x_qty_vals, z_vals, y_vals, u_vals):
        """从当前变量值中提取解决方案的结构化信息"""
        selected_lockers = [j for j in self.sites if y_vals[j] > 0.5]  # 选中的储物柜

        # customer_assignments_primary: 客户i分配到的储物柜列表 (基于zᵢⱼ=1, 现在一个客户可以分配给多个储物柜)
        customer_assignments_primary = {}
        # customer_assignment_quantities: 客户i分配给储物柜j的具体数量 (基于xᵢⱼ > 0)
        customer_assignment_quantities = {i: {} for i in self.customers}

        unassigned_customers_by_u = [i for i in self.customers if u_vals.get(i, 0) > 0.5]  # 根据uᵢ判断未分配的客户

        for i in self.customers:
            assigned_lockers_for_i = []  # 客户i分配到的储物柜列表
            for j in self.sites:  # 遍历所有可能的储物柜
                if z_vals.get((i, j), 0) > 0.5:  # 如果zᵢⱼ=1
                    assigned_lockers_for_i.append(j)

                qty = x_qty_vals.get((i, j), 0)  # 获取实际分配量
                if qty > 0.5:  # 使用0.5是为了处理浮点数精度问题 (Gurobi整数变量解也可能略有偏差)
                    customer_assignment_quantities[i][j] = round(qty)  # 四舍五入到整数

            if assigned_lockers_for_i:  # 如果客户有分配的储物柜
                customer_assignments_primary[i] = assigned_lockers_for_i  # 记录分配的储物柜列表

        # customer_service_modes 字段在当前模型下意义不大，因为分配了就是无人机
        customer_service_modes = {}
        return selected_lockers, customer_assignments_primary, customer_assignment_quantities, unassigned_customers_by_u, customer_service_modes

    def build_model(self):
        """构建Gurobi优化模型"""
        self.model = gp.Model("DroneLockerOptimization_ModelAligned")  # 模型名称
        self._create_variables()  # 创建决策变量
        self._set_objective()  # 设置目标函数
        self._add_constraints()  # 添加约束条件
        self.model.update()  # 更新模型以确保所有更改生效
        print(f"模型构建完成 - 统计信息: {self.model.NumVars}变量, {self.model.NumConstrs}约束")

    def _create_variables(self):
        """创建模型中的所有决策变量"""
        # xᵢⱼ: 客户i分配给储物柜j的需求量 (非负整数)
        for i in self.customers:
            for j in self.sites:
                self.x_qty[i, j] = self.model.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_qty_{i}_{j}")

        # zᵢⱼ: 客户i是否分配给储物柜j (二进制)
        for i in self.customers:
            for j in self.sites:
                self.z[i, j] = self.model.addVar(vtype=GRB.BINARY, name=f"z_{i}_{j}")

        # yⱼ: 是否在站点j开设储物柜 (二进制)
        for j in self.sites:
            self.y[j] = self.model.addVar(vtype=GRB.BINARY, name=f"y_{j}")

        # nⱼ: 储物柜j部署的无人机数量 (非负整数)
        for j in self.sites:
            self.n[j] = self.model.addVar(vtype=GRB.INTEGER, lb=0, name=f"n_{j}")

        # uᵢ: 客户i是否未被分配 (二进制)
        for i in self.customers:
            self.u[i] = self.model.addVar(vtype=GRB.BINARY, name=f"u_{i}")

    def _set_objective(self):
        """设置模型的目标函数"""
        # 目标函数组成部分:
        # 1. 储物柜开设固定成本: Σ cˡⱼ * yⱼ
        locker_cost = gp.quicksum(self.locker_fixed_cost[j] * self.y[j] for j in self.sites)

        # 2. 无人机往返运输成本: Σᵢ Σⱼ 2 * cᵈ * dᵢⱼ * xᵢⱼ (xᵢⱼ 是实际分配量 self.x_qty[i,j])
        transport_cost = gp.quicksum(
            2 * self.transport_unit_cost * self.distance[i, j] * self.x_qty[i, j]
            for i in self.customers for j in self.sites if (i, j) in self.distance
        )

        # 3. 无人机购置成本: Σ pᵈ * nⱼ
        drone_deployment_cost = gp.quicksum(self.drone_cost * self.n[j] for j in self.sites)

        # 4. 未分配客户需求的惩罚成本: Σᵢ cᴾ * (λᵢ - Σⱼ xᵢⱼ)
        unassigned_demand_penalty = gp.quicksum(
            self.penalty_cost_unassigned *
            (self.demand_deterministic[i] - gp.quicksum(self.x_qty[i, k] for k in self.sites if (i, k) in self.x_qty))
            for i in self.customers
        )

        # 5. 卡车运输成本 (通过回调函数动态计算)
        truck_cost_var = self.model.addVar(lb=0.0, name="truck_cost")  # 卡车成本变量
        self.model._truck_cost_var = truck_cost_var  # 存储变量以便回调函数访问
        self.model.addConstr(truck_cost_var >= 0, name="initial_truck_cost_lower_bound")  # 初始下界

        # 总成本 = 各部分成本之和
        total_cost = (locker_cost + transport_cost + drone_deployment_cost +
                      unassigned_demand_penalty + truck_cost_var)
        self.model.setObjective(total_cost, GRB.MINIMIZE)  # 最小化总成本

        # 初始化回调函数相关的缓存和计数器
        self.model._callback_count = 0
        self.model._solution_cache = {}  # 缓存解的卡车成本
        self.model._last_truck_cost = 0.0  # 记录上一次计算的卡车成本

        # 定义卡车成本回调函数 (Lazy Constraint Callback)
        def truck_cost_callback_for_x_qty(model, where):
            if where == GRB.Callback.MIPSOL:  # 当找到新的整数可行解时调用
                model._callback_count += 1

                # 获取当前整数解中的变量值
                x_qty_sol_vals = model.cbGetSolution(self.x_qty)
                y_sol_vals = model.cbGetSolution(self.y)

                selected_lockers_cb = [j_site for j_site in self.sites if y_sol_vals[j_site] > 0.5]  # 当前解选中的储物柜

                # 创建用于缓存的解的哈希键 (基于选中的储物柜和相关的x_qty值)
                # 只考虑分配量大于0.5 (避免浮点问题) 且分配给已选储物柜的x_qty值
                relevant_x_qty_tuple = tuple(sorted(
                    ((i_cust, j_lock), val)
                    for (i_cust, j_lock), val in x_qty_sol_vals.items()
                    if val > 0.5 and j_lock in selected_lockers_cb
                ))
                solution_key = (tuple(sorted(selected_lockers_cb)), relevant_x_qty_tuple)

                current_truck_cost_cb = 0.0
                if solution_key in model._solution_cache:  # 检查缓存
                    current_truck_cost_cb = model._solution_cache[solution_key]
                    # print(f"  [回调 {model._callback_count}] 使用缓存的卡车成本: {current_truck_cost_cb:.2f}")
                else:
                    # print(f"  [回调 {model._callback_count}] 计算新解的确定性卡车成本...")
                    # 调用calculate_truck_cost计算当前解的卡车成本 (回调中不生成图片)
                    current_truck_cost_cb = self.calculate_truck_cost(selected_lockers_cb, x_qty_sol_vals,
                                                                      make_plots=False)
                    model._solution_cache[solution_key] = current_truck_cost_cb  # 缓存结果
                    # print(f"  [回调 {model._callback_count}] 新计算的卡车成本: {current_truck_cost_cb:.2f}")

                # 添加 Lazy Constraint: truck_cost_var >= (刚计算得到的实际卡车成本)
                # 这会切掉当前解，除非目标函数中的truck_cost_var已经至少这么大
                model.cbLazy(model._truck_cost_var >= current_truck_cost_cb)
                model._last_truck_cost = current_truck_cost_cb  # 更新记录
                # print(f"  [回调 {model._callback_count}] 添加Lazy约束: truck_cost >= {current_truck_cost_cb:.2f}")

        self.model._callback = truck_cost_callback_for_x_qty  # 将回调函数注册到模型

    def _add_constraints(self):
        """添加模型的所有约束条件"""
        # 约束1: 至少开设一个储物柜: Σⱼ yⱼ ≥ 1
        self.model.addConstr(gp.quicksum(self.y[j] for j in self.sites) >= 1, "C1_AtLeastOneLocker")

        # 约束2 : 客户分配给储物柜的总需求不超过客户的总需求: Σⱼ xᵢⱼ ≤ λᵢ, ∀i
        for i in self.customers:
            self.model.addConstr(
                gp.quicksum(self.x_qty[i, j] for j in self.sites if (i, j) in self.x_qty) <= self.demand_deterministic[
                    i],
                name=f"C2_TotalAssignedQtyLimit_{i}"
            )


        # zᵢⱼ = 1 当且仅当 xᵢⱼ > 0
        for i in self.customers:
            for j in self.sites:
                # 如果 xᵢⱼ > 0, 则 zᵢⱼ 必须为 1.  实现方式: xᵢⱼ ≤ M*zᵢⱼ
                self.model.addConstr(self.x_qty[i, j] <= self.demand_deterministic[i] * self.z[i, j],
                                     name=f"Link_x_z_upper_{i}_{j}")
                # 如果 zᵢⱼ = 1, 则 xᵢⱼ 必须 > 0. 实现方式: xᵢⱼ ≥ zᵢⱼ
                self.model.addConstr(self.x_qty[i, j] >= self.z[i, j], name=f"Link_x_z_lower_{i}_{j}")

        # # 新的u定义约束：允许一个客户由多个储物柜服务
        # # Σⱼ zᵢⱼ ≤ M * (1 - uᵢ), ∀i：如果uᵢ=1(未分配)，则所有zᵢⱼ必须为0；
        # # Σⱼ zᵢⱼ ≥ (1 - uᵢ), ∀i：如果uᵢ=0(已分配)，则至少有一个zᵢⱼ为1；
        # for i in self.customers:
        #     sum_z_i = gp.quicksum(self.z[i, j] for j in self.sites)
        #     # 约束1: Σⱼ zᵢⱼ ≤ M * (1 - uᵢ)
        #     self.model.addConstr(sum_z_i <= len(self.sites) * (1 - self.u[i]),
        #                          name=f"CustomerAssignmentUpperBound_{i}")
        #     # 约束2: Σⱼ zᵢⱼ ≥ (1 - uᵢ)
        #     self.model.addConstr(sum_z_i >= (1 - self.u[i]),
        #                          name=f"CustomerAssignmentLowerBound_{i}")

        # 约束3: 储物柜开设约束 (只有开设的储物柜才能分配客户): zᵢⱼ ≤ yⱼ, ∀i,j
        for i in self.customers:
            for j in self.sites:
                self.model.addConstr(self.z[i, j] <= self.y[j], name=f"C3_AssignIfOpen_{i}_{j}")

        # 约束4 : 储物柜服务约束 (开设的储物柜至少服务一个客户): Σᵢ zᵢⱼ ≥ yⱼ, ∀j
        for j in self.sites:
            self.model.addConstr(gp.quicksum(self.z[i, j] for i in self.customers) >= self.y[j],
                                 name=f"C4_OpenLockerServesOne_{j}")

        # 约束5 : 客户点分配受无人机飞行距离限制: 2*dᵢⱼ*zᵢⱼ ≤ d_max, ∀i ∈ I, j ∈ J
        for i in self.customers:
            for j in self.sites:
                if (i, j) in self.distance:
                    self.model.addConstr(
                        2 * self.distance[i, j] * self.z[i, j] <= self.max_flight_distance,
                        name=f"C5_FlightDistanceLimit_{i}_{j}"
                    )

        # 约束6 : 无人机部署约束 (开设的储物柜至少配备一架无人机): nⱼ ≥ yⱼ, ∀j
        for j in self.sites:
            self.model.addConstr(self.n[j] >= self.y[j], name=f"C6_MinOneDroneIfOpen_{j}")

        # 约束7 : 无人机服务能力约束: Σᵢ xᵢⱼ * (2dᵢⱼ/v + t₀) ≤ nⱼH, ∀j
        # 服务能力取决于实际运送的需求量 xᵢⱼ
        for j in self.sites:
            total_drone_hours_needed_j = gp.quicksum(
                self.x_qty[i, j] * ((2 * self.distance[i, j] / self.drone_speed) + self.loading_time)
                for i in self.customers if (i, j) in self.distance
            )
            drone_hours_supplied_j = self.n[j] * self.H_drone_working_hours_per_day
            self.model.addConstr(total_drone_hours_needed_j <= drone_hours_supplied_j,
                                 name=f"C7_DroneServiceCapacity_{j}")

        # 约束8 : 储物柜最大服务能力约束: Σᵢ xᵢⱼ ≤ Qⱼyⱼ, ∀j
        # 储物柜容量也与实际处理的需求量 xᵢⱼ 相关
        for j in self.sites:
            self.model.addConstr(
                gp.quicksum(self.x_qty[i, j] for i in self.customers if (i, j) in self.x_qty) <= self.Q_locker_capacity[
                    j] * self.y[j],
                name=f"C8_LockerOrderCapacity_{j}"
            )

    def solve(self, time_limit: int = 3600, mip_gap: float = 0.01):
        """求解构建好的Gurobi模型"""
        if self.model is None: raise ValueError("模型尚未构建 (Model not built).")
        print("\n配置Gurobi求解器参数...")
        self.model.setParam('OutputFlag', 1)  # 显示Gurobi输出
        self.model.setParam('LogToConsole', 1)  # 将日志输出到控制台
        logging.getLogger('gurobipy').setLevel(logging.WARNING)  # 减少Gurobi的冗余日志

        self.model.setParam('TimeLimit', time_limit)  # 时间限制
        self.model.setParam('MIPGap', mip_gap)  # MIP Gap
        self.model.setParam('MIPFocus', 2)  # MIP焦点 (1: 更侧重于找到可行解  0：平衡  2：更侧重于找到最优解)
        self.model.setParam('Threads', 0)  # 使用所有可用线程
        self.model.setParam('NodefileStart', 0.5)  # 当内存使用达到0.5GB时开始使用节点文件
        self.model.setParam('DisplayInterval', 10)  # 每10秒更新一次日志
        self.model.setParam('LogFile', 'gurobi_model_log.txt')  # 日志文件名
        self.model.setParam('LazyConstraints', 1)  # 启用Lazy Constraints (用于回调函数)

        print("开始求解模型...")
        self.model.optimize(self.model._callback)  # 传入回调函数进行优化

        print(f"优化完成，状态: {self.model.status}")
        if hasattr(self.model, '_callback_count'):  # 显示回调函数统计信息
            print(f"\n回调函数统计:")
            print(f"  总调用次数: {self.model._callback_count}")
            print(f"  缓存命中数 (不同解): {len(self.model._solution_cache)}")  # 缓存中的条目数代表不同解的数量
            print(f"  最后一次计算的卡车成本: {self.model._last_truck_cost:.2f}")

        # 根据求解状态打印结果
        if self.model.status == GRB.OPTIMAL:
            print("找到最优解!")
            self._print_solution()
        elif self.model.status == GRB.TIME_LIMIT:
            print("求解时间到达限制，返回当前最佳解。")
            if self.model.SolCount > 0:
                self._print_solution()
            else:
                print("时间限制内未找到可行解。")
        else:
            print(f"优化失败，状态码: {self.model.status}")
            if self.model.status == GRB.INFEASIBLE:  # 如果模型不可行
                print("模型不可行，尝试计算不可行子集 (IIS)...")
                self.model.computeIIS()  # 计算IIS
                self.model.write("model_infeasible.ilp")  # 将IIS写入文件
                print("IIS已写入 model_infeasible.ilp")
            if self.model.SolCount > 0:  # 如果有解，即使不是最优
                print("尽管未达到最优或满足特定终止条件，但找到了可行解:")
                self._print_solution()

    def _print_solution(self):
        """打印优化结果的详细信息"""
        if self.model.SolCount == 0: print("模型未找到解."); return

        # 获取解中的变量值
        x_qty_sol = {(i, j): self.x_qty[i, j].x for i in self.customers for j in self.sites if (i, j) in self.x_qty}
        z_sol = {(i, j): self.z[i, j].x for i in self.customers for j in self.sites if (i, j) in self.z}
        y_sol = {j: self.y[j].x for j in self.sites}
        u_sol = {i: self.u[i].x for i in self.customers}
        n_sol = {j: self.n[j].x for j in self.sites}

        selected_lockers_final = [j for j in self.sites if y_sol[j] > 0.5]  # 最终选中的储物柜

        # 重新获取和组织客户分配信息用于打印
        _, customer_assignments_primary_final, customer_assignment_quantities_final, unassigned_customers_by_u_final, _ = \
            self._get_current_solution(x_qty_sol, z_sol, y_sol, u_sol)

        print("  计算最终解的卡车成本 (可能生成路线图)...")
        # 为最终解计算卡车成本，并获取路线信息 (允许生成图片)
        final_truck_cost, route_info_final = self.calculate_truck_cost(
            selected_lockers_final,
            x_qty_sol,
            make_plots=True,
            return_route_info=True
        )

        # 计算目标函数各组成部分的最终值
        locker_cost_val = sum(self.locker_fixed_cost[j] * y_sol[j] for j in self.sites if y_sol[j] > 0.5)
        transport_cost_val = sum(
            2 * self.transport_unit_cost * self.distance[i, j] * x_qty_sol.get((i, j), 0)
            for i in self.customers for j in self.sites
            if (i, j) in self.distance and x_qty_sol.get((i, j), 0) > 0.5  # 只计算实际分配的量
        )
        drone_deployment_cost_val = sum(self.drone_cost * n_sol[j] for j in self.sites if y_sol[j] > 0.5)

        unassigned_demand_penalty_val = sum(
            self.penalty_cost_unassigned *
            max(0, (self.demand_deterministic[i] - sum(x_qty_sol.get((i, k), 0) for k in self.sites if
                                                       (i, k) in self.x_qty and x_qty_sol.get((i, k), 0) > 0.5)))
            for i in self.customers
        )  # 使用max(0, ...)确保惩罚不为负

        gurobi_obj_val = self.model.ObjVal  # Gurobi报告的目标函数值
        # 手动计算总成本 (使用最终计算的卡车成本)
        manual_total_cost = (locker_cost_val + transport_cost_val + drone_deployment_cost_val +
                             final_truck_cost + unassigned_demand_penalty_val)

        print(f"\n目标函数值 (Gurobi报告, 可能包含回调中未精确的truck_cost): {gurobi_obj_val:.2f}")
        print(f"目标函数值 (手动计算, 使用最终精确的truck_cost): {manual_total_cost:.2f}")
        print(f"  - 储物柜固定成本: {locker_cost_val:.2f}")
        print(f"  - 无人机运输成本 (基于实际分配量xᵢⱼ): {transport_cost_val:.2f}")
        print(f"  - 无人机部署成本: {drone_deployment_cost_val:.2f}")
        print(f"  - 卡车运输成本 (DRL最终计算): {final_truck_cost:.2f}")
        print(f"  - 未分配需求惩罚成本: {unassigned_demand_penalty_val:.2f}")

        print("\n选定的储物柜位置及无人机数量:")
        for j_site in selected_lockers_final:
            print(f"  位置 {j_site}: 配备 {round(n_sol[j_site])} 架无人机")

        print("\n客户分配详情 (基于实际分配量 xᵢⱼ > 0):")
        for j_site in selected_lockers_final:  # 遍历选中的储物柜
            print(f"  储物柜 {j_site}:")
            customers_served_by_j_list = []  # 服务于此储物柜的客户列表
            total_demand_at_j_qty_val = 0  # 此储物柜处理的总需求量

            # 收集分配到此储物柜的客户及其需求量
            for i_cust, assigned_lockers_for_cust in customer_assignment_quantities_final.items():
                qty_ij_val = assigned_lockers_for_cust.get(j_site, 0)
                if qty_ij_val > 0:
                    customers_served_by_j_list.append(f"客户{i_cust}(数量:{qty_ij_val})")
                    total_demand_at_j_qty_val += qty_ij_val

            if not customers_served_by_j_list:
                print("    - 未分配任何客户的需求至此储物柜")
                continue

            print(f"    - 分配的客户及数量: {', '.join(customers_served_by_j_list)}")
            max_cap_j_val = self.Q_locker_capacity.get(j_site, float('inf'))  # 获取储物柜容量
            cap_util_j_val = total_demand_at_j_qty_val / max_cap_j_val if max_cap_j_val > 0 else 0  # 计算容量利用率
            print(
                f"    - 此储物柜总服务需求量: {total_demand_at_j_qty_val:.2f} (最大容量: {max_cap_j_val}, 利用率: {cap_util_j_val * 100:.1f}%)")

            # 计算此储物柜的无人机工时需求
            actual_hours_needed_j_val = sum(
                x_qty_sol.get((i_cust, j_site), 0) * (
                            (2 * self.distance[i_cust, j_site] / self.drone_speed) + self.loading_time)
                for i_cust in self.customers if
                (i_cust, j_site) in self.distance and x_qty_sol.get((i_cust, j_site), 0) > 0.5
            )
            supplied_hours_j_val = n_sol[j_site] * self.H_drone_working_hours_per_day  # 提供的总工时
            drone_util_j_val = actual_hours_needed_j_val / supplied_hours_j_val if supplied_hours_j_val > 0 else 0  # 无人机利用率
            print(f"    - 无人机工时需求: {actual_hours_needed_j_val:.2f} 小时/天")
            print(
                f"    - 无人机提供工时: {round(n_sol[j_site])}架 * {self.H_drone_working_hours_per_day}小时/架 = {supplied_hours_j_val:.2f} (利用率: {drone_util_j_val * 100:.1f}%)")

        if unassigned_customers_by_u_final:  # 打印通过uᵢ标记为未分配的客户
            print(f"\n通过 uᵢ=1 标记为完全未分配的客户: {unassigned_customers_by_u_final}")
        else:
            print("\n所有客户均未通过 uᵢ=1 标记为完全未分配 (可能部分满足或完全满足).")

        # 检查客户需求的满足情况
        print("\n客户需求满足情况汇总:")
        num_fully_served = 0
        num_partially_served = 0
        num_not_served_at_all = 0
        for i_cust in self.customers:
            total_served_for_i_val = sum(customer_assignment_quantities_final.get(i_cust, {}).values())
            demand_i = self.demand_deterministic[i_cust]
            if abs(total_served_for_i_val - demand_i) < 1e-6:  # 完全满足
                num_fully_served += 1
            elif total_served_for_i_val > 1e-6:  # 部分满足
                num_partially_served += 1
                print(f"  客户 {i_cust}: 总需求 {demand_i}, 实际服务量 {total_served_for_i_val:.2f} (部分满足)")
            else:  # 完全未满足 (total_served_for_i_val < 1e-6)
                num_not_served_at_all += 1
                # print(f"  客户 {i_cust}: 总需求 {demand_i}, 实际服务量 0 (完全未满足)")

        print(f"  - 完全满足需求的客户数: {num_fully_served}")
        print(f"  - 部分满足需求的客户数: {num_partially_served}")
        print(f"  - 完全未得到服务的客户数: {num_not_served_at_all}")

        self._print_truck_routes(route_info_final)  # 打印卡车路线信息

    def _print_truck_routes(self, route_info):  # 打印卡车路线信息的辅助函数
        if not route_info: print("\n卡车路线信息: 无路线信息"); return
        print(f"\n卡车路线信息:")
        print(f"  总卡车数量 (DRL估计): {route_info.get('num_trucks', '未知')}")
        print(f"  总行驶距离: {route_info.get('total_distance', 0):.2f} km")
        print(f"  卡车固定成本部分: {route_info.get('fixed_cost', 0):.2f}")
        print(f"  卡车距离成本部分: {route_info.get('distance_cost', 0):.2f}")
        print(f"  仓库坐标: {route_info.get('depot_coord', '未知')}")
        print(f"  卡车容量: {route_info.get('truck_capacity', '未知')}")

        routes_list = route_info.get('routes', [])
        num_trucks_from_drl = route_info.get('num_trucks', 0)
        print(f"  DRL返回的路线数量: {len(routes_list)}")
        if len(routes_list) != num_trucks_from_drl and num_trucks_from_drl != '未知':
            print(
                f"  ⚠ 注意: DRL返回的路线数量({len(routes_list)})与DRL估计的卡车数量({num_trucks_from_drl})不一致。这可能是由于DRL的内部逻辑或近似。")

        if not routes_list: print("  无具体路线详情"); return
        print(f"\n详细路线:")
        for idx, route_detail in enumerate(routes_list):
            vehicle_id_val = route_detail.get('vehicle_id', idx)  # 如果没有vehicle_id，用索引代替
            lockers_on_route_list = route_detail.get('lockers', [])
            total_demand_on_route_val = route_detail.get('total_demand', 0)
            locker_coords_on_route_list = route_detail.get('locker_coords', [])

            print(f"  卡车 {vehicle_id_val}:")
            if lockers_on_route_list:
                print(f"    路线: 仓库 → {' → '.join(map(str, lockers_on_route_list))} → 仓库")
                print(f"    配送储物柜: {lockers_on_route_list}")
            else:
                print(f"    路线: 该卡车可能未使用或无停靠点")

            print(f"    此路线上总需求量: {total_demand_on_route_val:.2f} 订单")

            if locker_coords_on_route_list:
                coord_str_list = [f"储物柜{lockers_on_route_list[k_idx]}({coord[0]:.1f}, {coord[1]:.1f})"
                                  for k_idx, coord in enumerate(locker_coords_on_route_list)]
                print(f"    储物柜坐标: {', '.join(coord_str_list)}")

            truck_cap_val = route_info.get('truck_capacity', 1)
            utilization_val = (total_demand_on_route_val / truck_cap_val) * 100 if truck_cap_val > 0 else 0
            print(f"    此路线上容量利用率: {utilization_val:.1f}% ({total_demand_on_route_val:.2f}/{truck_cap_val})")
        if 'error' in route_info: print(f"\n  ⚠ DRL路线解析时出现错误: {route_info['error']}")

    def get_solution(self) -> Dict:  # 获取结构化的解决方案字典
        if self.model is None or self.model.status not in [GRB.OPTIMAL, GRB.TIME_LIMIT,
                                                           GRB.SUBOPTIMAL] or self.model.SolCount == 0:
            print("没有找到有效解或模型未优化。")
            return None

        # 获取解中的变量值
        x_qty_sol_final = {(i, j): self.x_qty[i, j].x for i in self.customers for j in self.sites if
                           (i, j) in self.x_qty}
        z_sol_final = {(i, j): self.z[i, j].x for i in self.customers for j in self.sites if (i, j) in self.z}
        y_sol_final = {j: self.y[j].x for j in self.sites}
        u_sol_final = {i: self.u[i].x for i in self.customers}
        n_sol_final = {j: self.n[j].x for j in self.sites}

        selected_lockers_list_final = [j for j in self.sites if y_sol_final[j] > 0.5]

        # 提取客户分配信息
        _, customer_assignments_primary_dict, customer_quantities_dict, unassigned_customers_list_by_u, _ = \
            self._get_current_solution(x_qty_sol_final, z_sol_final, y_sol_final, u_sol_final)

        # 计算最终卡车成本 (不生成图片，避免在get_solution中重复生成)
        final_truck_cost_val = self.calculate_truck_cost(selected_lockers_list_final, x_qty_sol_final, make_plots=False)

        # 计算各成本项
        locker_cost_val_final = sum(
            self.locker_fixed_cost[j] * y_sol_final[j] for j in self.sites if y_sol_final[j] > 0.5)
        transport_cost_val_final = sum(
            2 * self.transport_unit_cost * self.distance[i, j] * x_qty_sol_final.get((i, j), 0)
            for i in self.customers for j in self.sites if
            (i, j) in self.distance and x_qty_sol_final.get((i, j), 0) > 0.5
        )
        drone_deployment_cost_val_final = sum(
            self.drone_cost * n_sol_final[j] for j in self.sites if y_sol_final[j] > 0.5)
        unassigned_demand_penalty_val_final = sum(
            self.penalty_cost_unassigned *
            max(0, (self.demand_deterministic[i] - sum(x_qty_sol_final.get((i, k), 0) for k in self.sites if
                                                       (i, k) in self.x_qty and x_qty_sol_final.get((i, k), 0) > 0.5)))
            for i in self.customers
        )
        total_calculated_cost_final = (
                    locker_cost_val_final + transport_cost_val_final + drone_deployment_cost_val_final +
                    final_truck_cost_val + unassigned_demand_penalty_val_final)

        # 构建解决方案字典
        solution_dict = {
            'objective_value': self.model.ObjVal,  # Gurobi报告的目标值
            'calculated_total_cost': total_calculated_cost_final,  # 手动计算的总成本 (使用精确卡车成本)
            'locker_cost': locker_cost_val_final,
            'transport_cost': transport_cost_val_final,
            'drone_deployment_cost': drone_deployment_cost_val_final,
            'truck_cost': final_truck_cost_val,
            'unassigned_penalty_cost': unassigned_demand_penalty_val_final,
            'selected_lockers': {j: True for j in selected_lockers_list_final},  # 选中的储物柜
            'customer_assignments_primary': customer_assignments_primary_dict,  # 基于zᵢⱼ的主要分配
            'customer_assigned_quantities': customer_quantities_dict,  # 基于xᵢⱼ的实际分配量
            'unassigned_customers_by_u': unassigned_customers_list_by_u,  # 基于uᵢ标记的未分配客户
            'drone_allocations': {j: int(round(n_sol_final[j])) for j in selected_lockers_list_final if
                                  j in n_sol_final}  # 无人机分配
        }
        return solution_dict

    def visualize_solution(self, solution: Dict):  # 可视化解决方案
        if not solution: print("无解决方案可供可视化 (No solution to visualize)."); return

        # 为可视化准备一个简化的解决方案结构 (如果visualize_solution需要特定格式)
        temp_solution_for_viz = solution.copy()

        # 直接传递多储物柜分配信息，让可视化模块处理
        primary_assignments = solution.get('customer_assignments_primary', {})
        temp_solution_for_viz['customer_assignments'] = primary_assignments

        # 在确定性模型中，所有分配的客户都使用无人机配送（模式0）
        customer_service_modes = {}
        for customer_id in primary_assignments:
            customer_service_modes[customer_id] = 0  # 0表示无人机配送
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # 移除可能引起混淆或不被visualize_solution使用的键
        if 'customer_assignments_primary' in temp_solution_for_viz: del temp_solution_for_viz[
            'customer_assignments_primary']
        if 'customer_assigned_quantities' in temp_solution_for_viz: del temp_solution_for_viz[
            'customer_assigned_quantities']
        if 'unassigned_customers_by_u' in temp_solution_for_viz:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']
            del temp_solution_for_viz['unassigned_customers_by_u']

        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,  # 使用调整后的solution字典
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="确定性无人机配送网络规划 (支持多储物柜服务)"
        )
        if plt_fig:  # 如果visualize_solution返回一个matplotlib图形对象
            # plt_fig.show() # 在某些环境下，直接调用show可能需要matplotlib.pyplot as plt
            import matplotlib.pyplot as plt  # 确保导入
            plt.show()


def create_deterministic_example_instance(
        num_customers: int = 15,
        num_sites: int = 6,
        use_kmeans_clustering: bool = False,
        demand_level: str = "medium",
        locker_cost_level: str = "medium",
        drone_cost_level: str = "medium",
        drone_transport_cost_level: str = "medium",
        use_generated_distances: bool = True,
        random_seed: int = RANDOM_SEED
):
    """创建示例问题的参数实例"""
    local_random = random.Random(random_seed)  # 使用局部随机数生成器以控制种子
    customers_list = list(range(1, num_customers + 1))
    sites_list = list(range(1, num_sites + 1))

    # 生成客户坐标
    customer_coords_dict = {i: (local_random.uniform(0, 15), local_random.uniform(0, 15)) for i in customers_list}

    # 生成储物柜站点坐标 (可选K-Means聚类)
    if use_kmeans_clustering:
        site_coords_dict = generate_locker_sites_with_kmeans(customer_coords_dict, num_sites, random_state=random_seed)
    else:
        site_coords_dict = {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in sites_list}

    # 生成客户确定性需求 (λᵢ)
    demand_params_dict = {"low": (1, 2), "medium": (2, 3), "high": (3, 4)}.get(demand_level, (2, 3))
    demand_deterministic_dict = {c: local_random.randint(demand_params_dict[0], demand_params_dict[1]) for c in
                                 customers_list}

    # 储物柜固定成本 (cˡⱼ)
    # 适当降低储物柜固定成本，使模型更愿意开设多个储物柜
    locker_base_cost_val = {"low": 600, "medium": 800, "high": 1200}.get(locker_cost_level, 800)  # 降低成本
    locker_fixed_cost_dict = {s: locker_base_cost_val for s in sites_list}

    # 无人机购置成本 (pᵈ)
    drone_cost_val_param = {"low": 2500, "medium": 3500, "high": 4500}.get(drone_cost_level, 3500)  # 调整了成本示例

    # 无人机运输单位成本 (cᵈ)
    transport_unit_cost_val_param = {"low": 0.008, "medium": 0.015, "high": 0.025}.get(drone_transport_cost_level,
                                                                                       0.015)  # 调整了成本示例

    # 系统参数
    drone_speed_param = 50.0
    loading_time_param = 300.0 / 3600.0
    max_flight_distance_param = 20.0
    H_drone_working_hours_per_day_param = 8.0

    # 未分配单位需求的惩罚成本 (cᴾ)
    penalty_cost_unassigned_param = 1000.0  # 提高到1000元/订单，使惩罚成本非常高

    # 储物柜最大服务能力 (Qⱼ, 订单/天)
    # 增加储物柜容量以适应更多客户需求
    avg_locker_capacity_val = 30
    Q_locker_capacity_dict = {s: avg_locker_capacity_val for s in sites_list}

    # DRL求解器相关参数 (卡车运输)
    # 仓库设置在坐标空间的中心位置，以优化DRL性能
    # 客户坐标范围(0,15)×(0,15)，中心位置为(7.5, 7.5)
    depot_coord_param = (0, 0)  # 仓库坐标 (中心位置)
    truck_capacity_param = 90  # 卡车容量 (订单) - 基于DRL训练特征优化
    truck_fixed_cost_param = 1000  # 卡车固定成本 (元/车次或天)
    truck_km_cost_param = 0.5  # 卡车每公里成本 (元/公里)

    # 生成距离矩阵 dᵢⱼ
    distance_matrix_dict = {}
    if use_generated_distances:  # 基于坐标计算欧氏距离
        for c_id_val_dist, c_coord_val_dist in customer_coords_dict.items():
            for s_id_val_dist, s_coord_val_dist in site_coords_dict.items():
                dist_val = math.sqrt(
                    (c_coord_val_dist[0] - s_coord_val_dist[0]) ** 2 + (c_coord_val_dist[1] - s_coord_val_dist[1]) ** 2)
                distance_matrix_dict[c_id_val_dist, s_id_val_dist] = round(dist_val, 2)
    else:  # 随机生成一些距离 (不常用，通常基于坐标)
        for c_id_val_dist_rand in customers_list:
            for s_id_val_dist_rand in sites_list:
                # 粗略估计一个基础距离，然后加随机扰动
                base_dist_val = math.sqrt(
                    (customer_coords_dict[c_id_val_dist_rand][0] - site_coords_dict[s_id_val_dist_rand][0]) ** 2 + \
                    (customer_coords_dict[c_id_val_dist_rand][1] - site_coords_dict[s_id_val_dist_rand][1]) ** 2)
                distance_matrix_dict[c_id_val_dist_rand, s_id_val_dist_rand] = round(
                    local_random.uniform(base_dist_val * 0.8, base_dist_val * 1.2), 1)

    # 返回参数字典
    return {
        'customers': customers_list, 'sites': sites_list,
        'demand_deterministic': demand_deterministic_dict,
        'distance_matrix': distance_matrix_dict, 'drone_speed': drone_speed_param,
        'loading_time': loading_time_param, 'max_flight_distance': max_flight_distance_param,
        'locker_fixed_cost': locker_fixed_cost_dict, 'transport_unit_cost': transport_unit_cost_val_param,
        'drone_cost': drone_cost_val_param, 'H_drone_working_hours_per_day': H_drone_working_hours_per_day_param,
        'penalty_cost_unassigned': penalty_cost_unassigned_param,
        'Q_locker_capacity': Q_locker_capacity_dict, 'customer_coords': customer_coords_dict,
        'site_coords': site_coords_dict, 'depot_coord': depot_coord_param,
        'truck_capacity': truck_capacity_param, 'truck_fixed_cost': truck_fixed_cost_param,
        'truck_km_cost': truck_km_cost_param
    }


# 主程序入口
if __name__ == "__main__":
    start_time_main = time.time()  # 记录主程序开始时间
    print(f"设置全局随机种子: {RANDOM_SEED}")

    set_drl_log_level(logging.WARNING)  # 设置DRL求解器的日志级别，减少不必要的输出
    print("DRL日志级别已设置为WARNING (减少DRL的详细输出)")

    print("\n创建确定性需求的示例数据...")
    # 创建一个规模较小的问题实例，以便快速测试
    deterministic_data_instance = create_deterministic_example_instance(
        demand_level="medium", locker_cost_level="medium", drone_cost_level="medium",
        drone_transport_cost_level="medium", use_generated_distances=True,
        num_customers=50,  # 客户数量
        num_sites=5,  # 候选储物柜站点数量
        use_kmeans_clustering=True,
        random_seed=RANDOM_SEED
    )
    print("确定性数据已创建。")

    # 显示一些创建的数据信息
    print("\n部分客户需求 (λᵢ):")
    for customer_id_main, demand_val_main in list(deterministic_data_instance['demand_deterministic'].items())[
                                             :5]:  # 只显示前5个
        print(f"  客户 {customer_id_main}: {demand_val_main} 订单/天")
    total_demand_val_main = sum(deterministic_data_instance['demand_deterministic'].values())
    print(f"总需求: {total_demand_val_main} 订单/天")

    print("\n" + "=" * 60 + "\n求解确定性无人机配送网络设计问题\n" + "=" * 60)

    solve_start_time_main = time.time()  # 记录求解开始时间

    optimizer_main = DeterministicDroneDeliveryOptimizer()  # 创建优化器实例
    optimizer_main.set_parameters(**deterministic_data_instance)  # 设置参数
    optimizer_main.build_model()  # 构建模型

    # 求解模型 (可以调整time_limit和mip_gap)
    # 对于测试，使用较短的时间限制和较大的gap
    optimizer_main.solve(time_limit=300, mip_gap=0.02)  # 例如: 5分钟时间限制, 10% MIP gap

    solve_time_main = time.time() - solve_start_time_main  # 计算求解耗时

    solution_main = optimizer_main.get_solution()  # 获取结构化的解

    if solution_main:  # 如果找到了解
        print("\n" + "=" * 60 + "\n确定性优化结果 (模型对齐版)\n" + "=" * 60)
        print(f"\n解决方案详情:")
        print(f"  Gurobi报告的目标函数值: {solution_main['objective_value']:.2f}")
        print(f"  手动计算的总成本 (含精确卡车成本): {solution_main['calculated_total_cost']:.2f}")
        print(f"  选定的储物柜站点: {list(solution_main['selected_lockers'].keys())}")
        print(f"  各站点无人机分配: {solution_main['drone_allocations']}")
        if solution_main['unassigned_customers_by_u']:
            print(f"  通过uᵢ标记为未分配的客户: {solution_main['unassigned_customers_by_u']}")

        print("\n所有客户分配数量详情 (xᵢⱼ > 0):")
        # 显示所有客户的分配信息，包括未分配的客户
        for cust_id_main in sorted(deterministic_data_instance['customers']):
            assignments_main = solution_main['customer_assigned_quantities'].get(cust_id_main, {})
            primary_assignments_main = solution_main['customer_assignments_primary'].get(cust_id_main, [])
            total_demand = deterministic_data_instance['demand_deterministic'][cust_id_main]
            total_assigned = sum(assignments_main.values()) if assignments_main else 0

            if assignments_main:  # 有分配的客户
                status = "完全满足" if total_assigned == total_demand else f"部分满足({total_assigned}/{total_demand})"
                locker_list_str = f"分配到储物柜: {primary_assignments_main}" if primary_assignments_main else ""
                print(f"  客户 {cust_id_main}: {assignments_main} - {status} ({locker_list_str})")
            else:  # 未分配的客户
                print(f"  客户 {cust_id_main}: 未分配 (需求: {total_demand})")

        print(f"\n客户分配统计:")
        assigned_customers = [cust for cust, assignments in solution_main['customer_assigned_quantities'].items() if assignments]
        unassigned_customers = [cust for cust in deterministic_data_instance['customers'] if cust not in assigned_customers]
        print(f"  - 有分配的客户: {sorted(assigned_customers)}")
        print(f"  - 完全未分配的客户: {sorted(unassigned_customers)}")

        print("\n显示解决方案可视化...")
        optimizer_main.visualize_solution(solution_main)  # 可视化

        print(f"\n模型求解耗时: {solve_time_main:.2f} 秒")
    else:
        print("\n⚠ 未能找到可行解或优化失败。")

    total_time_main = time.time() - start_time_main  # 计算总运行时间
    print(f"\n总运行时间: {total_time_main:.2f} 秒")