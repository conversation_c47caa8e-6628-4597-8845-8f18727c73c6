#!/usr/bin/env python3
"""
缓存清理工具
清理ALNS程序运行产生的各种缓存文件和临时文件
"""

import os
import shutil
import glob
import gc
from pathlib import Path

def get_folder_size(folder_path):
    """计算文件夹大小（MB）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except Exception as e:
        print(f"计算文件夹大小时出错: {e}")
    return total_size / (1024 * 1024)  # 转换为MB

def cleanup_python_cache():
    """清理Python缓存文件"""
    print("正在清理Python缓存文件...")
    
    current_dir = Path(".")
    total_cleaned = 0
    
    # 清理 __pycache__ 文件夹
    pycache_dirs = list(current_dir.rglob("__pycache__"))
    for pycache_dir in pycache_dirs:
        try:
            size_mb = get_folder_size(pycache_dir)
            shutil.rmtree(pycache_dir)
            total_cleaned += size_mb
            print(f"  删除: {pycache_dir} ({size_mb:.2f} MB)")
        except Exception as e:
            print(f"  删除失败: {pycache_dir} - {e}")
    
    # 清理 .pyc 文件
    pyc_files = list(current_dir.rglob("*.pyc"))
    for pyc_file in pyc_files:
        try:
            size_mb = os.path.getsize(pyc_file) / (1024 * 1024)
            os.remove(pyc_file)
            total_cleaned += size_mb
            print(f"  删除: {pyc_file} ({size_mb:.3f} MB)")
        except Exception as e:
            print(f"  删除失败: {pyc_file} - {e}")
    
    print(f"Python缓存清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def cleanup_temp_files():
    """清理临时文件"""
    print("正在清理临时文件...")
    
    temp_patterns = [
        "*.tmp",
        "*.temp", 
        "*.log",
        "*.bak",
        "*.swp",
        "*~",
        ".DS_Store",
        "Thumbs.db"
    ]
    
    total_cleaned = 0
    current_dir = Path(".")
    
    for pattern in temp_patterns:
        temp_files = list(current_dir.rglob(pattern))
        for temp_file in temp_files:
            try:
                size_mb = os.path.getsize(temp_file) / (1024 * 1024)
                os.remove(temp_file)
                total_cleaned += size_mb
                print(f"  删除: {temp_file} ({size_mb:.3f} MB)")
            except Exception as e:
                print(f"  删除失败: {temp_file} - {e}")
    
    print(f"临时文件清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def cleanup_gurobi_files():
    """清理Gurobi相关文件"""
    print("正在清理Gurobi临时文件...")
    
    gurobi_patterns = [
        "gurobi.log*",
        "*.lp",
        "*.mps", 
        "*.sol",
        "*.ilp"
    ]
    
    total_cleaned = 0
    current_dir = Path(".")
    
    for pattern in gurobi_patterns:
        gurobi_files = list(current_dir.rglob(pattern))
        for gurobi_file in gurobi_files:
            try:
                size_mb = os.path.getsize(gurobi_file) / (1024 * 1024)
                os.remove(gurobi_file)
                total_cleaned += size_mb
                print(f"  删除: {gurobi_file} ({size_mb:.3f} MB)")
            except Exception as e:
                print(f"  删除失败: {gurobi_file} - {e}")
    
    print(f"Gurobi文件清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def cleanup_output_files():
    """清理输出文件"""
    print("正在清理输出文件...")
    
    output_patterns = [
        "*.txt",
        "*.out",
        "*.csv",
        "*.json"
    ]
    
    # 保护重要文件
    protected_files = ["README.txt", "requirements.txt", "config.txt"]
    
    total_cleaned = 0
    current_dir = Path(".")
    
    for pattern in output_patterns:
        output_files = list(current_dir.glob(pattern))  # 只在当前目录查找
        for output_file in output_files:
            if output_file.name in protected_files:
                continue
                
            try:
                size_mb = os.path.getsize(output_file) / (1024 * 1024)
                print(f"  发现输出文件: {output_file} ({size_mb:.3f} MB)")
                
                # 询问是否删除
                response = input(f"    是否删除 {output_file}? (y/n/a=全部删除): ").lower()
                if response in ['y', 'yes', 'a', 'all']:
                    os.remove(output_file)
                    total_cleaned += size_mb
                    print(f"    已删除: {output_file}")
                    
                    if response in ['a', 'all']:
                        # 删除剩余的同类型文件
                        for remaining_file in output_files[output_files.index(output_file)+1:]:
                            if remaining_file.name not in protected_files:
                                try:
                                    remaining_size = os.path.getsize(remaining_file) / (1024 * 1024)
                                    os.remove(remaining_file)
                                    total_cleaned += remaining_size
                                    print(f"    已删除: {remaining_file}")
                                except:
                                    pass
                        break
                        
            except Exception as e:
                print(f"  处理失败: {output_file} - {e}")
    
    print(f"输出文件清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def force_garbage_collection():
    """强制垃圾回收"""
    print("正在执行强制垃圾回收...")
    
    # 多次垃圾回收确保彻底清理
    for i in range(3):
        collected = gc.collect()
        print(f"  第{i+1}次垃圾回收，清理对象: {collected}")
    
    print("垃圾回收完成")

def main():
    """主清理函数"""
    print("=" * 60)
    print("ALNS程序缓存清理工具")
    print("=" * 60)
    
    total_space_freed = 0
    
    # 1. 强制垃圾回收
    force_garbage_collection()
    
    # 2. 清理Python缓存
    total_space_freed += cleanup_python_cache()
    
    # 3. 清理临时文件
    total_space_freed += cleanup_temp_files()
    
    # 4. 清理Gurobi文件
    total_space_freed += cleanup_gurobi_files()
    
    # 5. 清理输出文件（需要用户确认）
    print("\n检查输出文件...")
    response = input("是否检查并清理输出文件? (y/n): ").lower()
    if response in ['y', 'yes']:
        total_space_freed += cleanup_output_files()
    
    print("\n" + "=" * 60)
    print(f"清理完成！总共释放空间: {total_space_freed:.2f} MB")
    print("=" * 60)
    
    # 最终垃圾回收
    force_garbage_collection()

if __name__ == "__main__":
    main()
