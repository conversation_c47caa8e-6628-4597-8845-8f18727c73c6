#!/usr/bin/env python3
"""
调试ALNS和g_i.py对储物柜[2,3]评估差异的脚本
"""

import sys
import os
import random
import numpy as np

# 设置随机种子
RANDOM_SEED = 606
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# 导入模块
from alns import *
from g_i import *

def debug_locker_23_evaluation():
    """
    对比ALNS和g_i.py对储物柜[2,3]配置的评估
    """
    print("=" * 80)
    print("调试储物柜[2,3]评估差异")
    print("=" * 80)

    # 分析关键参数差异
    print("\n1. 分析参数差异...")

    # 从2.txt和3.txt的输出分析
    print("  从输出文件分析:")
    print("  ALNS (2.txt): 储物柜[2,3] = 255.86元")
    print("  g_i.py (3.txt): 储物柜[2,3] = 132.16元")
    print("  差异: 123.7元 (93%)")

    # 成本参数对比
    print("\n2. 成本参数对比:")
    print("  储物柜固定成本: 5.07元/天 (两者相同)")
    print("  无人机固定成本: 1.35元/天 (两者相同)")
    print("  运输成本: 0.02元/公里 (两者相同)")
    print("  惩罚成本: 40元/订单 (两者相同)")
    print("  最大飞行距离: 20.0公里 (已修复)")

    # 分析可能的差异来源
    print("\n3. 可能的差异来源:")
    print("  a) 卡车成本计算方法不同")
    print("  b) 客户分配算法不同")
    print("  c) 约束处理方式不同")
    print("  d) 需求场景生成不同")

    # 具体分析
    print("\n4. 具体分析:")

    # 第一阶段成本 (应该相同)
    first_stage_cost = 2 * 5.07 + 2 * 1.35  # 2个储物柜 + 2架无人机
    print(f"  第一阶段成本: {first_stage_cost:.2f}元 (两者应该相同)")

    # 从3.txt分析g_i.py的成本分解
    print("  g_i.py成本分解 (从3.txt):")
    print("    储物柜固定成本: 10.13元/天")
    print("    无人机成本(部署+运输): 8.05元/天")
    print("    卡车成本(固定+运输): 113.92元/天")
    print("    其他成本(惩罚): 0.06元/天")
    print("    总计: 132.16元/天")

    # 推算ALNS的成本分解
    alns_total = 255.86
    alns_first_stage = first_stage_cost
    alns_second_stage = alns_total - alns_first_stage

    print(f"  ALNS推算成本分解:")
    print(f"    第一阶段: {alns_first_stage:.2f}元/天")
    print(f"    第二阶段: {alns_second_stage:.2f}元/天")
    print(f"    总计: {alns_total:.2f}元/天")

    # 差异分析
    gi_second_stage = 113.92 + 8.05 - 2.70 + 0.06  # 卡车 + 无人机运输 + 惩罚
    diff_second_stage = alns_second_stage - gi_second_stage

    print(f"\n5. 第二阶段成本差异:")
    print(f"  g_i.py第二阶段: {gi_second_stage:.2f}元")
    print(f"  ALNS第二阶段: {alns_second_stage:.2f}元")
    print(f"  差异: {diff_second_stage:.2f}元")

    if diff_second_stage > 100:
        print("  ⚠ 第二阶段成本差异巨大，主要问题在于:")
        print("    1. 卡车成本计算方法")
        print("    2. 客户分配效率")
        print("    3. 惩罚成本计算")

    print("\n6. 建议修复方向:")
    print("  1. 检查ALNS的卡车成本估算函数")
    print("  2. 对比客户分配结果")
    print("  3. 验证约束一致性")
    print("  4. 统一需求场景生成")


if __name__ == "__main__":
    debug_locker_23_evaluation()
