#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证ALNS解的质量：使用g_i.py的精确求解器评估ALNS得出的解

这个脚本将：
1. 使用与g_i.py相同的参数设置
2. 固定ALNS找到的储物柜配置
3. 使用Gurobi精确求解器评估该配置的真实成本
4. 与ALNS的评估结果进行对比
"""

import sys
import os
import time
import random
import numpy as np

# 添加当前目录到路径，以便导入g_i.py中的函数
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from g_i import (
        create_saa_example_problem_instance_unified_cost,
        SAA_Optimizer_Unified_Cost,
        generate_random_demand_samples
    )
    print("✅ 成功导入g_i.py模块")
except ImportError as e:
    print(f"❌ 导入g_i.py失败: {e}")
    print("请确保g_i.py在当前目录下")
    sys.exit(1)

# 全局随机种子（与原始实验保持一致）
RANDOM_SEED = 616

def validate_alns_solution():
    """
    验证ALNS解的主函数
    """
    print("=" * 80)
    print("ALNS解验证器 - 使用g_i.py精确求解器评估ALNS解")
    print("=" * 80)
    
    # 设置随机种子（与原始实验保持一致）
    random.seed(RANDOM_SEED)
    np.random.seed(RANDOM_SEED)
    print(f"设置全局随机种子: {RANDOM_SEED}")
    
    # 1. 创建与原始实验相同的问题实例
    print("\n📋 步骤1: 创建问题实例（与原始实验参数完全一致）")
    problem_instance = create_saa_example_problem_instance_unified_cost(
        num_customers=15,
        num_sites=4,
        max_trucks=1,
        demand_level="medium",
        locker_cost_level="medium", 
        drone_cost_level="medium",
        drone_transport_cost_level="medium",
        use_generated_distances=True,
        random_seed=RANDOM_SEED,
        annual_interest_rate=0.04,
        equipment_life_years=10,
        operating_days_per_year=365
    )
    
    # 2. ALNS找到的最优解配置
    print("\n🎯 步骤2: ALNS最优解配置")
    alns_solution = {
        'selected_lockers': [1, 2],  # ALNS选择的储物柜位置
        'drone_allocation': {1: 1, 2: 2},  # 每个储物柜的无人机数量
        'total_cost_alns': 135.30,  # ALNS评估的总成本
        'cost_breakdown_alns': {
            'locker_cost': 10.13,
            'drone_cost': 11.12, 
            'truck_cost': 113.17,
            'penalty_cost': 31.72
        }
    }
    
    print(f"  ALNS选择的储物柜: {alns_solution['selected_lockers']}")
    print(f"  无人机分配: {alns_solution['drone_allocation']}")
    print(f"  ALNS评估总成本: {alns_solution['total_cost_alns']:.2f} 元/天")
    
    # 3. 生成验证用的需求样本（与原始实验相同）
    print("\n📊 步骤3: 生成需求样本用于验证")
    num_scenarios_validation = 2000  # 与原始实验相同的验证样本数
    demand_samples_validation = generate_random_demand_samples(
        problem_instance, 
        num_scenarios_validation, 
        random_seed=RANDOM_SEED
    )
    print(f"  生成 {num_scenarios_validation} 个验证场景")
    
    # 4. 使用g_i.py的精确求解器评估ALNS解
    print("\n🔍 步骤4: 使用Gurobi精确求解器评估ALNS解")
    
    # 创建SAA优化器
    saa_optimizer = SAA_Optimizer_Unified_Cost(problem_instance)
    
    # 固定第一阶段决策为ALNS的解
    fixed_first_stage = {
        'y': {j: 1 if j in alns_solution['selected_lockers'] else 0 
              for j in problem_instance.sites},
        'n': {j: alns_solution['drone_allocation'].get(j, 0) 
              for j in problem_instance.sites}
    }
    
    print(f"  固定储物柜配置: {fixed_first_stage['y']}")
    print(f"  固定无人机配置: {fixed_first_stage['n']}")
    
    # 评估固定配置的成本
    start_time = time.time()
    
    try:
        gurobi_cost = evaluate_fixed_solution_with_gurobi(
            saa_optimizer, 
            fixed_first_stage, 
            demand_samples_validation
        )
        evaluation_time = time.time() - start_time
        
        print(f"  ✅ Gurobi评估完成，耗时: {evaluation_time:.2f}秒")
        print(f"  📊 Gurobi精确评估结果: {gurobi_cost:.2f} 元/天")
        
        # 5. 对比分析
        print("\n📈 步骤5: 结果对比分析")
        print("=" * 60)
        print(f"  ALNS评估成本:   {alns_solution['total_cost_alns']:.2f} 元/天")
        print(f"  Gurobi评估成本: {gurobi_cost:.2f} 元/天")
        
        cost_difference = gurobi_cost - alns_solution['total_cost_alns']
        cost_difference_pct = (cost_difference / alns_solution['total_cost_alns']) * 100
        
        print(f"  成本差异:       {cost_difference:+.2f} 元/天 ({cost_difference_pct:+.2f}%)")
        
        if abs(cost_difference_pct) < 5:
            print("  ✅ 评估结果基本一致（差异<5%）")
        elif cost_difference > 0:
            print("  ⚠️ Gurobi评估成本更高，可能ALNS低估了成本")
        else:
            print("  🎉 ALNS评估成本更高，说明ALNS可能过于保守")
            
    except Exception as e:
        print(f"  ❌ Gurobi评估失败: {e}")
        return None
    
    # 6. 与g_i.py原始解对比
    print("\n🏆 步骤6: 与g_i.py原始最优解对比")
    print("=" * 60)
    
    gurobi_original_cost = 143.58  # 来自g.txt的结果
    gurobi_original_config = [2, 3, 4]  # g_i.py找到的储物柜配置
    
    print(f"  g_i.py原始解:")
    print(f"    储物柜配置: {gurobi_original_config}")
    print(f"    总成本: {gurobi_original_cost:.2f} 元/天")
    print(f"  ALNS解 (Gurobi重新评估):")
    print(f"    储物柜配置: {alns_solution['selected_lockers']}")
    print(f"    总成本: {gurobi_cost:.2f} 元/天")
    
    improvement = gurobi_original_cost - gurobi_cost
    improvement_pct = (improvement / gurobi_original_cost) * 100
    
    print(f"  改进幅度: {improvement:+.2f} 元/天 ({improvement_pct:+.2f}%)")
    
    if improvement > 0:
        print("  🎉 ALNS找到了比g_i.py更好的解！")
    elif improvement < -1:
        print("  ❌ ALNS解实际上比g_i.py解更差")
    else:
        print("  ➡️ 两个解的质量相当")
    
    return {
        'alns_cost': alns_solution['total_cost_alns'],
        'gurobi_evaluated_cost': gurobi_cost,
        'gurobi_original_cost': gurobi_original_cost,
        'cost_difference': cost_difference,
        'improvement_over_original': improvement
    }

def evaluate_fixed_solution_with_gurobi(saa_optimizer, fixed_first_stage, demand_samples):
    """
    使用Gurobi精确求解器评估固定的第一阶段解
    
    Args:
        saa_optimizer: SAA优化器实例
        fixed_first_stage: 固定的第一阶段解 {'y': {}, 'n': {}}
        demand_samples: 需求场景样本
    
    Returns:
        float: 平均总成本
    """
    print("    正在使用Gurobi精确求解器评估固定解...")
    
    # 提取固定的储物柜和无人机配置
    selected_lockers = [j for j, val in fixed_first_stage['y'].items() if val > 0.5]
    n_star = fixed_first_stage['n']
    
    # 计算第一阶段固定成本
    first_stage_cost = sum(saa_optimizer.problem.locker_fixed_cost[j] for j in selected_lockers)
    first_stage_cost += sum(saa_optimizer.problem.drone_cost * n_star.get(j, 0) for j in selected_lockers)
    
    print(f"    第一阶段固定成本: {first_stage_cost:.2f} 元/天")
    print(f"    开始评估 {len(demand_samples)} 个场景的第二阶段成本...")
    
    # 评估所有场景的第二阶段成本
    total_second_stage_cost = 0
    scenario_costs = []
    
    for k, demand_scenario in enumerate(demand_samples):
        if (k + 1) % 500 == 0:
            print(f"      已处理 {k+1}/{len(demand_samples)} 个场景...")
        
        # 使用精确求解器求解第二阶段问题
        second_stage_cost = solve_second_stage_for_validation(
            saa_optimizer, selected_lockers, n_star, demand_scenario, k
        )
        
        total_cost_scenario = first_stage_cost + second_stage_cost
        scenario_costs.append(total_cost_scenario)
        total_second_stage_cost += second_stage_cost
    
    # 计算平均成本
    avg_second_stage_cost = total_second_stage_cost / len(demand_samples)
    avg_total_cost = first_stage_cost + avg_second_stage_cost
    
    print(f"    第二阶段平均成本: {avg_second_stage_cost:.2f} 元/天")
    print(f"    总平均成本: {avg_total_cost:.2f} 元/天")
    
    return avg_total_cost

def solve_second_stage_for_validation(saa_optimizer, selected_lockers, n_star, demand_scenario, scenario_idx):
    """
    为验证目的求解第二阶段问题的简化版本

    Args:
        saa_optimizer: SAA优化器实例
        selected_lockers: 选定的储物柜列表
        n_star: 无人机分配字典
        demand_scenario: 单个需求场景
        scenario_idx: 场景索引

    Returns:
        float: 该场景的第二阶段成本
    """
    try:
        # 使用SAA优化器的现有方法
        return saa_optimizer.solve_second_stage_exact(selected_lockers, n_star, demand_scenario, scenario_idx)
    except AttributeError:
        # 如果方法不存在，使用简化的评估方法
        return solve_second_stage_simplified(saa_optimizer, selected_lockers, n_star, demand_scenario)

def solve_second_stage_simplified(saa_optimizer, selected_lockers, n_star, demand_scenario):
    """
    简化的第二阶段求解（如果精确方法不可用）
    """
    problem = saa_optimizer.problem

    # 计算储物柜容量
    locker_capacities = {}
    for j in selected_lockers:
        drone_count = n_star.get(j, 0)
        max_capacity = problem.Q_locker_capacity.get(j, 30)
        drone_capacity = drone_count * problem.H_drone_working_hours_per_day * problem.drone_speed / 2
        locker_capacities[j] = min(max_capacity, drone_capacity)

    # 简单的贪心分配
    total_transport_cost = 0
    total_penalty_cost = 0
    unassigned_demand = 0

    for customer, demand in demand_scenario.items():
        best_cost = float('inf')
        best_locker = None

        for locker in selected_lockers:
            if locker_capacities.get(locker, 0) >= demand:
                distance = problem.distance.get((customer, locker), float('inf'))
                transport_cost = 2 * problem.transport_unit_cost * distance * demand

                if transport_cost < best_cost:
                    best_cost = transport_cost
                    best_locker = locker

        if best_locker is not None:
            total_transport_cost += best_cost
            locker_capacities[best_locker] -= demand
        else:
            unassigned_demand += demand

    total_penalty_cost = unassigned_demand * problem.penalty_cost_unassigned

    # 简化的卡车成本估算
    total_locker_demand = sum(max(0, problem.Q_locker_capacity.get(j, 30) - locker_capacities.get(j, 0))
                             for j in selected_lockers)
    truck_cost = problem.truck_fixed_cost
    if total_locker_demand > 0:
        # 简单的TSP估算
        avg_distance = 5.0  # 假设平均距离
        truck_cost += len(selected_lockers) * avg_distance * problem.truck_km_cost

    return total_transport_cost + total_penalty_cost + truck_cost

if __name__ == "__main__":
    try:
        results = validate_alns_solution()

        if results:
            print("\n" + "=" * 80)
            print("验证完成！")
            print("=" * 80)
            print("\n📋 验证结果摘要:")
            print(f"  ALNS原始评估:     {results['alns_cost']:.2f} 元/天")
            print(f"  Gurobi重新评估:   {results['gurobi_evaluated_cost']:.2f} 元/天")
            print(f"  g_i.py原始解:     {results['gurobi_original_cost']:.2f} 元/天")
            print(f"  评估差异:         {results['cost_difference']:+.2f} 元/天")
            print(f"  相对原始解改进:   {results['improvement_over_original']:+.2f} 元/天")

    except KeyboardInterrupt:
        print("\n用户中断验证过程")
    except Exception as e:
        print(f"\n验证过程出错: {e}")
        import traceback
        traceback.print_exc()
