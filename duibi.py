import numpy as np
from typing import Dict, List, Tuple, Any
import random
import time
import math
from collections import defaultdict
import copy

# 【修改】Numba已移除，使用纯Python实现以体现3.py的优越性
NUMBA_AVAILABLE = False
print("⚠️ 使用纯Python实现")
try:
    from drl import DRL_CVRP_Solver, set_drl_log_level
    from clustering import generate_locker_sites_with_kmeans
    from visualization import visualize_solution
    DRL_AVAILABLE = True
except ImportError:
    print("警告: drl, clustering, 或 visualization 模块未找到。DRL和可视化功能将不可用。")
    DRL_AVAILABLE = False
    # 提供这些类的虚拟实现，以避免 NameError
    class DRL_CVRP_Solver:
        def __init__(self, *args, **kwargs): pass
        def solve(self, *args, **kwargs): return 0.0, None # 返回一个默认值
    def set_drl_log_level(level): pass
    def generate_locker_sites_with_kmeans(customer_coords, num_sites, random_state):
        print("警告: K-Means聚类不可用，将随机生成储物柜站点。")
        local_random = random.Random(random_state)
        return {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in range(1, num_sites + 1)}
    def visualize_solution(*args, **kwargs):
        print("警告: 可视化功能不可用。")
        return None


import logging # logging 应该在顶部导入

# 设置随机种子以确保结果可重现
RANDOM_SEED = 609
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# SAA 参数 (调整以提高统计稳定性)
SAA_MAX_REPLICATIONS_M = 10  # 最大复制次数
SAA_MIN_REPLICATIONS_M = 2   # 最小复制次数 (设为最小值，主要依靠终止条件)
SAA_SAMPLES_K = 40          # 训练样本数量 N (减少以提高求解效率)
SAA_SAMPLES_K_PRIME = 2000   # 验证样本数量 N' (适当减少以平衡计算效率)
SAA_CONFIDENCE_LEVEL_ALPHA = 0.1  # 置信水平 (90%置信区间)
SAA_GAP_TOLERANCE_PERCENT = 0.03  # Gap阈值 ε' = 3%
SAA_VARIANCE_TOLERANCE_PERCENT = 0.05  # 方差阈值 ε = 5%

# 第二阶段算法配置 - 固定使用启发式求解器
USE_EXACT_SECOND_STAGE_SOLVER = False  # 固定使用启发式求解器

# ============================================================================
# 核心算法 (已移除Numba优化，使用纯Python实现)
# ============================================================================

# ============================================================================
# 启发式评估策略说明：
#
# 核心评估函数: _calculate_objective_heuristic 只调用 _estimate_service_costs
# 这个函数使用快速启发式算法，速度很快
#
# 缓存评估函数: calculate_objective_cached 使用启发式求解器
# 用于缓存和重复使用已计算的解
#
# 预期效果：使用纯启发式算法，显著提高求解效率
# ============================================================================

# ---------------------------------------------------------------------------
# create_deterministic_example_instance 函数 (全局作用域)
# ---------------------------------------------------------------------------
def create_deterministic_example_instance(
        num_customers: int = 15,
        num_sites: int = 6,
        use_kmeans_clustering: bool = False,
        demand_level: str = "medium",
        locker_cost_level: str = "medium",
        drone_cost_level: str = "medium",
        drone_transport_cost_level: str = "medium",
        use_generated_distances: bool = True,
        random_seed: int = RANDOM_SEED,
        # 新增年化成本计算参数
        annual_interest_rate: float = 0.04,  # IR: 年利率 4%
        equipment_life_years: int = 10,      # T_life: 设备生命周期 10年
        operating_days_per_year: int = 365   # D_year: 年运营天数
):
    """
    创建示例问题的参数实例 (用于确定性或期望值)

    修正版：使用年化成本计算，确保时间单位一致性
    - 储物柜和无人机成本从一次性投资转换为日均固定成本
    - 所有成本项统一使用日成本单位
    """
    local_random = random.Random(random_seed)
    customers_list = list(range(1, num_customers + 1))
    sites_list = list(range(1, num_sites + 1))

    customer_coords_dict = {i: (local_random.uniform(0, 15), local_random.uniform(0, 15)) for i in customers_list}

    if use_kmeans_clustering and DRL_AVAILABLE: # K-Means依赖clustering模块
        site_coords_dict = generate_locker_sites_with_kmeans(customer_coords_dict, num_sites, random_state=random_seed)
    else:
        site_coords_dict = {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in sites_list}

    demand_params_dict = {"low": (2, 4), "medium": (4, 6), "high": (6, 8)}.get(demand_level, (2, 3))
    demand_dict_for_instance = {c: local_random.randint(demand_params_dict[0], demand_params_dict[1]) for c in customers_list}

    # === 修正版成本计算：统一时间单位为日成本 ===

    # 第1步：计算资本回收因子 (Capital Recovery Factor)
    IR = annual_interest_rate
    T_life = equipment_life_years
    capital_recovery_factor = (IR * (1 + IR)**T_life) / ((1 + IR)**T_life - 1)

    print(f"  成本计算参数:")
    print(f"    年利率 (IR): {IR*100:.1f}%")
    print(f"    设备生命周期 (T_life): {T_life}年")
    print(f"    资本回收因子: {capital_recovery_factor:.6f}")
    print(f"    年运营天数: {operating_days_per_year}天")

    # 第2步：储物柜初始建设成本 -> 日均固定成本
    locker_initial_cost_val = {"low": 10000, "medium": 15000, "high": 20000}.get(locker_cost_level, 10000)
    locker_annual_cost = locker_initial_cost_val * capital_recovery_factor  # c_l^a
    locker_daily_cost = locker_annual_cost / operating_days_per_year        # c_l^daily
    locker_fixed_cost_dict = {s: locker_daily_cost for s in sites_list}

    print(f"  储物柜成本转换:")
    print(f"    初始建设成本: {locker_initial_cost_val:,.0f}元")
    print(f"    年化成本: {locker_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {locker_daily_cost:.2f}元/天")

    # 第3步：无人机初始采购成本 -> 日均固定成本
    drone_initial_cost_val = {"low": 3000, "medium": 4000, "high": 5000}.get(drone_cost_level, 3000)
    drone_annual_cost = drone_initial_cost_val * capital_recovery_factor     # c_d^a
    drone_daily_cost = drone_annual_cost / operating_days_per_year           # c_d^daily
    drone_cost_val_param = drone_daily_cost

    print(f"  无人机成本转换:")
    print(f"    初始采购成本: {drone_initial_cost_val:,.0f}元")
    print(f"    年化成本: {drone_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {drone_daily_cost:.2f}元/天")

    # 无人机运输单位成本
    transport_unit_cost_val_param = {"low": 0.01, "medium": 0.02, "high": 0.03}.get(drone_transport_cost_level, 0.01)

    drone_speed_param = 50.0
    loading_time_param = 300.0 / 3600.0
    max_flight_distance_param = 15.0
    H_drone_working_hours_per_day_param = 8.0
    penalty_cost_unassigned_param = 40.0  # 高惩罚成本促使系统分配更多客户
    avg_locker_capacity_val = 30
    Q_locker_capacity_dict = {s: avg_locker_capacity_val for s in sites_list}

    depot_coord_param = (0, 0)
    truck_capacity_param = 90
    truck_fixed_cost_param = 100
    truck_km_cost_param = 0.5

    distance_matrix_dict = {}
    if use_generated_distances:
        for c_id_val_dist, c_coord_val_dist in customer_coords_dict.items():
            for s_id_val_dist, s_coord_val_dist in site_coords_dict.items():
                dist_val = math.sqrt(
                    (c_coord_val_dist[0] - s_coord_val_dist[0]) ** 2 + (c_coord_val_dist[1] - s_coord_val_dist[1]) ** 2)
                # 确保距离不为零，设置最小距离为0.01
                dist_val = max(0.01, dist_val)
                distance_matrix_dict[c_id_val_dist, s_id_val_dist] = round(dist_val, 2)
    else:
        for c_id_val_dist_rand in customers_list:
            for s_id_val_dist_rand in sites_list:
                base_dist_val = math.sqrt(
                    (customer_coords_dict[c_id_val_dist_rand][0] - site_coords_dict[s_id_val_dist_rand][0]) ** 2 + \
                    (customer_coords_dict[c_id_val_dist_rand][1] - site_coords_dict[s_id_val_dist_rand][1]) ** 2)
                # 确保基础距离不为零
                base_dist_val = max(0.01, base_dist_val)
                distance_matrix_dict[c_id_val_dist_rand, s_id_val_dist_rand] = round(
                    local_random.uniform(base_dist_val * 0.8, base_dist_val * 1.2), 1)

    # === 成本单位统一性验证 ===
    print(f"\n  成本单位统一性检查:")
    print(f"    储物柜固定成本: {locker_daily_cost:.2f} 元/天")
    print(f"    无人机固定成本: {drone_daily_cost:.2f} 元/天")
    print(f"    无人机运输成本: {transport_unit_cost_val_param:.3f} 元/公里 (按实际运输量)")
    print(f"    卡车固定成本: {truck_fixed_cost_param} 元/天")
    print(f"    卡车运输成本: {truck_km_cost_param} 元/公里 (按实际运输量)")
    print(f"    ✓ 所有固定成本已统一为日成本单位")
    print(f"    ✓ 运输成本保持按实际使用量计费")

    return {
        'customers': customers_list, 'sites': sites_list,
        'demand_deterministic': demand_dict_for_instance,
        'distance_matrix': distance_matrix_dict, 'drone_speed': drone_speed_param,
        'loading_time': loading_time_param, 'max_flight_distance': max_flight_distance_param,
        'locker_fixed_cost': locker_fixed_cost_dict, 'transport_unit_cost': transport_unit_cost_val_param,
        'drone_cost': drone_cost_val_param, 'H_drone_working_hours_per_day': H_drone_working_hours_per_day_param,
        'penalty_cost_unassigned': penalty_cost_unassigned_param,
        'Q_locker_capacity': Q_locker_capacity_dict, 'customer_coords': customer_coords_dict,
        'site_coords': site_coords_dict, 'depot_coord': depot_coord_param,
        'truck_capacity': truck_capacity_param, 'truck_fixed_cost': truck_fixed_cost_param,
        'truck_km_cost': truck_km_cost_param
    }

# ---------------------------------------------------------------------------
# ALNS 整体求解器类定义
# ---------------------------------------------------------------------------
#
# 架构说明：
# 1. ALNS_IntegratedSolver: ALNS整体求解器，同时优化第一阶段和第二阶段决策
#    - 第一阶段决策：储物柜选址(y)和无人机配置(n)
#    - 第二阶段决策：客户分配(x)和卡车路径
#    - 目标函数：在K个给定需求样本下的期望总成本
#
# 2. 解表示（整体求解版）：
#    solution = {
#        'y': {j: 0/1},           # 第一阶段：储物柜选址决策
#        'n': {j: int},           # 第一阶段：无人机配置决策
#        'x': {(i,j,k): float},   # 第二阶段：客户分配决策
#        'truck_routes': {k: [...]} # 第二阶段：卡车路径决策
#    }
#
# 这种设计实现了真正的ALNS整体优化，避免了分阶段求解的次优性。
# ---------------------------------------------------------------------------
class ALNS_IntegratedSolver:
    """
    ALNS (Adaptive Large Neighborhood Search) 整体求解器
    同时优化第一阶段和第二阶段决策，实现真正的整体优化

    解表示（整体求解版）：
    solution = {
        'y': {j: 0/1},           # 第一阶段：储物柜选址决策
        'n': {j: int},           # 第一阶段：无人机配置决策
        'x': {(i,j,k): float},   # 第二阶段：客户分配决策
        'truck_routes': {k: [...]} # 第二阶段：卡车路径决策
    }

    ALNS算子操作完整的解：
    - 破坏算子：同时破坏储物柜选址、客户分配和卡车路径
    - 修复算子：重新构建完整的可行解
    - 目标函数：基于完整解的精确成本计算
    """

    def __init__(self, problem_instance, demand_samples, alns_config=None):
        self.problem = problem_instance
        self.demand_samples = demand_samples
        self.num_scenarios = len(demand_samples)

        # 获取客户数量用于自适应参数设置
        num_customers = len(self.problem.customers)

        # ALNS配置参数（整体求解版）
        default_config = {
            'max_iterations': 1000,  # 减少迭代次数以提高效率
            'initial_temperature': 100,  # T₀ = 100 (论文设置)
            'cooling_rate': 0.995,  # α = 0.995 (论文设置)
            'min_temperature': 0.01,  # 最小温度
            'segment_size': 100,  # 每100次迭代更新一次权重
            'reaction_factor': 0.1,  # r = 0.1 (论文设置)
            'convergence_patience': 500,  # 500次迭代无改进则认为收敛
            'max_iterations_without_improvement': 500,  # 无改进最大迭代次数
            'use_adaptive_weights': True,  # 启用自适应权重
            'use_score_based_weights': True,  # 启用基于分数的权重更新
            'weight_update_frequency': 100,  # 权重更新频率
            'weight_update_coefficient': 0.1,  # 权重更新系数
            'score_new_best': 5,      # δ_1 = 5（论文设置）
            'score_better': 3,        # δ_2 = 3（论文设置）
            'score_accepted': 1,      # δ_3 = 1（论文设置）
        }

        # 合并用户配置
        self.config = default_config.copy()
        if alns_config:
            self.config.update(alns_config)

        # ALNS算子：操作完整解（第一阶段+第二阶段决策）
        # 破坏算子：同时破坏储物柜选址、客户分配和卡车路径
        self.destroy_operators = [
            self.integrated_random_removal,      # 随机移除储物柜及相关分配
            self.integrated_worst_removal,       # 移除效益最差的储物柜及相关分配
            self.integrated_cluster_removal,     # 簇移除算子
            self.integrated_route_removal,       # 路径移除算子
        ]

        # 修复算子：重新构建完整的可行解
        self.repair_operators = [
            self.integrated_greedy_insertion,    # 贪心插入储物柜并重新分配
            self.integrated_regret_insertion,    # 后悔值插入
            self.integrated_smart_insertion,     # 智能插入
        ]

        # 算子权重和统计
        self.destroy_weights = {op.__name__: 1.0 for op in self.destroy_operators}
        self.repair_weights = {op.__name__: 1.0 for op in self.repair_operators}
        self.destroy_usage = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_usage = {op.__name__: 0 for op in self.repair_operators}
        self.destroy_success = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_success = {op.__name__: 0 for op in self.repair_operators}
        self.destroy_scores = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_scores = {op.__name__: 0 for op in self.repair_operators}

        # 历史最优解记录
        self.historical_best_obj = float('inf')
        self.historical_best_solution = None

        # 兼容性属性（用于旧代码）
        self.heuristic_eval_count = 0
        self.heuristic_eval_times = []
        self.sample_consistency_scores = []
        self.capacity_penalty_weight = 5000  # 容量违反惩罚权重
        self.min_penalty_weight = 1000       # 惩罚权重下限
        self.max_penalty_weight = 50000      # 惩罚权重上限
        self.penalty_adjustment_factor = 1.05 # 权重调整因子
        self.cache_hits = 0
        self.cache_misses = 0

    def set_parameters(self,
                       customers: List,
                       sites: List,
                       expected_demand: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        """设置模型参数"""
        # 设置基础参数
        self.customers = customers
        self.sites = sites
        self.expected_demand = expected_demand
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.drone_transport_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned
        self.Q_locker_capacity = Q_locker_capacity

        # 设置坐标信息
        self.customer_coords = customer_coords or {}
        self.site_coords = site_coords or {}
        self.depot_location = depot_coord

        # 设置卡车运输参数
        self.truck_capacity = truck_capacity or 100
        self.truck_fixed_cost = truck_fixed_cost or 100
        self.truck_variable_cost = truck_km_cost or 1.0

        # 计算卡车运输距离矩阵
        self._calculate_truck_distances()

        # 估算最大卡车数量
        total_expected_demand = sum(self.expected_demand.values())
        self.max_trucks = max(1, math.ceil(total_expected_demand / self.truck_capacity)) if self.truck_capacity else len(self.sites)

        print(f"参数设置完成: {len(customers)}个客户, {len(sites)}个候选站点, 最大{self.max_trucks}辆卡车")

    def _calculate_truck_distances(self):
        """计算卡车运输距离矩阵"""
        self.truck_distances = {}

        if not self.depot_location or not self.site_coords:
            # 如果没有坐标信息，使用简化距离
            for j in self.sites:
                self.truck_distances[0, j] = 10.0  # 仓库到储物柜的默认距离
                for k in self.sites:
                    if j != k:
                        self.truck_distances[j, k] = 5.0  # 储物柜间的默认距离
            return

        # 计算仓库到各储物柜的距离
        for j in self.sites:
            if j in self.site_coords:
                dist = math.sqrt((self.depot_location[0] - self.site_coords[j][0])**2 +
                               (self.depot_location[1] - self.site_coords[j][1])**2)
                self.truck_distances[0, j] = dist

        # 计算储物柜间的距离
        for j in self.sites:
            for k in self.sites:
                if j != k and j in self.site_coords and k in self.site_coords:
                    dist = math.sqrt((self.site_coords[j][0] - self.site_coords[k][0])**2 +
                                   (self.site_coords[j][1] - self.site_coords[k][1])**2)
                    self.truck_distances[j, k] = dist

    def _generate_demand_samples(self, num_samples: int) -> List[Dict[int, float]]:
        """
        生成需求场景样本，客户需求服从泊松分布
        泊松分布的参数λ等于期望需求值，天然产生非负整数
        """
        demand_scenarios = []
        for _ in range(num_samples):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]  # 泊松分布参数λ
                # 使用泊松分布生成需求，λ既是均值也是方差
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)  # 转换为float以保持一致性
            demand_scenarios.append(current_scenario_demand)
        return demand_scenarios

    def _calculate_initial_temperature(self, initial_solution):
        """
        自动计算初始温度，使得接受一个平均恶化程度的解的概率为预设值
        """
        print(f"  正在自动计算初始温度...")

        sample_size = self.config['auto_temp_sample_size']
        acceptance_prob = self.config['auto_temp_acceptance_prob']

        # 计算初始解的目标值（使用快速评估保持一致性）
        initial_obj = self._calculate_objective_heuristic(initial_solution, 0)

        # 生成邻域解样本并计算目标值差异
        deltas = []
        valid_neighbors = 0
        feasible_neighbors = 0

        for i in range(sample_size):
            try:
                # 随机选择破坏和修复算子
                destroy_op = random.choice(self.destroy_operators)
                repair_op = random.choice(self.repair_operators)

                # 生成邻域解
                temp_solution = destroy_op(copy.deepcopy(initial_solution))
                neighbor_solution = repair_op(temp_solution, 0)

                if neighbor_solution is not None:
                    valid_neighbors += 1
                    if self._is_feasible(neighbor_solution):
                        feasible_neighbors += 1
                        neighbor_obj = self._calculate_objective_heuristic(neighbor_solution, 0)
                        delta = neighbor_obj - initial_obj
                        if delta > 0:  # 只考虑恶化的解
                            deltas.append(delta)
                        elif delta < 0:  # 改进的解也记录（用于调试）
                            deltas.append(abs(delta))  # 使用绝对值
            except Exception as e:
                continue

        print(f"  温度计算统计: 尝试{sample_size}次, 有效邻域{valid_neighbors}个, 可行邻域{feasible_neighbors}个, 有效差值{len(deltas)}个")

        if not deltas:
            print(f"  无法生成有效的邻域解样本，使用默认初始温度: 1000.0")
            return 1000.0

        # 计算平均恶化程度
        avg_delta = sum(deltas) / len(deltas)

        # 根据公式 p = exp(-avg_delta / T0) 计算初始温度
        # T0 = -avg_delta / ln(p)
        if acceptance_prob > 0 and acceptance_prob < 1:
            initial_temp = -avg_delta / math.log(acceptance_prob)
        else:
            initial_temp = avg_delta  # 回退值

        print(f"  自动计算的初始温度: {initial_temp:.2f} (基于{len(deltas)}个样本，平均恶化: {avg_delta:.2f})")
        return max(initial_temp, 1.0)  # 确保温度至少为1.0

    def solve(self, initial_solution=None, time_limit=300):
        """
        ALNS整体求解器主循环

        求解SAA生成的确定性两阶段随机规划问题：
        - 同时优化第一阶段决策：储物柜选址(y)和无人机配置(n)
        - 同时优化第二阶段决策：客户分配(x)和卡车路径
        - 目标函数：在K个给定需求样本下的期望总成本（精确计算）

        Args:
            initial_solution: 初始完整解 {'y': {}, 'n': {}, 'x': {}, 'truck_routes': {}}
            time_limit: 时间限制（秒）

        Returns:
            Dict: 最优完整解
        """
        print(f"  开始ALNS整体求解，时间限制: {time_limit}秒")
        print(f"  ALNS参数设置:")
        print(f"    初始温度 T₀: {self.config['initial_temperature']}")
        print(f"    冷却因子 κ: {self.config['cooling_rate']}")
        print(f"    温度阈值 T*: {self.config['min_temperature']}")
        print(f"    最大迭代次数: {self.config['max_iterations']}")
        print(f"    收敛耐心: {self.config['convergence_patience']}")
        print(f"    分数设置 δ₁/δ₂/δ₃: {self.config['score_new_best']}/{self.config['score_better']}/{self.config['score_accepted']}")
        print(f"    求解模式: 整体求解 (精确目标函数)")

        start_time = time.time()

        # 生成初始解
        if initial_solution is None:
            current_solution = self.create_initial_solution()
        else:
            current_solution = copy.deepcopy(initial_solution)

        if current_solution is None:
            print("  无法生成初始解")
            return None

        # 使用精确目标函数计算初始目标值
        current_obj = self._calculate_objective_exact(current_solution)
        best_obj = current_obj
        print(f"  初始解目标值(精确): {current_obj:.2f}")

        best_solution = copy.deepcopy(current_solution)
        self.historical_best_obj = best_obj

        # 设置历史最优解
        self.historical_best_obj = best_obj

        # 使用论文中的固定初始温度
        temperature = self.config['initial_temperature']  # T₀ = 100
        print(f"  使用论文设置的初始温度: {temperature}")

        # ALNS主循环
        iteration = 0
        iterations_without_improvement = 0

        # 主循环（改进的终止条件）
        max_iterations = self.config['max_iterations']  # 减少到5000
        quality_convergence_count = 0  # 解质量收敛计数
        last_best_obj = float('inf')

        while (iteration < max_iterations and
               iterations_without_improvement < self.config['max_iterations_without_improvement'] and
               time.time() - start_time < time_limit):  # 简化终止条件，专注速度

            iteration += 1

            # 注意：early_termination_threshold 已经通过 while 循环条件处理
            # self.config['max_iterations_without_improvement'] = 10 已经是合适的阈值

            # 选择破坏和修复算子（移除强化破坏模式）
            destroy_op = self._select_operator(self.destroy_operators, self.destroy_weights)

            # 选择修复算子
            repair_op = self._select_operator(self.repair_operators, self.repair_weights)

            # 记录算子使用
            self.destroy_usage[destroy_op.__name__] += 1
            self.repair_usage[repair_op.__name__] += 1

            # 生成新解
            try:
                temp_solution = destroy_op(copy.deepcopy(current_solution))
                new_solution = repair_op(temp_solution, iteration)

                if new_solution is None or not self._is_feasible(new_solution):
                    continue

                # 使用精确目标函数
                new_obj = self._calculate_objective_exact(new_solution)

                # 标准ALNS接受准则：模拟退火
                accept = False
                delta = new_obj - current_obj

                # 1. 改进的解总是接受
                if delta < 0:
                    accept = True
                # 2. 模拟退火接受较差解
                elif temperature > 0 and random.random() < math.exp(-delta / temperature):
                    accept = True

                if accept:
                    current_solution = new_solution
                    current_obj = new_obj

                    # 记录算子成功（传统方式）
                    self.destroy_success[destroy_op.__name__] += 1
                    self.repair_success[repair_op.__name__] += 1

                    # 基于分数的奖励机制
                    score = 0

                    # 整体求解模式：使用精确评估结果
                    if new_obj < best_obj:
                        improvement_percent = (best_obj - new_obj) / best_obj * 100
                        print(f"    迭代 {iteration}: 发现改进 {improvement_percent:.1f}% ({new_obj:.2f} < {best_obj:.2f})")

                        # 更新最优解
                        best_obj = new_obj
                        best_solution = copy.deepcopy(new_solution)
                        self.historical_best_obj = best_obj
                        iterations_without_improvement = 0

                        best_solution = copy.deepcopy(new_solution)
                        best_obj = new_obj
                        iterations_without_improvement = 0

                        # 判断是否为历史最优解
                        if new_obj < self.historical_best_obj:
                            self.historical_best_obj = new_obj
                            score = self.config['score_new_best']  # 找到历史最优解
                            print(f"  迭代 {iteration}: 找到历史最优解，精确目标值: {best_obj:.2f}")
                            # 重置质量收敛计数
                            quality_convergence_count = 0
                            last_best_obj = best_obj
                        else:
                            score = self.config['score_better']  # 找到比当前解好的解
                            print(f"  迭代 {iteration}: 找到更优解，精确目标值: {best_obj:.2f}")
                            # 重置质量收敛计数
                            quality_convergence_count = 0
                            last_best_obj = best_obj
                    elif new_obj < current_obj:
                        score = self.config['score_better']  # 找到比当前解好的解
                        iterations_without_improvement += 1
                    else:
                        score = self.config['score_accepted']  # 被接受的较差解
                        iterations_without_improvement += 1

                    # 更新算子分数
                    if self.config['use_score_based_weights']:
                        self.destroy_scores[destroy_op.__name__] += score
                        self.repair_scores[repair_op.__name__] += score
                else:
                    iterations_without_improvement += 1

                # 检查解质量收敛（如果没有找到更好解）
                if abs(best_obj - last_best_obj) < 0.01:  # 解质量变化很小
                    quality_convergence_count += 1
                else:
                    quality_convergence_count = 0
                    last_best_obj = best_obj

                # 降温
                temperature *= self.config['cooling_rate']

                # 定期更新算子权重
                if iteration % self.config['weight_update_frequency'] == 0:
                    self._update_operator_weights()

                # 【修改1】自适应调整容量惩罚权重
                if iteration % 50 == 0:  # 每50次迭代调整一次
                    self._current_iter = iteration  # 设置当前迭代数用于日志控制
                    self._adjust_capacity_penalty_weight(current_solution)

            except Exception as e:
                print(f"  迭代 {iteration} 出错: {str(e)}")
                import traceback
                print(f"  错误详情: {traceback.format_exc()}")
                continue

        solve_time = time.time() - start_time

        # 确定终止原因
        termination_reason = []
        if iteration >= max_iterations:
            termination_reason.append(f"达到最大迭代次数({max_iterations})")
        if temperature <= self.config['min_temperature']:
            termination_reason.append(f"温度降至阈值({self.config['min_temperature']})")
        if iterations_without_improvement >= self.config['max_iterations_without_improvement']:
            termination_reason.append(f"连续{self.config['max_iterations_without_improvement']}次无改进")
        if quality_convergence_count >= 200:
            termination_reason.append(f"解质量收敛(连续{quality_convergence_count}次变化<0.01)")
        if time.time() - start_time >= time_limit:
            termination_reason.append(f"达到时间限制({time_limit}秒)")

        print(f"  ALNS求解完成，耗时: {solve_time:.2f}秒")
        print(f"  总迭代次数: {iteration}")
        print(f"  最终温度: {temperature:.4f}")
        print(f"  连续无改进次数: {iterations_without_improvement}")
        print(f"  终止原因: {' & '.join(termination_reason) if termination_reason else '未知'}")

        # 启发式模式：显示最终结果
        if best_solution is not None:
            print(f"  最终启发式目标值: {best_obj:.2f}")

        # 输出缓存统计信息
        total_evaluations = self.cache_hits + self.cache_misses
        if total_evaluations > 0:
            cache_hit_rate = self.cache_hits / total_evaluations * 100
            print(f"  解缓存统计: {self.cache_hits} 命中, {self.cache_misses} 未命中, 命中率: {cache_hit_rate:.1f}%")
            print(f"  缓存大小: {len(getattr(self, 'solution_cache', {}))}")

        # 启发式评估性能统计
        self._print_heuristic_evaluation_stats()

        # 【新增】输出容量惩罚权重统计信息
        if hasattr(self, 'capacity_penalty_weight'):
            print(f"  容量惩罚机制统计:")
            print(f"    当前惩罚权重: {self.capacity_penalty_weight:.0f}")
            print(f"    权重范围: [{self.min_penalty_weight:.0f}, {self.max_penalty_weight:.0f}]")
            print(f"    调整因子: {self.penalty_adjustment_factor:.3f} (温和调整)")
            print(f"    惩罚函数: 平滑指数函数 exp(5*(load_ratio-0.8))")

            # 显示当前最优解的容量使用情况
            if best_solution:
                try:
                    _, _, penalty_val, assigned_demands = self._estimate_service_costs(best_solution)
                    print(f"    最优解容量惩罚值: {penalty_val:.2f}")

                    # 显示各储物柜的负载率
                    selected_lockers = {j for j, val in best_solution['y'].items() if val > 0.5}
                    if selected_lockers and assigned_demands:
                        print(f"    储物柜负载率:")
                        for j in selected_lockers:
                            demand_on_j = assigned_demands.get(j, 0)
                            cap_j = self.problem.Q_locker_capacity.get(j, 1)
                            load_ratio = demand_on_j / cap_j if cap_j > 0 else 0
                            print(f"      储物柜{j}: {load_ratio:.2f} ({demand_on_j:.1f}/{cap_j:.1f})")
                except:
                    pass



        return best_solution





    def _calculate_feasibility_penalty(self, solution):
        """计算解可行性惩罚 - 与saa_g_r.py约束一致"""
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        penalty = 0

        # 检查是否至少有一个储物柜 (对应saa_g_r.py的C1约束)
        if not selected_lockers:
            return 10000  # 适度惩罚

        # 检查无人机配置合理性 (对应saa_g_r.py的C6约束: n_j >= y_j)
        for j in selected_lockers:
            if n_star.get(j, 0) < 1:  # 开放储物柜至少需要1架无人机
                penalty += 1000

        return penalty



    def _local_search_improvement(self, solution, iteration=0):
        """
        Variable Neighborhood Descent (VND) 局部搜索改进

        实现多层次邻域搜索：
        N1: 无人机优化邻域（最快）- 微调无人机数量
        N2: 单储物柜操作邻域（中等）- Add/Drop储物柜
        N3: 交换储物柜邻域（最慢）- Swap储物柜位置

        搜索策略：按邻域复杂度递增搜索，找到改进后回到N1重新开始
        """
        try:
            current_solution = copy.deepcopy(solution)
            current_obj = self._calculate_objective_heuristic(current_solution, iteration)

            # VND主循环：持续迭代直到所有邻域都无法改进
            neighborhood_index = 1  # 从最简单的邻域开始
            max_neighborhoods = 3
            improvement_found = True

            while improvement_found and neighborhood_index <= max_neighborhoods:
                improvement_found = False

                if neighborhood_index == 1:
                    # N1: 无人机优化邻域
                    improved_solution, improved_obj = self._search_drone_optimization_neighborhood(
                        current_solution, current_obj, iteration)
                elif neighborhood_index == 2:
                    # N2: 单储物柜操作邻域
                    improved_solution, improved_obj = self._search_single_locker_neighborhood(
                        current_solution, current_obj, iteration)
                elif neighborhood_index == 3:
                    # N3: 交换储物柜邻域
                    improved_solution, improved_obj = self._search_swap_locker_neighborhood(
                        current_solution, current_obj, iteration)

                if improved_solution is not None and improved_obj < current_obj:
                    # 找到改进，更新当前解并回到第一个邻域
                    current_solution = improved_solution
                    current_obj = improved_obj
                    neighborhood_index = 1  # 重新从最简单的邻域开始
                    improvement_found = True
                else:
                    # 当前邻域无改进，尝试下一个更复杂的邻域
                    neighborhood_index += 1

            # 检查是否有改进
            original_obj = self._calculate_objective_heuristic(solution, iteration)
            if current_obj < original_obj:
                return current_solution
            else:
                return None

        except Exception as e:
            return None

    def _search_drone_optimization_neighborhood(self, solution, current_obj, iteration=0):
        """
        N1: 无人机优化邻域搜索
        保持储物柜位置不变，仅微调每个开放储物柜的无人机数量（±1架）
        这是最快的操作，优先搜索
        """
        best_solution = None
        best_obj = current_obj

        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        for j in open_lockers:
            current_drones = solution['n'][j]

            # 尝试增加1架无人机（最多8架）
            if current_drones < 8:
                temp_solution = copy.deepcopy(solution)
                temp_solution['n'][j] = current_drones + 1

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

            # 尝试减少1架无人机（最少1架）
            if current_drones > 1:
                temp_solution = copy.deepcopy(solution)
                temp_solution['n'][j] = current_drones - 1

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

        return best_solution, best_obj

    def _search_single_locker_neighborhood(self, solution, current_obj, iteration=0):
        """
        N2: 单储物柜操作邻域搜索
        Add: 尝试开启一个当前关闭的储物柜
        Drop: 尝试关闭一个当前开启的储物柜
        """
        best_solution = None
        best_obj = current_obj

        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        closed_lockers = [j for j in self.problem.sites if j not in open_lockers]

        # Add操作：尝试开启关闭的储物柜
        for j in closed_lockers:
            temp_solution = copy.deepcopy(solution)
            temp_solution['y'][j] = 1
            temp_solution['n'][j] = 1  # 默认配置1架无人机

            if self._is_feasible(temp_solution):
                temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                if temp_obj < best_obj:
                    best_solution = temp_solution
                    best_obj = temp_obj

        # Drop操作：尝试关闭开启的储物柜（至少保留1个）
        if len(open_lockers) > 1:
            for j in open_lockers:
                temp_solution = copy.deepcopy(solution)
                temp_solution['y'][j] = 0
                temp_solution['n'][j] = 0

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

        return best_solution, best_obj

    def _search_swap_locker_neighborhood(self, solution, current_obj, iteration=0):
        """
        N3: 交换储物柜邻域搜索
        Swap: 关闭一个已开启的储物柜，同时开启一个当前关闭的储物柜
        保持储物柜总数不变，但改变布局
        """
        best_solution = None
        best_obj = current_obj

        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        closed_lockers = [j for j in self.problem.sites if j not in open_lockers]

        # 尝试所有可能的交换组合
        for close_j in open_lockers:
            for open_j in closed_lockers:
                temp_solution = copy.deepcopy(solution)

                # 保存原来的无人机配置
                original_drones = temp_solution['n'][close_j]

                # 关闭一个储物柜
                temp_solution['y'][close_j] = 0
                temp_solution['n'][close_j] = 0

                # 开启另一个储物柜（使用相同的无人机配置）
                temp_solution['y'][open_j] = 1
                temp_solution['n'][open_j] = original_drones

                if self._is_feasible(temp_solution):
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj

        return best_solution, best_obj

    def _adjust_capacity_penalty_weight(self, solution):
        """
        【平滑惩罚版】自适应调整容量惩罚权重
        配合平滑惩罚函数，使用更温和的调整策略
        """
        try:
            # 使用 _estimate_service_costs 检查当前解的容量违反情况
            _, _, capacity_violation_penalty, _ = self._estimate_service_costs(solution)

            # 【修改】基于惩罚值的大小来判断调整方向，而不是简单的0/非0
            # 由于使用了平滑惩罚函数，即使轻微超载也会有惩罚值
            # 我们设定一个阈值，只有当惩罚值较大时才认为需要调整
            penalty_threshold = self.capacity_penalty_weight * 0.1  # 阈值设为基础权重的10%

            if capacity_violation_penalty > penalty_threshold:
                # 惩罚值较大，说明容量压力大，需要温和增加惩罚权重
                old_weight = self.capacity_penalty_weight
                self.capacity_penalty_weight *= self.penalty_adjustment_factor
                # 确保不超过上限
                self.capacity_penalty_weight = min(self.capacity_penalty_weight, self.max_penalty_weight)

                # 输出调整信息（仅在权重实际改变时且每100次迭代输出一次）
                if self.capacity_penalty_weight != old_weight and hasattr(self, '_last_penalty_log_iter'):
                    if not hasattr(self, '_last_penalty_log_iter') or getattr(self, '_current_iter', 0) - self._last_penalty_log_iter >= 100:
                        print(f"    容量压力较大(惩罚值:{capacity_violation_penalty:.1f})，权重增加: {old_weight:.0f} → {self.capacity_penalty_weight:.0f}")
                        self._last_penalty_log_iter = getattr(self, '_current_iter', 0)
            elif capacity_violation_penalty < penalty_threshold * 0.1:
                # 惩罚值很小，说明容量压力小，可以适当减小惩罚权重以鼓励探索
                old_weight = self.capacity_penalty_weight
                self.capacity_penalty_weight /= self.penalty_adjustment_factor
                # 确保不低于下限
                self.capacity_penalty_weight = max(self.capacity_penalty_weight, self.min_penalty_weight)

                # 输出调整信息（仅在权重实际改变时且每100次迭代输出一次）
                if self.capacity_penalty_weight != old_weight and hasattr(self, '_last_penalty_log_iter'):
                    if not hasattr(self, '_last_penalty_log_iter') or getattr(self, '_current_iter', 0) - self._last_penalty_log_iter >= 100:
                        print(f"    容量压力很小(惩罚值:{capacity_violation_penalty:.1f})，权重减少: {old_weight:.0f} → {self.capacity_penalty_weight:.0f}")
                        self._last_penalty_log_iter = getattr(self, '_current_iter', 0)
            # 如果惩罚值在中等范围内，不调整权重
        except Exception as e:
            # 如果计算失败，保持当前权重不变
            # print(f"  警告: 调整容量惩罚权重失败: {e}")
            pass



    def _solution_to_key(self, solution):
        """
        将解转换为可用作字典键的字符串
        """
        # 更精确的键生成：包含所有储物柜的状态和无人机数量
        y_items = tuple(sorted((j, int(val > 0.5)) for j, val in solution['y'].items()))
        n_items = tuple(sorted((j, int(val)) for j, val in solution['n'].items() if solution['y'].get(j, 0) > 0.5))
        return (y_items, n_items)



    def calculate_objective_cached(self, solution):
        """
        带缓存的启发式目标函数计算
        """
        # 生成解的键（只基于第一阶段决策变量）
        solution_key = self._solution_to_key(solution)

        # 检查缓存
        if solution_key in self.solution_cache:
            self.cache_hits += 1
            return self.solution_cache[solution_key]

        # 缓存未命中，使用启发式计算目标值
        self.cache_misses += 1
        if self.cache_misses <= 5:  # 只显示前5次未命中的详细信息
            open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
            drone_config = {j: solution['n'][j] for j in open_lockers}
            print(f"    缓存未命中 #{self.cache_misses}: 储物柜{open_lockers}, 无人机{drone_config}")

        # 使用启发式目标函数
        obj_value = self._calculate_objective_heuristic(solution, 0)

        # 存入缓存
        self.solution_cache[solution_key] = obj_value

        return obj_value



    def _solve_second_stage_subproblem(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        求解第二阶段客户分配子问题（启发式模式）

        给定第一阶段决策和具体需求场景，使用启发式算法求解客户分配
        """
        # 固定使用贪心启发式算法
        if not hasattr(self.problem, 'fast_solver') or self.problem.fast_solver is None:
            self.problem.fast_solver = FastAssignmentSolver(self.problem)
        return self.problem.fast_solver.solve_assignment_heuristic(
            y_star, n_star, selected_lockers, demand_scenario
        )



    def _calculate_second_stage_costs(self, assignment, demand_scenario, selected_lockers):
        """
        计算第二阶段成本：运输成本和惩罚成本
        """
        transport_cost = 0
        locker_demands = {j: 0 for j in selected_lockers}

        # 计算运输成本和储物柜需求
        for (customer, locker), quantity in assignment.items():
            if quantity > 0:
                distance = self.problem.distance.get((customer, locker), 0)
                transport_cost += 2 * self.problem.transport_unit_cost * distance * quantity
                locker_demands[locker] += quantity

        # 计算惩罚成本（与g_i.py保持一致的精确方法）
        penalty_cost = 0
        for customer_id, demand in demand_scenario.items():
            customer_assigned = sum(assignment.get((customer_id, locker), 0) for locker in selected_lockers)
            shortage = max(0, demand - customer_assigned)
            penalty_cost += self.problem.penalty_cost_unassigned * shortage

        return transport_cost, penalty_cost, locker_demands



    def _calculate_objective_heuristic(self, solution, iteration=0):
        """
        【优化版】启发式目标函数：基于多个随机样本的平均值
        用小批量随机场景的微观模拟，替代原有的基于期望值的宏观估计。
        这使得启发式目标与真实目标函数高度相关，能更准确地指导ALNS。
        """
        import time
        eval_start_time = time.time()
        self.heuristic_eval_count += 1

        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            return float('inf')

        # 1. 第一阶段成本（精确，保持不变）
        first_stage_cost = (sum(self.problem.locker_fixed_cost.get(j, 0) for j in selected_lockers) +
                           sum(self.problem.drone_cost * n_star.get(j, 0) for j in selected_lockers))

        # 2. 初始化 FastAssignmentSolver (如果需要)
        if not hasattr(self.problem, 'fast_solver') or self.problem.fast_solver is None:
            self.problem.fast_solver = FastAssignmentSolver(self.problem)

        # 3. 【核心改进】随机抽取一小批(k_small)真实需求场景进行快速评估
        k_small = 15  # 【修复】使用与3.py一致的样本数量，实现真正的快速评估
        # 【优化】使用分层抽样，确保样本代表性
        sample_indices = self._stratified_sample_selection(k_small, iteration)

        total_second_stage_costs = []
        total_truck_costs = []

        for idx in sample_indices:
            demand_scenario = self.demand_samples[idx]

            try:
                # 使用FastAssignmentSolver进行智能客户分配
                assignment = self.problem.fast_solver.solve_assignment_heuristic(
                    y_star, n_star, selected_lockers, demand_scenario
                )

                # 根据分配结果计算第二阶段成本
                transport_cost, penalty_cost, locker_demands = self._calculate_second_stage_costs(
                    assignment, demand_scenario, selected_lockers
                )

                # 计算容量违反惩罚（基于实际分配结果）
                capacity_penalty = self._calculate_capacity_penalty(locker_demands, n_star, selected_lockers)

                second_stage_cost = transport_cost + penalty_cost + capacity_penalty
                total_second_stage_costs.append(second_stage_cost)

                # 估算卡车成本
                truck_cost = self._fallback_truck_cost_estimation({
                    j: {'coord': self.problem.site_coords[j], 'demand': round(demand)}
                    for j, demand in locker_demands.items() if demand > 0.5
                })
                total_truck_costs.append(truck_cost)

            except Exception as e:
                # 如果FastAssignmentSolver失败，使用简化估算作为回退
                print(f"    警告: FastAssignmentSolver失败 (样本{idx}): {e}")
                # 使用简化的成本估算作为回退
                fallback_cost = len(demand_scenario) * 50  # 简化的回退估算
                total_second_stage_costs.append(fallback_cost)
                total_truck_costs.append(len(selected_lockers) * 200)

        # 4. 计算平均第二阶段成本和卡车成本
        avg_second_stage_cost = sum(total_second_stage_costs) / len(total_second_stage_costs) if total_second_stage_costs else 0
        avg_truck_cost = sum(total_truck_costs) / len(total_truck_costs) if total_truck_costs else 0

        # 【新增】记录评估性能统计
        eval_time = time.time() - eval_start_time
        self.heuristic_eval_times.append(eval_time)

        # 计算样本一致性得分（标准差越小越好）
        if len(total_second_stage_costs) > 1:
            import numpy as np
            consistency_score = np.std(total_second_stage_costs) / (avg_second_stage_cost + 1e-6)
            self.sample_consistency_scores.append(consistency_score)

        return first_stage_cost + avg_second_stage_cost + avg_truck_cost

    def _stratified_sample_selection(self, k_small, iteration):
        """
        【改进版】分层抽样选择代表性样本，实现真正的快速评估
        """
        total_samples = len(self.demand_samples)

        # 确保k_small小于总样本数，实现真正的快速评估
        k_small = min(k_small, total_samples)

        if k_small >= total_samples:
            return list(range(total_samples))

        # 【改进】使用更智能的抽样策略
        if iteration % self.config.get('full_evaluation_frequency', 10) == 0:
            # 每隔一定迭代使用固定间隔采样，确保覆盖全部样本空间
            step = total_samples / k_small
            indices = [int(i * step) for i in range(k_small)]
            # 确保不超出范围
            indices = [min(idx, total_samples - 1) for idx in indices]
            return indices
        else:
            # 使用随机抽样，保持多样性和随机性
            return random.sample(range(total_samples), k_small)

    def _print_heuristic_evaluation_stats(self):
        """
        输出启发式评估性能统计信息
        """
        if self.heuristic_eval_count > 0:
            print(f"  启发式评估性能统计:")
            print(f"    总评估次数: {self.heuristic_eval_count}")

            if self.heuristic_eval_times:
                avg_time = sum(self.heuristic_eval_times) / len(self.heuristic_eval_times)
                max_time = max(self.heuristic_eval_times)
                min_time = min(self.heuristic_eval_times)
                print(f"    平均评估时间: {avg_time:.4f} 秒")
                print(f"    评估时间范围: {min_time:.4f} - {max_time:.4f} 秒")

            if self.sample_consistency_scores:
                avg_consistency = sum(self.sample_consistency_scores) / len(self.sample_consistency_scores)
                print(f"    样本一致性得分: {avg_consistency:.4f} (越小越好)")
                print(f"    ↳ 说明: 小批量样本评估的稳定性指标")

            # 【新增】评估一致性统计
            if hasattr(self, 'evaluation_biases') and self.evaluation_biases:
                avg_bias = sum(self.evaluation_biases) / len(self.evaluation_biases)
                max_bias = max(self.evaluation_biases)
                print(f"    启发式-精确评估偏差: {avg_bias:.2%} (平均), {max_bias:.2%} (最大)")
                print(f"    ↳ 说明: 启发式评估与精确评估的差异程度")

    def _calculate_capacity_penalty(self, locker_demands, n_star, selected_lockers):
        """
        基于实际分配结果计算容量违反惩罚
        """
        capacity_penalty = 0

        for j in selected_lockers:
            demand_on_j = locker_demands.get(j, 0)

            # 物理容量检查
            locker_cap = self.problem.Q_locker_capacity.get(j, 0)
            if locker_cap > 0:
                load_ratio_locker = demand_on_j / locker_cap
                try:
                    penalty1 = (self.capacity_penalty_weight / 100) * math.exp(5 * (load_ratio_locker - 0.8))
                    penalty1 = min(penalty1, self.capacity_penalty_weight * 10)
                except OverflowError:
                    penalty1 = self.capacity_penalty_weight * 10
                capacity_penalty += penalty1

            # 无人机容量检查
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                drone_cap = self.problem.fast_solver._get_drone_capacity_fast(j, n_star.get(j, 0))
                if drone_cap > 0:
                    load_ratio_drone = demand_on_j / drone_cap
                    try:
                        penalty2 = (self.capacity_penalty_weight / 200) * math.exp(5 * (load_ratio_drone - 0.8))
                        penalty2 = min(penalty2, self.capacity_penalty_weight * 5)
                    except OverflowError:
                        penalty2 = self.capacity_penalty_weight * 5
                    capacity_penalty += penalty2

        return capacity_penalty





    def _fallback_truck_cost_estimation(self, active_lockers_info):
        """
        DRL不可用时的回退卡车成本估算方法
        """
        if not active_lockers_info:
            return 0

        # 使用原来的TSP近似方法作为回退
        total_demand = sum(info['demand'] for info in active_lockers_info.values())
        coords = [self.problem.depot_coord] + [info['coord'] for info in active_lockers_info.values()]

        # TSP距离估算
        if len(coords) > 1:
            centroid_x = sum(c[0] for c in coords) / len(coords)
            centroid_y = sum(c[1] for c in coords) / len(coords)
            avg_dist_to_centroid = sum(math.sqrt((c[0]-centroid_x)**2 + (c[1]-centroid_y)**2) for c in coords) / len(coords)
            estimated_distance = avg_dist_to_centroid * len(coords) * 2.5  # 经验系数
        else:
            estimated_distance = 0

        # 组合成本
        num_trucks = math.ceil(total_demand / self.problem.truck_capacity) if self.problem.truck_capacity > 0 else 0
        return (num_trucks * self.problem.truck_fixed_cost) + (estimated_distance * self.problem.truck_km_cost)



    def _estimate_service_costs(self, solution):
        """
        估算服务成本：【增强版】引入风险考量和动态惩罚
        【修改1】增加容量违反惩罚项
        【改进】引入安全边际考虑需求波动性

        Returns:
            tuple: (total_transport_cost, total_penalty_cost, capacity_violation_penalty, assigned_demand_to_locker)
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = {j for j, val in y_star.items() if val > 0.5}

        # 初始化
        total_transport_cost = 0
        total_penalty_cost = 0
        assigned_demand_to_locker = defaultdict(float)

        # 【新增】风险调整参数
        # z_score = 1.645 代表 95% 的置信水平，即我们希望系统有95%的概率能满足需求
        z_score = 1.645

        if not selected_lockers:
            return total_transport_cost, total_penalty_cost, 0, assigned_demand_to_locker

        # 1. 估算每个储物柜的有效服务能力 (容量和无人机运力的较小者)
        effective_capacities = {j: 0.0 for j in selected_lockers}
        for j in selected_lockers:
            locker_cap = self.problem.Q_locker_capacity.get(j, 0)

            # 使用预计算的无人机运力（如果可用）
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                drone_cap = self.problem.fast_solver._get_drone_capacity_fast(j, n_star.get(j, 0))
            else:
                # 回退到简化计算
                num_drones = n_star.get(j, 0)
                if num_drones > 0:
                    # 简化的无人机运力估算
                    avg_service_time = 0.5  # 假设平均服务时间
                    drone_cap = num_drones * self.problem.H_drone_working_hours_per_day / avg_service_time
                else:
                    drone_cap = 0

            effective_capacities[j] = min(locker_cap, drone_cap)

        # 2. 模拟一个考虑竞争的贪心分配
        unassigned_demand = self.problem.expected_demand.copy()

        # 按多重标准排序客户，优先处理"困难"客户
        def customer_difficulty(i):
            # 获取可达储物柜
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                reachable = [j for j in self.problem.fast_solver.reachable_lockers.get(i, []) if j in selected_lockers]
            else:
                reachable = [j for j in selected_lockers
                           if (i, j) in self.problem.distance and
                           2 * self.problem.distance[i, j] <= self.problem.max_flight_distance]

            if not reachable:
                return (0, float('inf'))  # 无法服务的客户最优先

            # 计算可用总容量（考虑竞争）
            total_available_capacity = sum(effective_capacities.get(j, 0) for j in reachable)

            # 困难度 = 需求量 / 可用容量，值越大越困难
            difficulty = unassigned_demand[i] / max(total_available_capacity, 1e-6)

            return (len(reachable), difficulty)  # 先按可达储物柜数量排序，再按困难度排序

        customer_priority = sorted(self.problem.customers, key=customer_difficulty)

        # 3. 执行贪心分配
        for i in customer_priority:
            demand_to_assign = unassigned_demand[i]
            if demand_to_assign <= 1e-6:
                continue

            # 找到成本最低且有容量的储物柜
            best_j = -1
            min_cost = float('inf')

            # 获取可达储物柜
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                reachable_lockers = self.problem.fast_solver.reachable_lockers.get(i, [])
            else:
                # 回退到基于距离的可达性检查
                reachable_lockers = [j for j in selected_lockers
                                   if (i, j) in self.problem.distance and
                                   2 * self.problem.distance[i, j] <= self.problem.max_flight_distance]

            for j in reachable_lockers:
                if j in selected_lockers and effective_capacities[j] > 1e-6:
                    cost = 2 * self.problem.transport_unit_cost * self.problem.distance.get((i, j), float('inf'))
                    if cost < min_cost:
                        min_cost = cost
                        best_j = j

            if best_j != -1:
                assigned_amount = min(demand_to_assign, effective_capacities[best_j])
                total_transport_cost += min_cost * assigned_amount
                effective_capacities[best_j] -= assigned_amount
                unassigned_demand[i] -= assigned_amount
                assigned_demand_to_locker[best_j] += assigned_amount

                # 容量紧张惩罚：当储物柜容量接近耗尽时，增加额外成本
                original_capacity = min(self.problem.Q_locker_capacity.get(best_j, 1),
                                      self.problem.fast_solver._get_drone_capacity_fast(best_j, n_star.get(best_j, 0))
                                      if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver else 100)
                remaining_capacity_ratio = effective_capacities[best_j] / max(original_capacity, 1)

                if remaining_capacity_ratio < 0.2:  # 剩余容量不足20%
                    # 增加拥堵成本，模拟容量紧张对后续分配的影响
                    congestion_penalty = min_cost * assigned_amount * 0.1 * (1 - remaining_capacity_ratio)
                    total_transport_cost += congestion_penalty

        # 4. 计算总惩罚成本和【新的】容量违反惩罚
        total_penalty_cost = sum(d * self.problem.penalty_cost_unassigned for d in unassigned_demand.values())

        # 【修正版】使用平滑的、非线性的惩罚函数
        capacity_violation_penalty = 0
        z_score = 1.645

        for j in selected_lockers:
            expected_demand_on_locker = assigned_demand_to_locker.get(j, 0)

            # --- 物理容量检查 ---
            locker_cap = self.problem.Q_locker_capacity.get(j, 0)
            # 【核心修改】计算超载率，而不是绝对值
            # 即使没超载，接近饱和也应该有轻微的"拥堵成本"
            load_ratio_locker = expected_demand_on_locker / locker_cap if locker_cap > 0 else 100

            # 使用指数惩罚函数。当load_ratio <= 0.8时，惩罚很小；超过1.0后，惩罚急剧增加
            # 调整参数 5 和 self.capacity_penalty_weight/100 来控制曲线的陡峭程度
            try:
                penalty1 = (self.capacity_penalty_weight / 100) * math.exp(5 * (load_ratio_locker - 0.8))
                # 防止数值溢出
                penalty1 = min(penalty1, self.capacity_penalty_weight * 10)
            except OverflowError:
                penalty1 = self.capacity_penalty_weight * 10
            capacity_violation_penalty += penalty1

            # --- 无人机容量检查 ---
            drone_cap = 0
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                drone_cap = self.problem.fast_solver._get_drone_capacity_fast(j, n_star.get(j, 0))

            if drone_cap > 0:
                # 同样使用风险调整后的需求来评估无人机系统压力
                variance_on_locker = expected_demand_on_locker
                std_dev_on_locker = math.sqrt(variance_on_locker) if variance_on_locker > 0 else 0
                risk_adjusted_demand = expected_demand_on_locker + z_score * std_dev_on_locker

                load_ratio_drone = risk_adjusted_demand / drone_cap

                # 对无人机也使用类似的指数惩罚
                try:
                    penalty2 = (self.capacity_penalty_weight / 200) * math.exp(5 * (load_ratio_drone - 0.8))
                    # 防止数值溢出
                    penalty2 = min(penalty2, self.capacity_penalty_weight * 5)
                except OverflowError:
                    penalty2 = self.capacity_penalty_weight * 5
                capacity_violation_penalty += penalty2

        return total_transport_cost, total_penalty_cost, capacity_violation_penalty, assigned_demand_to_locker

    def _is_feasible(self, solution):
        """
        检查解是否满足基本约束条件（简化版本，避免过于严格）
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 1. 至少开设一个储物柜
        if not selected_lockers:
            return False

        # 2. 每个开放的储物柜至少配置一架无人机
        for j in selected_lockers:
            if n_star.get(j, 0) < 1:
                return False

        # 3. 基本合理性检查：无人机数量不能过多
        for j in selected_lockers:
            if n_star.get(j, 0) > 10:  # 限制最大无人机数量
                return False

        return True

    def create_initial_solution(self, return_multiple=False):
        """
        生成完整的初始解（整体求解版）

        返回格式：
        solution = {
            'y': {j: 0/1},                    # 第一阶段：储物柜选址
            'n': {j: num_drones},             # 第一阶段：无人机配置
            'x': {(i,j,k): float},            # 第二阶段：客户分配
            'truck_routes': {k: [...]}        # 第二阶段：卡车路径
        }

        整体求解：同时生成第一阶段和第二阶段的完整决策。

        Args:
            return_multiple: 如果为True，返回多个候选解列表；否则返回最佳解
        """
        try:
            # 调试信息已移除以减少冗余输出

            # 生成多个候选解
            candidates = []

            # 使用动态成本效益策略（去掉预设覆盖率的主观限制）
            try:
                print(f"  使用动态成本效益策略生成初始解...")
                solution = self._create_cost_benefit_solution_dynamic()
                if solution and self._is_feasible(solution):
                    obj = self._calculate_objective_heuristic(solution, 0)
                    candidates.append({
                        'solution': solution,
                        'objective': obj,
                        'strategy': 'dynamic_cost_benefit'
                    })
                    print(f"    成功生成解，目标值: {obj:.2f}")
            except Exception as e:
                print(f"    动态成本效益策略失败: {str(e)}")

            # 备用策略：简单解
            if not candidates:
                try:
                    fallback_solution = self._create_fallback_solution()
                    if fallback_solution and self._is_feasible(fallback_solution):
                        obj = self._calculate_objective_heuristic(fallback_solution, 0)
                        candidates.append({
                            'solution': fallback_solution,
                            'objective': obj,
                            'strategy': 'fallback'
                        })
                        print(f"    备用解目标值: {obj:.2f}")
                except Exception as e2:
                    print(f"    备用策略也失败: {str(e2)}")

            # 如果没有生成任何候选解，使用回退解
            if not candidates:
                fallback = self._create_fallback_solution()
                if fallback:
                    candidates.append({
                        'solution': fallback,
                        'objective': self._calculate_objective_heuristic(fallback, 0),
                        'strategy': 'fallback'
                    })

            if not candidates:
                return [] if return_multiple else None

            # 按目标值排序
            candidates.sort(key=lambda x: x['objective'])

            if return_multiple:
                # 返回前3-5个不同的候选解，确保多样性
                diverse_candidates = []
                for candidate in candidates[:8]:  # 从前8个中选择
                    # 检查是否与已选择的解足够不同
                    is_diverse = True
                    for selected in diverse_candidates:
                        if self._solutions_too_similar(candidate['solution'], selected['solution']):
                            is_diverse = False
                            break

                    if is_diverse:
                        diverse_candidates.append(candidate)
                        if len(diverse_candidates) >= 5:  # 最多返回5个
                            break

                print(f"  生成了 {len(diverse_candidates)} 个多样化候选解:")
                for i, candidate in enumerate(diverse_candidates):
                    print(f"    候选解{i+1}: {candidate['strategy']}, 目标值: {candidate['objective']:.2f}")

                return [c['solution'] for c in diverse_candidates]
            else:
                # 返回最佳解
                return candidates[0]['solution']

        except Exception as e:
            print(f"  初始解生成失败: {e}")
            fallback = self._create_fallback_solution()
            return [fallback] if return_multiple and fallback else fallback

    def _solutions_too_similar(self, solution1, solution2, threshold=0.7):
        """
        检查两个解是否过于相似

        Args:
            solution1, solution2: 要比较的解
            threshold: 相似度阈值，超过此值认为过于相似
        """
        if not solution1 or not solution2:
            return False

        # 比较储物柜选择的相似度
        lockers1 = set(j for j, val in solution1['y'].items() if val > 0.5)
        lockers2 = set(j for j, val in solution2['y'].items() if val > 0.5)

        if not lockers1 or not lockers2:
            return False

        # 计算Jaccard相似度
        intersection = len(lockers1.intersection(lockers2))
        union = len(lockers1.union(lockers2))
        jaccard_similarity = intersection / union if union > 0 else 0

        # 如果储物柜选择相似度过高，认为解过于相似
        return jaccard_similarity > threshold

    def _create_greedy_solution(self):
        """基于贪心策略的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化所有储物柜为关闭状态
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算每个储物柜的"吸引力"分数
        locker_scores = {}
        for j in self.problem.sites:
            score = 0
            reachable_customers = 0
            total_expected_demand = 0

            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers += 1
                        # 距离越近，分数越高
                        distance_score = 1.0 / (1.0 + self.problem.distance[i, j])
                        demand_score = self.problem.expected_demand[i]
                        score += distance_score * demand_score
                        total_expected_demand += self.problem.expected_demand[i]

            # 考虑储物柜固定成本
            if reachable_customers > 0:
                cost_penalty = self.problem.locker_fixed_cost[j] / 10000.0  # 归一化
                locker_scores[j] = score - cost_penalty
            else:
                locker_scores[j] = -float('inf')  # 无法服务任何客户

        # 贪心选择储物柜，确保满足服务能力要求
        sorted_lockers = sorted(locker_scores.items(), key=lambda x: x[1], reverse=True)

        # 计算总需求
        total_demand = sum(self.problem.expected_demand.values())

        # 选择足够的储物柜以避免过高的惩罚成本
        if sorted_lockers and sorted_lockers[0][1] > -float('inf'):
            # 更积极的初始选择：选择更多储物柜以确保服务覆盖
            positive_score_lockers = [s for s in sorted_lockers if s[1] > 0]
            num_to_select = min(max(4, len(positive_score_lockers) // 2), 8)  # 选择4-8个储物柜
            num_to_select = max(1, num_to_select)  # 至少选择1个

            # 确保不超过可用储物柜数量
            num_to_select = min(num_to_select, len(sorted_lockers))

            for i in range(num_to_select):
                if i < len(sorted_lockers):  # 双重检查索引有效性
                    j = sorted_lockers[i][0]
                    solution['y'][j] = 1

                # 估算需要的无人机数量
                estimated_demand = 0
                for customer in self.problem.customers:
                    if (customer, j) in self.problem.distance:
                        flight_distance = 2 * self.problem.distance[customer, j]
                        if flight_distance <= self.problem.max_flight_distance:
                            # 简单分配：按距离权重分配需求
                            weight = 1.0 / (1.0 + self.problem.distance[customer, j])
                            estimated_demand += self.problem.expected_demand[customer] * weight / num_to_select

                # 计算所需无人机数量（更精确的计算）
                if estimated_demand > 0:
                    # 计算该储物柜的平均服务距离
                    total_distance = 0
                    reachable_count = 0
                    for customer in self.problem.customers:
                        if (customer, j) in self.problem.distance:
                            flight_distance = 2 * self.problem.distance[customer, j]
                            if flight_distance <= self.problem.max_flight_distance:
                                total_distance += self.problem.distance[customer, j]
                                reachable_count += 1

                    if reachable_count > 0:
                        avg_distance = total_distance / reachable_count
                        avg_service_time = (2 * avg_distance / self.problem.drone_speed) + self.problem.loading_time
                        total_time_needed = estimated_demand * avg_service_time
                        drones_needed = math.ceil(total_time_needed / self.problem.H_drone_working_hours_per_day)
                        # 确保有足够的无人机运力，考虑服务能力要求
                        min_drones = max(1, math.ceil(estimated_demand / (self.problem.H_drone_working_hours_per_day / avg_service_time)))
                        # 适当增加无人机配置以提供服务缓冲
                        recommended_drones = max(min_drones, math.ceil(drones_needed * 1.5))  # 增加50%缓冲
                        solution['n'][j] = min(recommended_drones, 15)  # 增加上限到15架
                    else:
                        solution['n'][j] = 2  # 如果无法计算，默认2架
                else:
                    solution['n'][j] = 1  # 至少1架无人机

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution



    def _create_coverage_based_solution(self):
        """基于服务覆盖率的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算每个储物柜的覆盖能力
        coverage_scores = {}
        for j in self.problem.sites:
            covered_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        covered_demand += self.problem.expected_demand[i]
            coverage_scores[j] = covered_demand

        # 选择覆盖能力最强的储物柜
        sorted_by_coverage = sorted(coverage_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前5个储物柜，并智能配置无人机
        for i in range(min(5, len(sorted_by_coverage))):
            j = sorted_by_coverage[i][0]
            if sorted_by_coverage[i][1] > 0:  # 确保有覆盖能力
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution

    def _create_balanced_solution(self):
        """平衡成本和覆盖的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算成本效益比
        efficiency_scores = {}
        for j in self.problem.sites:
            covered_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        covered_demand += self.problem.expected_demand[i]

            if covered_demand > 0:
                # 效益 = 覆盖需求 / (固定成本 + 无人机成本)
                total_cost = self.problem.locker_fixed_cost[j] + self.problem.drone_cost
                efficiency_scores[j] = covered_demand / total_cost
            else:
                efficiency_scores[j] = 0

        # 选择效益最高的储物柜
        sorted_by_efficiency = sorted(efficiency_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前5个储物柜，智能配置无人机
        for i in range(min(5, len(sorted_by_efficiency))):
            j = sorted_by_efficiency[i][0]
            if sorted_by_efficiency[i][1] > 0:
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution

    def _create_saa_inspired_solution(self):
        """基于saa_g_r.py最优解的启发式初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 基于saa_g_r.py的最优解模式：选择储物柜[1,2,4,5,6]
        optimal_pattern = [1, 2, 4, 5, 6]
        for j in optimal_pattern:
            if j in self.problem.sites:
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量，而非固定1架
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution





    def _create_cost_benefit_solution_dynamic(self):
        """
        【整体求解版】基于边际效益的初始解生成策略，生成完整的解。
        同时决定储物柜选址、客户分配和卡车路径。
        """
        # 初始化完整解结构
        solution = {
            'y': {},
            'n': {},
            'x': {},
            'truck_routes': {}
        }

        # 初始化第一阶段决策
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化第二阶段决策
        for k in range(len(self.demand_samples)):
            solution['truck_routes'][k] = []
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0.0

        # 1. 计算所有潜在储物柜的"潜在效益分"
        # 这里的效益分可以是覆盖的期望需求，或者更复杂的指标
        potential_lockers = []
        for j in self.problem.sites:
            # 估算可覆盖的期望需求
            reachable_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance and \
                   2 * self.problem.distance[i, j] <= self.problem.max_flight_distance:
                    reachable_demand += self.problem.expected_demand[i]

            if reachable_demand > 0:
                # 成本效益分 = 潜在覆盖需求 / (储物柜成本 + 1架无人机成本)
                base_cost = self.problem.locker_fixed_cost.get(j, 0) + self.problem.drone_cost
                if base_cost > 0:
                    score = reachable_demand / base_cost
                    potential_lockers.append({'id': j, 'score': score, 'cost': base_cost})

        # 2. 按得分从高到低排序
        potential_lockers.sort(key=lambda item: item['score'], reverse=True)

        # 3. 序贯决策：动态决定是否开启下一个储物柜
        selected_lockers = set()

        # 初始状态：没有储物柜，所有需求都是未满足的，惩罚成本巨大
        # 我们用一个简化的方式来估算总惩罚成本
        total_expected_demand = sum(self.problem.expected_demand.values())
        current_estimated_penalty = total_expected_demand * self.problem.penalty_cost_unassigned

        for locker_info in potential_lockers:
            j = locker_info['id']

            # --- 估算开启这个储物柜的"边际效益" ---
            # 效益 = 能新覆盖的客户所带来的惩罚成本的降低

            # 找到只被这个新储物柜 j 覆盖，而未被已选储物柜覆盖的客户
            newly_covered_demand = 0
            for i in self.problem.customers:
                # 检查客户 i 是否能被 j 服务
                can_be_served_by_j = (i, j) in self.problem.distance and \
                                     2 * self.problem.distance[i, j] <= self.problem.max_flight_distance
                if not can_be_served_by_j:
                    continue

                # 检查客户 i 是否已经被之前选的储物柜服务
                already_covered = False
                for selected_j in selected_lockers:
                    if (i, selected_j) in self.problem.distance and \
                       2 * self.problem.distance[i, selected_j] <= self.problem.max_flight_distance:
                        already_covered = True
                        break

                if not already_covered:
                    newly_covered_demand += self.problem.expected_demand[i]

            # 边际效益 = 新覆盖的需求 * 惩罚单价
            marginal_benefit = newly_covered_demand * self.problem.penalty_cost_unassigned

            # --- 估算开启这个储物柜的"边际成本" ---
            # 成本 = 固定成本 + 推荐的无人机成本
            # (这里为了简化，我们只考虑储物柜和1架无人机的基础成本)
            marginal_cost = locker_info['cost']

            # --- 决策：如果边际效益大于边际成本，就开启 ---
            if marginal_benefit > marginal_cost:
                # 决策：开启！
                selected_lockers.add(j)
                # 更新估算的惩罚成本（简单减去效益）
                current_estimated_penalty -= marginal_benefit
            else:
                # 如果对于当前性价比最高的储物柜，开启它都已经"不划算"了，
                # 那么对于后续性价比更低的储物柜，就更不划算了。可以提前终止。
                break

        # 4. 确保至少有一个储物柜被开启 (防止极端情况)
        if not selected_lockers and potential_lockers:
            j = potential_lockers[0]['id']
            selected_lockers.add(j)

        # 5. 根据最终选择的储物柜构建第一阶段解
        for j in selected_lockers:
            solution['y'][j] = 1
            # 重新为最终选择的储物柜智能配置无人机
            estimated_demand = self._estimate_locker_demand(j, solution)
            recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
            solution['n'][j] = max(1, recommended_drones)

        # 6. 生成第二阶段决策：客户分配和卡车路径
        selected_locker_list = list(selected_lockers)
        solution = self._complete_solution_with_second_stage(solution, selected_locker_list)

        return solution

    def _complete_solution_with_second_stage(self, solution, selected_lockers):
        """
        为给定的第一阶段决策生成完整的第二阶段决策
        包括客户分配和卡车路径
        """
        # 为每个需求场景生成客户分配和卡车路径
        for k, demand_scenario in enumerate(self.demand_samples):
            # 使用贪心算法进行客户分配
            assignment = self._solve_customer_assignment_greedy(
                selected_lockers, demand_scenario, solution['n']
            )

            # 更新客户分配决策
            for i in self.problem.customers:
                for j in self.problem.sites:
                    if j in selected_lockers and (i, j) in assignment:
                        solution['x'][(i, j, k)] = assignment[(i, j)]
                    else:
                        solution['x'][(i, j, k)] = 0.0

            # 计算储物柜需求并生成卡车路径
            locker_demands = {}
            for j in selected_lockers:
                total_demand = sum(solution['x'][(i, j, k)] for i in self.problem.customers)
                if total_demand > 0:
                    locker_demands[j] = total_demand

            # 生成卡车路径（简化版TSP）
            if locker_demands:
                truck_route = self._generate_truck_route_simple(locker_demands)
                solution['truck_routes'][k] = truck_route
            else:
                solution['truck_routes'][k] = []

        return solution

    def _solve_customer_assignment_greedy(self, selected_lockers, demand_scenario, drone_config):
        """
        使用贪心算法为给定场景分配客户到储物柜（带容量约束）
        """
        assignment = {}

        # 使用储物柜的真实容量限制
        locker_capacities = {}
        for j in selected_lockers:
            # 使用储物柜的物理容量限制
            capacity = self.problem.Q_locker_capacity.get(j, 30)  # 默认容量30
            locker_capacities[j] = capacity

        # 跟踪每个储物柜的已分配需求
        locker_assigned = {j: 0 for j in selected_lockers}

        # 按客户需求从大到小排序，优先分配大需求客户
        customers_sorted = sorted(self.problem.customers,
                                key=lambda i: demand_scenario.get(i, 0), reverse=True)

        for i in customers_sorted:
            demand = demand_scenario.get(i, 0)
            if demand <= 0:
                continue

            # 找到最近的可达且有足够容量的储物柜
            best_locker = None
            best_distance = float('inf')

            for j in selected_lockers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        # 检查容量约束
                        remaining_capacity = locker_capacities[j] - locker_assigned[j]
                        if remaining_capacity >= demand:
                            if self.problem.distance[i, j] < best_distance:
                                best_distance = self.problem.distance[i, j]
                                best_locker = j

            if best_locker is not None:
                assignment[(i, best_locker)] = demand
                locker_assigned[best_locker] += demand
            else:
                # 如果没有储物柜能完全满足需求，尝试部分分配
                remaining_demand = demand
                for j in selected_lockers:
                    if remaining_demand <= 0:
                        break
                    if (i, j) in self.problem.distance:
                        flight_distance = 2 * self.problem.distance[i, j]
                        if flight_distance <= self.problem.max_flight_distance:
                            remaining_capacity = locker_capacities[j] - locker_assigned[j]
                            if remaining_capacity > 0:
                                allocated = min(remaining_demand, remaining_capacity)
                                if (i, j) in assignment:
                                    assignment[(i, j)] += allocated
                                else:
                                    assignment[(i, j)] = allocated
                                locker_assigned[j] += allocated
                                remaining_demand -= allocated

        return assignment

    def _generate_truck_route_simple(self, locker_demands):
        """
        生成简化的卡车路径（最近邻启发式）
        """
        if not locker_demands:
            return []

        # 简化版：按储物柜ID排序访问
        route = [0]  # 从仓库开始
        route.extend(sorted(locker_demands.keys()))
        route.append(0)  # 回到仓库

        return route

    def _create_fallback_solution(self):
        """备用简单完整解（整体求解版）"""
        # 初始化完整解结构
        solution = {
            'y': {},
            'n': {},
            'x': {},
            'truck_routes': {}
        }

        # 检查是否有可用的储物柜站点
        if not self.problem.sites:
            print("  警告: 没有可用的储物柜站点")
            return None

        # 选择第一个储物柜站点作为备用解
        first_site = self.problem.sites[0]

        # 初始化第一阶段决策
        for j in self.problem.sites:
            solution['y'][j] = 1 if j == first_site else 0
            solution['n'][j] = 2 if j == first_site else 0

        # 初始化第二阶段决策
        for k in range(len(self.demand_samples)):
            solution['truck_routes'][k] = []
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0.0

        # 生成完整的第二阶段决策
        solution = self._complete_solution_with_second_stage(solution, [first_site])

        return solution

    def _select_operator(self, operators, weights):
        """
        根据权重随机选择算子
        """
        total_weight = sum(weights[op.__name__] for op in operators)
        if total_weight <= 0:
            return random.choice(operators)

        rand_val = random.uniform(0, total_weight)
        cumulative_weight = 0

        for op in operators:
            cumulative_weight += weights[op.__name__]
            if rand_val <= cumulative_weight:
                return op

        return operators[-1]  # 回退

    def _update_operator_weights(self):
        """
        根据算子的成功率或分数更新权重
        """
        if self.config['use_score_based_weights']:
            # 基于分数的权重更新
            self._update_weights_by_score()
        else:
            # 传统的基于成功率的权重更新
            self._update_weights_by_success_rate()

    def _reset_operator_weights(self):
        """
        重置算子权重和统计信息，用于重启机制
        """
        print(f"      重置算子权重和统计信息...")

        # 重置权重为初始值
        self.destroy_weights = {op.__name__: 1.0 for op in self.destroy_operators}
        self.repair_weights = {op.__name__: 1.0 for op in self.repair_operators}

        # 重置使用统计
        self.destroy_usage = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_usage = {op.__name__: 0 for op in self.repair_operators}

        # 重置成功统计
        self.destroy_success = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_success = {op.__name__: 0 for op in self.repair_operators}

        # 重置分数统计
        self.destroy_scores = {op.__name__: 0.0 for op in self.destroy_operators}
        self.repair_scores = {op.__name__: 0.0 for op in self.repair_operators}

    def _update_weights_by_score(self):
        """
        基于论文公式的权重更新策略：ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
        其中：ω为权重，μ为更新系数，π为算子得分，β为算子使用次数
        """
        mu = self.config['weight_update_coefficient']  # μ = 0.1

        # 更新破坏算子权重
        for op in self.destroy_operators:
            op_name = op.__name__
            if self.destroy_usage[op_name] > 0:
                # π/β：平均得分
                avg_score = self.destroy_scores[op_name] / self.destroy_usage[op_name]
                # ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
                self.destroy_weights[op_name] = (self.destroy_weights[op_name] * (1 - mu) +
                                               mu * avg_score)
            self.destroy_weights[op_name] = max(0.1, self.destroy_weights[op_name])  # 最小权重

        # 更新修复算子权重
        for op in self.repair_operators:
            op_name = op.__name__
            if self.repair_usage[op_name] > 0:
                # π/β：平均得分
                avg_score = self.repair_scores[op_name] / self.repair_usage[op_name]
                # ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
                self.repair_weights[op_name] = (self.repair_weights[op_name] * (1 - mu) +
                                              mu * avg_score)
            self.repair_weights[op_name] = max(0.1, self.repair_weights[op_name])  # 最小权重

    def _update_weights_by_success_rate(self):
        """
        传统的基于成功率的权重更新策略
        """
        decay = self.config['weight_decay']

        # 更新破坏算子权重
        for op in self.destroy_operators:
            op_name = op.__name__
            if self.destroy_usage[op_name] > 0:
                success_rate = self.destroy_success[op_name] / self.destroy_usage[op_name]
                self.destroy_weights[op_name] = (self.destroy_weights[op_name] * decay +
                                               success_rate * (1 - decay))
            self.destroy_weights[op_name] = max(0.1, self.destroy_weights[op_name])  # 最小权重

        # 更新修复算子权重
        for op in self.repair_operators:
            op_name = op.__name__
            if self.repair_usage[op_name] > 0:
                success_rate = self.repair_success[op_name] / self.repair_usage[op_name]
                self.repair_weights[op_name] = (self.repair_weights[op_name] * decay +
                                              success_rate * (1 - decay))
            self.repair_weights[op_name] = max(0.1, self.repair_weights[op_name])  # 最小权重

    # ===== 整体求解破坏算子 =====

    def integrated_random_removal(self, solution, iteration=0):
        """
        随机移除储物柜及其相关的客户分配和卡车路径（整体求解版）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution  # 至少保留一个储物柜

        # 随机选择要移除的储物柜数量（1-2个）
        num_to_remove = min(random.randint(1, 2), len(open_lockers) - 1)
        lockers_to_remove = random.sample(open_lockers, num_to_remove)

        # 移除储物柜（第一阶段决策）
        for j in lockers_to_remove:
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        # 移除相关的客户分配（第二阶段决策）
        for k in range(len(self.demand_samples)):
            for i in self.problem.customers:
                for j in lockers_to_remove:
                    new_solution['x'][(i, j, k)] = 0.0

        # 重新生成卡车路径
        remaining_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]
        new_solution = self._complete_solution_with_second_stage(new_solution, remaining_lockers)

        return new_solution

    def integrated_worst_removal(self, solution, iteration=0):
        """
        移除贡献最小的储物柜及其相关分配（整体求解版）
        贡献度 = 该储物柜对整体解质量的贡献，通过移除前后的目标函数差值计算
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution

        # 计算每个储物柜的真实贡献度（移除该储物柜后的成本增加）
        current_obj = self._calculate_objective_exact(solution)
        locker_contributions = {}

        for j in open_lockers:
            # 创建移除储物柜j的临时解
            temp_solution = copy.deepcopy(solution)
            temp_solution['y'][j] = 0
            temp_solution['n'][j] = 0

            # 移除相关的客户分配
            for k in range(len(self.demand_samples)):
                for i in self.problem.customers:
                    temp_solution['x'][(i, j, k)] = 0.0

            # 重新生成完整解
            remaining_lockers = [l for l, val in temp_solution['y'].items() if val > 0.5]
            temp_solution = self._complete_solution_with_second_stage(temp_solution, remaining_lockers)

            # 计算移除后的目标函数值
            temp_obj = self._calculate_objective_exact(temp_solution)

            # 贡献度 = 移除后的成本增加（越小说明贡献越小）
            contribution = temp_obj - current_obj
            locker_contributions[j] = contribution

        # 移除贡献最小的储物柜（即移除后成本增加最少的）
        sorted_lockers = sorted(locker_contributions.items(), key=lambda x: x[1])
        num_to_remove = random.randint(1, max(1, len(open_lockers) // 3))  # 最多移除1/3

        lockers_to_remove = []
        for i in range(min(num_to_remove, len(sorted_lockers))):
            j = sorted_lockers[i][0]
            lockers_to_remove.append(j)
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        # 移除相关的客户分配
        for k in range(len(self.demand_samples)):
            for i in self.problem.customers:
                for j in lockers_to_remove:
                    new_solution['x'][(i, j, k)] = 0.0

        # 重新生成卡车路径
        remaining_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]
        new_solution = self._complete_solution_with_second_stage(new_solution, remaining_lockers)

        return new_solution

    def integrated_cluster_removal(self, solution, iteration=0):
        """
        移除相关储物柜簇及其分配（整体求解版：综合地理位置和客户服务重叠度）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution

        seed_locker = random.choice(open_lockers)

        # 计算每个储物柜和种子储物柜的相关性
        relatedness = {}

        # 1. 预计算每个客户到所有开放储物柜的成本列表
        customer_service_options = {}
        for i in self.problem.customers:
            options = []
            for j in open_lockers:
                 if (i, j) in self.problem.distance and \
                    2 * self.problem.distance[i, j] <= self.problem.max_flight_distance:
                     cost = self.problem.distance[i, j]  # 简化成本为距离
                     options.append((j, cost))
            options.sort(key=lambda x: x[1])
            customer_service_options[i] = [opt[0] for opt in options]

        for j in open_lockers:
            if j == seed_locker:
                continue

            # a. 地理位置相关性 (权重 w1)
            if j in self.problem.site_coords and seed_locker in self.problem.site_coords:
                coord1 = self.problem.site_coords[seed_locker]
                coord2 = self.problem.site_coords[j]
                # 归一化距离
                geo_dist = math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
                max_dist = 20  # 假设场景最大距离
                geo_relatedness = max(0, 1 - (geo_dist / max_dist))
            else:
                geo_relatedness = 0

            # b. 客户服务重叠度 (权重 w2)
            shared_customers = 0
            for i in self.problem.customers:
                # 如果两个储物柜都是某客户的前2优选择，则认为它们共享该客户
                top_options = customer_service_options.get(i, [])[:2]
                if seed_locker in top_options and j in top_options:
                    shared_customers += 1

            service_relatedness = shared_customers / len(self.problem.customers) if self.problem.customers else 0

            # 综合相关性
            w1, w2 = 0.5, 0.5
            relatedness[j] = w1 * geo_relatedness + w2 * service_relatedness

        # 按相关性从高到低排序，移除最相关的几个
        lockers_to_remove = []
        if relatedness:
            sorted_by_relatedness = sorted(relatedness.items(), key=lambda x: x[1], reverse=True)
            num_to_remove = random.randint(1, max(1, len(open_lockers) // 3))

            for i in range(min(num_to_remove, len(sorted_by_relatedness))):
                j_to_remove = sorted_by_relatedness[i][0]
                lockers_to_remove.append(j_to_remove)
                new_solution['y'][j_to_remove] = 0
                new_solution['n'][j_to_remove] = 0

        # 移除相关的客户分配
        for k in range(len(self.demand_samples)):
            for i in self.problem.customers:
                for j in lockers_to_remove:
                    new_solution['x'][(i, j, k)] = 0.0

        # 重新生成卡车路径
        remaining_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]
        new_solution = self._complete_solution_with_second_stage(new_solution, remaining_lockers)

        return new_solution

    def integrated_route_removal(self, solution, iteration=0):
        """
        基于卡车路径的移除算子（整体求解版）
        移除卡车路径上的部分储物柜及其相关分配
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution

        # 随机选择一个场景的卡车路径
        scenario_idx = random.randint(0, len(self.demand_samples) - 1)
        truck_route = solution['truck_routes'].get(scenario_idx, [])

        if len(truck_route) <= 2:  # 只有仓库的路径
            return self.integrated_random_removal(solution, iteration)

        # 从路径中选择连续的储物柜段进行移除
        route_lockers = [j for j in truck_route if j != 0 and j in open_lockers]
        if len(route_lockers) <= 1:
            return self.integrated_random_removal(solution, iteration)

        # 选择连续的储物柜段
        start_idx = random.randint(0, len(route_lockers) - 1)
        end_idx = min(start_idx + random.randint(1, 3), len(route_lockers))
        lockers_to_remove = route_lockers[start_idx:end_idx]

        # 确保不移除所有储物柜
        if len(lockers_to_remove) >= len(open_lockers):
            lockers_to_remove = lockers_to_remove[:len(open_lockers) - 1]

        # 移除储物柜
        for j in lockers_to_remove:
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        # 移除相关的客户分配
        for k in range(len(self.demand_samples)):
            for i in self.problem.customers:
                for j in lockers_to_remove:
                    new_solution['x'][(i, j, k)] = 0.0

        # 重新生成卡车路径
        remaining_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]
        new_solution = self._complete_solution_with_second_stage(new_solution, remaining_lockers)

        return new_solution

    def drone_adjustment_removal(self, solution, iteration=0):
        """
        调整无人机配置（减少无人机数量）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        # 随机选择几个储物柜    减少无人机
        num_to_adjust = random.randint(1, max(1, len(open_lockers)))
        lockers_to_adjust = random.sample(open_lockers, num_to_adjust)

        for j in lockers_to_adjust:
            current_drones = solution['n'][j]
            if current_drones > 1:
                reduction = random.randint(1, max(1, int(current_drones // 2)))
                new_solution['n'][j] = max(1, current_drones - reduction)

        return new_solution

    def cluster_removal(self, solution, iteration=0):
        """
        簇移除：基于客户分配关系，移除功能上相似的储物柜。
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 2:
            return new_solution

        # 1. 估算每个客户主要由哪个储物柜服务
        customer_main_locker = {}
        for i in self.problem.customers:
            best_j = -1
            min_dist = float('inf')
            for j in open_lockers:
                if (i, j) in self.problem.distance:
                    dist = self.problem.distance[i, j]
                    if dist < min_dist:
                        min_dist = dist
                        best_j = j
            if best_j != -1:
                customer_main_locker[i] = best_j

        # 2. 随机选择一个种子储物柜
        seed_locker = random.choice(open_lockers)

        # 3. 找到与种子储物柜服务相似客户群体的其他储物柜
        seed_customers = {i for i, j in customer_main_locker.items() if j == seed_locker}
        if not seed_customers:
            return new_solution  # 种子储物柜没服务客户，无法形成簇

        cluster_to_remove = {seed_locker}
        for j in open_lockers:
            if j != seed_locker:
                other_customers = {i for i, l in customer_main_locker.items() if l == j}
                # 计算Jaccard相似度
                intersection = len(seed_customers.intersection(other_customers))
                union = len(seed_customers.union(other_customers))
                if union > 0 and (intersection / union) > 0.2:  # 相似度阈值
                    cluster_to_remove.add(j)

        # 4. 移除整个簇（但至少保留一个储物柜）
        if len(open_lockers) - len(cluster_to_remove) < 1:
            # 如果要移除所有，则只移除一部分
            cluster_to_remove = set(random.sample(list(cluster_to_remove), len(cluster_to_remove) - 1))

        for j in cluster_to_remove:
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def zone_removal(self, solution, iteration=0):
        """
        区域移除算子：基于地理位置的大范围破坏
        """
        try:
            y_star = solution['y']
            n_star = solution['n']
            open_lockers = [j for j, val in y_star.items() if val > 0.5]

            if len(open_lockers) <= 1:
                return solution

            # 随机选择一个中心储物柜
            center_j = random.choice(open_lockers)

            # 计算所有储物柜到中心的距离
            distances = []
            for j in open_lockers:
                if j != center_j:
                    # 使用客户作为中介计算储物柜间的"服务距离"
                    min_dist = float('inf')
                    for i in self.problem.customers:
                        if ((center_j, i) in self.problem.distance and
                            (j, i) in self.problem.distance):
                            dist = abs(self.problem.distance[center_j, i] - self.problem.distance[j, i])
                            min_dist = min(min_dist, dist)

                    if min_dist < float('inf'):
                        distances.append((j, min_dist))

            if not distances:
                return solution

            # 按距离排序，移除最近的几个储物柜（形成一个"空白区域"）
            distances.sort(key=lambda x: x[1])
            removal_count = min(len(distances), max(1, len(open_lockers) // 3))

            new_solution = copy.deepcopy(solution)
            new_solution['y'][center_j] = 0  # 移除中心
            new_solution['n'][center_j] = 0

            for i in range(removal_count):
                j = distances[i][0]
                new_solution['y'][j] = 0
                new_solution['n'][j] = 0

            return new_solution

        except Exception as e:
            return solution

    def radical_restructure(self, solution, iteration=0):
        """
        激进重构算子：大幅度改变解的结构，专门用于跳出局部最优
        """
        try:
            y_star = solution['y']
            n_star = solution['n']
            open_lockers = [j for j, val in y_star.items() if val > 0.5]

            if len(open_lockers) <= 2:
                return solution

            new_solution = copy.deepcopy(solution)

            # 策略1: 随机关闭50-70%的储物柜
            if random.random() < 0.4:
                removal_ratio = random.uniform(0.5, 0.7)
                removal_count = max(1, int(len(open_lockers) * removal_ratio))
                lockers_to_remove = random.sample(open_lockers, removal_count)

                for j in lockers_to_remove:
                    new_solution['y'][j] = 0
                    new_solution['n'][j] = 0

            # 策略2: 【改进】智能重新配置无人机分布
            elif random.random() < 0.7:
                # 保持储物柜选择，但智能重新分配无人机
                for j in open_lockers:
                    # 重新估算该储物柜的需求和推荐无人机数量
                    estimated_demand = self._estimate_locker_demand(j, new_solution)
                    recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

                    # 添加一些随机扰动以增加多样性
                    perturbation = random.randint(-1, 2)  # -1, 0, 1, 2的随机扰动
                    final_drones = max(1, recommended_drones + perturbation)
                    new_solution['n'][j] = min(final_drones, 8)  # 限制最大值

            # 策略3: 混合策略 - 部分关闭 + 重新配置
            else:
                # 关闭30-50%的储物柜
                removal_ratio = random.uniform(0.3, 0.5)
                removal_count = max(1, int(len(open_lockers) * removal_ratio))
                lockers_to_remove = random.sample(open_lockers, removal_count)

                for j in lockers_to_remove:
                    new_solution['y'][j] = 0
                    new_solution['n'][j] = 0

                # 【改进】对剩余储物柜智能重新配置无人机
                remaining_lockers = [j for j in open_lockers if j not in lockers_to_remove]
                for j in remaining_lockers:
                    # 重新估算需求和推荐无人机数量
                    estimated_demand = self._estimate_locker_demand(j, new_solution)
                    recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

                    # 添加随机扰动
                    perturbation = random.randint(-1, 2)
                    final_drones = max(1, recommended_drones + perturbation)
                    new_solution['n'][j] = min(final_drones, 8)

            return new_solution

        except Exception as e:
            return solution

    # ===== 整体求解修复算子 =====

    def integrated_greedy_insertion(self, solution, iteration=0):
        """
        贪心插入储物柜并重新生成完整解（整体求解版）
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 评估每个候选储物柜的插入效果
        current_obj = self._calculate_objective_exact(solution)
        best_locker = None
        best_drones = 1
        best_improvement = 0

        for j in closed_lockers:
            # 尝试不同的无人机配置
            for num_drones in [1, 2, 3]:
                # 创建临时解
                temp_solution = copy.deepcopy(solution)
                temp_solution['y'][j] = 1
                temp_solution['n'][j] = num_drones

                # 重新生成完整的第二阶段决策
                open_lockers = [l for l, val in temp_solution['y'].items() if val > 0.5]
                temp_solution = self._complete_solution_with_second_stage(temp_solution, open_lockers)

                # 计算改进
                temp_obj = self._calculate_objective_exact(temp_solution)
                improvement = current_obj - temp_obj  # 成本减少为正的改进

                if improvement > best_improvement:
                    best_improvement = improvement
                    best_locker = j
                    best_drones = num_drones

        # 插入最佳储物柜
        if best_locker is not None:
            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

            # 重新生成完整的第二阶段决策
            open_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]
            new_solution = self._complete_solution_with_second_stage(new_solution, open_lockers)

        return new_solution

    def integrated_regret_insertion(self, solution, iteration=0):
        """
        基于后悔值的储物柜插入（整体求解版）
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 计算每个储物柜的后悔值
        current_obj = self._calculate_objective_exact(solution)
        regret_values = {}

        for j in closed_lockers:
            # 计算插入该储物柜的最佳改进
            best_improvement = 0
            for num_drones in [1, 2, 3]:
                temp_solution = copy.deepcopy(solution)
                temp_solution['y'][j] = 1
                temp_solution['n'][j] = num_drones

                open_lockers = [l for l, val in temp_solution['y'].items() if val > 0.5]
                temp_solution = self._complete_solution_with_second_stage(temp_solution, open_lockers)

                temp_obj = self._calculate_objective_exact(temp_solution)
                improvement = current_obj - temp_obj
                best_improvement = max(best_improvement, improvement)

            regret_values[j] = best_improvement

        # 选择后悔值最大的储物柜
        if regret_values:
            best_locker = max(regret_values.items(), key=lambda x: x[1])[0]

            # 为最佳储物柜找到最佳无人机配置
            best_drones = 1
            best_obj = float('inf')
            for num_drones in [1, 2, 3]:
                temp_solution = copy.deepcopy(solution)
                temp_solution['y'][best_locker] = 1
                temp_solution['n'][best_locker] = num_drones

                open_lockers = [l for l, val in temp_solution['y'].items() if val > 0.5]
                temp_solution = self._complete_solution_with_second_stage(temp_solution, open_lockers)

                temp_obj = self._calculate_objective_exact(temp_solution)
                if temp_obj < best_obj:
                    best_obj = temp_obj
                    best_drones = num_drones

            # 插入最佳储物柜
            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

            open_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]
            new_solution = self._complete_solution_with_second_stage(new_solution, open_lockers)

        return new_solution

    def integrated_smart_insertion(self, solution, iteration=0):
        """
        智能插入算子（整体求解版）
        考虑储物柜的地理位置和需求分布
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 计算每个储物柜的智能分数
        locker_scores = {}
        for j in closed_lockers:
            score = 0

            # 1. 覆盖需求分数
            covered_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        covered_demand += self.problem.expected_demand[i]

            # 2. 成本效益分数
            fixed_cost = self.problem.locker_fixed_cost.get(j, 0) + self.problem.drone_cost
            if fixed_cost > 0:
                cost_efficiency = covered_demand / fixed_cost
            else:
                cost_efficiency = 0

            # 3. 地理分散性分数（与现有储物柜的距离）
            open_lockers = [l for l, val in solution['y'].items() if val > 0.5]
            min_distance = float('inf')
            for l in open_lockers:
                if j in self.problem.site_coords and l in self.problem.site_coords:
                    dist = math.sqrt((self.problem.site_coords[j][0] - self.problem.site_coords[l][0])**2 +
                                   (self.problem.site_coords[j][1] - self.problem.site_coords[l][1])**2)
                    min_distance = min(min_distance, dist)

            diversity_score = min_distance if min_distance != float('inf') else 10

            # 综合分数
            locker_scores[j] = 0.5 * cost_efficiency + 0.3 * (covered_demand / 10) + 0.2 * (diversity_score / 10)

        # 选择分数最高的储物柜
        if locker_scores:
            best_locker = max(locker_scores.items(), key=lambda x: x[1])[0]

            # 估算最佳无人机数量
            covered_demand = sum(self.problem.expected_demand[i] for i in self.problem.customers
                               if (i, best_locker) in self.problem.distance and
                               2 * self.problem.distance[i, best_locker] <= self.problem.max_flight_distance)
            best_drones = max(1, min(3, int(covered_demand / 10)))

            # 插入储物柜
            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

            open_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]
            new_solution = self._complete_solution_with_second_stage(new_solution, open_lockers)

        return new_solution

    def _calculate_objective_exact(self, solution):
        """
        精确计算完整解的目标函数值（整体求解版）
        基于完整的第一阶段和第二阶段决策
        """
        # 1. 第一阶段成本
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if not selected_lockers:
            return float('inf')

        first_stage_cost = (sum(self.problem.locker_fixed_cost.get(j, 0) for j in selected_lockers) +
                           sum(self.problem.drone_cost * solution['n'].get(j, 0) for j in selected_lockers))

        # 2. 第二阶段期望成本
        total_second_stage_cost = 0
        num_scenarios = len(self.demand_samples)

        for k in range(num_scenarios):
            # 2.1 无人机运输成本
            drone_transport_cost = 0
            for i in self.problem.customers:
                for j in selected_lockers:
                    assignment = solution['x'].get((i, j, k), 0)
                    if assignment > 0 and (i, j) in self.problem.distance:
                        # 往返距离 * 运输成本 * 分配量
                        drone_transport_cost += 2 * self.problem.distance[i, j] * self.problem.transport_unit_cost * assignment

            # 2.2 惩罚成本（未分配的需求）
            penalty_cost = 0
            for i in self.problem.customers:
                total_assigned = sum(solution['x'].get((i, j, k), 0) for j in selected_lockers)
                demand = self.demand_samples[k][i]
                if total_assigned < demand:
                    penalty_cost += (demand - total_assigned) * self.problem.penalty_cost_unassigned

            # 2.3 卡车运输成本
            truck_cost = 0
            truck_route = solution['truck_routes'].get(k, [])
            if len(truck_route) > 1:
                # 固定成本
                truck_cost += self.problem.truck_fixed_cost

                # 可变成本
                for i in range(len(truck_route) - 1):
                    from_node = truck_route[i]
                    to_node = truck_route[i + 1]
                    if (from_node, to_node) in self.problem.truck_distances:
                        truck_cost += self.problem.truck_distances[from_node, to_node] * self.problem.truck_km_cost

            total_second_stage_cost += drone_transport_cost + penalty_cost + truck_cost

        # 期望第二阶段成本
        expected_second_stage_cost = total_second_stage_cost / num_scenarios

        return first_stage_cost + expected_second_stage_cost

    def _estimate_insertion_delta(self, solution, locker_j, num_drones):
        """
        快速估算插入储物柜j的成本变化（增量评估）
        """
        # 1. 增加的固定成本
        delta_cost = self.problem.locker_fixed_cost[locker_j] + self.problem.drone_cost * num_drones

        # 2. 估算由于新储物柜的加入而减少的运输成本和惩罚成本
        cost_reduction = 0

        # 计算当前解中每个客户的最佳服务成本
        current_customer_costs = {}
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        for i in self.problem.customers:
            min_cost = float('inf')
            for j in open_lockers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, j]
                        min_cost = min(min_cost, transport_cost)

            if min_cost == float('inf'):
                # 客户无法被服务，使用惩罚成本
                current_customer_costs[i] = self.problem.penalty_cost_unassigned
            else:
                current_customer_costs[i] = min_cost

        # 计算新储物柜能为每个客户提供的服务成本
        for i in self.problem.customers:
            if (i, locker_j) in self.problem.distance:
                flight_distance = 2 * self.problem.distance[i, locker_j]
                if flight_distance <= self.problem.max_flight_distance:
                    new_transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, locker_j]

                    # 如果新储物柜能提供更好的服务，计算成本减少
                    if new_transport_cost < current_customer_costs[i]:
                        # 简化：假设客户需求按期望值分配
                        expected_demand = self.problem.expected_demand[i]
                        cost_reduction += (current_customer_costs[i] - new_transport_cost) * expected_demand

        return delta_cost - cost_reduction

    def regret_insertion(self, solution, iteration=0):
        """
        后悔值插入法（支持增量成本估算）
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 计算每个储物柜的插入成本和后悔值
        insertion_costs = {}

        if self.config['use_delta_evaluation']:
            # 使用增量成本估算
            for j in closed_lockers:
                costs = []
                for num_drones in range(1, 4):
                    try:
                        delta_cost = self._estimate_insertion_delta(solution, j, num_drones)
                        costs.append((delta_cost, num_drones))
                    except:
                        costs.append((float('inf'), num_drones))

                # 排序找到最好和第二好的插入位置
                costs.sort()
                if len(costs) >= 2:
                    best_cost, best_drones = costs[0]
                    second_best_cost, _ = costs[1]
                    regret = second_best_cost - best_cost
                    insertion_costs[j] = (regret, best_cost, best_drones)
                elif len(costs) == 1:
                    best_cost, best_drones = costs[0]
                    insertion_costs[j] = (0, best_cost, best_drones)
        else:
            # 使用快速启发式评估
            current_obj = self._calculate_objective_heuristic(solution, iteration)

            for j in closed_lockers:
                costs = []
                for num_drones in range(1, 4):
                    temp_solution = copy.deepcopy(solution)
                    temp_solution['y'][j] = 1
                    temp_solution['n'][j] = num_drones

                    try:
                        temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                        cost = temp_obj - current_obj
                        costs.append((cost, num_drones))
                    except:
                        costs.append((float('inf'), num_drones))

                # 排序找到最好和第二好的插入位置
                costs.sort()
                if len(costs) >= 2:
                    best_cost, best_drones = costs[0]
                    second_best_cost, _ = costs[1]
                    regret = second_best_cost - best_cost
                    insertion_costs[j] = (regret, best_cost, best_drones)
                elif len(costs) == 1:
                    best_cost, best_drones = costs[0]
                    insertion_costs[j] = (0, best_cost, best_drones)

        # 选择后悔值最大的储物柜插入
        if insertion_costs:
            best_locker = max(insertion_costs.keys(),
                            key=lambda x: insertion_costs[x][0])
            _, _, best_drones = insertion_costs[best_locker]

            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

        return new_solution

    def drone_optimization(self, solution, iteration=0):
        """
        智能无人机配置优化：基于需求估算合理的无人机数量
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if not open_lockers:
            return new_solution

        # 对每个开放的储物柜智能优化无人机数量
        for j in open_lockers:
            # 1. 估算该储物柜的服务需求
            estimated_demand = self._estimate_locker_demand(j, solution)

            # 2. 基于需求计算推荐的无人机数量
            recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

            # 3. 在推荐值附近搜索最优配置
            best_drones = solution['n'][j]
            best_obj = self._calculate_objective_heuristic(solution, iteration)

            # 搜索范围：推荐值 ± 2，但至少包含1-5的基本范围
            search_range = set(range(1, 6))  # 基本范围
            search_range.update(range(max(1, recommended_drones - 2), recommended_drones + 3))  # 推荐值附近
            search_range = sorted(search_range)

            for num_drones in search_range:
                if num_drones == solution['n'][j]:
                    continue

                temp_solution = copy.deepcopy(solution)
                temp_solution['n'][j] = num_drones

                try:
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_obj = temp_obj
                        best_drones = num_drones
                except:
                    continue

            new_solution['n'][j] = best_drones

        return new_solution

    def _estimate_locker_demand(self, locker_j, solution):
        """
        估算储物柜j的服务需求量
        """
        # 获取该储物柜可以服务的客户
        if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
            # 使用预计算的可达性数据
            reachable_customers = []
            for i in self.problem.customers:
                if locker_j in self.problem.fast_solver.reachable_lockers.get(i, []):
                    reachable_customers.append(i)
        else:
            # 回退到距离计算
            reachable_customers = []
            for i in self.problem.customers:
                if (i, locker_j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, locker_j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers.append(i)

        if not reachable_customers:
            return 0

        # 估算该储物柜的潜在服务需求
        # 考虑与其他储物柜的竞争，使用保守估算
        selected_lockers = {j for j, val in solution['y'].items() if val > 0.5}
        total_potential_demand = sum(self.problem.expected_demand[i] for i in reachable_customers)

        # 计算竞争因子：该储物柜在所有可达储物柜中的"吸引力"
        competition_factor = 1.0
        if len(selected_lockers) > 1:
            # 简化的竞争模型：假设需求在可达储物柜间均匀分配
            avg_competitors = 0
            for i in reachable_customers:
                if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                    competitors = len([j for j in self.problem.fast_solver.reachable_lockers.get(i, []) if j in selected_lockers])
                else:
                    competitors = len([j for j in selected_lockers
                                     if (i, j) in self.problem.distance and
                                     2 * self.problem.distance[i, j] <= self.problem.max_flight_distance])
                avg_competitors += max(1, competitors)

            competition_factor = len(reachable_customers) / max(1, avg_competitors)

        estimated_demand = total_potential_demand * competition_factor
        return max(0, estimated_demand)

    def _calculate_recommended_drones(self, locker_j, estimated_demand):
        """
        基于估算需求计算推荐的无人机数量
        """
        if estimated_demand <= 1e-6:
            return 1  # 至少1架

        # 计算平均服务时间
        if hasattr(self.problem, 'fast_solver') and hasattr(self.problem.fast_solver, 'locker_avg_service_time'):
            avg_service_time = self.problem.fast_solver.locker_avg_service_time.get(locker_j, 0.5)
        else:
            # 回退到简化估算
            avg_service_time = 0.5  # 假设平均服务时间为0.5小时

        if avg_service_time <= 0:
            return 1

        # 计算所需的总工作时间
        total_work_hours = estimated_demand * avg_service_time

        # 【修复】采用与3.py一致的保守策略，避免过度配置
        # 计算所需的无人机数量（不添加缓冲，使用精确计算）
        required_drones = total_work_hours / self.problem.H_drone_working_hours_per_day

        # 使用更保守的策略：只有在明显需要时才增加无人机
        if required_drones <= 1.0:
            recommended_drones = 1
        elif required_drones <= 1.8:  # 给1架无人机更多机会
            recommended_drones = 1  # 优先尝试1架
        else:
            recommended_drones = math.ceil(required_drones)

        # 限制在合理范围内，但优先较少的配置
        return max(1, min(recommended_drones, 4))  # 降低最大值从8到4

# ---------------------------------------------------------------------------
# 快速客户分配求解器（第二阶段子问题辅助工具）
# ---------------------------------------------------------------------------
class FastAssignmentSolver:
    """
    快速客户分配求解器

    这不是一个独立的ALNS算法，而是ALNS_Solver的辅助工具，
    专门用于快速求解第二阶段客户分配子问题。

    当ALNS_Solver优化第一阶段决策(y, n)时，需要评估每个候选解
    在多个需求场景下的成本，这就需要快速求解大量的客户分配子问题。

    该类提供快速贪心启发式算法。
    """

    def __init__(self, problem_instance):
        self.problem = problem_instance

        # 预计算优化数据结构
        self._precompute_efficiency_data()
        self._precompute_drone_capacities()

        # Numba优化已移除

        # 缓存相关
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0



    def _precompute_efficiency_data(self):
        """
        预计算客户-储物柜的效率信息，避免重复计算
        """
        self.customer_locker_efficiency = {}
        self.reachable_lockers = {}

        for i in self.problem.customers:
            self.reachable_lockers[i] = []
            self.customer_locker_efficiency[i] = {}

            for j in self.problem.sites:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        # 成本效率 = 1 / (运输成本 + 距离惩罚)
                        transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, j]
                        efficiency = 1.0 / (transport_cost + self.problem.distance[i, j])
                        self.customer_locker_efficiency[i][j] = efficiency
                        self.reachable_lockers[i].append(j)

            # 预排序：按效率从高到低
            self.reachable_lockers[i].sort(
                key=lambda j: self.customer_locker_efficiency[i][j],
                reverse=True
            )

    def _precompute_drone_capacities(self):
        """
        预计算每个储物柜的无人机运力相关数据
        """
        self.locker_avg_service_time = {}
        self.locker_reachable_customers = {}

        for j in self.problem.sites:
            total_distance = 0
            reachable_customers = 0
            self.locker_reachable_customers[j] = []

            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        total_distance += self.problem.distance[i, j]
                        reachable_customers += 1
                        self.locker_reachable_customers[j].append(i)

            if reachable_customers > 0:
                avg_distance = total_distance / reachable_customers
                self.locker_avg_service_time[j] = (2 * avg_distance / self.problem.drone_speed) + self.problem.loading_time
            else:
                self.locker_avg_service_time[j] = float('inf')

    def solve_assignment_heuristic(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        【改进版】使用基于后悔值的贪心启发式，快速求解客户分配问题

        核心思想：优先处理那些"选择最少"或"不选这个就亏大了"的客户，
        这正是"后悔值插入法"（Regret Insertion）的精髓。
        """
        # 快速检查：如果没有选中的储物柜，直接返回空分配
        if not selected_lockers:
            return {(i, j): 0.0 for i in self.problem.customers for j in self.problem.sites}

        # 使用Python实现
        return self._solve_with_python(y_star, n_star, selected_lockers, demand_scenario)

    def _create_cache_key(self, selected_lockers, n_star, demand_scenario):
        """
        创建缓存键
        """
        # 创建一个基于输入参数的哈希键
        lockers_tuple = tuple(sorted(selected_lockers))
        drones_tuple = tuple(n_star.get(j, 0) for j in selected_lockers)
        demand_tuple = tuple(round(demand_scenario.get(i, 0), 2) for i in sorted(self.problem.customers))
        return (lockers_tuple, drones_tuple, demand_tuple)

    def _get_drone_capacity_fast(self, locker_j, num_drones):
        """
        快速计算储物柜j的无人机运力（使用预计算数据）
        """
        if num_drones <= 0:
            return 0

        avg_service_time = self.locker_avg_service_time.get(locker_j, float('inf'))
        if avg_service_time == float('inf'):
            return 0

        # 每架无人机的日运力
        single_drone_capacity = self.problem.H_drone_working_hours_per_day / avg_service_time
        return num_drones * single_drone_capacity


    def _solve_with_python(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        Python实现的客户分配算法
        """
        # 缓存检查
        if hasattr(self.problem, 'use_assignment_cache') and self.problem.use_assignment_cache:
            cache_key = self._create_cache_key(selected_lockers, n_star, demand_scenario)
            if cache_key in self.cache:
                self.cache_hits += 1
                return self.cache[cache_key].copy()
            self.cache_misses += 1

        selected_lockers_set = set(selected_lockers)
        assignment = defaultdict(float)

        # 预计算储物柜的【当前剩余】容量和无人机运力
        rem_locker_caps = {j: self.problem.Q_locker_capacity.get(j, 0) for j in selected_lockers}
        rem_drone_caps = {j: self._get_drone_capacity_fast(j, n_star.get(j, 0)) for j in selected_lockers}

        # 找出所有需要分配的客户及其需求
        unassigned_demands = {i: demand for i, demand in demand_scenario.items() if demand > 1e-6}

        # 主循环：直到所有客户需求都被分配或无法再分配
        while unassigned_demands:
            customer_regrets = {}

            # 1. 为每个【未完全分配的】客户计算"后悔值"
            for i, demand in unassigned_demands.items():
                costs = []
                # 找到服务该客户成本最低和次低的两个储物柜选项
                for j in self.reachable_lockers[i]:
                    if j in selected_lockers_set:
                        # 检查是否有剩余容量
                        available_cap = min(rem_locker_caps.get(j, 0), rem_drone_caps.get(j, 0))
                        if available_cap > 1e-6:
                            cost = 2 * self.problem.transport_unit_cost * self.problem.distance.get((i, j), float('inf'))
                            costs.append({'cost': cost, 'locker': j})

                if not costs:
                    continue  # 该客户已无法被服务

                costs.sort(key=lambda x: x['cost'])

                best_cost = costs[0]['cost']
                # 如果只有一个选项，后悔值设为无穷大，必须优先处理
                second_best_cost = costs[1]['cost'] if len(costs) > 1 else float('inf')

                regret_value = second_best_cost - best_cost
                customer_regrets[i] = {'regret': regret_value, 'best_option': costs[0]}

            if not customer_regrets:
                break  # 没有客户可以被分配了，退出循环

            # 2. 选择后悔值最大的客户进行分配
            customer_to_assign = max(customer_regrets, key=lambda i: customer_regrets[i]['regret'])

            # 3. 将该客户分配给其最优选择
            best_option = customer_regrets[customer_to_assign]['best_option']
            best_locker = best_option['locker']
            demand_to_assign = unassigned_demands[customer_to_assign]

            # 计算实际可分配量
            available_cap = min(rem_locker_caps.get(best_locker, 0), rem_drone_caps.get(best_locker, 0))
            assigned_amount = min(demand_to_assign, available_cap)

            if assigned_amount > 1e-6:
                assignment[(customer_to_assign, best_locker)] += assigned_amount

                # 更新储物柜剩余容量
                rem_locker_caps[best_locker] -= assigned_amount
                rem_drone_caps[best_locker] -= assigned_amount

                # 更新客户剩余需求
                unassigned_demands[customer_to_assign] -= assigned_amount
                if unassigned_demands[customer_to_assign] < 1e-6:
                    del unassigned_demands[customer_to_assign]
            else:
                # 如果最优选择都无法分配，则认为该客户暂时无法分配
                del unassigned_demands[customer_to_assign]

        # 转换为标准格式（包含所有客户-储物柜对）
        result = {}
        for i in self.problem.customers:
            for j in selected_lockers:
                result[(i, j)] = assignment.get((i, j), 0.0)

        # 缓存结果
        if hasattr(self.problem, 'use_assignment_cache') and self.problem.use_assignment_cache:
            if 'cache_key' in locals():
                self.cache[cache_key] = result.copy()
                # 限制缓存大小，避免内存溢出
                if len(self.cache) > 10000:
                    # 删除最旧的一半缓存
                    keys_to_remove = list(self.cache.keys())[:5000]
                    for key in keys_to_remove:
                        del self.cache[key]

        return result

# ---------------------------------------------------------------------------
# StochasticDroneDeliveryOptimizerSAA 类的定义
# ---------------------------------------------------------------------------
class StochasticDroneDeliveryOptimizerSAA:

    def __init__(self):
        self.customers = []
        self.sites = []
        self.expected_demand = {}
        self.distance = {}
        self.locker_fixed_cost = {}
        self.Q_locker_capacity = {}
        self.drone_speed = None
        self.loading_time = None
        self.max_flight_distance = None
        self.transport_unit_cost = None
        self.drone_cost = None
        self.H_drone_working_hours_per_day = None
        self.penalty_cost_unassigned = None
        self.service_time_per_unit = None  # 添加缺失的属性
        self.customer_coords = {}
        self.site_coords = {}
        self.depot_coord = None
        self.truck_capacity = None
        self.truck_fixed_cost = None
        self.truck_km_cost = None
        self.truck_distances = {}  # 卡车距离矩阵
        self.BIG_M = 1e6  # 用于Big-M约束的大数

        # SAA 相关结果存储
        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = [] # 存储每次复制在K'个样本上的平均卡车成本
        self.best_solution_validation_costs = []  # 存储最佳解在每个验证场景上的成本

        # 第二阶段求解器性能统计
        self.second_stage_solver_stats = {
            'total_scenarios': 0,
            'heuristic_solver_used': 0,
            'avg_heuristic_solve_time': 0,
            'heuristic_solve_times': []
        }

        # DRL求解器实例 (每个优化器实例共享，如果参数不变)
        self._drl_solver = None
        self._drl_solver_params = None
        self._drl_solver_no_plots = None
        self._drl_solver_no_plots_params = None





        # 快速启发式求解器
        self.fast_solver = None
        self.use_assignment_cache = True  # 是否使用分配结果缓存
        self.assignment_cache = {}  # 缓存分配结果


    def set_parameters(self,
                       customers: List,
                       sites: List,
                       expected_demand: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        self.customers = customers
        self.sites = sites
        self.expected_demand = expected_demand
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned
        self.Q_locker_capacity = Q_locker_capacity
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        # 计算平均服务时间（用于容量约束）
        self._calculate_service_time_per_unit()

        # 计算卡车距离矩阵（仓库到储物柜，储物柜间距离）
        self._build_truck_distance_matrix()

        # 设置Big-M值（与saa_g_r.py一致）
        if self.expected_demand:
            max_expected_demand_val = max(self.expected_demand.values()) if self.expected_demand else 0
            self.BIG_M = max_expected_demand_val * 2 if max_expected_demand_val > 0 else 1e6
        else:
            self.BIG_M = 1e6

    def _calculate_service_time_per_unit(self):
        """
        计算平均单位服务时间，用于无人机容量约束
        基于所有客户-储物柜对的平均距离计算
        """
        if not self.distance or not self.drone_speed or self.loading_time is None:
            # 如果缺少必要参数，使用默认值
            self.service_time_per_unit = 0.5  # 默认0.5小时/单位
            return

        total_service_time = 0
        count = 0

        # 计算所有可达客户-储物柜对的平均服务时间
        for (i, j), distance in self.distance.items():
            if i in self.customers and j in self.sites:
                flight_distance = 2 * distance  # 往返距离
                if flight_distance <= self.max_flight_distance:
                    service_time = (flight_distance / self.drone_speed) + self.loading_time
                    total_service_time += service_time
                    count += 1

        if count > 0:
            self.service_time_per_unit = total_service_time / count
        else:
            # 如果没有可达的客户-储物柜对，使用基于平均距离的估算
            if self.distance:
                avg_distance = sum(self.distance.values()) / len(self.distance)
                self.service_time_per_unit = (2 * avg_distance / self.drone_speed) + self.loading_time
            else:
                self.service_time_per_unit = 0.5  # 默认值

    def _build_truck_distance_matrix(self):
        """构建卡车距离矩阵，包括仓库到储物柜和储物柜间的距离"""
        self.truck_distances = {}

        if not self.depot_coord or not self.site_coords:
            return

        # 仓库到储物柜的距离
        for j in self.sites:
            if j in self.site_coords:
                dist = math.sqrt((self.depot_coord[0] - self.site_coords[j][0])**2 +
                               (self.depot_coord[1] - self.site_coords[j][1])**2)
                self.truck_distances[(0, j)] = dist
                self.truck_distances[(j, 0)] = dist

        # 储物柜间的距离
        for i in self.sites:
            for j in self.sites:
                if i != j and i in self.site_coords and j in self.site_coords:
                    dist = math.sqrt((self.site_coords[i][0] - self.site_coords[j][0])**2 +
                                   (self.site_coords[i][1] - self.site_coords[j][1])**2)
                    self.truck_distances[(i, j)] = dist

    def _get_drl_solver(self, make_plots: bool = True):
        if not DRL_AVAILABLE: return None # 如果DRL不可用，返回None

        current_params = (self.depot_coord, self.truck_capacity, self.truck_fixed_cost, self.truck_km_cost)
        if make_plots:
            if self._drl_solver is None or self._drl_solver_params != current_params:
                set_drl_log_level(logging.WARNING)
                self._drl_solver = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=True
                )
                self._drl_solver_params = current_params
            return self._drl_solver
        else:
            if self._drl_solver_no_plots is None or self._drl_solver_no_plots_params != current_params:
                set_drl_log_level(logging.WARNING)
                self._drl_solver_no_plots = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=False
                )
                self._drl_solver_no_plots_params = current_params
            return self._drl_solver_no_plots

    def calculate_truck_cost(self,
                             selected_lockers: List[int] = None,
                             x_qty_solution_values: Dict[Tuple[int, int], float] = None,
                             make_plots: bool = True,
                             return_route_info: bool = False,
                             active_lockers_info_override: Dict[int, Dict[str, Any]] = None):
        if not DRL_AVAILABLE or self.truck_capacity is None or self.truck_fixed_cost is None or self.truck_km_cost is None:
            # print("  [calculate_truck_cost] DRL不可用或卡车参数未设置，卡车成本返回0。")
            return (0.0, None) if return_route_info else 0.0

        # 如果提供了override参数，直接使用它
        if active_lockers_info_override is not None:
            if not active_lockers_info_override:
                return (0.0, None) if return_route_info else 0.0
            try:
                drl_solver = self._get_drl_solver(make_plots=make_plots)
                if drl_solver is None:
                    return (0.0, None) if return_route_info else 0.0

                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info_override, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            except Exception as e:
                # 使用简化估算
                total_demand = sum(info['demand'] for info in active_lockers_info_override.values())
                num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost

        # 原有逻辑：从selected_lockers和x_qty_solution_values构建active_lockers_info
        # 在修正的模型中，这个逻辑主要用于向后兼容
        if selected_lockers is None or not selected_lockers:
            return (0.0, None) if return_route_info else 0.0

        if x_qty_solution_values is None:
            # 如果没有客户分配信息，返回0成本
            return (0.0, None) if return_route_info else 0.0

        try:
            drl_solver = self._get_drl_solver(make_plots=make_plots)
            if drl_solver is None: return (0.0, None) if return_route_info else 0.0 # Double check

            locker_total_demands = {locker_id: 0.0 for locker_id in selected_lockers}
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    locker_total_demands[locker_id] += quantity
            active_lockers_info = {}
            for locker_id in selected_lockers:
                demand_at_locker = locker_total_demands.get(locker_id, 0)
                if demand_at_locker > 1e-6:
                    if locker_id in self.site_coords:
                        active_lockers_info[locker_id] = {
                            'coord': self.site_coords[locker_id],
                            'demand': round(demand_at_locker)  # 四舍五入为整数
                        }
            if active_lockers_info:
                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            else:
                return (0.0, None) if return_route_info else 0.0
        except Exception as e:
            # print(f"  [calculate_truck_cost] DRL求解失败: {str(e)}, 使用简化估算")
            if x_qty_solution_values:
                total_demand_overall = 0
                for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                    if locker_id in selected_lockers and quantity > 1e-6:
                        total_demand_overall += quantity
                num_trucks = math.ceil(total_demand_overall / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost
            else:
                return (0.0, None) if return_route_info else 0.0

    def _generate_demand_samples(self, num_samples: int) -> List[Dict[int, float]]:
        """
        生成需求场景样本，客户需求服从泊松分布
        泊松分布的参数λ等于期望需求值，天然产生非负整数
        """
        demand_scenarios = []
        for _ in range(num_samples):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]  # 泊松分布参数λ
                # 使用泊松分布生成需求，λ既是均值也是方差
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)  # 转换为float以保持一致性
            demand_scenarios.append(current_scenario_demand)
        return demand_scenarios



    def calculate_objective(self, solution, demand_samples_k):
        """
        计算第一阶段解的目标函数值（修正版两阶段结构）

        现在解只包含第一阶段决策变量：
        - y: 储物柜选址
        - n: 无人机配置

        给定第一阶段决策，对每个需求场景求解第二阶段子问题，计算期望总成本。
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            return float('inf')  # 无效解

        # 1. 计算第一阶段成本
        first_stage_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers) + \
                          sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)

        # 2. 对每个需求场景求解第二阶段子问题，计算期望成本
        total_second_stage_costs = 0
        batch_active_lockers_info = []

        try:
            # 对每个需求场景求解第二阶段客户分配子问题
            for k_idx, demand_scenario in enumerate(demand_samples_k):
                # 求解第二阶段最优客户分配
                optimal_assignment = self._solve_optimal_assignment_for_scenario(
                    y_star, n_star, selected_lockers, demand_scenario
                )

                # 计算该场景下的第二阶段成本
                transport_cost_k = sum(2 * self.transport_unit_cost * self.distance[i, j] * quantity
                                      for (i, j), quantity in optimal_assignment.items()
                                      if (i, j) in self.distance)

                # 计算惩罚成本
                total_assigned = sum(optimal_assignment.values())
                total_demand = sum(demand_scenario.values())
                penalty_cost_k = self.penalty_cost_unassigned * max(0, total_demand - total_assigned)

                # 准备卡车成本计算数据
                locker_demands = {j: sum(optimal_assignment.get((i, j), 0) for i in self.customers)
                                for j in selected_lockers}
                active_info = {j: {'coord': self.site_coords[j], 'demand': round(demand)}
                              for j, demand in locker_demands.items() if demand > 1e-6}
                batch_active_lockers_info.append(active_info)

                # 累加第二阶段成本（不含卡车成本）
                total_second_stage_costs += (transport_cost_k + penalty_cost_k)

            # 批量计算卡车成本
            batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)
            avg_truck_cost = sum(batch_truck_costs) / len(batch_truck_costs) if batch_truck_costs else 0

            # 计算期望值
            avg_second_stage_cost = total_second_stage_costs / len(demand_samples_k)

            # 返回总期望成本
            return first_stage_cost + avg_second_stage_cost + avg_truck_cost

        except Exception as e:
            # 如果计算失败，返回一个很大的值
            print(f"    两阶段目标函数计算失败: {str(e)}")
            return float('inf')

    def solve_saa_with_alns(self, time_limit_per_replication: int = 300):
        """
        使用ALNS统一求解器求解SAA问题

        SAA方法论（整体求解版）：
        1. 对每个复制m，生成K个需求样本
        2. 使用ALNS整体求解确定性的两阶段问题：
           - 同时优化第一阶段：储物柜选址和无人机配置
           - 同时优化第二阶段：客户分配和卡车路径
        3. 在固定的K'个验证样本上评估解质量
        4. 计算统计下界和上界，检查收敛条件

        ALNS实现真正的整体优化，避免分阶段求解的次优性。
        """
        print(f"\n开始SAA优化（使用ALNS整体求解），最多 {SAA_MAX_REPLICATIONS_M} 次复制...")
        print(f"SAA终止条件 (必须同时满足):")
        print(f"  1. 相对差距阈值: Gap/UB ≤ {SAA_GAP_TOLERANCE_PERCENT*100:.0f}% 且 Gap ≥ 0")
        print(f"  2. 方差阈值: δ²_Gap/UB ≤ {SAA_VARIANCE_TOLERANCE_PERCENT*100:.0f}%")
        print(f"  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)")
        print(f"  最少需要 {SAA_MIN_REPLICATIONS_M} 次有效复制")

        # 生成固定的验证样本集 (所有复制共用)
        print(f"生成 {SAA_SAMPLES_K_PRIME} 个固定验证样本 (所有复制共用)...")

        # 重置随机种子以确保验证样本一致性
        original_random_state = random.getstate()
        original_np_state = np.random.get_state()
        random.seed(RANDOM_SEED + 1000)  # 使用不同的种子避免与训练样本重复
        np.random.seed(RANDOM_SEED + 1000)

        self.fixed_validation_samples = self._generate_demand_samples(num_samples=SAA_SAMPLES_K_PRIME)

        # 恢复随机状态
        random.setstate(original_random_state)
        np.random.set_state(original_np_state)

        # 调试：输出前几个样本的统计信息
        if len(self.fixed_validation_samples) > 0:
            first_sample = self.fixed_validation_samples[0]
            total_demand_first = sum(first_sample.values())
            # 调试信息已移除以减少冗余输出

        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = []

        best_solution_info = {'y': None, 'n': None, 'x': None, 'truck_routes': None, 'avg_obj_k_prime': float('inf'), 'replication_idx': -1, 'truck_cost_k_prime': 0.0}

        for m_rep in range(SAA_MAX_REPLICATIONS_M):
            print(f"\n--- SAA 复制 {m_rep + 1}/{SAA_MAX_REPLICATIONS_M} (使用ALNS整体求解) ---")
            demand_samples_for_k = self._generate_demand_samples(num_samples=SAA_SAMPLES_K)
            print(f"  已生成 {SAA_SAMPLES_K} 个需求场景用于求解。")

            # 创建并运行ALNS整体求解器
            print(f"  开始ALNS整体求解复制 {m_rep + 1}...")
            alns_solver = ALNS_IntegratedSolver(problem_instance=self, demand_samples=demand_samples_for_k)

            solve_start_time_rep = time.time()
            best_complete_solution = alns_solver.solve(time_limit=time_limit_per_replication)
            solve_time_rep = time.time() - solve_start_time_rep

            if best_complete_solution is not None:
                # 获取目标值（下界估计）
                obj_val_k = alns_solver._calculate_objective_exact(best_complete_solution)
                self.saa_objective_values_k.append(obj_val_k)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K} 个样本上的目标值: {obj_val_k:.2f}")

                y_star_m = best_complete_solution['y']
                n_star_m = best_complete_solution['n']
                x_star_m = best_complete_solution['x']
                routes_star_m = best_complete_solution['truck_routes']

                # 保存完整解
                self.saa_solutions_first_stage.append({
                    'y': y_star_m,
                    'n': n_star_m,
                    'x': x_star_m,
                    'truck_routes': routes_star_m
                })

                print(f"  评估复制 {m_rep + 1} 的解在 {SAA_SAMPLES_K_PRIME} 个固定验证样本上的性能...")
                eval_start_time = time.time()
                self._current_replication = m_rep + 1  # 设置当前复制编号用于分析
                # 传递完整的第一阶段解
                first_stage_solution = self.saa_solutions_first_stage[-1]
                avg_obj_k_prime, avg_truck_cost_for_k_prime_eval, scenario_costs = self._evaluate_solution_on_new_samples_corrected(first_stage_solution, self.fixed_validation_samples)
                eval_time = time.time() - eval_start_time
                self.saa_upper_bounds_k_prime.append(avg_obj_k_prime)
                self.saa_truck_costs_k_prime.append(avg_truck_cost_for_k_prime_eval)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均目标值 (UB估计): {avg_obj_k_prime:.2f}")
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均卡车成本: {avg_truck_cost_for_k_prime_eval:.2f}")
                print(f"  时间分析: ALNS求解 {solve_time_rep:.2f}s, 验证评估 {eval_time:.2f}s, 总计 {solve_time_rep + eval_time:.2f}s")

                if avg_obj_k_prime < best_solution_info['avg_obj_k_prime']:
                    best_solution_info.update(first_stage_solution)  # 保存完整解
                    best_solution_info['avg_obj_k_prime'] = avg_obj_k_prime
                    best_solution_info['replication_idx'] = m_rep
                    best_solution_info['truck_cost_k_prime'] = avg_truck_cost_for_k_prime_eval
                    # 记录最佳解在每个验证场景上的成本
                    self.best_solution_validation_costs = scenario_costs
            else:
                print(f"  复制 {m_rep + 1} ALNS未能找到可行解。")
                self.saa_objective_values_k.append(float('inf'))
                self.saa_upper_bounds_k_prime.append(float('inf'))
                self.saa_truck_costs_k_prime.append(float('inf'))
                self.saa_solutions_first_stage.append(None)

            # 检查SAA终止条件 (从第2次复制开始检查)
            if m_rep + 1 >= SAA_MIN_REPLICATIONS_M:
                should_terminate, gap_info = self._check_saa_termination_criteria()
                if should_terminate:
                    print(f"\n✓ SAA终止条件满足，在第 {m_rep + 1} 次复制后停止")
                    print(f"  {gap_info}")
                    break
                else:
                    print(f"  当前SAA状态: {gap_info}")
            else:
                # 即使在最小复制次数之前，也显示当前状态（如果有足够的数据）
                if len([s for s in self.saa_solutions_first_stage if s is not None]) >= 2:
                    _, gap_info = self._check_saa_termination_criteria()
                    print(f"  当前SAA状态: {gap_info}")

        # 处理结果（与原来的solve_saa方法相同的逻辑）
        if not any(s is not None for s in self.saa_solutions_first_stage):
            print("\nSAA 未能产生任何有效解。")
            return None

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 正确的SAA下界：M次复制的训练目标值的平均
        statistical_lower_bound = np.mean(valid_obj_k) if valid_obj_k else float('inf')
        std_lower_bound = np.std(valid_obj_k) if valid_obj_k else float('inf')

        # 正确的SAA上界：最佳解在验证样本上的成本（不是平均）
        statistical_upper_bound = best_solution_info['avg_obj_k_prime'] if best_solution_info['y'] is not None else float('inf')

        # 所有复制验证成本的统计信息（仅用于分析）
        avg_all_validation_costs = np.mean(valid_ub_k_prime) if valid_ub_k_prime else float('inf')
        std_all_validation_costs = np.std(valid_ub_k_prime) if valid_ub_k_prime else float('inf')

        actual_replications = len([s for s in self.saa_solutions_first_stage if s is not None])
        print(f"\n📊 SAA 统计结果汇总 ({actual_replications} 次有效复制，使用ALNS)")
        print(f"=" * 60)
        print(f"  下界估计 cost_N^m: {statistical_lower_bound:.2f} 元/天")
        print(f"    ↳ 计算方法: 前{actual_replications}次复制的小样本优化成本的算术平均")
        print(f"    ↳ 含义: 系统真实期望成本的下界估计")
        print(f"    - 下界标准差: {std_lower_bound:.2f}")
        print(f"    - 下界方差 δ²(cost_N): {std_lower_bound**2/(actual_replications*(actual_replications-1)) if actual_replications > 1 else 0:.4f}")
        print(f"  上界估计 cost_{SAA_SAMPLES_K_PRIME}(ŝ): {statistical_upper_bound:.2f} 元/天")
        print(f"    ↳ 计算方法: 最佳解在{SAA_SAMPLES_K_PRIME}个大样本场景下的平均成本")
        print(f"    ↳ 含义: 最佳方案长期运营的期望日均成本")
        print(f"  SAA Gap: {statistical_upper_bound - statistical_lower_bound:.2f} 元/天 ({((statistical_upper_bound - statistical_lower_bound)/statistical_upper_bound*100):.1f}%)")
        print(f"  所有复制验证成本统计: {avg_all_validation_costs:.2f} ± {std_all_validation_costs:.2f} 元/天")

        if best_solution_info.get('y') is not None:
            # 计算最佳解的成本分解信息
            selected_lockers = [j for j, val in best_solution_info['y'].items() if val > 0.5]
            locker_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers)
            drone_cost = sum(self.drone_cost * best_solution_info['n'].get(j, 0) for j in selected_lockers)
            truck_cost = best_solution_info.get('truck_cost_k_prime', 0)
            total_cost = best_solution_info['avg_obj_k_prime']

            print(f"\n🏆 最佳解详情 (来自复制 {best_solution_info['replication_idx'] + 1})")
            print(f"  总成本: {total_cost:.2f} 元/天")
            print(f"  开放储物柜: {len(selected_lockers)} 个")
            # 【修复】计算准确的成本分解
            # 使用实际的成本计算而不是简化假设
            actual_costs = self._calculate_accurate_cost_breakdown(best_solution_info)

            drone_transport_cost = actual_costs['transport_cost']
            penalty_cost = actual_costs['penalty_cost']
            drone_total_temp = drone_cost + drone_transport_cost

            print(f"  成本构成: 储物柜 {locker_cost:.2f} + 无人机(部署+运输) {drone_total_temp:.2f} + 卡车(固定+运输) {truck_cost:.2f} + 惩罚 {penalty_cost:.2f}")
            print(f"  ↳ 其中: 无人机部署 {drone_cost:.2f} + 无人机运输 {drone_transport_cost:.2f} + 惩罚 {penalty_cost:.2f}")

            # 启发式模式：直接使用启发式结果
            print(f"\n[最终验证] 使用启发式结果作为最终目标值")
            final_solution_saa = {
                'objective_value_k_prime_estimate': best_solution_info['avg_obj_k_prime'],
                'selected_lockers_y': best_solution_info['y'],
                'drone_allocations_n': best_solution_info['n'],
                'truck_cost_k_prime_estimate': best_solution_info['truck_cost_k_prime'],
            }


            print(f"  最佳解选定的储物柜 (y*): {[j for j, val in best_solution_info['y'].items() if val > 0.5]}")
            print(f"  最佳解无人机分配 (n*): {dict((j, round(val)) for j, val in best_solution_info['n'].items() if best_solution_info['y'].get(j,0) > 0.5)}")
            return final_solution_saa
        else:
            print("\nSAA 未能找到任何有效的最终解。")
            return None

    def _calculate_accurate_cost_breakdown(self, solution_info):
        """
        计算准确的成本分解，避免使用简化假设

        Args:
            solution_info: 包含解信息的字典

        Returns:
            dict: 包含各项成本的字典
        """
        y_star = solution_info
        n_star = solution_info
        selected_lockers = [j for j, val in y_star.get('y', {}).items() if val > 0.5]

        # 【修复】使用与详细计算相同的样本集，确保一致性
        if hasattr(self, 'fixed_validation_samples') and self.fixed_validation_samples:
            sample_scenarios = self.fixed_validation_samples  # 使用全部验证样本确保一致性
            print(f"  [调试] 使用全部{len(sample_scenarios)}个验证样本进行快速成本分解计算")
        else:
            # 回退到生成少量样本
            sample_scenarios = self._generate_demand_samples(num_samples=50)

        total_transport_cost = 0
        total_penalty_cost = 0
        total_shortage_units = 0  # 添加调试信息

        for scenario_idx, scenario in enumerate(sample_scenarios):
            # 为该场景求解最优分配
            optimal_assignment = self._solve_optimal_assignment_for_scenario(
                y_star.get('y', {}), y_star.get('n', {}), selected_lockers, scenario
            )

            # 计算该场景下的运输成本和惩罚成本
            scenario_transport_cost = 0
            total_shortage = 0

            for i in self.customers:
                actual_demand = scenario[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage += shortage

                # 计算无人机运输成本
                for j in selected_lockers:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        scenario_transport_cost += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 未分配惩罚成本
            scenario_penalty_cost = self.penalty_cost_unassigned * total_shortage

            total_transport_cost += scenario_transport_cost
            total_penalty_cost += scenario_penalty_cost
            total_shortage_units += total_shortage  # 累计未分配需求

        # 计算平均成本
        avg_transport_cost = total_transport_cost / len(sample_scenarios)
        avg_penalty_cost = total_penalty_cost / len(sample_scenarios)
        avg_shortage_units = total_shortage_units / len(sample_scenarios)

        # 添加调试信息
        print(f"  [调试] 惩罚成本计算详情:")
        print(f"    样本数量: {len(sample_scenarios)}")
        print(f"    总未分配需求: {total_shortage_units:.2f} 单位")
        print(f"    平均未分配需求: {avg_shortage_units:.2f} 单位/场景")
        print(f"    单位惩罚成本: {self.penalty_cost_unassigned:.0f} 元/单位")
        print(f"    计算惩罚成本: {avg_shortage_units:.2f} × {self.penalty_cost_unassigned:.0f} = {avg_penalty_cost:.2f} 元")

        return {
            'transport_cost': avg_transport_cost,
            'penalty_cost': avg_penalty_cost
        }

    def _print_detailed_cost_breakdown(self, saa_solution_dict):
        """打印详细的成本构成分析 - 1.py ALNS版本"""
        print(f"\n  详细成本构成分析 (1.py - ALNS算法):")
        print(f"  " + "-" * 50)

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        locker_fixed_cost = sum(self.locker_fixed_cost[j] * y_star.get(j, 0) for j in selected_lockers)
        drone_deployment_cost = sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)
        first_stage_total = locker_fixed_cost + drone_deployment_cost

        print(f"  第一阶段成本 (here-and-now): {first_stage_total:.2f}")
        print(f"    - 储物柜固定成本: {locker_fixed_cost:.2f}")
        print(f"    - 无人机部署成本: {drone_deployment_cost:.2f}")

        # 【修复】使用与主评估流程相同的固定验证样本，确保成本分解的一致性
        print(f"  正在计算第二阶段期望成本 (wait-and-see)...")
        if hasattr(self, 'fixed_validation_samples') and self.fixed_validation_samples:
            sample_scenarios = self.fixed_validation_samples
            print(f"  使用固定验证样本集 ({len(sample_scenarios)} 个场景) 确保一致性")
        else:
            # 回退到生成新样本（不应该发生）
            sample_scenarios = self._generate_demand_samples(num_samples=100)
            print(f"  警告：未找到固定验证样本，生成新样本 ({len(sample_scenarios)} 个场景)")
        total_transport_cost = 0
        total_penalty_cost = 0
        total_truck_cost = 0

        for scenario_idx, scenario in enumerate(sample_scenarios):
            # 为该场景求解最优分配
            optimal_assignment = self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, scenario)

            # 计算该场景下的成本
            total_shortage = 0
            scenario_transport_cost = 0

            for i in self.customers:
                actual_demand = scenario[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage += shortage

                # 计算无人机运输成本
                for j in selected_lockers:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        scenario_transport_cost += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            scenario_penalty_cost = self.penalty_cost_unassigned * total_shortage

            total_transport_cost += scenario_transport_cost
            total_penalty_cost += scenario_penalty_cost

        # 计算第二阶段期望成本
        avg_transport_cost = total_transport_cost / len(sample_scenarios)
        avg_penalty_cost = total_penalty_cost / len(sample_scenarios)
        truck_cost_estimate = saa_solution_dict.get('truck_cost_k_prime_estimate', 0)

        second_stage_total = avg_transport_cost + avg_penalty_cost + truck_cost_estimate

        print(f"  第二阶段期望成本 (wait-and-see): {second_stage_total:.2f}")
        print(f"    - 无人机运输成本 (随实际需求变化): {avg_transport_cost:.2f}")
        print(f"    - 未分配惩罚成本 (随需求不确定性): {avg_penalty_cost:.2f}")
        print(f"    - 卡车运输成本 (随储物柜需求变化): {truck_cost_estimate:.2f}")

        total_cost = first_stage_total + second_stage_total
        print(f"  总成本 (重新计算): {total_cost:.2f}")

        # 显示与SAA主评估的差异（用于调试）
        main_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        difference = abs(total_cost - main_objective)
        print(f"  SAA主评估目标值: {main_objective:.2f}")
        print(f"  差异: {difference:.2f} ({difference/main_objective*100:.1f}%)")
        print(f"  注意: 应以SAA主评估目标值为准，此处重新计算仅用于成本构成分析")

        # 成本占比分析（基于SAA主评估目标值）
        if main_objective > 0:
            print(f"\n  成本占比分析 (基于SAA主评估目标值):")
            print(f"    - 第一阶段占比: {first_stage_total/main_objective*100:.1f}%")
            print(f"    - 第二阶段占比: {(main_objective-first_stage_total)/main_objective*100:.1f}%")
            print(f"    - 储物柜固定成本占比: {locker_fixed_cost/main_objective*100:.1f}%")
            print(f"    - 无人机成本(部署+运输)占比: {(drone_deployment_cost + avg_transport_cost)/main_objective*100:.1f}%")
            print(f"    - 卡车成本(固定+运输)占比: {truck_cost_estimate/main_objective*100:.1f}%")
            print(f"    - 惩罚成本占比: {avg_penalty_cost/main_objective*100:.1f}%")

            # 第二阶段求解器性能分析已移除以减少冗余输出



    def _evaluate_solution_on_new_samples_corrected(self, first_stage_solution: Dict, demand_samples_k_prime: List[Dict[int,float]]):
        """
        修正版：评估完整第一阶段解在新样本上的性能
        """
        import time
        eval_start_time = time.time()

        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        # 记录每个验证场景的总成本，用于计算Var(UB)
        scenario_total_costs = []

        # 提取第一阶段解
        y_star = first_stage_solution['y']
        n_star = first_stage_solution['n']

        # 第一阶段固定成本
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        first_stage_cost = locker_cost_fixed + drone_deployment_cost_fixed

        # 准备批量卡车成本计算的数据
        batch_active_lockers_info = []

        # 时间统计
        assignment_start_time = time.time()

        # 使用串行求解所有场景的客户分配问题

        all_optimal_assignments = self._solve_assignments_sequential(y_star, n_star, selected_lockers_eval, demand_samples_k_prime)

        # 根据所有最优分配结果计算卡车运输需求
        for k_prime_idx, optimal_assignment in enumerate(all_optimal_assignments):
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 调试信息已移除以减少冗余输出

            # 根据最优分配计算每个储物柜的实际需求量
            active_lockers_info_k_prime = {}

            for j_locker in selected_lockers_eval:
                total_actual_demand_j = sum(optimal_assignment.get((i, j_locker), 0) for i in self.customers)
                if total_actual_demand_j > 1e-6:
                    active_lockers_info_k_prime[j_locker] = {
                        'coord': self.site_coords[j_locker],
                        'demand': round(total_actual_demand_j)
                    }

            batch_active_lockers_info.append(active_lockers_info_k_prime)

        assignment_time = time.time() - assignment_start_time

        # 使用DRL批量求解计算所有场景的卡车成本（仅第一次复制显示详细信息）
        truck_cost_start_time = time.time()
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  使用DRL批量求解计算 {num_scenarios_k_prime} 个验证场景的卡车成本...")
        batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)
        truck_cost_time = time.time() - truck_cost_start_time

        # 计算总成本并记录每个场景的成本
        cost_calc_start_time = time.time()
        for k_prime_idx in range(num_scenarios_k_prime):
            truck_cost_k_prime = batch_truck_costs[k_prime_idx]
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 使用缓存的最优分配结果，避免重复计算
            optimal_assignment = all_optimal_assignments[k_prime_idx]

            # 计算该场景下的第二阶段成本
            # 1. 无人机运输成本（基于最优分配）
            transport_cost_k_prime = 0
            total_shortage_k_prime = 0

            for i in self.customers:
                actual_demand = demand_scenario_k_prime[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers_eval)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage_k_prime += shortage

                # 计算无人机运输成本
                for j in selected_lockers_eval:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        transport_cost_k_prime += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            penalty_cost_k_prime = self.penalty_cost_unassigned * total_shortage_k_prime

            # 3. 卡车运输成本
            total_truck_cost_over_k_prime_samples += truck_cost_k_prime

            # 总成本 = 第一阶段成本 + 第二阶段成本
            total_cost_for_scenario_k_prime = (first_stage_cost + transport_cost_k_prime +
                                              penalty_cost_k_prime + truck_cost_k_prime)
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime

            # 记录每个场景的总成本
            scenario_total_costs.append(total_cost_for_scenario_k_prime)

        cost_calc_time = time.time() - cost_calc_start_time
        total_eval_time = time.time() - eval_start_time

        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')

        # 详细时间分析已移除以减少冗余输出

        # 返回平均成本、卡车成本和每个场景的成本列表
        return avg_total_cost_k_prime, avg_truck_cost_k_prime, scenario_total_costs

    def _solve_assignments_sequential(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_samples: List[Dict[int, float]]) -> List[Dict]:
        """
        使用串行求解所有场景的客户分配问题（启发式模式）
        """
        all_assignments = []

        current_rep = getattr(self, '_current_replication', None)
        if current_rep == 1:
            print(f"  使用启发式串行求解")

        for k_idx, demand_scenario in enumerate(demand_samples):
            try:
                assignment = self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, demand_scenario)
                all_assignments.append(assignment)
            except Exception as e:
                if hasattr(self, '_current_replication') and self._current_replication == 1:
                    print(f"  场景 {k_idx+1} 启发式求解失败: {str(e)}")
                # 使用默认分配（全部为0）
                default_assignment = {(i, j): 0.0 for i in self.customers for j in selected_lockers}
                all_assignments.append(default_assignment)

        return all_assignments

    def _solve_optimal_assignment_for_scenario(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_scenario: Dict[int, float]) -> Dict:
        """
        为给定的需求场景求解最优客户分配（启发式模式）
        """
        import time

        # 更新总场景计数
        self.second_stage_solver_stats['total_scenarios'] += 1
        # 固定使用启发式求解器
        if not hasattr(self, 'fast_solver') or self.fast_solver is None:
            self.fast_solver = FastAssignmentSolver(self)

        start_time = time.time()
        result = self.fast_solver.solve_assignment_heuristic(y_star, n_star, selected_lockers, demand_scenario)
        solve_time = time.time() - start_time

        self.second_stage_solver_stats['heuristic_solver_used'] += 1
        self.second_stage_solver_stats['heuristic_solve_times'].append(solve_time)
        return result








    def _analyze_truck_cost_details_corrected(self, y_solution, n_solution, validation_samples, replication, first_stage_assignment, sample_active_lockers_info):
        """
        修正版的卡车成本详细分析
        """
        print(f"  === 复制 {replication} 卡车成本详细分析（修正版两阶段） ===")

        # 统计开放的储物柜
        open_lockers = [site for site in self.sites if y_solution.get(site, 0) > 0.5]
        print(f"  开放储物柜数量: {len(open_lockers)}")
        print(f"  开放储物柜ID: {open_lockers}")

        # 显示第一阶段客户分配
        print(f"  第一阶段客户分配（基于期望需求）:")
        for i in self.customers:
            assignments = []
            for j in open_lockers:
                qty = first_stage_assignment.get((i, j), 0)
                if qty > 0.5:
                    assignments.append(f"储物柜{j}({qty:.1f})")
            if assignments:
                print(f"    客户{i}: {', '.join(assignments)} (期望需求: {self.expected_demand[i]})")

        # 分析前几个验证场景
        print(f"  前{len(sample_active_lockers_info)}个验证场景的卡车需求:")
        for i, active_lockers_info in enumerate(sample_active_lockers_info):
            print(f"  --- 场景 {i+1} ---")
            if active_lockers_info:
                total_demand = sum(info['demand'] for info in active_lockers_info.values())
                print(f"    总需求: {total_demand:.1f}")
                for locker_id, info in active_lockers_info.items():
                    print(f"    储物柜{locker_id}: 需求{info['demand']:.1f}, 坐标{info['coord']}")
            else:
                print(f"    无卡车运输需求")

        print(f"  ================================")

    def _debug_solution_analysis(self, replication, y_solution, n_solution, x_qty_solution, z_solution, u_solution):
        """
        详细调试解的分配情况（修正版：不依赖第一阶段客户分配）
        """
        print(f"\n  🔍 复制 {replication} 解的详细调试分析:")
        print(f"  " + "=" * 60)

        # 1. 储物柜开放情况
        open_lockers = [j for j, val in y_solution.items() if val > 0.5]
        print(f"  开放储物柜: {open_lockers}")

        # 2. 无人机配置情况
        drone_config = {j: round(n_solution.get(j, 0)) for j in open_lockers}
        print(f"  无人机配置: {drone_config}")

        # 3. 第一阶段成本分析
        print(f"  第一阶段成本分析:")
        locker_cost = sum(self.locker_fixed_cost[j] * y_solution.get(j, 0) for j in open_lockers)
        drone_cost = sum(self.drone_cost * n_solution.get(j, 0) for j in open_lockers)
        first_stage_cost = locker_cost + drone_cost

        print(f"    储物柜固定成本: {locker_cost:.2f}")
        print(f"    无人机部署成本: {drone_cost:.2f}")
        print(f"    第一阶段总成本: {first_stage_cost:.2f}")

        # 4. 储物柜资源配置分析
        print(f"  储物柜资源配置:")
        for j in open_lockers:
            capacity_j = self.Q_locker_capacity[j]
            drone_count = n_solution.get(j, 0)
            available_hours = drone_count * self.H_drone_working_hours_per_day

            # 距离约束检查
            reachable_customers = []
            unreachable_customers = []
            for i in self.customers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        reachable_customers.append(i)
                    else:
                        unreachable_customers.append(i)

            print(f"    储物柜{j}: 容量{capacity_j}, 无人机{drone_count}架, 可用时间{available_hours:.1f}h")
            print(f"      可达客户: {len(reachable_customers)}/{len(self.customers)}")
            if unreachable_customers:
                print(f"      不可达客户: {unreachable_customers}")

        print(f"  注意: 在修正的两阶段模型中，客户分配在第二阶段根据实际需求动态优化")

        # 添加惩罚成本分析
        self._analyze_penalty_cost_causes(y_solution, n_solution)

    def _analyze_penalty_cost_causes(self, y_solution, n_solution):
        """
        分析惩罚成本高的原因
        """
        print(f"\n  🔍 惩罚成本分析:")
        print(f"  " + "=" * 50)

        open_lockers = [j for j, val in y_solution.items() if val > 0.5]
        total_expected_demand = sum(self.expected_demand.values())

        print(f"  总期望需求: {total_expected_demand:.1f}")
        print(f"  惩罚成本单价: {self.penalty_cost_unassigned:.0f} 元/单位")
        print(f"  开放储物柜: {open_lockers}")

        # 分析每个客户的可达性
        unreachable_customers = []
        reachable_customers = []
        total_unreachable_demand = 0

        for i in self.customers:
            customer_reachable = False
            min_distance = float('inf')

            for j in open_lockers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        customer_reachable = True
                        min_distance = min(min_distance, self.distance[i, j])

            if not customer_reachable:
                unreachable_customers.append(i)
                total_unreachable_demand += self.expected_demand[i]
            else:
                reachable_customers.append((i, min_distance))

        print(f"\n  距离约束分析:")
        print(f"    最大飞行距离: {self.max_flight_distance:.1f} km")
        print(f"    不可达客户数: {len(unreachable_customers)}/{len(self.customers)}")
        print(f"    不可达需求量: {total_unreachable_demand:.1f}/{total_expected_demand:.1f} ({total_unreachable_demand/total_expected_demand*100:.1f}%)")

        if unreachable_customers:
            print(f"    不可达客户: {unreachable_customers[:10]}{'...' if len(unreachable_customers) > 10 else ''}")

        # 分析容量约束
        print(f"\n  容量约束分析:")
        total_locker_capacity = sum(self.Q_locker_capacity[j] for j in open_lockers)
        total_drone_capacity = 0

        for j in open_lockers:
            locker_capacity = self.Q_locker_capacity[j]
            drone_count = n_solution.get(j, 0)

            # 计算无人机运力
            reachable_demand_for_j = 0
            avg_distance_j = 0
            reachable_count_j = 0

            for i in self.customers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        reachable_demand_for_j += self.expected_demand[i]
                        avg_distance_j += self.distance[i, j]
                        reachable_count_j += 1

            if reachable_count_j > 0:
                avg_distance_j /= reachable_count_j
                avg_service_time = (2 * avg_distance_j / self.drone_speed) + self.loading_time
                drone_capacity_j = drone_count * self.H_drone_working_hours_per_day / avg_service_time
            else:
                drone_capacity_j = 0

            total_drone_capacity += drone_capacity_j

            print(f"    储物柜{j}: 容量{locker_capacity}, 无人机{drone_count}架, 运力{drone_capacity_j:.1f}")

        print(f"    总储物柜容量: {total_locker_capacity:.1f}")
        print(f"    总无人机运力: {total_drone_capacity:.1f}")
        print(f"    可达需求量: {total_expected_demand - total_unreachable_demand:.1f}")

        # 分析瓶颈
        effective_capacity = min(total_locker_capacity, total_drone_capacity)
        reachable_demand = total_expected_demand - total_unreachable_demand

        print(f"\n  瓶颈分析:")
        print(f"    有效服务容量: {effective_capacity:.1f}")
        print(f"    可达需求量: {reachable_demand:.1f}")

        if effective_capacity < reachable_demand:
            shortage = reachable_demand - effective_capacity
            print(f"    容量不足: {shortage:.1f} ({shortage/reachable_demand*100:.1f}%)")
            print(f"    容量不足惩罚成本: {shortage * self.penalty_cost_unassigned:.0f} 元")

        total_shortage = total_unreachable_demand + max(0, reachable_demand - effective_capacity)
        total_penalty = total_shortage * self.penalty_cost_unassigned

        print(f"\n  总惩罚成本预估:")
        print(f"    距离约束导致: {total_unreachable_demand * self.penalty_cost_unassigned:.0f} 元")
        print(f"    容量约束导致: {max(0, reachable_demand - effective_capacity) * self.penalty_cost_unassigned:.0f} 元")
        print(f"    总惩罚成本: {total_penalty:.0f} 元")
        print(f"    占总成本比例: {total_penalty/(total_penalty + 49500 + 2028):.1%}")



    def calculate_truck_cost_batch(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        批量计算卡车成本 - 智能分组批量求解

        将具有相同储物柜配置的场景分组进行批量求解，
        以最大化DRL批量求解的效率。

        Args:
            batch_active_lockers_info: 批量活跃储物柜信息列表

        Returns:
            卡车成本列表
        """
        if not DRL_AVAILABLE:
            print("  DRL不可用，使用简化估算计算批量卡车成本")
            return self._calculate_simplified_batch_costs(batch_active_lockers_info)

        if not batch_active_lockers_info:
            return []

        try:
            # 按储物柜配置分组
            groups = {}
            for i, scenario in enumerate(batch_active_lockers_info):
                locker_ids = tuple(sorted(scenario.keys()))
                if locker_ids not in groups:
                    groups[locker_ids] = []
                groups[locker_ids].append((i, scenario))

            if hasattr(self, '_current_replication') and self._current_replication == 1:
                print(f"  将 {len(batch_active_lockers_info)} 个场景分为 {len(groups)} 组进行批量求解")

            # 初始化结果列表
            batch_costs = [0.0] * len(batch_active_lockers_info)
            drl_solver = self._get_drl_solver(make_plots=False)

            # 对每组进行批量求解
            for group_idx, (locker_ids, scenarios) in enumerate(groups.items()):
                scenario_indices = [idx for idx, _ in scenarios]
                scenario_data = [data for _, data in scenarios]

                if len(scenario_data) > 1:
                    # 批量求解
                    group_costs = drl_solver.solve_batch(scenario_data, return_route_info=False)
                else:
                    # 单个求解
                    group_costs = [drl_solver.solve(scenario_data[0], return_route_info=False)]

                # 将结果放回原始位置
                for i, cost in enumerate(group_costs):
                    batch_costs[scenario_indices[i]] = cost

            if hasattr(self, '_current_replication') and self._current_replication == 1:
                print(f"  分组批量求解成功，平均卡车成本: {sum(batch_costs)/len(batch_costs):.2f}")
            return batch_costs
        except Exception as e:
            print(f"  DRL批量求解失败: {str(e)}，回退到逐个求解")
            return self._fallback_individual_solving(batch_active_lockers_info)





    def _calculate_simplified_batch_costs(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        简化估算批量成本计算
        """
        batch_costs = []
        for active_lockers_info in batch_active_lockers_info:
            batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _calculate_simplified_cost(self, active_lockers_info: Dict[int, Dict[str, Any]]) -> float:
        """
        单个场景的简化成本估算
        """
        if not active_lockers_info:
            return 0.0

        total_demand = sum(info['demand'] for info in active_lockers_info.values())
        num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
        return num_trucks * self.truck_fixed_cost

    def _fallback_individual_solving(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        回退到逐个求解的方法
        """
        batch_costs = []
        for i, active_lockers_info in enumerate(batch_active_lockers_info):
            try:
                cost = self.calculate_truck_cost([], {}, make_plots=False, active_lockers_info_override=active_lockers_info)
                batch_costs.append(cost)
            except Exception as e2:
                print(f"  场景 {i+1} 求解失败: {str(e2)}，使用简化估算")
                batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _check_saa_termination_criteria(self):
        """
        检查SAA终止条件
        返回: (should_terminate: bool, gap_info: str)
        """
        import scipy.stats as stats

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 至少需要2次有效复制才能计算统计量
        if len(valid_obj_k) < 2 or len(valid_ub_k_prime) < 2:
            return False, f"有效解数量不足 ({len(valid_obj_k)}, {len(valid_ub_k_prime)} < 2)，需要至少2次有效复制"

        # 找到当前最佳解的信息
        best_validation_cost = float('inf')
        for i, ub_val in enumerate(self.saa_upper_bounds_k_prime):
            if ub_val != float('inf') and ub_val < best_validation_cost:
                best_validation_cost = ub_val

        # 计算正确的SAA统计量
        m = len(valid_obj_k)  # 当前迭代次数
        statistical_lower_bound = np.mean(valid_obj_k)  # cost_N^m = (1/m) * Σ cost_N^{m'}
        statistical_upper_bound = best_validation_cost  # UB_{N'}(ŝ) (最佳解的验证成本)

        # SAA Gap计算 (按正确的论文公式)
        saa_gap = statistical_upper_bound - statistical_lower_bound
        gap_percent = saa_gap / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 计算下界方差：δ²(cost_N) = (1/[m(m-1)]) * Σ(cost_N^{m'} - cost_N^m)²
        if m > 1:
            variance_sum = sum((cost_val - statistical_lower_bound)**2 for cost_val in valid_obj_k)
            var_lower_bound = variance_sum / (m * (m - 1))
            std_lower_bound = np.sqrt(var_lower_bound)
        else:
            var_lower_bound = 0
            std_lower_bound = 0

        # 计算上界方差：δ²_{N'}(ŝ) = (1/[N'(N'-1)]) * Σ(Q(ŝ,ξ_n) - cost_{N'}(ŝ))²
        # 这里简化处理，假设每个复制的验证样本数量相同
        N_prime = SAA_SAMPLES_K_PRIME
        if len(valid_ub_k_prime) > 1:
            # 使用最佳解对应的上界值计算方差
            best_ub_index = valid_ub_k_prime.index(best_validation_cost)
            # 简化计算：使用所有上界值的方差除以验证样本数量
            var_upper_bound = np.var(valid_ub_k_prime, ddof=1) / N_prime
        else:
            var_upper_bound = 0

        # Gap的总方差：δ²_Gap(ŝ) = δ²(cost_N) + δ²_{N'}(ŝ)
        gap_variance = var_lower_bound + var_upper_bound
        gap_std = np.sqrt(gap_variance) if gap_variance > 0 else 0
        variance_percent = gap_variance / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 置信区间计算 (使用t分布)
        n_replications = len(valid_obj_k)
        if n_replications > 1:
            t_critical = stats.t.ppf(1 - SAA_CONFIDENCE_LEVEL_ALPHA/2, n_replications - 1)
            # 下界置信区间
            lb_margin = t_critical * std_lower_bound / np.sqrt(n_replications) if std_lower_bound > 0 else 0
        else:
            lb_margin = 0

        # SAA终止条件检查 (必须同时满足两个条件)
        # 条件1: 相对差距阈值 Gap_{N,N'}(ŝ) / cost_{N'}(ŝ) ≤ ε (如 ε = 3%)
        relative_gap_condition = (gap_percent <= SAA_GAP_TOLERANCE_PERCENT and saa_gap >= 0)

        # 条件2: 方差阈值 δ²_Gap(ŝ) / cost_{N'}(ŝ) ≤ ε' (如 ε' = 5%)
        variance_condition = variance_percent <= SAA_VARIANCE_TOLERANCE_PERCENT

        # 主要终止条件：必须同时满足相对差距阈值和方差阈值
        should_terminate = relative_gap_condition and variance_condition

        # 如果gap为负，说明统计估计不稳定，不应该终止
        if saa_gap < 0:
            should_terminate = False

        # 计算下界变异系数
        cv_lower_bound = std_lower_bound / statistical_lower_bound if statistical_lower_bound > 0 else float('inf')

        # 构建详细的状态信息
        gap_condition_status = "✓" if relative_gap_condition else "✗"
        variance_condition_status = "✓" if variance_condition else "✗"

        gap_info = (f"相对差距: {gap_percent:.2%} {gap_condition_status} (阈值: ≤{SAA_GAP_TOLERANCE_PERCENT:.0%}), "
                   f"方差比例: {variance_percent:.2%} {variance_condition_status} (阈值: ≤{SAA_VARIANCE_TOLERANCE_PERCENT:.0%}), "
                   f"LB(m={m}): {statistical_lower_bound:.2f}, "
                   f"UB: {statistical_upper_bound:.2f}, "
                   f"Gap: {saa_gap:.2f}, "
                   f"δ²_Gap: {gap_variance:.4f}")

        return should_terminate, gap_info



    def _print_saa_solution(self, saa_solution_dict: Dict):
        if not saa_solution_dict:
            print("SAA 未找到有效解。")
            return

        print("\n" + "=" * 60 + "\nSAA 优化结果 (ALNS算法)\n" + "=" * 60)

        # 提取解决方案信息
        y_star_final = saa_solution_dict.get('selected_lockers_y', {})
        n_star_final = saa_solution_dict.get('drone_allocations_n', {})
        selected_lockers_print = [j for j, val in y_star_final.items() if val > 0.5]
        total_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        truck_cost = saa_solution_dict.get('truck_cost_k_prime_estimate', 0)

        # 计算成本分解 - 需要从other_costs中分离出无人机运输成本和惩罚成本
        locker_fixed_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers_print)
        drone_deployment_cost = sum(self.drone_cost * n_star_final.get(j, 0) for j in selected_lockers_print)

        # 计算无人机运输成本和惩罚成本（从第二阶段成本中分离）
        # other_costs = 无人机运输成本 + 惩罚成本
        other_costs = total_objective - locker_fixed_cost - drone_deployment_cost - truck_cost

        # 为了正确分解，我们需要重新计算无人机运输成本和惩罚成本
        # 使用与成本分解函数相同的逻辑
        drone_transport_cost = 0
        penalty_cost = 0

        # 如果有固定验证样本，使用它们来计算分解
        if hasattr(self, 'fixed_validation_samples') and self.fixed_validation_samples:
            total_transport = 0
            total_penalty = 0

            print(f"  正在重新计算成本分解（使用{len(self.fixed_validation_samples)}个验证样本）...")

            sample_count = len(self.fixed_validation_samples)  # 使用所有样本
            for idx, scenario in enumerate(self.fixed_validation_samples):
                # 求解该场景的客户分配
                assignment = self.fast_solver.solve_assignment_heuristic(
                    y_star_final, n_star_final, selected_lockers_print, scenario
                )

                # 计算该场景的无人机运输成本
                scenario_transport = 0
                scenario_penalty = 0
                total_demand_scenario = sum(scenario.values())
                total_assigned_scenario = 0

                for i in self.customers:
                    actual_demand = scenario[i]
                    total_assigned = sum(assignment.get((i, j), 0) for j in selected_lockers_print)
                    total_assigned_scenario += total_assigned
                    shortage = max(0, actual_demand - total_assigned)
                    scenario_penalty += self.penalty_cost_unassigned * shortage

                    # 计算无人机运输成本
                    for j in selected_lockers_print:
                        assigned_qty = assignment.get((i, j), 0)
                        if assigned_qty > 1e-6 and (i, j) in self.distance:
                            scenario_transport += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

                # 调试信息：显示前几个场景的详细信息
                if idx < 3:
                    total_shortage_scenario = total_demand_scenario - total_assigned_scenario
                    print(f"    场景{idx+1}: 总需求={total_demand_scenario:.1f}, 总分配={total_assigned_scenario:.1f}, 短缺={total_shortage_scenario:.1f}, 惩罚={scenario_penalty:.2f}")

                # 每处理500个场景显示一次进度
                if (idx + 1) % 500 == 0:
                    print(f"    已处理 {idx + 1}/{sample_count} 个场景...")

                total_transport += scenario_transport
                total_penalty += scenario_penalty

            drone_transport_cost = total_transport / sample_count
            penalty_cost = total_penalty / sample_count

            print(f"  重新计算结果: 无人机运输成本={drone_transport_cost:.2f}, 惩罚成本={penalty_cost:.2f}")
        else:
            # 回退：假设other_costs中惩罚成本占比较小
            # 这是一个近似估算
            drone_transport_cost = other_costs * 0.8  # 假设80%是运输成本
            penalty_cost = other_costs * 0.2  # 假设20%是惩罚成本
            print(f"  使用近似估算: 无人机运输成本={drone_transport_cost:.2f}, 惩罚成本={penalty_cost:.2f}")

        # 重新计算分类后的成本
        drone_total_cost = drone_deployment_cost + drone_transport_cost  # 无人机成本（部署+运输）
        truck_total_cost = truck_cost  # 卡车成本（固定+运输）
        penalty_total_cost = penalty_cost  # 惩罚成本

        # 主要结果展示
        print(f"  📊 核心指标:")
        print(f"    开放储物柜数量: {len(selected_lockers_print)} 个")
        print(f"    总成本: {total_objective:.2f} 元/天")
        print(f"    无人机成本(部署+运输): {drone_total_cost:.2f} 元/天 ({drone_total_cost/total_objective*100:.1f}%)")
        print(f"    卡车成本(固定+运输): {truck_total_cost:.2f} 元/天 ({truck_total_cost/total_objective*100:.1f}%)")

        print(f"\n  💰 详细成本分解:")
        print(f"    储物柜固定成本: {locker_fixed_cost:.2f} 元/天 ({locker_fixed_cost/total_objective*100:.1f}%)")
        print(f"    无人机成本(部署+运输): {drone_total_cost:.2f} 元/天 ({drone_total_cost/total_objective*100:.1f}%)")
        print(f"    卡车成本(固定+运输): {truck_total_cost:.2f} 元/天 ({truck_total_cost/total_objective*100:.1f}%)")
        print(f"    其他成本(惩罚): {penalty_total_cost:.2f} 元/天 ({penalty_total_cost/total_objective*100:.1f}%)")

        print(f"\n  🏪 储物柜配置:")
        print(f"    选定站点: {selected_lockers_print}")
        total_drones = 0
        for j_site_p in selected_lockers_print:
            drones = round(n_star_final.get(j_site_p,0))
            total_drones += drones
            print(f"    位置 {j_site_p}: {drones} 架无人机")
        print(f"    无人机总数: {total_drones} 架")

        print(f"\n  📈 运营指标:")
        print(f"    总期望需求量: {sum(self.expected_demand.values()):.2f} 订单/天")
        print(f"    平均每储物柜服务: {sum(self.expected_demand.values())/len(selected_lockers_print):.2f} 订单/天")
        print(f"    平均每无人机服务: {sum(self.expected_demand.values())/total_drones:.2f} 订单/天" if total_drones > 0 else "    平均每无人机服务: N/A")

        print("\n  📝 模型说明:")
        print("    第一阶段决策：储物柜选址和无人机配置（已确定）")
        print("    第二阶段决策：根据实际需求场景动态优化客户分配和配送")
        print("    成本为考虑需求不确定性后的期望日均成本")

        # 详细的客户分配方案分析已移除以减少冗余输出

        if DRL_AVAILABLE:
            viz_solution_saa = {
                'selected_lockers': {j:True for j in selected_lockers_print},
                'customer_assignments_primary': {},  # 空的，因为客户分配在第二阶段
                'unassigned_customers_by_u': list(self.customers),  # 所有客户都在第二阶段分配
                'drone_allocations': {j: round(n_star_final.get(j,0)) for j in selected_lockers_print},
                'objective_value': saa_solution_dict.get('objective_value_k_prime_estimate', 0.0)
            }
            print("\n  显示SAA最终解的可视化:")
            print("  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）")
            print("  ↳ 客户分配将在第二阶段根据实际需求场景动态优化")
            print("  ↳ 上述成本是考虑需求不确定性后的期望值")
            self.visualize_solution(viz_solution_saa)

    def _print_detailed_customer_assignment_analysis(self, saa_solution_dict: Dict, selected_lockers: List[int]):
        """
        输出详细的客户分配方案分析
        """
        print(f"\n  详细客户分配方案分析:")
        print(f"  ============================================================")

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        # 使用第一阶段分配（基于期望需求）进行分析
        first_stage_assignment = self._solve_first_stage_assignment_for_analysis(y_star, n_star, selected_lockers)

        if first_stage_assignment:
            print(f"  基于期望需求的第一阶段客户分配:")

            # 按储物柜分组显示分配
            for j in selected_lockers:
                assigned_customers = []
                total_assigned_demand = 0

                for i in self.customers:
                    assigned_qty = first_stage_assignment.get((i, j), 0)
                    if assigned_qty > 0.1:  # 避免浮点数精度问题
                        assigned_customers.append((i, assigned_qty))
                        total_assigned_demand += assigned_qty

                print(f"    储物柜 {j}:")
                print(f"      总分配需求: {total_assigned_demand:.1f}")
                print(f"      容量利用率: {total_assigned_demand/self.Q_locker_capacity[j]*100:.1f}%")
                print(f"      分配的客户: {len(assigned_customers)} 个")

                if assigned_customers:
                    print(f"      详细分配:")
                    for customer_id, qty in sorted(assigned_customers):
                        expected_demand = self.expected_demand[customer_id]
                        allocation_rate = qty / expected_demand * 100 if expected_demand > 0 else 0
                        distance = self.distance.get((customer_id, j), 0)
                        print(f"        客户 {customer_id}: {qty:.1f}/{expected_demand:.1f} ({allocation_rate:.1f}%), 距离: {distance:.1f}km")
                else:
                    print(f"      无客户分配")
                print()

            # 分析未分配的客户
            unassigned_customers = []
            for i in self.customers:
                total_assigned = sum(first_stage_assignment.get((i, j), 0) for j in selected_lockers)
                expected_demand = self.expected_demand[i]
                if total_assigned < expected_demand - 0.1:
                    shortage = expected_demand - total_assigned
                    unassigned_customers.append((i, shortage, expected_demand))

            if unassigned_customers:
                print(f"  未完全分配的客户 ({len(unassigned_customers)} 个):")
                total_unassigned_demand = 0
                for customer_id, shortage, expected_demand in sorted(unassigned_customers):
                    print(f"    客户 {customer_id}: 短缺 {shortage:.1f}/{expected_demand:.1f} ({shortage/expected_demand*100:.1f}%)")
                    total_unassigned_demand += shortage
                print(f"  总未分配需求: {total_unassigned_demand:.1f}")
                print(f"  未分配比例: {total_unassigned_demand/sum(self.expected_demand.values())*100:.1f}%")
            else:
                print(f"  ✓ 所有客户的期望需求都已完全分配")

        else:
            print(f"  ⚠ 无法获取第一阶段客户分配信息")

    def _solve_first_stage_assignment_for_analysis(self, y_star: Dict, n_star: Dict, selected_lockers: List[int]) -> Dict:
        """
        为分析目的求解第一阶段的客户分配问题（基于期望需求）
        """
        try:
            import gurobipy as gp
            from gurobipy import GRB

            model_fs = gp.Model("FirstStageAssignmentAnalysis")
            model_fs.setParam('OutputFlag', 0)
            model_fs.setParam('Threads', 1)

            # 决策变量
            x_fs = {}
            for i in self.customers:
                for j in selected_lockers:
                    x_fs[i, j] = model_fs.addVar(vtype=GRB.CONTINUOUS, lb=0, name=f"x_fs_{i}_{j}")

            # 目标函数：最小化运输成本和惩罚成本
            transport_cost_fs = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), self.BIG_M) * x_fs[i, j]
                for i in self.customers for j in selected_lockers
            )
            penalty_cost_fs = gp.quicksum(
                self.penalty_cost_unassigned * (self.expected_demand[i] - gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers))
                for i in self.customers
            )
            model_fs.setObjective(transport_cost_fs + penalty_cost_fs, GRB.MINIMIZE)

            # 约束
            for i in self.customers:
                # 分配量不超过期望需求
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers) <= self.expected_demand[i])
                for j in selected_lockers:
                    # 飞行距离限制
                    if (i, j) in self.distance:
                        model_fs.addConstr(2 * self.distance[i, j] * x_fs[i, j] <= self.max_flight_distance * self.expected_demand[i])

            for j in selected_lockers:
                # 无人机服务能力约束
                total_hours_needed_j = gp.quicksum(
                    x_fs.get((i, j), 0) * ((2 * self.distance.get((i, j), self.BIG_M) / self.drone_speed) + self.loading_time)
                    for i in self.customers
                )
                model_fs.addConstr(total_hours_needed_j <= n_star.get(j, 0) * self.H_drone_working_hours_per_day)
                # 储物柜容量约束
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for i in self.customers) <= self.Q_locker_capacity[j])

            model_fs.optimize()

            if model_fs.status == GRB.OPTIMAL:
                assignment = {}
                for i in self.customers:
                    for j in selected_lockers:
                        if (i, j) in x_fs:
                            assignment[i, j] = x_fs[i, j].X
                del model_fs
                return assignment
            else:
                del model_fs
                return None

        except Exception as e:
            print(f"  第一阶段分配分析失败: {str(e)}")
            return None

    def _print_second_stage_solver_analysis(self):
        """
        输出第二阶段求解器性能分析
        """
        stats = self.second_stage_solver_stats

        if stats['total_scenarios'] == 0:
            print(f"\n  第二阶段求解器性能分析:")
            print(f"    无求解统计数据")
            return

        print(f"\n  第二阶段求解器性能分析:")
        print(f"  ============================================================")
        print(f"  求解器配置: 启发式算法 (FastAssignmentSolver)")
        print(f"  总验证场景数: {stats['total_scenarios']}")

        # 启发式求解器模式的统计
        print(f"  启发式求解: {stats['heuristic_solver_used']} (100.0%)")

        if stats['heuristic_solve_times']:
            avg_heuristic_time = sum(stats['heuristic_solve_times']) / len(stats['heuristic_solve_times'])
            print(f"  平均启发式求解时间: {avg_heuristic_time:.3f} 秒")
            print(f"  启发式求解时间范围: {min(stats['heuristic_solve_times']):.3f} - {max(stats['heuristic_solve_times']):.3f} 秒")

        print(f"  ✓ 使用启发式算法，求解速度快")

    def visualize_solution(self, solution: Dict):
        if not DRL_AVAILABLE: return
        if not solution: print("无解决方案可供可视化。"); return
        temp_solution_for_viz = solution.copy()
        primary_assignments = solution.get('customer_assignments_primary', {})

        # 修改：支持多储物柜分配的可视化
        # 直接传递完整的储物柜列表给可视化函数
        customer_assignments_viz = {}
        for cust, lockers in primary_assignments.items():
            if lockers: # 如果有分配的储物柜
                # 直接传递储物柜列表，visualization.py会自动处理多储物柜连线
                customer_assignments_viz[cust] = lockers

        temp_solution_for_viz['customer_assignments'] = customer_assignments_viz


        customer_service_modes = {}
        for customer_id in customer_assignments_viz: # 使用调整后的键
            customer_service_modes[customer_id] = 0
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # unassigned_customers 可能需要从 solution['unassigned_customers_by_u'] 获取
        if 'unassigned_customers_by_u' in solution:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']


        # 移除可能引起混淆的旧键
        if 'customer_assignments_primary' in temp_solution_for_viz: del temp_solution_for_viz['customer_assignments_primary']
        if 'unassigned_customers_by_u' in temp_solution_for_viz and 'unassigned_customers' in temp_solution_for_viz :
             if temp_solution_for_viz['unassigned_customers_by_u'] == temp_solution_for_viz['unassigned_customers']:
                 del temp_solution_for_viz['unassigned_customers_by_u']


        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="SAA 无人机配送网络规划 (基于期望需求分配)"
        )
        if plt_fig:
            import matplotlib.pyplot as plt
            plt.show(block=False) # 使用 block=False 避免阻塞后续代码，如果需要交互则去掉
            plt.pause(1) # 暂停一下，确保图像显示

# --- 主程序入口 ---
if __name__ == "__main__":
    start_time_main = time.time()
    print(f"设置全局随机种子: {RANDOM_SEED}")
    if DRL_AVAILABLE:
        set_drl_log_level(logging.WARNING)
        print("DRL日志级别已设置为WARNING")
    else:
        print("DRL模块不可用，相关功能将跳过。")


    print("\n创建随机需求的示例数据 (使用期望需求)...")
    print("=" * 60)
    print("成本计算方法改进：统一时间单位")
    print("=" * 60)
    print("修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用")
    print("修正后方案：使用资本回收因子将所有固定成本统一为日成本单位")
    print("优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差")
    print("=" * 60)

    stochastic_data_instance = create_deterministic_example_instance(
        demand_level="low",
        locker_cost_level="medium",
        drone_cost_level="medium",
        drone_transport_cost_level="medium",
        use_generated_distances=True,
        num_customers=10, # 与g_i.py保持一致
        num_sites=4,    #  与g_i.py保持一致
        use_kmeans_clustering=True,
        random_seed=RANDOM_SEED,
        # 年化成本参数
        annual_interest_rate=0.04,    # 4% 年利率
        equipment_life_years=10,      # 10年设备生命周期
        operating_days_per_year=365   # 365天年运营天数
    )
    stochastic_data_instance['expected_demand'] = stochastic_data_instance.pop('demand_deterministic')
    print("随机需求数据 (期望值) 已创建。")

    print("\n所有客户期望需求 (λᵢ_bar):")
    for customer_id_main, demand_val_main in stochastic_data_instance['expected_demand'].items():
        print(f"  客户 {customer_id_main}: {demand_val_main} 订单/天 (期望)")
    total_expected_demand_val_main = sum(stochastic_data_instance['expected_demand'].values())
    print(f"总期望需求: {total_expected_demand_val_main} 订单/天")

    # 调试信息已移除以减少冗余输出


    print("\n" + "=" * 60 + "\n求解带随机需求的无人机配送网络设计问题 (SAA)\n" + "=" * 60)
    solve_start_time_saa_main = time.time()

    optimizer_saa_main = StochasticDroneDeliveryOptimizerSAA()
    optimizer_saa_main.set_parameters(**stochastic_data_instance)

    # 使用ALNS整体求解方法求解SAA问题
    print("使用ALNS整体求解方法求解SAA问题...")
    final_saa_solution = optimizer_saa_main.solve_saa_with_alns(
        time_limit_per_replication=120  # ALNS时间限制
    )

    solve_time_saa_main = time.time() - solve_start_time_saa_main

    if final_saa_solution:
        optimizer_saa_main._print_saa_solution(final_saa_solution)
        print(f"\nSAA模型求解总耗时: {solve_time_saa_main:.2f} 秒")
    else:
        print("\n⚠ SAA未能找到可行解或优化失败。")

    total_time_main = time.time() - start_time_main
    print(f"\n总运行时间: {total_time_main:.2f} 秒")
    if DRL_AVAILABLE:
        print("测试完成。如果图像窗口仍然打开，请手动关闭。")
