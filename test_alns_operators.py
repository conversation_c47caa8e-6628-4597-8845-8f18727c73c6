#!/usr/bin/env python3
"""
测试修改后的ALNS算子
验证是否能自主搜索到[1,2,3]方案
"""

import sys
import os
import time
import numpy as np

# 导入ALNS模块
from alns import *

def test_alns_operators():
    """测试ALNS算子的有效性"""
    print("=" * 60)
    print("测试修改后的ALNS算子")
    print("=" * 60)
    
    # 创建问题实例
    problem_data = create_deterministic_example_instance(
        num_customers=15,
        num_sites=4,
        use_kmeans_clustering=False,
        demand_level="medium",
        random_seed=611
    )
    
    # 创建问题对象
    problem = ProblemInstance()
    problem.set_parameters(**problem_data)
    
    # 生成需求样本
    demand_samples = []
    for _ in range(40):
        scenario = {}
        for i in problem.customers:
            scenario[i] = np.random.poisson(problem.expected_demand[i])
        demand_samples.append(scenario)
    
    # 创建ALNS求解器
    alns_solver = ALNS_Solver(problem, demand_samples)
    
    print("\n🔧 测试新的精确算子...")
    
    # 测试从[1,3,4]开始，看能否找到[1,2,3]
    initial_solution = {'y': {1: 1, 2: 0, 3: 1, 4: 1}, 'n': {1: 1, 2: 0, 3: 1, 4: 1}}
    print(f"\n📍 初始解: [1,3,4]")
    
    initial_cost = alns_solver.calculate_objective_direct(initial_solution)
    print(f"   初始成本: {initial_cost:.2f} 元/天")
    
    # 测试精确移除算子
    print(f"\n🔨 测试精确移除算子...")
    removed_solution = alns_solver.exact_cost_removal(initial_solution, iteration=1)
    removed_lockers = [j for j, val in removed_solution['y'].items() if val > 0.5]
    removed_cost = alns_solver.calculate_objective_direct(removed_solution)
    print(f"   移除后方案: {removed_lockers}")
    print(f"   移除后成本: {removed_cost:.2f} 元/天")
    
    # 测试精确插入算子
    print(f"\n🔧 测试精确插入算子...")
    repaired_solution = alns_solver.exact_cost_insertion(removed_solution, iteration=1)
    repaired_lockers = [j for j, val in repaired_solution['y'].items() if val > 0.5]
    repaired_cost = alns_solver.calculate_objective_direct(repaired_solution)
    print(f"   修复后方案: {repaired_lockers}")
    print(f"   修复后成本: {repaired_cost:.2f} 元/天")
    
    # 检查是否找到了[1,2,3]
    if set(repaired_lockers) == {1, 2, 3}:
        print(f"\n✅ 成功！算子组合找到了[1,2,3]方案！")
        print(f"   成本改进: {initial_cost - repaired_cost:.2f} 元/天")
    else:
        print(f"\n⚠️  算子组合找到了{repaired_lockers}方案")
        print(f"   成本变化: {repaired_cost - initial_cost:.2f} 元/天")
    
    return repaired_lockers, repaired_cost

def test_alns_search():
    """测试完整的ALNS搜索"""
    print("\n" + "=" * 60)
    print("测试完整的ALNS搜索（不强制[1,2,3]）")
    print("=" * 60)
    
    # 创建问题实例
    problem_data = create_deterministic_example_instance(
        num_customers=15,
        num_sites=4,
        use_kmeans_clustering=False,
        demand_level="medium",
        random_seed=611
    )
    
    # 创建问题对象
    problem = ProblemInstance()
    problem.set_parameters(**problem_data)
    
    # 生成需求样本
    demand_samples = []
    for _ in range(40):
        scenario = {}
        for i in problem.customers:
            scenario[i] = np.random.poisson(problem.expected_demand[i])
        demand_samples.append(scenario)
    
    # 创建ALNS求解器
    alns_solver = ALNS_Solver(problem, demand_samples)
    
    print(f"\n🚀 开始ALNS搜索（时间限制: 120秒）...")
    start_time = time.time()
    
    # 运行ALNS
    best_solution = alns_solver.solve(time_limit=120)
    
    solve_time = time.time() - start_time
    
    if best_solution:
        selected_lockers = [j for j, val in best_solution['y'].items() if val > 0.5]
        print(f"\n🏆 ALNS找到的最优解:")
        print(f"   选定储物柜: {selected_lockers}")
        print(f"   求解时间: {solve_time:.2f} 秒")
        
        # 验证是否找到了[1,2,3]
        if set(selected_lockers) == {1, 2, 3}:
            print(f"   ✅ 成功！ALNS自主找到了[1,2,3]方案！")
        else:
            print(f"   ⚠️  ALNS找到了{selected_lockers}方案")
            
            # 比较与[1,2,3]的成本差异
            solution_123 = {'y': {1: 1, 2: 1, 3: 1, 4: 0}, 'n': {1: 1, 2: 1, 3: 1, 4: 0}}
            cost_123 = alns_solver.calculate_objective_direct(solution_123)
            cost_found = alns_solver.calculate_objective_direct(best_solution)
            
            print(f"   [1,2,3]成本: {cost_123:.2f} 元/天")
            print(f"   找到解成本: {cost_found:.2f} 元/天")
            print(f"   成本差异: {cost_found - cost_123:.2f} 元/天")
    else:
        print(f"\n❌ ALNS未找到解")
    
    return best_solution

if __name__ == "__main__":
    print("开始测试修改后的ALNS算子...")
    
    # 测试1: 算子有效性
    operator_result = test_alns_operators()
    
    # 测试2: 完整ALNS搜索
    search_result = test_alns_search()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
