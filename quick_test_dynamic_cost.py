#!/usr/bin/env python3
"""
快速测试动态拥堵成本是否生效
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """快速测试动态成本"""
    print("快速测试动态拥堵成本...")
    
    # 导入模块
    try:
        from main import *
        print("✓ 模块导入成功")
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return
    
    # 创建一个简单的问题实例
    print("创建测试问题...")
    
    try:
        # 创建小规模问题：5个客户，3个储物柜
        problem = create_deterministic_example_instance(
            num_customers=5,
            num_sites=3,
            demand_level="high",  # 高需求，容易产生拥堵
            random_seed=42
        )
        
        print(f"✓ 问题创建成功: {len(problem['customers'])}客户, {len(problem['sites'])}储物柜")
        
        # 创建优化器
        optimizer = StochasticDroneDeliveryOptimizerSAA()
        optimizer.set_parameters(**problem)
        
        # 生成少量需求场景
        print("生成需求场景...")
        optimizer.generate_demand_samples(K=5)  # 只生成5个场景，快速测试
        
        print(f"✓ 生成了{len(optimizer.demand_samples)}个需求场景")
        
        # 创建一个紧张的解来测试动态成本
        print("测试动态拥堵成本...")
        
        # 选择前2个储物柜，配置较少无人机，制造容量紧张
        y_star = {site: 1 if site in problem['sites'][:2] else 0 for site in problem['sites']}
        n_star = {site: 2 if site in problem['sites'][:2] else 0 for site in problem['sites']}  # 每个储物柜只配2架无人机
        
        selected_lockers = [site for site, val in y_star.items() if val > 0]
        print(f"选择的储物柜: {selected_lockers}")
        print(f"无人机配置: {[(site, n_star[site]) for site in selected_lockers]}")
        
        # 使用第一个需求场景进行测试
        demand_scenario = optimizer.demand_samples[0]
        print(f"测试需求场景: {dict(list(demand_scenario.items())[:3])}...")  # 只显示前3个
        
        # 调用FastAssignmentSolver
        if not hasattr(optimizer, 'fast_solver') or optimizer.fast_solver is None:
            optimizer.fast_solver = FastAssignmentSolver(optimizer)
        
        print("\n" + "="*50)
        print("开始分配过程 (应该显示动态成本调试信息):")
        print("="*50)
        
        # 执行分配
        assignment = optimizer.fast_solver.solve_assignment_heuristic(
            y_star, n_star, selected_lockers, demand_scenario
        )
        
        print("="*50)
        print("分配完成!")
        
        # 分析结果
        print("\n分配结果分析:")
        total_assigned = {}
        for (customer, locker), amount in assignment.items():
            if amount > 1e-6:
                if locker not in total_assigned:
                    total_assigned[locker] = 0
                total_assigned[locker] += amount
                print(f"  客户{customer} → 储物柜{locker}: {amount:.2f}")
        
        print("\n储物柜负载:")
        for locker in selected_lockers:
            assigned = total_assigned.get(locker, 0)
            capacity = optimizer.Q_locker_capacity.get(locker, 0)
            load_ratio = assigned / capacity if capacity > 0 else 0
            print(f"  储物柜{locker}: {assigned:.2f}/{capacity:.2f} = {load_ratio:.1%}")
        
        print("\n✓ 测试完成!")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
