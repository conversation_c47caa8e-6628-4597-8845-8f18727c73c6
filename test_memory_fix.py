#!/usr/bin/env python3
"""
测试内存优化效果的脚本
"""

import time
import psutil
import os
from alns import create_deterministic_example_instance, TwoStageProblem

def monitor_memory():
    """监控内存使用"""
    try:
        process = psutil.Process()
        memory_mb = process.memory_info().rss / (1024 * 1024)
        return memory_mb
    except:
        return 0

def test_memory_optimization():
    """测试内存优化效果"""
    print("=" * 60)
    print("内存优化测试")
    print("=" * 60)
    
    # 记录初始内存
    initial_memory = monitor_memory()
    print(f"初始内存使用: {initial_memory:.1f} MB")
    
    # 创建问题实例
    print("\n1. 创建问题实例...")
    instance_data = create_deterministic_example_instance(
        num_customers=15,
        num_sites=6,
        demand_level="medium"
    )
    
    memory_after_instance = monitor_memory()
    print(f"创建实例后内存: {memory_after_instance:.1f} MB (+{memory_after_instance-initial_memory:.1f} MB)")
    
    # 创建两阶段问题
    print("\n2. 创建两阶段问题...")
    problem = TwoStageProblem(instance_data)
    
    memory_after_problem = monitor_memory()
    print(f"创建问题后内存: {memory_after_problem:.1f} MB (+{memory_after_problem-memory_after_instance:.1f} MB)")
    
    # 运行SAA（限制时间和样本数）
    print("\n3. 运行SAA优化（限制版本）...")
    
    # 临时修改参数以减少内存使用
    import alns
    original_k = alns.SAA_SAMPLES_K
    original_k_prime = alns.SAA_SAMPLES_K_PRIME
    original_max_rep = alns.SAA_MAX_REPLICATIONS_M
    
    # 使用更小的参数
    alns.SAA_SAMPLES_K = 10  # 减少训练样本
    alns.SAA_SAMPLES_K_PRIME = 100  # 减少验证样本
    alns.SAA_MAX_REPLICATIONS_M = 2  # 只运行2次复制
    
    try:
        start_time = time.time()
        best_solution, best_obj, saa_stats = problem.solve_saa_with_alns(
            time_limit_per_replication=30  # 限制每次复制30秒
        )
        end_time = time.time()
        
        memory_after_saa = monitor_memory()
        print(f"\nSAA完成后内存: {memory_after_saa:.1f} MB (+{memory_after_saa-memory_after_problem:.1f} MB)")
        print(f"总运行时间: {end_time-start_time:.1f} 秒")
        
        if best_solution:
            print(f"最优目标值: {best_obj:.2f}")
        else:
            print("未找到可行解")
            
    except Exception as e:
        print(f"SAA运行出错: {e}")
        memory_after_error = monitor_memory()
        print(f"出错时内存: {memory_after_error:.1f} MB")
    
    finally:
        # 恢复原始参数
        alns.SAA_SAMPLES_K = original_k
        alns.SAA_SAMPLES_K_PRIME = original_k_prime
        alns.SAA_MAX_REPLICATIONS_M = original_max_rep
    
    # 手动清理
    print("\n4. 执行手动内存清理...")
    del problem
    del instance_data
    
    import gc
    collected = 0
    for _ in range(5):
        collected += gc.collect()
    
    memory_after_cleanup = monitor_memory()
    print(f"清理后内存: {memory_after_cleanup:.1f} MB (-{memory_after_saa-memory_after_cleanup:.1f} MB)")
    print(f"垃圾回收对象: {collected}")
    
    # 总结
    print("\n" + "=" * 60)
    print("内存使用总结:")
    print(f"  初始内存: {initial_memory:.1f} MB")
    print(f"  峰值内存: {max(memory_after_instance, memory_after_problem, memory_after_saa):.1f} MB")
    print(f"  最终内存: {memory_after_cleanup:.1f} MB")
    print(f"  净增长: {memory_after_cleanup-initial_memory:.1f} MB")
    print("=" * 60)

if __name__ == "__main__":
    test_memory_optimization()
