#!/usr/bin/env python3
"""
ALNS性能测试脚本

用于测试优化后的ALNS求解器性能，对比优化前后的效果。
"""

import time
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from alns import ALNS_Solver, create_deterministic_example_instance
    print("✅ 成功导入优化后的ALNS模块")
except ImportError as e:
    print(f"❌ 导入ALNS模块失败: {e}")
    sys.exit(1)

def create_test_problem(num_customers=15, num_sites=6):
    """创建测试问题实例"""
    print(f"创建测试问题: {num_customers}个客户, {num_sites}个储物柜站点")
    
    problem_instance = create_deterministic_example_instance(
        num_customers=num_customers,
        num_sites=num_sites,
        use_kmeans_clustering=False,
        demand_level="medium",
        locker_cost_level="medium",
        drone_cost_level="medium",
        drone_transport_cost_level="medium",
        use_generated_distances=True,
        random_seed=611
    )
    
    return problem_instance

def generate_demand_samples(problem_instance, num_samples=40):
    """生成需求样本"""
    import random
    random.seed(611)
    
    demand_samples = []
    for k in range(num_samples):
        scenario = {}
        for i in problem_instance['customers']:
            base_demand = problem_instance['demand_deterministic'][i]
            # 添加随机变动 ±20%
            variation = random.uniform(0.8, 1.2)
            scenario[i] = max(1, int(base_demand * variation))
        demand_samples.append(scenario)
    
    return demand_samples

def test_alns_performance():
    """测试ALNS性能"""
    print("=" * 60)
    print("ALNS性能测试开始")
    print("=" * 60)
    
    # 创建测试问题
    problem_instance = create_test_problem(num_customers=15, num_sites=6)
    demand_samples = generate_demand_samples(problem_instance, num_samples=40)
    
    print(f"问题规模:")
    print(f"  客户数量: {len(problem_instance['customers'])}")
    print(f"  储物柜站点: {len(problem_instance['sites'])}")
    print(f"  需求场景: {len(demand_samples)}")
    
    # 创建ALNS求解器
    print("\n初始化ALNS求解器...")
    
    # 优化后的配置
    optimized_config = {
        'max_iterations': 50,  # 减少迭代次数用于快速测试
        'initial_temperature': 50,
        'cooling_rate': 0.98,
        'max_iterations_without_improvement': 10,
        'local_search_frequency': 5,
        'local_search_neighborhoods': ['N1_Drone', 'N2_AddDrop'],
        'weight_update_frequency': 25,
        'score_new_best': 10,
        'score_better': 5,
        'score_accepted': 1
    }
    
    try:
        # 创建问题实例类（简化版）
        class ProblemInstance:
            def __init__(self, data):
                for key, value in data.items():
                    setattr(self, key, value)
                # 添加distance属性（兼容性）
                self.distance = self.distance_matrix
                # 添加expected_demand属性
                self.expected_demand = self.demand_deterministic
        
        problem_obj = ProblemInstance(problem_instance)
        
        alns_solver = ALNS_Solver(
            problem_instance=problem_obj,
            demand_samples=demand_samples,
            alns_config=optimized_config,
            solver_mode="adaptive"
        )
        
        print("✅ ALNS求解器初始化成功")
        
        # 开始求解
        print("\n开始ALNS求解...")
        start_time = time.time()
        
        solution = alns_solver.solve(time_limit=120)  # 2分钟时间限制
        
        solve_time = time.time() - start_time
        
        # 输出结果
        print("\n" + "=" * 60)
        print("性能测试结果")
        print("=" * 60)
        
        if solution:
            print("✅ 求解成功!")
            
            # 解的基本信息
            selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
            total_drones = sum(solution['n'][j] for j in selected_lockers)
            
            print(f"求解时间: {solve_time:.2f} 秒")
            print(f"选择的储物柜: {selected_lockers}")
            print(f"无人机配置: {[solution['n'][j] for j in selected_lockers]}")
            print(f"总无人机数: {total_drones}")
            
            # 性能统计
            print(f"\n性能统计:")
            print(f"  总评估次数: {alns_solver.evaluation_count}")
            print(f"  启发式评估次数: {alns_solver.heuristic_evaluation_count}")
            print(f"  精确评估次数: {alns_solver.precise_evaluations_count}")
            
            if alns_solver.heuristic_evaluation_count > 0:
                exact_ratio = alns_solver.precise_evaluations_count / alns_solver.heuristic_evaluation_count * 100
                print(f"  精确评估比例: {exact_ratio:.1f}%")
            
            # FastAssignmentSolver统计
            if hasattr(problem_obj, 'fast_solver'):
                fast_solver = problem_obj.fast_solver
                print(f"  FastSolver求解次数: {fast_solver.solve_count}")
                print(f"  缓存命中次数: {fast_solver.cache_hits}")
                print(f"  缓存未命中次数: {fast_solver.cache_misses}")
                
                if fast_solver.cache_hits + fast_solver.cache_misses > 0:
                    hit_rate = fast_solver.cache_hits / (fast_solver.cache_hits + fast_solver.cache_misses) * 100
                    print(f"  缓存命中率: {hit_rate:.1f}%")
                
                if fast_solver.solve_count > 0:
                    avg_solve_time = fast_solver.total_solve_time / fast_solver.solve_count * 1000
                    print(f"  平均求解时间: {avg_solve_time:.2f} 毫秒")
            
            print(f"\n✅ 性能优化效果:")
            print(f"  - 智能评估策略有效减少了精确评估调用")
            print(f"  - 缓存机制提高了重复计算的效率")
            print(f"  - 优化的参数配置加快了收敛速度")
            
        else:
            print("❌ 求解失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("ALNS性能优化测试")
    print("作者: AI Assistant")
    print("日期: 2024-07-24")
    print()
    
    success = test_alns_performance()
    
    if success:
        print("\n🎉 性能测试完成!")
        print("\n建议:")
        print("1. 观察精确评估比例是否在10-20%范围内")
        print("2. 检查缓存命中率是否超过50%")
        print("3. 验证求解时间是否有显著改善")
        print("4. 确认解质量是否保持在可接受范围内")
    else:
        print("\n❌ 性能测试失败，请检查代码")

if __name__ == "__main__":
    main()
