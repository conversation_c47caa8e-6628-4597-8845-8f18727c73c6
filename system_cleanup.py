#!/usr/bin/env python3
"""
系统级缓存清理工具
清理Python和相关程序在系统中产生的缓存
"""

import os
import shutil
import tempfile
import psutil
from pathlib import Path

def get_disk_usage():
    """获取磁盘使用情况"""
    try:
        # Windows C盘
        if os.name == 'nt':
            disk_usage = shutil.disk_usage('C:')
        else:
            disk_usage = shutil.disk_usage('/')
        
        total_gb = disk_usage.total / (1024**3)
        used_gb = disk_usage.used / (1024**3)
        free_gb = disk_usage.free / (1024**3)
        
        return {
            'total_gb': total_gb,
            'used_gb': used_gb,
            'free_gb': free_gb,
            'used_percent': (used_gb / total_gb) * 100
        }
    except Exception as e:
        print(f"获取磁盘使用情况失败: {e}")
        return None

def cleanup_python_temp():
    """清理Python临时文件"""
    print("正在清理Python临时文件...")
    
    temp_dir = tempfile.gettempdir()
    print(f"临时文件目录: {temp_dir}")
    
    total_cleaned = 0
    python_temp_patterns = [
        "tmp*",
        "python*",
        "pip*",
        "*.tmp",
        "*.temp"
    ]
    
    try:
        temp_path = Path(temp_dir)
        for pattern in python_temp_patterns:
            temp_files = list(temp_path.glob(pattern))
            for temp_file in temp_files:
                try:
                    if temp_file.is_file():
                        size_mb = temp_file.stat().st_size / (1024 * 1024)
                        temp_file.unlink()
                        total_cleaned += size_mb
                        print(f"  删除文件: {temp_file} ({size_mb:.3f} MB)")
                    elif temp_file.is_dir():
                        size_mb = get_folder_size(temp_file)
                        shutil.rmtree(temp_file)
                        total_cleaned += size_mb
                        print(f"  删除目录: {temp_file} ({size_mb:.2f} MB)")
                except Exception as e:
                    print(f"  删除失败: {temp_file} - {e}")
    except Exception as e:
        print(f"清理临时文件时出错: {e}")
    
    print(f"Python临时文件清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def cleanup_pip_cache():
    """清理pip缓存"""
    print("正在清理pip缓存...")
    
    total_cleaned = 0
    
    # pip缓存位置
    if os.name == 'nt':  # Windows
        pip_cache_dirs = [
            os.path.expanduser("~\\AppData\\Local\\pip\\Cache"),
            os.path.expanduser("~\\pip\\cache")
        ]
    else:  # Linux/Mac
        pip_cache_dirs = [
            os.path.expanduser("~/.cache/pip"),
            os.path.expanduser("~/.pip/cache")
        ]
    
    for cache_dir in pip_cache_dirs:
        if os.path.exists(cache_dir):
            try:
                size_mb = get_folder_size(cache_dir)
                shutil.rmtree(cache_dir)
                total_cleaned += size_mb
                print(f"  删除pip缓存: {cache_dir} ({size_mb:.2f} MB)")
            except Exception as e:
                print(f"  删除pip缓存失败: {cache_dir} - {e}")
    
    print(f"pip缓存清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def cleanup_conda_cache():
    """清理conda缓存"""
    print("正在清理conda缓存...")
    
    total_cleaned = 0
    
    # conda缓存位置
    conda_cache_dirs = []
    
    if os.name == 'nt':  # Windows
        conda_cache_dirs = [
            os.path.expanduser("~\\AppData\\Local\\conda\\conda\\pkgs"),
            os.path.expanduser("~\\.conda\\pkgs"),
            "C:\\ProgramData\\Anaconda3\\pkgs",
            "C:\\Users\\<USER>\\Anaconda3\\pkgs"
        ]
    else:  # Linux/Mac
        conda_cache_dirs = [
            os.path.expanduser("~/anaconda3/pkgs"),
            os.path.expanduser("~/miniconda3/pkgs"),
            os.path.expanduser("~/.conda/pkgs")
        ]
    
    for cache_dir in conda_cache_dirs:
        if os.path.exists(cache_dir):
            try:
                # 只清理缓存文件，不删除整个目录
                cache_path = Path(cache_dir)
                for item in cache_path.iterdir():
                    if item.is_file() and item.suffix in ['.tar.bz2', '.conda', '.json']:
                        size_mb = item.stat().st_size / (1024 * 1024)
                        item.unlink()
                        total_cleaned += size_mb
                        print(f"  删除conda缓存文件: {item} ({size_mb:.3f} MB)")
            except Exception as e:
                print(f"  清理conda缓存失败: {cache_dir} - {e}")
    
    print(f"conda缓存清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def get_folder_size(folder_path):
    """计算文件夹大小（MB）"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(folder_path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except Exception as e:
        print(f"计算文件夹大小时出错: {e}")
    return total_size / (1024 * 1024)

def cleanup_jupyter_cache():
    """清理Jupyter缓存"""
    print("正在清理Jupyter缓存...")
    
    total_cleaned = 0
    
    jupyter_dirs = []
    if os.name == 'nt':  # Windows
        jupyter_dirs = [
            os.path.expanduser("~\\AppData\\Roaming\\jupyter"),
            os.path.expanduser("~\\.jupyter")
        ]
    else:  # Linux/Mac
        jupyter_dirs = [
            os.path.expanduser("~/.jupyter"),
            os.path.expanduser("~/.local/share/jupyter")
        ]
    
    for jupyter_dir in jupyter_dirs:
        if os.path.exists(jupyter_dir):
            try:
                # 只清理缓存相关文件
                jupyter_path = Path(jupyter_dir)
                cache_patterns = ["*.log", "*.sqlite", "runtime/*"]
                
                for pattern in cache_patterns:
                    cache_files = list(jupyter_path.rglob(pattern))
                    for cache_file in cache_files:
                        try:
                            size_mb = cache_file.stat().st_size / (1024 * 1024)
                            cache_file.unlink()
                            total_cleaned += size_mb
                            print(f"  删除Jupyter缓存: {cache_file} ({size_mb:.3f} MB)")
                        except:
                            pass
            except Exception as e:
                print(f"  清理Jupyter缓存失败: {jupyter_dir} - {e}")
    
    print(f"Jupyter缓存清理完成，释放空间: {total_cleaned:.2f} MB")
    return total_cleaned

def main():
    """主清理函数"""
    print("=" * 60)
    print("系统级Python缓存清理工具")
    print("=" * 60)
    
    # 显示磁盘使用情况
    disk_info = get_disk_usage()
    if disk_info:
        print(f"磁盘使用情况:")
        print(f"  总容量: {disk_info['total_gb']:.1f} GB")
        print(f"  已使用: {disk_info['used_gb']:.1f} GB ({disk_info['used_percent']:.1f}%)")
        print(f"  可用空间: {disk_info['free_gb']:.1f} GB")
        print()
    
    total_space_freed = 0
    
    # 清理各种缓存
    print("开始清理系统缓存...")
    
    total_space_freed += cleanup_python_temp()
    print()
    
    total_space_freed += cleanup_pip_cache()
    print()
    
    total_space_freed += cleanup_conda_cache()
    print()
    
    total_space_freed += cleanup_jupyter_cache()
    print()
    
    print("=" * 60)
    print(f"系统清理完成！总共释放空间: {total_space_freed:.2f} MB ({total_space_freed/1024:.2f} GB)")
    
    # 显示清理后的磁盘使用情况
    disk_info_after = get_disk_usage()
    if disk_info and disk_info_after:
        space_gained = disk_info_after['free_gb'] - disk_info['free_gb']
        print(f"磁盘空间变化: +{space_gained:.2f} GB")
    
    print("=" * 60)
    
    print("\n建议:")
    print("1. 定期运行此清理工具")
    print("2. 考虑使用虚拟环境隔离项目依赖")
    print("3. 定期清理不需要的Python包")
    print("4. 监控大型数据文件和日志文件")

if __name__ == "__main__":
    main()
