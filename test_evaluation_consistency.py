#!/usr/bin/env python3
"""
测试ALNS和g_i.py评估函数的一致性
使用相同的样本数据进行比较
"""

import sys
import os
import time
import numpy as np

# 导入模块
from alns import *
from g_i import *

def test_evaluation_consistency():
    """测试评估函数的一致性"""
    print("=" * 60)
    print("测试ALNS和g_i.py评估函数的一致性")
    print("=" * 60)
    
    # 设置相同的随机种子
    np.random.seed(611)
    
    # 创建问题实例
    problem_data = create_deterministic_example_instance(
        num_customers=15,
        num_sites=4,
        use_kmeans_clustering=False,
        demand_level="medium",
        random_seed=611
    )
    
    # 创建问题对象
    problem = ProblemInstance()
    problem.set_parameters(**problem_data)
    
    # 生成固定的需求样本（确保两种方法使用相同样本）
    print("\n📊 生成固定需求样本...")
    demand_samples = []
    np.random.seed(611)  # 重新设置种子确保一致性
    for k in range(40):  # 使用40个样本
        scenario = {}
        for i in problem.customers:
            scenario[i] = np.random.poisson(problem.expected_demand[i])
        demand_samples.append(scenario)
    
    print(f"   生成了{len(demand_samples)}个需求样本")
    
    # 测试方案
    test_solutions = [
        {'name': '[1,2]', 'y': {1: 1, 2: 1, 3: 0, 4: 0}, 'n': {1: 1, 2: 1, 3: 0, 4: 0}},
        {'name': '[1,3]', 'y': {1: 1, 2: 0, 3: 1, 4: 0}, 'n': {1: 1, 2: 0, 3: 1, 4: 0}},
        {'name': '[1,2,3]', 'y': {1: 1, 2: 1, 3: 1, 4: 0}, 'n': {1: 1, 2: 1, 3: 1, 4: 0}},
        {'name': '[1,3,4]', 'y': {1: 1, 2: 0, 3: 1, 4: 1}, 'n': {1: 1, 2: 0, 3: 1, 4: 1}},
    ]
    
    print("\n🔍 比较不同评估方法的结果：")
    print("-" * 70)
    print(f"{'方案':<10} {'ALNS评估':<15} {'g_i评估':<15} {'差异':<15}")
    print("-" * 70)
    
    # 创建ALNS求解器
    alns_solver = ALNS_Solver(problem, demand_samples)
    
    results = []
    for sol in test_solutions:
        print(f"\n评估方案 {sol['name']}...")
        
        # ALNS评估
        try:
            alns_cost = alns_solver.calculate_objective_direct(sol)
        except Exception as e:
            print(f"   ALNS评估失败: {e}")
            alns_cost = float('inf')
        
        # g_i评估（使用相同的样本）
        try:
            # 创建g_i的问题实例
            gi_problem = ProblemInstance()
            gi_problem.set_parameters(**problem_data)
            
            # 使用g_i的评估方法
            gi_cost = evaluate_solution_with_gi(sol, gi_problem, demand_samples)
        except Exception as e:
            print(f"   g_i评估失败: {e}")
            gi_cost = float('inf')
        
        # 计算差异
        if alns_cost != float('inf') and gi_cost != float('inf'):
            diff = alns_cost - gi_cost
            diff_pct = (diff / gi_cost) * 100 if gi_cost != 0 else 0
        else:
            diff = float('inf')
            diff_pct = float('inf')
        
        results.append({
            'name': sol['name'],
            'alns_cost': alns_cost,
            'gi_cost': gi_cost,
            'diff': diff,
            'diff_pct': diff_pct
        })
        
        print(f"{sol['name']:<10} {alns_cost:<15.2f} {gi_cost:<15.2f} {diff:<15.2f}")
    
    print("-" * 70)
    
    # 分析结果
    print(f"\n📈 结果分析:")
    
    # 找到最优方案
    valid_results = [r for r in results if r['alns_cost'] != float('inf') and r['gi_cost'] != float('inf')]
    
    if valid_results:
        alns_best = min(valid_results, key=lambda x: x['alns_cost'])
        gi_best = min(valid_results, key=lambda x: x['gi_cost'])
        
        print(f"   ALNS最优方案: {alns_best['name']} ({alns_best['alns_cost']:.2f}元/天)")
        print(f"   g_i最优方案:  {gi_best['name']} ({gi_best['gi_cost']:.2f}元/天)")
        
        if alns_best['name'] == gi_best['name']:
            print(f"   ✅ 两种方法找到相同的最优方案！")
        else:
            print(f"   ❌ 两种方法找到不同的最优方案")
        
        # 检查评估一致性
        max_diff = max(abs(r['diff']) for r in valid_results)
        avg_diff = sum(abs(r['diff']) for r in valid_results) / len(valid_results)
        
        print(f"   最大评估差异: {max_diff:.2f}元/天")
        print(f"   平均评估差异: {avg_diff:.2f}元/天")
        
        if max_diff < 1.0:
            print(f"   ✅ 评估函数基本一致（差异<1元/天）")
        elif max_diff < 5.0:
            print(f"   ⚠️  评估函数有小差异（差异<5元/天）")
        else:
            print(f"   ❌ 评估函数差异较大（差异≥5元/天）")
    
    return results

def evaluate_solution_with_gi(solution, problem, demand_samples):
    """使用g_i的方法评估解"""
    # 这里需要实现与g_i.py一致的评估逻辑
    # 由于g_i.py的代码比较复杂，这里简化实现
    
    y_star = solution['y']
    n_star = solution['n']
    selected_lockers = [j for j, val in y_star.items() if val > 0.5]
    
    if not selected_lockers:
        return float('inf')
    
    # 第一阶段成本
    first_stage_cost = (sum(problem.locker_fixed_cost[j] for j in selected_lockers) +
                       sum(problem.drone_cost * n_star.get(j, 0) for j in selected_lockers))
    
    # 第二阶段期望成本（简化版本）
    total_second_stage_cost = 0
    
    for demand_scenario in demand_samples:
        # 简化的第二阶段求解
        scenario_cost = 0
        
        # 运输成本估算
        for i in problem.customers:
            demand = demand_scenario.get(i, 0)
            if demand > 0:
                # 找到最近的储物柜
                min_cost = float('inf')
                for j in selected_lockers:
                    if (i, j) in problem.distance:
                        transport_cost = 2 * problem.transport_unit_cost * problem.distance[i, j] * demand
                        if transport_cost < min_cost:
                            min_cost = transport_cost
                
                if min_cost != float('inf'):
                    scenario_cost += min_cost
                else:
                    # 惩罚成本
                    scenario_cost += problem.penalty_cost_unassigned * demand
        
        total_second_stage_cost += scenario_cost
    
    # 平均第二阶段成本
    avg_second_stage_cost = total_second_stage_cost / len(demand_samples)
    
    return first_stage_cost + avg_second_stage_cost

if __name__ == "__main__":
    print("开始测试评估函数一致性...")
    
    results = test_evaluation_consistency()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
