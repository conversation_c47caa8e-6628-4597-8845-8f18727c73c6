#!/usr/bin/env python3
"""
快速测试：直接验证[1,2]配置的无人机优化
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("=" * 50)
    print("快速验证无人机配置修复")
    print("=" * 50)
    
    # 直接运行一个简化的ALNS测试
    print("建议：")
    print("1. 直接运行完整的ALNS算法")
    print("2. 观察是否能在迭代过程中找到[1,2] + {1:1, 2:1}配置")
    print("3. 检查smart_drone_tuner的日志输出")
    
    print("\n修复要点：")
    print("✅ 强制优先测试每储物柜1架无人机的配置")
    print("✅ 对少量储物柜使用精确评估")
    print("✅ 按无人机总数排序，优先测试少无人机配置")
    
    print("\n预期结果：")
    print("- ALNS应该能在迭代中找到[1,2] + {1:1, 2:1}配置")
    print("- 该配置的成本应该接近133.41元/天")
    print("- smart_drone_tuner应该输出找到更优配置的日志")
    
    print("\n建议运行：")
    print("python alns.py")
    print("然后观察输出中是否出现：")
    print("'智能调优发现更优配置: {1: 1, 2: 1}, 总无人机: 2'")

if __name__ == "__main__":
    main()
