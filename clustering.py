import numpy as np
from sklearn.cluster import KMeans
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)

def generate_locker_sites_with_kmeans(
    customer_coords: Dict[int, Tuple[float, float]], 
    num_sites: int, 
    random_state: int = 42
) -> Dict[int, Tuple[float, float]]:
    """
    Use k-means clustering to generate locker candidate sites based on customer locations.
    
    Args:
        customer_coords: Dictionary mapping customer IDs to their coordinates (x, y)
        num_sites: Number of locker sites to generate
        random_state: Random seed for reproducibility
        
    Returns:
        Dictionary mapping site IDs to their coordinates (x, y)
    """
    logger.info(f"Generating {num_sites} locker sites using k-means clustering on {len(customer_coords)} customer points")
    
    # Extract customer coordinates as a numpy array
    customer_points = np.array([coords for coords in customer_coords.values()])
    
    # Ensure we don't try to create more clusters than data points
    actual_num_sites = min(num_sites, len(customer_points))
    if actual_num_sites < num_sites:
        logger.warning(f"Requested {num_sites} sites but only {actual_num_sites} can be created from {len(customer_points)} customers")
    
    # Apply k-means clustering
    kmeans = KMeans(n_clusters=actual_num_sites, random_state=random_state, n_init=10)
    kmeans.fit(customer_points)
    
    # Get cluster centers as locker sites
    site_coords = {}
    for i, center in enumerate(kmeans.cluster_centers_):
        site_id = i + 1  # Site IDs start from 1
        site_coords[site_id] = (float(center[0]), float(center[1]))
    
    logger.info(f"Generated {len(site_coords)} locker sites using k-means clustering")
    return site_coords

def visualize_clustering(
    customer_coords: Dict[int, Tuple[float, float]],
    site_coords: Dict[int, Tuple[float, float]],
    save_path: str = None
):
    """
    Visualize the clustering results with customers and locker sites.
    
    Args:
        customer_coords: Dictionary mapping customer IDs to their coordinates
        site_coords: Dictionary mapping site IDs to their coordinates
        save_path: If provided, save the visualization to this path
    """
    import matplotlib.pyplot as plt
    
    plt.figure(figsize=(10, 8))
    
    # Plot customers
    customer_x = [coord[0] for coord in customer_coords.values()]
    customer_y = [coord[1] for coord in customer_coords.values()]
    plt.scatter(customer_x, customer_y, c='blue', marker='o', alpha=0.6, label='Customers')
    
    # Plot locker sites (cluster centers)
    site_x = [coord[0] for coord in site_coords.values()]
    site_y = [coord[1] for coord in site_coords.values()]
    plt.scatter(site_x, site_y, c='red', marker='s', s=100, label='Locker Sites (Cluster Centers)')
    
    # Add site labels
    for site_id, coord in site_coords.items():
        plt.text(coord[0] + 0.1, coord[1] + 0.1, f"S{site_id}", fontsize=10, color='red')
    
    plt.title('K-means Clustering for Locker Site Generation')
    plt.xlabel('X Coordinate')
    plt.ylabel('Y Coordinate')
    plt.legend()
    plt.grid(True)
    
    if save_path:
        plt.savefig(save_path)
        logger.info(f"Clustering visualization saved to {save_path}")
    
    return plt 