import gurobipy as gp
from gurobipy import GRB
import numpy as np
from typing import Dict, List, Tuple, Any
import random
import time
import math
from collections import defaultdict
try:
    from drl import DRL_CVRP_Solver, set_drl_log_level
    from clustering import generate_locker_sites_with_kmeans
    from visualization import visualize_solution
    DRL_AVAILABLE = True
except ImportError:
    print("警告: drl, clustering, 或 visualization 模块未找到。DRL和可视化功能将不可用。")
    DRL_AVAILABLE = False
    # 提供这些类的虚拟实现，以避免 NameError
    class DRL_CVRP_Solver:
        def __init__(self, *args, **kwargs): pass
        def solve(self, *args, **kwargs): return 0.0, None # 返回一个默认值
    def set_drl_log_level(level): pass
    def generate_locker_sites_with_kmeans(customer_coords, num_sites, random_state):
        print("警告: K-Means聚类不可用，将随机生成储物柜站点。")
        local_random = random.Random(random_state)
        return {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in range(1, num_sites + 1)}
    def visualize_solution(*args, **kwargs):
        print("警告: 可视化功能不可用。")
        return None


import logging # logging 应该在顶部导入

# 设置随机种子以确保结果可重现
RANDOM_SEED = 606
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# SAA 参数 (调整以提高统计稳定性)
SAA_MAX_REPLICATIONS_M = 10  # 最大复制次数
SAA_MIN_REPLICATIONS_M = 2   # 最小复制次数 (设为最小值，主要依靠终止条件)
SAA_SAMPLES_K = 40          # 训练样本数量 N (减少以提高求解效率)
SAA_SAMPLES_K_PRIME = 2000   # 验证样本数量 N' (适当减少以平衡计算效率)
SAA_CONFIDENCE_LEVEL_ALPHA = 0.1  # 置信水平 (90%置信区间)
SAA_GAP_TOLERANCE_PERCENT = 0.03  # Gap阈值 ε' = 3%
SAA_VARIANCE_TOLERANCE_PERCENT = 0.05  # 方差阈值 ε = 5%

# ---------------------------------------------------------------------------
# create_deterministic_example_instance 函数 (全局作用域)
# ---------------------------------------------------------------------------
def create_deterministic_example_instance(
        num_customers: int = 15,
        num_sites: int = 6,
        use_kmeans_clustering: bool = False,
        demand_level: str = "medium",
        locker_cost_level: str = "medium",
        drone_cost_level: str = "medium",
        drone_transport_cost_level: str = "medium",
        use_generated_distances: bool = True,
        random_seed: int = RANDOM_SEED,
        # 新增年化成本计算参数
        annual_interest_rate: float = 0.04,  # IR: 年利率 4%
        equipment_life_years: int = 10,      # T_life: 设备生命周期 10年
        operating_days_per_year: int = 365   # D_year: 年运营天数
):
    """
    创建示例问题的参数实例 (用于确定性或期望值)

    修正版：使用年化成本计算，确保时间单位一致性
    - 储物柜和无人机成本从一次性投资转换为日均固定成本
    - 所有成本项统一使用日成本单位
    """
    local_random = random.Random(random_seed)
    customers_list = list(range(1, num_customers + 1))
    sites_list = list(range(1, num_sites + 1))

    customer_coords_dict = {i: (local_random.uniform(0, 15), local_random.uniform(0, 15)) for i in customers_list}

    if use_kmeans_clustering and DRL_AVAILABLE: # K-Means依赖clustering模块
        site_coords_dict = generate_locker_sites_with_kmeans(customer_coords_dict, num_sites, random_state=random_seed)
    else:
        site_coords_dict = {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in sites_list}

    demand_params_dict = {"low": (1, 2), "medium": (2, 3), "high": (3, 4)}.get(demand_level, (2, 3))
    demand_dict_for_instance = {c: local_random.randint(demand_params_dict[0], demand_params_dict[1]) for c in customers_list}

    # === 修正版成本计算：统一时间单位为日成本 ===

    # 第1步：计算资本回收因子 (Capital Recovery Factor)
    IR = annual_interest_rate
    T_life = equipment_life_years
    capital_recovery_factor = (IR * (1 + IR)**T_life) / ((1 + IR)**T_life - 1)

    print(f"  [saa_g_r.py] 成本计算参数:")
    print(f"    年利率 (IR): {IR*100:.1f}%")
    print(f"    设备生命周期 (T_life): {T_life}年")
    print(f"    资本回收因子: {capital_recovery_factor:.6f}")
    print(f"    年运营天数: {operating_days_per_year}天")

    # 第2步：储物柜初始建设成本 -> 日均固定成本
    locker_initial_cost_val = {"low": 10000, "medium": 15000, "high": 20000}.get(locker_cost_level, 10000)
    locker_annual_cost = locker_initial_cost_val * capital_recovery_factor  # c_l^a
    locker_daily_cost = locker_annual_cost / operating_days_per_year        # c_l^daily
    locker_fixed_cost_dict = {s: locker_daily_cost for s in sites_list}

    print(f"  [saa_g_r.py] 储物柜成本转换:")
    print(f"    初始建设成本: {locker_initial_cost_val:,.0f}元")
    print(f"    年化成本: {locker_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {locker_daily_cost:.2f}元/天")

    # 第3步：无人机初始采购成本 -> 日均固定成本
    drone_initial_cost_val = {"low": 3000, "medium": 4000, "high": 5000}.get(drone_cost_level, 3000)
    drone_annual_cost = drone_initial_cost_val * capital_recovery_factor     # c_d^a
    drone_daily_cost = drone_annual_cost / operating_days_per_year           # c_d^daily
    drone_cost_val_param = drone_daily_cost

    print(f"  [saa_g_r.py] 无人机成本转换:")
    print(f"    初始采购成本: {drone_initial_cost_val:,.0f}元")
    print(f"    年化成本: {drone_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {drone_daily_cost:.2f}元/天")

    # 无人机运输单位成本
    transport_unit_cost_val_param = {"low": 0.01, "medium": 0.02, "high": 0.03}.get(drone_transport_cost_level, 0.01)

    drone_speed_param = 50.0
    loading_time_param = 300.0 / 3600.0
    max_flight_distance_param = 20.0
    H_drone_working_hours_per_day_param = 8.0
    penalty_cost_unassigned_param = 1000.0  # 高惩罚成本促使系统分配更多客户
    avg_locker_capacity_val = 30
    Q_locker_capacity_dict = {s: avg_locker_capacity_val for s in sites_list}

    depot_coord_param = (0, 0)
    truck_capacity_param = 90
    truck_fixed_cost_param = 100
    truck_km_cost_param = 0.5

    distance_matrix_dict = {}
    if use_generated_distances:
        for c_id_val_dist, c_coord_val_dist in customer_coords_dict.items():
            for s_id_val_dist, s_coord_val_dist in site_coords_dict.items():
                dist_val = math.sqrt(
                    (c_coord_val_dist[0] - s_coord_val_dist[0]) ** 2 + (c_coord_val_dist[1] - s_coord_val_dist[1]) ** 2)
                distance_matrix_dict[c_id_val_dist, s_id_val_dist] = round(dist_val, 2)
    else:
        for c_id_val_dist_rand in customers_list:
            for s_id_val_dist_rand in sites_list:
                base_dist_val = math.sqrt(
                    (customer_coords_dict[c_id_val_dist_rand][0] - site_coords_dict[s_id_val_dist_rand][0]) ** 2 + \
                    (customer_coords_dict[c_id_val_dist_rand][1] - site_coords_dict[s_id_val_dist_rand][1]) ** 2)
                distance_matrix_dict[c_id_val_dist_rand, s_id_val_dist_rand] = round(
                    local_random.uniform(base_dist_val * 0.8, base_dist_val * 1.2), 1)

    # === 成本单位统一性验证 ===
    print(f"\n  [saa_g_r.py] 成本单位统一性检查:")
    print(f"    储物柜固定成本: {locker_daily_cost:.2f} 元/天")
    print(f"    无人机固定成本: {drone_daily_cost:.2f} 元/天")
    print(f"    无人机运输成本: {transport_unit_cost_val_param:.3f} 元/公里 (按实际运输量)")
    print(f"    卡车固定成本: {truck_fixed_cost_param} 元/天")
    print(f"    卡车运输成本: {truck_km_cost_param} 元/公里 (按实际运输量)")
    print(f"    ✓ 所有固定成本已统一为日成本单位")
    print(f"    ✓ 运输成本保持按实际使用量计费")

    return {
        'customers': customers_list, 'sites': sites_list,
        'demand_deterministic': demand_dict_for_instance,
        'distance_matrix': distance_matrix_dict, 'drone_speed': drone_speed_param,
        'loading_time': loading_time_param, 'max_flight_distance': max_flight_distance_param,
        'locker_fixed_cost': locker_fixed_cost_dict, 'transport_unit_cost': transport_unit_cost_val_param,
        'drone_cost': drone_cost_val_param, 'H_drone_working_hours_per_day': H_drone_working_hours_per_day_param,
        'penalty_cost_unassigned': penalty_cost_unassigned_param,
        'Q_locker_capacity': Q_locker_capacity_dict, 'customer_coords': customer_coords_dict,
        'site_coords': site_coords_dict, 'depot_coord': depot_coord_param,
        'truck_capacity': truck_capacity_param, 'truck_fixed_cost': truck_fixed_cost_param,
        'truck_km_cost': truck_km_cost_param
    }

# ---------------------------------------------------------------------------
# StochasticDroneDeliveryOptimizerSAA 类的定义
# ---------------------------------------------------------------------------
class StochasticDroneDeliveryOptimizerSAA:

    def __init__(self):
        self.customers = []
        self.sites = []
        self.expected_demand = {}
        self.distance = {}
        self.locker_fixed_cost = {}
        self.Q_locker_capacity = {}
        self.drone_speed = None
        self.loading_time = None
        self.max_flight_distance = None
        self.transport_unit_cost = None
        self.drone_cost = None
        self.H_drone_working_hours_per_day = None
        self.penalty_cost_unassigned = None
        self.customer_coords = {}
        self.site_coords = {}
        self.depot_coord = None
        self.truck_capacity = None
        self.truck_fixed_cost = None
        self.truck_km_cost = None
        self.max_trucks = 5  # 最大卡车数量
        self.truck_distances = {}  # 卡车距离矩阵
        self.BIG_M = 1e6

        # SAA 相关结果存储
        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = [] # 存储每次复制在K'个样本上的平均卡车成本
        self.best_solution_validation_costs = []  # 存储最佳解在每个验证场景上的成本

        # DRL求解器实例 (每个优化器实例共享，如果参数不变)
        self._drl_solver = None
        self._drl_solver_params = None
        self._drl_solver_no_plots = None
        self._drl_solver_no_plots_params = None



        # 时间统计 (用于分析求解时间分布)
        self.time_stats = defaultdict(list)  # 存储各阶段的时间统计


    def set_parameters(self,
                       customers: List,
                       sites: List,
                       expected_demand: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        self.customers = customers
        self.sites = sites
        self.expected_demand = expected_demand
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned
        self.Q_locker_capacity = Q_locker_capacity
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        # 计算卡车距离矩阵（仓库到储物柜，储物柜间距离）
        self._build_truck_distance_matrix()

        if self.expected_demand:
            max_expected_demand_val = max(self.expected_demand.values()) if self.expected_demand else 0
            self.BIG_M = max_expected_demand_val * 2 if max_expected_demand_val > 0 else 1e6
        else:
            self.BIG_M = 1e6

    def _build_truck_distance_matrix(self):
        """构建卡车距离矩阵，包括仓库到储物柜和储物柜间的距离"""
        self.truck_distances = {}

        if not self.depot_coord or not self.site_coords:
            return

        # 仓库到储物柜的距离
        for j in self.sites:
            if j in self.site_coords:
                dist = math.sqrt((self.depot_coord[0] - self.site_coords[j][0])**2 +
                               (self.depot_coord[1] - self.site_coords[j][1])**2)
                self.truck_distances[(0, j)] = dist
                self.truck_distances[(j, 0)] = dist

        # 储物柜间的距离
        for i in self.sites:
            for j in self.sites:
                if i != j and i in self.site_coords and j in self.site_coords:
                    dist = math.sqrt((self.site_coords[i][0] - self.site_coords[j][0])**2 +
                                   (self.site_coords[i][1] - self.site_coords[j][1])**2)
                    self.truck_distances[(i, j)] = dist

    def _get_drl_solver(self, make_plots: bool = True):
        if not DRL_AVAILABLE: return None # 如果DRL不可用，返回None

        current_params = (self.depot_coord, self.truck_capacity, self.truck_fixed_cost, self.truck_km_cost)
        if make_plots:
            if self._drl_solver is None or self._drl_solver_params != current_params:
                set_drl_log_level(logging.WARNING)
                self._drl_solver = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=True
                )
                self._drl_solver_params = current_params
            return self._drl_solver
        else:
            if self._drl_solver_no_plots is None or self._drl_solver_no_plots_params != current_params:
                set_drl_log_level(logging.WARNING)
                self._drl_solver_no_plots = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=False
                )
                self._drl_solver_no_plots_params = current_params
            return self._drl_solver_no_plots

    def calculate_truck_cost(self,
                             selected_lockers: List[int] = None,
                             x_qty_solution_values: Dict[Tuple[int, int], float] = None,
                             make_plots: bool = True,
                             return_route_info: bool = False,
                             active_lockers_info_override: Dict[int, Dict[str, Any]] = None):
        if not DRL_AVAILABLE or self.truck_capacity is None or self.truck_fixed_cost is None or self.truck_km_cost is None:
            # print("  [calculate_truck_cost] DRL不可用或卡车参数未设置，卡车成本返回0。")
            return (0.0, None) if return_route_info else 0.0

        # 如果提供了override参数，直接使用它
        if active_lockers_info_override is not None:
            if not active_lockers_info_override:
                return (0.0, None) if return_route_info else 0.0
            try:
                drl_solver = self._get_drl_solver(make_plots=make_plots)
                if drl_solver is None:
                    return (0.0, None) if return_route_info else 0.0

                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info_override, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            except Exception as e:
                # 使用简化估算
                total_demand = sum(info['demand'] for info in active_lockers_info_override.values())
                num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost

        # 原有逻辑：从selected_lockers和x_qty_solution_values构建active_lockers_info
        # 在修正的模型中，这个逻辑主要用于向后兼容
        if selected_lockers is None or not selected_lockers:
            return (0.0, None) if return_route_info else 0.0

        if x_qty_solution_values is None:
            # 如果没有客户分配信息，返回0成本
            return (0.0, None) if return_route_info else 0.0

        try:
            drl_solver = self._get_drl_solver(make_plots=make_plots)
            if drl_solver is None: return (0.0, None) if return_route_info else 0.0 # Double check

            locker_total_demands = {locker_id: 0.0 for locker_id in selected_lockers}
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    locker_total_demands[locker_id] += quantity
            active_lockers_info = {}
            for locker_id in selected_lockers:
                demand_at_locker = locker_total_demands.get(locker_id, 0)
                if demand_at_locker > 1e-6:
                    if locker_id in self.site_coords:
                        active_lockers_info[locker_id] = {
                            'coord': self.site_coords[locker_id],
                            'demand': round(demand_at_locker)  # 四舍五入为整数
                        }
            if active_lockers_info:
                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            else:
                return (0.0, None) if return_route_info else 0.0
        except Exception as e:
            # print(f"  [calculate_truck_cost] DRL求解失败: {str(e)}, 使用简化估算")
            if x_qty_solution_values:
                total_demand_overall = 0
                for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                    if locker_id in selected_lockers and quantity > 1e-6:
                        total_demand_overall += quantity
                num_trucks = math.ceil(total_demand_overall / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost
            else:
                return (0.0, None) if return_route_info else 0.0

    def _generate_demand_samples(self, num_samples: int) -> List[Dict[int, float]]:
        """
        生成需求场景样本，客户需求服从泊松分布
        泊松分布的参数λ等于期望需求值，天然产生非负整数
        """
        demand_scenarios = []
        for _ in range(num_samples):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]  # 泊松分布参数λ
                # 使用泊松分布生成需求，λ既是均值也是方差
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)  # 转换为float以保持一致性
            demand_scenarios.append(current_scenario_demand)
        return demand_scenarios

    def _build_saa_model_for_one_replication(self, demand_samples_k: List[Dict[int, float]]):
        model = gp.Model(f"SAA_DroneLocker_Rep")
        num_scenarios = len(demand_samples_k)

        # 第一阶段决策变量（不依赖场景）
        y_rep = {j: model.addVar(vtype=GRB.BINARY, name=f"y_{j}") for j in self.sites}  # 储物柜选址
        n_rep = {j: model.addVar(vtype=GRB.INTEGER, lb=0, name=f"n_{j}") for j in self.sites}  # 无人机配置

        # 第二阶段决策变量（依赖场景）
        x_actual_rep = {}  # 实际配送量（仅受储物柜容量和无人机运力约束）
        shortage_rep = {}  # 短缺量（实际需求超出实际配送的部分）

        for k_idx in range(num_scenarios):
            # 第二阶段实际配送变量
            for i in self.customers:
                for j in self.sites:
                    x_actual_rep[i, j, k_idx] = model.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_actual_{i}_{j}_{k_idx}")
                shortage_rep[i, k_idx] = model.addVar(vtype=GRB.INTEGER, lb=0, name=f"shortage_{i}_{k_idx}")

        # 卡车成本变量（通过回调函数动态计算）
        truck_cost_var = model.addVar(lb=0.0, name="truck_cost_saa")
        model._truck_cost_var = truck_cost_var  # 存储变量以便回调函数访问
        model.addConstr(truck_cost_var >= 0, name="initial_truck_cost_lower_bound_saa")

        # 第一阶段成本（here-and-now decisions，不依赖需求场景）
        first_stage_cost = gp.quicksum(self.locker_fixed_cost[j] * y_rep[j] for j in self.sites) + \
                          gp.quicksum(self.drone_cost * n_rep[j] for j in self.sites)

        # 第二阶段期望成本（wait-and-see decisions，依赖需求场景实现）
        expected_second_stage_cost = gp.LinExpr()
        for k_idx in range(num_scenarios):
            demand_for_scenario_k = demand_samples_k[k_idx]

            # 第二阶段成本包括所有随需求场景变化的成本：
            # 1. 无人机运输成本（基于实际配送量）
            transport_cost_k = gp.quicksum(
                2 * self.transport_unit_cost * self.distance[i, j] * x_actual_rep[i, j, k_idx]
                for i in self.customers for j in self.sites if (i, j) in self.distance
            )

            # 2. 未分配惩罚成本（基于短缺量）
            unassigned_penalty_k = gp.quicksum(
                self.penalty_cost_unassigned * shortage_rep[i, k_idx]
                for i in self.customers
            )

            # 第二阶段总成本（不包含卡车成本，卡车成本通过回调函数处理）
            second_stage_cost_k = transport_cost_k + unassigned_penalty_k
            expected_second_stage_cost += (1.0 / num_scenarios) * second_stage_cost_k

        # 总目标函数：第一阶段成本 + 第二阶段期望成本 + 卡车成本变量
        total_expected_cost = first_stage_cost + expected_second_stage_cost + truck_cost_var
        model.setObjective(total_expected_cost, GRB.MINIMIZE)

        # 第一阶段约束（基于期望需求）
        model.addConstr(gp.quicksum(y_rep[j] for j in self.sites) >= 1, "C1_AtLeastOneLocker_SAA")

        # 无人机配置约束
        for j in self.sites:
            model.addConstr(n_rep[j] >= y_rep[j], name=f"C6_MinOneDroneIfOpen_SAA_{j}")

        # 第二阶段约束（基于实际需求场景）
        for k_idx in range(num_scenarios):
            demand_for_scenario_k = demand_samples_k[k_idx]

            for i in self.customers:
                # 短缺量 = 实际需求 - 实际配送总量
                model.addConstr(
                    shortage_rep[i, k_idx] == demand_for_scenario_k[i] - gp.quicksum(x_actual_rep[i, j, k_idx] for j in self.sites),
                    name=f"ShortageDefinition_SAA_{i}_{k_idx}"
                )

                for j in self.sites:
                    # 实际配送量约束：只有开放的储物柜才能配送
                    model.addConstr(
                        x_actual_rep[i, j, k_idx] <= demand_for_scenario_k[i] * y_rep[j],
                        name=f"ActualDeliveryIfOpen_SAA_{i}_{j}_{k_idx}"
                    )

                    # 飞行距离限制
                    if (i, j) in self.distance:
                        model.addConstr(
                            2 * self.distance[i, j] * x_actual_rep[i, j, k_idx] <= self.max_flight_distance * demand_for_scenario_k[i],
                            name=f"FlightDistanceLimit_SAA_{i}_{j}_{k_idx}"
                        )

            # 储物柜容量约束（基于实际配送量）
            for j in self.sites:
                model.addConstr(
                    gp.quicksum(x_actual_rep[i, j, k_idx] for i in self.customers) <= self.Q_locker_capacity[j] * y_rep[j],
                    name=f"LockerCapacity_SAA_{j}_{k_idx}"
                )

                # 无人机运力约束（基于实际配送量）
                total_drone_hours_needed_j_k = gp.quicksum(
                    x_actual_rep[i, j, k_idx] * ((2 * self.distance[i, j] / self.drone_speed) + self.loading_time)
                    for i in self.customers if (i, j) in self.distance
                )
                drone_hours_supplied_j = n_rep[j] * self.H_drone_working_hours_per_day
                model.addConstr(
                    total_drone_hours_needed_j_k <= drone_hours_supplied_j,
                    name=f"DroneCapacity_SAA_{j}_{k_idx}"
                )

        # 初始化回调函数相关的缓存和计数器
        model._callback_count = 0
        model._solution_cache = {}  # 缓存解的卡车成本
        model._last_truck_cost = 0.0  # 记录上一次计算的卡车成本

        # 定义卡车成本回调函数 (Lazy Constraint Callback)
        def truck_cost_callback_for_saa(model, where):
            if where == GRB.Callback.MIPSOL:  # 当找到新的整数可行解时调用
                model._callback_count += 1

                # 获取当前整数解中的变量值
                y_sol_vals = model.cbGetSolution(y_rep)
                n_sol_vals = model.cbGetSolution(n_rep)

                selected_lockers_cb = [j_site for j_site in self.sites if y_sol_vals[j_site] > 0.5]

                # 创建用于缓存的解的哈希键（基于选中的储物柜和无人机配置）
                solution_key = (tuple(sorted(selected_lockers_cb)),
                               tuple(sorted((j, round(n_sol_vals[j])) for j in selected_lockers_cb)))

                current_truck_cost_cb = 0.0
                if solution_key in model._solution_cache:  # 检查缓存
                    current_truck_cost_cb = model._solution_cache[solution_key]
                else:
                    # 计算当前解的卡车成本
                    # 为了计算卡车成本，我们需要估算储物柜的需求量
                    # 使用期望需求作为估算
                    if selected_lockers_cb:
                        # 简化：假设需求均匀分配到所有开放的储物柜
                        total_expected_demand = sum(self.expected_demand.values())
                        demand_per_locker = total_expected_demand / len(selected_lockers_cb)

                        active_lockers_info_cb = {}
                        for j_locker in selected_lockers_cb:
                            active_lockers_info_cb[j_locker] = {
                                'coord': self.site_coords[j_locker],
                                'demand': round(demand_per_locker)
                            }

                        # 调用DRL计算卡车成本
                        try:
                            drl_solver = self._get_drl_solver(make_plots=False)
                            if drl_solver is not None:
                                current_truck_cost_cb = drl_solver.solve(active_lockers_info_cb, return_route_info=False)
                            else:
                                # 回退到简化估算
                                num_trucks = math.ceil(total_expected_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
                                current_truck_cost_cb = num_trucks * self.truck_fixed_cost
                        except Exception as e:
                            # 如果DRL失败，使用简化估算
                            num_trucks = math.ceil(total_expected_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
                            current_truck_cost_cb = num_trucks * self.truck_fixed_cost

                    model._solution_cache[solution_key] = current_truck_cost_cb  # 缓存结果

                # 添加 Lazy Constraint: truck_cost_var >= (刚计算得到的实际卡车成本)
                model.cbLazy(model._truck_cost_var >= current_truck_cost_cb)
                model._last_truck_cost = current_truck_cost_cb  # 更新记录

        model._callback = truck_cost_callback_for_saa  # 将回调函数注册到模型

        model.update()
        return model, y_rep, n_rep, x_actual_rep, shortage_rep
    def solve_saa(self, time_limit_per_replication: int = 300, mip_gap_per_replication: float = 0.01):
        print(f"\n开始SAA优化，最多 {SAA_MAX_REPLICATIONS_M} 次复制...")
        print(f"SAA终止条件 (必须同时满足):")
        print(f"  1. 相对差距阈值: Gap/UB ≤ {SAA_GAP_TOLERANCE_PERCENT*100:.0f}% 且 Gap ≥ 0")
        print(f"  2. 方差阈值: δ²_Gap/UB ≤ {SAA_VARIANCE_TOLERANCE_PERCENT*100:.0f}%")
        print(f"  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)")
        print(f"  最少需要 {SAA_MIN_REPLICATIONS_M} 次有效复制")



        # 生成固定的验证样本集 (所有复制共用)
        print(f"生成 {SAA_SAMPLES_K_PRIME} 个固定验证样本 (所有复制共用)...")

        # 重置随机种子以确保验证样本一致性
        original_random_state = random.getstate()
        original_np_state = np.random.get_state()
        random.seed(RANDOM_SEED + 1000)  # 使用不同的种子避免与训练样本重复
        np.random.seed(RANDOM_SEED + 1000)

        self.fixed_validation_samples = self._generate_demand_samples(num_samples=SAA_SAMPLES_K_PRIME)

        # 恢复随机状态
        random.setstate(original_random_state)
        np.random.set_state(original_np_state)

        # 调试：输出前几个样本的统计信息
        if len(self.fixed_validation_samples) > 0:
            first_sample = self.fixed_validation_samples[0]
            total_demand_first = sum(first_sample.values())
            print(f"  调试：第一个验证样本总需求 = {total_demand_first:.2f}")

            # 更可靠的样本一致性检查
            sample_checksum = sum(sum(sample.values()) for sample in self.fixed_validation_samples[:10])
            first_10_demands = [sum(sample.values()) for sample in self.fixed_validation_samples[:10]]
            print(f"  调试：前10个样本总需求和 = {sample_checksum:.2f}")
            print(f"  调试：前10个样本需求 = {[f'{d:.2f}' for d in first_10_demands]}")

        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = []

        best_solution_info = {'y': None, 'n': None, 'avg_obj_k_prime': float('inf'), 'replication_idx': -1, 'truck_cost_k_prime': 0.0}

        for m_rep in range(SAA_MAX_REPLICATIONS_M):
            print(f"\n--- SAA 复制 {m_rep + 1}/{SAA_MAX_REPLICATIONS_M} ---")
            demand_samples_for_k = self._generate_demand_samples(num_samples=SAA_SAMPLES_K)
            print(f"  已生成 {SAA_SAMPLES_K} 个需求场景用于求解。")

            saa_model_rep, y_vars, n_vars, x_actual_vars, shortage_vars = \
                self._build_saa_model_for_one_replication(demand_samples_for_k)

            saa_model_rep.setParam('TimeLimit', time_limit_per_replication)
            saa_model_rep.setParam('MIPGap', mip_gap_per_replication)
            saa_model_rep.setParam('OutputFlag', 0)
            saa_model_rep.setParam('LogToConsole', 0)
            saa_model_rep.setParam('LazyConstraints', 1)  # 启用惰性约束用于回调函数
            saa_model_rep.setParam('Threads', 1) # 使用单线程

            print(f"  开始求解复制 {m_rep + 1} 的SAA模型（使用回调函数处理卡车成本）...")
            print(f"    模型统计: {saa_model_rep.NumVars} 个变量, {saa_model_rep.NumConstrs} 个约束")
            solve_start_time_rep = time.time()
            saa_model_rep.optimize(saa_model_rep._callback)  # 使用回调函数
            solve_time_rep = time.time() - solve_start_time_rep

            # 详细的求解状态分析
            status_msg = self._get_gurobi_status_message(saa_model_rep.status)
            print(f"  复制 {m_rep + 1} 求解完成，耗时: {solve_time_rep:.2f} 秒，状态: {saa_model_rep.status} ({status_msg})")

            # 显示回调函数统计信息
            if hasattr(saa_model_rep, '_callback_count'):
                print(f"    回调函数调用次数: {saa_model_rep._callback_count}")
                print(f"    缓存的解数量: {len(saa_model_rep._solution_cache)}")
                print(f"    最后计算的卡车成本: {saa_model_rep._last_truck_cost:.2f}")

            # 如果求解失败，进行诊断
            if saa_model_rep.status == 3:  # INFEASIBLE
                print(f"    模型不可行，正在计算IIS...")
                try:
                    saa_model_rep.computeIIS()
                    print(f"    IIS包含 {saa_model_rep.IISConstrCount} 个约束")
                except:
                    print(f"    无法计算IIS")
            elif saa_model_rep.status == 4:  # INF_OR_UNBD
                print(f"    模型不可行或无界，建议检查约束设置")

            if saa_model_rep.SolCount > 0 : # 即使不是OPTIMAL，但有解就评估
                obj_val_k = saa_model_rep.ObjVal
                self.saa_objective_values_k.append(obj_val_k)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K} 个样本上的目标值: {obj_val_k:.2f}")

                y_star_m = {j: y_vars[j].X for j in self.sites}
                n_star_m = {j: n_vars[j].X for j in self.sites}

                # 添加详细的调试信息（仅第一次复制）
                if m_rep == 0:  # 只在第一次复制时输出
                    self._debug_solution_analysis(m_rep + 1, y_star_m, n_star_m, {}, {}, {})

                self.saa_solutions_first_stage.append({
                    'y': y_star_m,
                    'n': n_star_m
                })


                print(f"  评估复制 {m_rep + 1} 的解在 {SAA_SAMPLES_K_PRIME} 个固定验证样本上的性能...")
                eval_start_time = time.time()
                self._current_replication = m_rep + 1  # 设置当前复制编号用于分析
                # 传递完整的第一阶段解
                first_stage_solution = self.saa_solutions_first_stage[-1]
                avg_obj_k_prime, avg_truck_cost_for_k_prime_eval, scenario_costs = self._evaluate_solution_on_new_samples_corrected(first_stage_solution, self.fixed_validation_samples)
                eval_time = time.time() - eval_start_time
                self.saa_upper_bounds_k_prime.append(avg_obj_k_prime)
                self.saa_truck_costs_k_prime.append(avg_truck_cost_for_k_prime_eval)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均目标值 (UB估计): {avg_obj_k_prime:.2f}")
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均卡车成本: {avg_truck_cost_for_k_prime_eval:.2f}")
                print(f"  时间分析: SAA求解 {solve_time_rep:.2f}s, 验证评估 {eval_time:.2f}s, 总计 {solve_time_rep + eval_time:.2f}s")


                if avg_obj_k_prime < best_solution_info['avg_obj_k_prime']:
                    best_solution_info.update(first_stage_solution)  # 保存完整的第一阶段解
                    best_solution_info['avg_obj_k_prime'] = avg_obj_k_prime
                    best_solution_info['replication_idx'] = m_rep
                    best_solution_info['truck_cost_k_prime'] = avg_truck_cost_for_k_prime_eval
                    # 记录最佳解在每个验证场景上的成本
                    self.best_solution_validation_costs = scenario_costs
            else:
                print(f"  复制 {m_rep + 1} 未能找到可行解。")
                self.saa_objective_values_k.append(float('inf'))
                self.saa_upper_bounds_k_prime.append(float('inf'))
                self.saa_truck_costs_k_prime.append(float('inf'))
                self.saa_solutions_first_stage.append(None)


            del saa_model_rep

            # 检查SAA终止条件 (从第2次复制开始检查)
            if m_rep + 1 >= SAA_MIN_REPLICATIONS_M:
                should_terminate, gap_info = self._check_saa_termination_criteria()
                if should_terminate:
                    print(f"\n✓ SAA终止条件满足，在第 {m_rep + 1} 次复制后停止")
                    print(f"  {gap_info}")
                    break
                else:
                    print(f"  当前SAA状态: {gap_info}")
            else:
                # 即使在最小复制次数之前，也显示当前状态（如果有足够的数据）
                if len([s for s in self.saa_solutions_first_stage if s is not None]) >= 2:
                    _, gap_info = self._check_saa_termination_criteria()
                    print(f"  当前SAA状态: {gap_info}")

        if not any(s is not None for s in self.saa_solutions_first_stage):
            print("\nSAA 未能产生任何有效解。")
            return None

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 正确的SAA下界：M次复制的训练目标值的平均
        statistical_lower_bound = np.mean(valid_obj_k) if valid_obj_k else float('inf')
        std_lower_bound = np.std(valid_obj_k) if valid_obj_k else float('inf')

        # 正确的SAA上界：最佳解在验证样本上的成本（不是平均）
        statistical_upper_bound = best_solution_info['avg_obj_k_prime'] if best_solution_info['y'] is not None else float('inf')

        # 所有复制验证成本的统计信息（仅用于分析）
        avg_all_validation_costs = np.mean(valid_ub_k_prime) if valid_ub_k_prime else float('inf')
        std_all_validation_costs = np.std(valid_ub_k_prime) if valid_ub_k_prime else float('inf')

        actual_replications = len([s for s in self.saa_solutions_first_stage if s is not None])
        print(f"\n--- SAA 结果汇总 ({actual_replications} 次有效复制) ---")
        print(f"  下界估计 cost_N^m: {statistical_lower_bound:.2f}")
        print(f"    ↳ 计算方法: 前{actual_replications}次复制的小样本优化成本的算术平均")
        print(f"    ↳ 含义: 系统真实期望成本的下界估计")
        print(f"    - 下界标准差: {std_lower_bound:.2f}")
        print(f"    - 下界方差 δ²(cost_N): {std_lower_bound**2/(actual_replications*(actual_replications-1)) if actual_replications > 1 else 0:.4f}")
        print(f"  上界估计 cost_{SAA_SAMPLES_K_PRIME}(ŝ): {statistical_upper_bound:.2f}")
        print(f"    ↳ 计算方法: 最佳解在{SAA_SAMPLES_K_PRIME}个大样本场景下的平均成本")
        print(f"    ↳ 含义: 最佳方案长期运营的期望日均成本")
        print(f"  SAA Gap: {statistical_upper_bound - statistical_lower_bound:.2f} ({((statistical_upper_bound - statistical_lower_bound)/statistical_upper_bound*100):.1f}%)")
        print(f"  所有复制验证成本统计: {avg_all_validation_costs:.2f} ± {std_all_validation_costs:.2f}")

        if best_solution_info.get('y') is not None:
            print(f"\n最佳解来自复制 {best_solution_info['replication_idx'] + 1}，其在 {SAA_SAMPLES_K_PRIME} 个样本上的评估目标值为: {best_solution_info['avg_obj_k_prime']:.2f}")
            final_solution_saa = {
                'objective_value_k_prime_estimate': best_solution_info['avg_obj_k_prime'],
                'selected_lockers_y': best_solution_info['y'],
                'drone_allocations_n': best_solution_info['n'],
                'truck_cost_k_prime_estimate': best_solution_info['truck_cost_k_prime'],
            }
            print(f"  最佳解选定的储物柜 (y*): {[j for j, val in best_solution_info['y'].items() if val > 0.5]}")
            print(f"  最佳解无人机分配 (n*): {dict((j, round(val)) for j, val in best_solution_info['n'].items() if best_solution_info['y'].get(j,0) > 0.5)}")
            return final_solution_saa
        else:
            print("\nSAA 未能找到任何有效的最终解。")
            return None

    def _print_detailed_cost_breakdown(self, saa_solution_dict):
        """打印详细的成本构成分析 - SAA_G_R.PY版本（修正两阶段结构）"""
        print(f"\n  详细成本构成分析 (SAA_G_R.PY - 修正两阶段结构):")
        print(f"  " + "-" * 50)

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        locker_fixed_cost = sum(self.locker_fixed_cost[j] * y_star.get(j, 0) for j in selected_lockers)
        drone_deployment_cost = sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)
        first_stage_total = locker_fixed_cost + drone_deployment_cost

        print(f"  第一阶段成本 (here-and-now): {first_stage_total:.2f}")
        print(f"    - 储物柜固定成本: {locker_fixed_cost:.2f}")
        print(f"    - 无人机部署成本: {drone_deployment_cost:.2f}")

        # 使用固定验证样本估算第二阶段期望成本（wait-and-see decisions）
        print(f"  正在计算第二阶段期望成本 (wait-and-see)...")
        if hasattr(self, 'fixed_validation_samples') and self.fixed_validation_samples:
            sample_scenarios = self.fixed_validation_samples
            print(f"  使用固定验证样本集 ({len(sample_scenarios)} 个场景) 确保一致性")
        else:
            # 回退到生成新样本（不应该发生）
            sample_scenarios = self._generate_demand_samples(num_samples=100)
            print(f"  警告：未找到固定验证样本，生成新样本 ({len(sample_scenarios)} 个场景)")
        total_transport_cost = 0
        total_penalty_cost = 0
        total_truck_cost = 0

        for scenario_idx, scenario in enumerate(sample_scenarios):
            # 为该场景求解最优分配
            optimal_assignment = self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, scenario)

            # 计算该场景下的成本
            total_shortage = 0
            scenario_transport_cost = 0

            for i in self.customers:
                actual_demand = scenario[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage += shortage

                # 计算无人机运输成本
                for j in selected_lockers:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        scenario_transport_cost += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            scenario_penalty_cost = self.penalty_cost_unassigned * total_shortage

            total_transport_cost += scenario_transport_cost
            total_penalty_cost += scenario_penalty_cost

        # 计算第二阶段期望成本
        avg_transport_cost = total_transport_cost / len(sample_scenarios)
        avg_penalty_cost = total_penalty_cost / len(sample_scenarios)
        truck_cost_estimate = saa_solution_dict.get('truck_cost_k_prime_estimate', 0)

        second_stage_total = avg_transport_cost + avg_penalty_cost + truck_cost_estimate

        print(f"  第二阶段期望成本 (wait-and-see): {second_stage_total:.2f}")
        print(f"    - 无人机运输成本 (随实际需求变化): {avg_transport_cost:.2f}")
        print(f"    - 未分配惩罚成本 (随需求不确定性): {avg_penalty_cost:.2f}")
        print(f"    - 卡车运输成本 (随储物柜需求变化): {truck_cost_estimate:.2f}")

        total_cost = first_stage_total + second_stage_total
        print(f"  总成本 (重新计算): {total_cost:.2f}")

        # 显示与SAA主评估的差异（用于调试）
        main_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        difference = abs(total_cost - main_objective)
        print(f"  SAA主评估目标值: {main_objective:.2f}")
        print(f"  差异: {difference:.2f} ({difference/main_objective*100:.1f}%)")
        print(f"  注意: 应以SAA主评估目标值为准，此处重新计算仅用于成本构成分析")

        # 成本占比分析（基于SAA主评估目标值）
        if main_objective > 0:
            print(f"\n  成本占比分析 (基于SAA主评估目标值):")
            print(f"    - 第一阶段占比: {first_stage_total/main_objective*100:.1f}%")
            print(f"    - 第二阶段占比: {(main_objective-first_stage_total)/main_objective*100:.1f}%")
            print(f"    - 卡车成本占比: {truck_cost_estimate/main_objective*100:.1f}%")
            print(f"    - 无人机总成本占比: {(drone_deployment_cost + avg_transport_cost)/main_objective*100:.1f}%")
            print(f"    - 惩罚成本占比: {avg_penalty_cost/main_objective*100:.1f}%")

    def _evaluate_solution_on_new_samples(self, y_star: Dict, n_star: Dict, demand_samples_k_prime: List[Dict[int,float]]):
        """
        评估第一阶段解在新样本上的性能
        注意：这里需要重新获取第一阶段的客户分配决策
        """
        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        # 记录每个验证场景的总成本，用于计算Var(UB)
        scenario_total_costs = []

        # 第一阶段固定成本
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 需要重新获取第一阶段的客户分配决策（基于期望需求的最优分配）
        # 这里我们需要求解一个确定性子问题来获取第一阶段的客户分配
        first_stage_assignment = self._solve_first_stage_assignment(y_star, n_star, selected_lockers_eval)

        if first_stage_assignment is None:
            print("  警告：无法获取第一阶段客户分配，使用简化估算")
            # 使用简化的均匀分配作为回退
            first_stage_assignment = self._get_fallback_assignment(selected_lockers_eval)

        # 准备批量卡车成本计算的数据
        batch_active_lockers_info = []

        # 对每个验证场景，根据第一阶段分配和实际需求计算卡车运输需求
        for k_prime_idx, demand_scenario_k_prime in enumerate(demand_samples_k_prime):
            # 根据第一阶段分配和实际需求计算每个储物柜的实际需求量
            active_lockers_info_k_prime = {}

            for j_locker in selected_lockers_eval:
                # 计算分配给该储物柜的客户的实际总需求
                total_actual_demand_j = 0
                for i in self.customers:
                    if first_stage_assignment.get((i, j_locker), 0) > 0.5:  # 如果客户i被分配给储物柜j
                        # 按分配比例计算实际需求
                        allocation_ratio = first_stage_assignment.get((i, j_locker), 0) / max(self.expected_demand[i], 1e-6)
                        actual_demand_from_i = demand_scenario_k_prime[i] * allocation_ratio
                        total_actual_demand_j += actual_demand_from_i

                if total_actual_demand_j > 1e-6:
                    active_lockers_info_k_prime[j_locker] = {
                        'coord': self.site_coords[j_locker],
                        'demand': round(total_actual_demand_j)
                    }

            batch_active_lockers_info.append(active_lockers_info_k_prime)

        # 使用DRL批量求解计算所有场景的卡车成本
        print(f"  使用DRL批量求解计算 {num_scenarios_k_prime} 个验证场景的卡车成本...")
        batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)

        # 计算第一阶段的无人机运输成本（基于期望需求和第一阶段分配）
        first_stage_transport_cost = 0
        first_stage_penalty_cost = 0

        for i in self.customers:
            total_assigned_to_i = sum(first_stage_assignment.get((i, j), 0) for j in selected_lockers_eval)
            # 无人机运输成本
            for j in selected_lockers_eval:
                if (i, j) in self.distance and first_stage_assignment.get((i, j), 0) > 1e-6:
                    first_stage_transport_cost += 2 * self.transport_unit_cost * self.distance[i, j] * first_stage_assignment[i, j]
            # 未分配惩罚成本
            unassigned_demand = max(0, self.expected_demand[i] - total_assigned_to_i)
            first_stage_penalty_cost += self.penalty_cost_unassigned * unassigned_demand

        # 计算总成本并记录每个场景的成本
        for k_prime_idx in range(num_scenarios_k_prime):
            truck_cost_k_prime = batch_truck_costs[k_prime_idx]

            total_truck_cost_over_k_prime_samples += truck_cost_k_prime
            # 总成本 = 第一阶段成本 + 第二阶段成本（仅卡车成本）
            total_cost_for_scenario_k_prime = (locker_cost_fixed + drone_deployment_cost_fixed +
                                              first_stage_transport_cost + first_stage_penalty_cost + truck_cost_k_prime)
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime

            # 记录每个场景的总成本
            scenario_total_costs.append(total_cost_for_scenario_k_prime)

        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')

        # 添加详细的卡车成本分析（仅对第一次复制进行详细分析以避免输出过多）
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            self._analyze_truck_cost_details_corrected(y_star, n_star, demand_samples_k_prime, self._current_replication, first_stage_assignment, batch_active_lockers_info[:5])

        # 返回平均成本、卡车成本和每个场景的成本列表
        return avg_total_cost_k_prime, avg_truck_cost_k_prime, scenario_total_costs

    def _evaluate_solution_on_new_samples_corrected(self, first_stage_solution: Dict, demand_samples_k_prime: List[Dict[int,float]]):
        """
        修正版：评估完整第一阶段解在新样本上的性能
        """
        import time
        eval_start_time = time.time()

        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        # 记录每个验证场景的总成本，用于计算Var(UB)
        scenario_total_costs = []

        # 提取第一阶段解
        y_star = first_stage_solution['y']
        n_star = first_stage_solution['n']

        # 第一阶段固定成本
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        first_stage_cost = locker_cost_fixed + drone_deployment_cost_fixed

        # 准备批量卡车成本计算的数据
        batch_active_lockers_info = []

        # 时间统计
        assignment_start_time = time.time()

        # 使用串行求解所有场景的客户分配问题
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  使用串行求解 {num_scenarios_k_prime} 个场景的客户分配...")

        all_optimal_assignments = self._solve_assignments_sequential(y_star, n_star, selected_lockers_eval, demand_samples_k_prime)

        # 根据所有最优分配结果计算卡车运输需求
        for k_prime_idx, optimal_assignment in enumerate(all_optimal_assignments):
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 调试：检查前几个场景的分配结果（仅第一次复制）
            if k_prime_idx < 3 and hasattr(self, '_current_replication') and self._current_replication == 1:
                total_assigned = sum(optimal_assignment.get((i, j), 0) for i in self.customers for j in selected_lockers_eval)
                total_demand = sum(demand_scenario_k_prime.values())
                print(f"    场景 {k_prime_idx+1}: 总需求 {total_demand:.1f}, 总分配 {total_assigned:.1f}")

            # 根据最优分配计算每个储物柜的实际需求量
            active_lockers_info_k_prime = {}

            for j_locker in selected_lockers_eval:
                total_actual_demand_j = sum(optimal_assignment.get((i, j_locker), 0) for i in self.customers)
                if total_actual_demand_j > 1e-6:
                    active_lockers_info_k_prime[j_locker] = {
                        'coord': self.site_coords[j_locker],
                        'demand': round(total_actual_demand_j)
                    }

            batch_active_lockers_info.append(active_lockers_info_k_prime)

        assignment_time = time.time() - assignment_start_time

        # 使用DRL批量求解计算所有场景的卡车成本（仅第一次复制显示详细信息）
        truck_cost_start_time = time.time()
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  使用DRL批量求解计算 {num_scenarios_k_prime} 个验证场景的卡车成本...")
        batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)
        truck_cost_time = time.time() - truck_cost_start_time

        # 计算总成本并记录每个场景的成本
        cost_calc_start_time = time.time()
        for k_prime_idx in range(num_scenarios_k_prime):
            truck_cost_k_prime = batch_truck_costs[k_prime_idx]
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 使用缓存的最优分配结果，避免重复计算
            optimal_assignment = all_optimal_assignments[k_prime_idx]

            # 计算该场景下的第二阶段成本
            # 1. 无人机运输成本（基于最优分配）
            transport_cost_k_prime = 0
            total_shortage_k_prime = 0

            for i in self.customers:
                actual_demand = demand_scenario_k_prime[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers_eval)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage_k_prime += shortage

                # 计算无人机运输成本
                for j in selected_lockers_eval:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        transport_cost_k_prime += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            penalty_cost_k_prime = self.penalty_cost_unassigned * total_shortage_k_prime

            # 3. 卡车运输成本
            total_truck_cost_over_k_prime_samples += truck_cost_k_prime

            # 总成本 = 第一阶段成本 + 第二阶段成本
            total_cost_for_scenario_k_prime = (first_stage_cost + transport_cost_k_prime +
                                              penalty_cost_k_prime + truck_cost_k_prime)
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime

            # 记录每个场景的总成本
            scenario_total_costs.append(total_cost_for_scenario_k_prime)

        cost_calc_time = time.time() - cost_calc_start_time
        total_eval_time = time.time() - eval_start_time

        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')

        # 时间分析报告（仅第一次复制显示）
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  验证阶段详细时间分析:")
            print(f"    客户分配求解: {assignment_time:.2f}s ({assignment_time/total_eval_time*100:.1f}%)")
            print(f"    DRL卡车成本计算: {truck_cost_time:.2f}s ({truck_cost_time/total_eval_time*100:.1f}%)")
            print(f"    成本计算汇总: {cost_calc_time:.2f}s ({cost_calc_time/total_eval_time*100:.1f}%)")
            print(f"    验证总时间: {total_eval_time:.2f}s")

        # 返回平均成本、卡车成本和每个场景的成本列表
        return avg_total_cost_k_prime, avg_truck_cost_k_prime, scenario_total_costs

    def _solve_assignments_sequential(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_samples: List[Dict[int, float]]) -> List[Dict]:
        """
        使用串行求解所有场景的客户分配问题
        """
        all_assignments = []

        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  使用串行求解")

        for k_idx, demand_scenario in enumerate(demand_samples):
            try:
                assignment = self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, demand_scenario)
                all_assignments.append(assignment)
            except Exception as e:
                print(f"  场景 {k_idx+1} 串行求解失败: {str(e)}")
                # 使用默认分配（全部为0）
                default_assignment = {(i, j): 0.0 for i in self.customers for j in selected_lockers}
                all_assignments.append(default_assignment)

        return all_assignments

    def _solve_optimal_assignment_for_scenario(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_scenario: Dict[int, float]) -> Dict:
        """
        为给定的需求场景求解最优客户分配
        """
        try:
            model_scenario = gp.Model("ScenarioAssignment")
            model_scenario.setParam('OutputFlag', 0)
            model_scenario.setParam('Threads', 1)

            # 决策变量：实际配送量
            x_scenario = {}
            for i in self.customers:
                for j in selected_lockers:
                    x_scenario[i, j] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_{i}_{j}")

            # 短缺变量
            shortage_scenario = {}
            for i in self.customers:
                shortage_scenario[i] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"shortage_{i}")

            # 目标函数：最小化无人机运输成本和未分配惩罚
            transport_cost = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), self.BIG_M) * x_scenario[i, j]
                for i in self.customers for j in selected_lockers
            )
            penalty_cost = gp.quicksum(
                self.penalty_cost_unassigned * shortage_scenario[i]
                for i in self.customers
            )
            model_scenario.setObjective(transport_cost + penalty_cost, GRB.MINIMIZE)

            # 约束
            for i in self.customers:
                # 短缺量定义
                model_scenario.addConstr(
                    shortage_scenario[i] == demand_scenario[i] - gp.quicksum(x_scenario[i, j] for j in selected_lockers),
                    name=f"ShortageDefinition_{i}"
                )

                for j in selected_lockers:
                    # 实际配送量不超过实际需求
                    model_scenario.addConstr(
                        x_scenario[i, j] <= demand_scenario[i],
                        name=f"DeliveryLimit_{i}_{j}"
                    )

                    # 飞行距离限制：只有当实际配送时才需要满足
                    if (i, j) in self.distance:
                        # 如果 x_scenario[i,j] > 0，则必须满足距离限制
                        # 使用 Big-M 方法：2*distance*x <= max_flight_distance*demand
                        model_scenario.addConstr(
                            2 * self.distance[i, j] * x_scenario[i, j] <= self.max_flight_distance * demand_scenario[i],
                            name=f"FlightDistance_{i}_{j}"
                        )

            # 储物柜容量约束
            for j in selected_lockers:
                model_scenario.addConstr(
                    gp.quicksum(x_scenario[i, j] for i in self.customers) <= self.Q_locker_capacity[j],
                    name=f"LockerCapacity_{j}"
                )

                # 无人机运力约束
                total_drone_hours_needed = gp.quicksum(
                    x_scenario[i, j] * ((2 * self.distance[i, j] / self.drone_speed) + self.loading_time)
                    for i in self.customers if (i, j) in self.distance
                )
                drone_hours_supplied = n_star.get(j, 0) * self.H_drone_working_hours_per_day
                model_scenario.addConstr(
                    total_drone_hours_needed <= drone_hours_supplied,
                    name=f"DroneCapacity_{j}"
                )

            model_scenario.optimize()

            if model_scenario.SolCount > 0:
                assignment = {(i, j): x_scenario[i, j].X for i in self.customers for j in selected_lockers}
                # 调试：检查分配结果
                total_assigned = sum(assignment.values())
                total_demand = sum(demand_scenario.values())
                if total_assigned < 1e-6:
                    print(f"    警告：场景分配求解成功但分配量为0，总需求: {total_demand:.1f}")
                return assignment
            else:
                # 如果无解，返回空分配
                print(f"    警告：场景分配求解无可行解，状态: {model_scenario.status}")
                return {(i, j): 0.0 for i in self.customers for j in selected_lockers}

        except Exception as e:
            print(f"  场景分配求解失败: {str(e)}")
            return {(i, j): 0.0 for i in self.customers for j in selected_lockers}

    def _solve_first_stage_assignment(self, y_star: Dict, n_star: Dict, selected_lockers: List[int]) -> Dict:
        """
        求解第一阶段的客户分配问题（基于期望需求）
        """
        try:
            model_fs = gp.Model("FirstStageAssignment")
            model_fs.setParam('OutputFlag', 0)
            model_fs.setParam('Threads', 1)

            # 决策变量
            x_fs = {}
            # 移除冗余的u_fs和z_fs变量，因为惩罚成本通过直接计算短缺量实现
            # z_fs可以从x_fs推导：如果x_fs > 0则客户被分配给储物柜

            for i in self.customers:
                for j in selected_lockers:
                    x_fs[i, j] = model_fs.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_fs_{i}_{j}")

            # 目标函数：最小化无人机运输成本和未分配惩罚
            transport_cost_fs = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), self.BIG_M) * x_fs[i, j]
                for i in self.customers for j in selected_lockers
            )
            penalty_cost_fs = gp.quicksum(
                self.penalty_cost_unassigned * (self.expected_demand[i] - gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers))
                for i in self.customers
            )
            model_fs.setObjective(transport_cost_fs + penalty_cost_fs, GRB.MINIMIZE)

            # 约束
            for i in self.customers:
                # 分配量不超过期望需求
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers) <= self.expected_demand[i])
                for j in selected_lockers:
                    # 飞行距离限制：使用Big-M方法
                    if (i, j) in self.distance:
                        # 如果x_fs[i,j] > 0，则必须满足距离限制
                        model_fs.addConstr(2 * self.distance[i, j] * x_fs[i, j] <= self.max_flight_distance * self.expected_demand[i])

            for j in selected_lockers:
                # 无人机服务能力约束
                total_hours_needed_j = gp.quicksum(
                    x_fs.get((i, j), 0) * ((2 * self.distance.get((i, j), self.BIG_M) / self.drone_speed) + self.loading_time)
                    for i in self.customers
                )
                model_fs.addConstr(total_hours_needed_j <= n_star.get(j, 0) * self.H_drone_working_hours_per_day)
                # 储物柜容量约束
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for i in self.customers) <= self.Q_locker_capacity[j])

            model_fs.optimize()

            if model_fs.status == GRB.OPTIMAL:
                assignment = {}
                for i in self.customers:
                    for j in selected_lockers:
                        if (i, j) in x_fs:
                            assignment[i, j] = x_fs[i, j].X
                del model_fs
                return assignment
            else:
                del model_fs
                return None

        except Exception as e:
            print(f"  第一阶段分配求解失败: {str(e)}")
            return None

    def _get_fallback_assignment(self, selected_lockers: List[int]) -> Dict:
        """
        获取回退的客户分配（简单均匀分配）
        """
        assignment = {}
        if not selected_lockers:
            return assignment

        for i in self.customers:
            # 简单地将客户需求均匀分配给所有选中的储物柜
            demand_per_locker = self.expected_demand[i] / len(selected_lockers)
            for j in selected_lockers:
                assignment[i, j] = demand_per_locker

        return assignment

    def _analyze_truck_cost_details_corrected(self, y_solution, n_solution, validation_samples, replication, first_stage_assignment, sample_active_lockers_info):
        """
        修正版的卡车成本详细分析
        """
        print(f"  === 复制 {replication} 卡车成本详细分析（修正版两阶段） ===")

        # 统计开放的储物柜
        open_lockers = [site for site in self.sites if y_solution.get(site, 0) > 0.5]
        print(f"  开放储物柜数量: {len(open_lockers)}")
        print(f"  开放储物柜ID: {open_lockers}")

        # 显示第一阶段客户分配
        print(f"  第一阶段客户分配（基于期望需求）:")
        for i in self.customers:
            assignments = []
            for j in open_lockers:
                qty = first_stage_assignment.get((i, j), 0)
                if qty > 0.5:
                    assignments.append(f"储物柜{j}({qty:.1f})")
            if assignments:
                print(f"    客户{i}: {', '.join(assignments)} (期望需求: {self.expected_demand[i]})")

        # 分析前几个验证场景
        print(f"  前{len(sample_active_lockers_info)}个验证场景的卡车需求:")
        for i, active_lockers_info in enumerate(sample_active_lockers_info):
            print(f"  --- 场景 {i+1} ---")
            if active_lockers_info:
                total_demand = sum(info['demand'] for info in active_lockers_info.values())
                print(f"    总需求: {total_demand:.1f}")
                for locker_id, info in active_lockers_info.items():
                    print(f"    储物柜{locker_id}: 需求{info['demand']:.1f}, 坐标{info['coord']}")
            else:
                print(f"    无卡车运输需求")

        print(f"  ================================")

    def _debug_solution_analysis(self, replication, y_solution, n_solution, x_qty_solution, z_solution, u_solution):
        """
        详细调试解的分配情况（修正版：不依赖第一阶段客户分配）
        """
        print(f"\n  🔍 复制 {replication} 解的详细调试分析:")
        print(f"  " + "=" * 60)

        # 1. 储物柜开放情况
        open_lockers = [j for j, val in y_solution.items() if val > 0.5]
        print(f"  开放储物柜: {open_lockers}")

        # 2. 无人机配置情况
        drone_config = {j: round(n_solution.get(j, 0)) for j in open_lockers}
        print(f"  无人机配置: {drone_config}")

        # 3. 第一阶段成本分析
        print(f"  第一阶段成本分析:")
        locker_cost = sum(self.locker_fixed_cost[j] * y_solution.get(j, 0) for j in open_lockers)
        drone_cost = sum(self.drone_cost * n_solution.get(j, 0) for j in open_lockers)
        first_stage_cost = locker_cost + drone_cost

        print(f"    储物柜固定成本: {locker_cost:.2f}")
        print(f"    无人机部署成本: {drone_cost:.2f}")
        print(f"    第一阶段总成本: {first_stage_cost:.2f}")

        # 4. 储物柜资源配置分析
        print(f"  储物柜资源配置:")
        for j in open_lockers:
            capacity_j = self.Q_locker_capacity[j]
            drone_count = n_solution.get(j, 0)
            available_hours = drone_count * self.H_drone_working_hours_per_day

            # 距离约束检查
            reachable_customers = []
            unreachable_customers = []
            for i in self.customers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        reachable_customers.append(i)
                    else:
                        unreachable_customers.append(i)

            print(f"    储物柜{j}: 容量{capacity_j}, 无人机{drone_count}架, 可用时间{available_hours:.1f}h")
            print(f"      可达客户: {len(reachable_customers)}/{len(self.customers)}")
            if unreachable_customers:
                print(f"      不可达客户: {unreachable_customers}")

        print(f"  注意: 在修正的两阶段模型中，客户分配在第二阶段根据实际需求动态优化")
    def _analyze_truck_cost_details(self, y_solution, n_solution, validation_samples, replication, scenario_x_qty_solutions):
        """
        详细分析卡车成本的构成，帮助理解为什么DRL成本高于Gurobi
        """
        print(f"  === 复制 {replication} 卡车成本详细分析 ===")

        # 统计开放的储物柜
        open_lockers = []
        for site in self.sites:
            if y_solution.get(site, 0) > 0.5:  # 储物柜开放
                open_lockers.append(site)

        print(f"  开放储物柜数量: {len(open_lockers)}")
        print(f"  开放储物柜ID: {open_lockers}")

        # 分析前几个验证场景的详细情况
        sample_scenarios = validation_samples[:5]  # 分析前5个场景
        sample_x_solutions = scenario_x_qty_solutions[:5]  # 对应的客户分配解
        total_trucks_needed = 0
        total_distance = 0

        for i, (scenario, x_qty_sol) in enumerate(zip(sample_scenarios, sample_x_solutions)):
            print(f"  --- 场景 {i+1} 详细分析 ---")

            # 计算该场景下各储物柜的需求
            active_lockers_info = {}
            total_demand = 0

            for site in open_lockers:
                site_coord = self.site_coords.get(site, (0, 0))
                site_demand = 0

                # 使用正确的客户分配变量计算需求
                for customer in self.customers:
                    customer_allocation = x_qty_sol.get((customer, site), 0)
                    if customer_allocation > 0:
                        site_demand += customer_allocation

                if site_demand > 0:
                    active_lockers_info[site] = {
                        'coord': site_coord,
                        'demand': site_demand
                    }
                    total_demand += site_demand

            print(f"    总需求: {total_demand:.1f}")
            print(f"    活跃储物柜: {len(active_lockers_info)}")

            # 使用DRL计算该场景的卡车成本
            if active_lockers_info:
                try:
                    drl_solver = self._get_drl_solver(make_plots=False)
                    truck_cost, route_info = drl_solver.solve(active_lockers_info, return_route_info=True)

                    if route_info:
                        num_trucks = route_info.get('num_trucks', 0)
                        distance = route_info.get('total_distance', 0)
                        fixed_cost = route_info.get('fixed_cost', 0)
                        distance_cost = route_info.get('distance_cost', 0)

                        print(f"    DRL结果: {num_trucks}辆卡车, {distance:.1f}km, 成本{truck_cost:.1f}")
                        print(f"    成本构成: 固定{fixed_cost:.1f} + 距离{distance_cost:.1f}")

                        total_trucks_needed += num_trucks
                        total_distance += distance

                        # 显示路线详情
                        routes = route_info.get('routes', [])
                        for route in routes:
                            lockers = route.get('lockers', [])
                            route_demand = route.get('total_demand', 0)
                            utilization = (route_demand / self.truck_capacity) * 100 if self.truck_capacity > 0 else 0
                            print(f"      卡车{route.get('vehicle_id', 0)}: 储物柜{lockers}, 需求{route_demand:.1f}, 利用率{utilization:.1f}%")
                    else:
                        print(f"    DRL求解失败，成本: {truck_cost:.1f}")

                except Exception as e:
                    print(f"    DRL求解出错: {str(e)}")
            else:
                print(f"    无活跃储物柜")

        # 统计信息
        avg_trucks_per_scenario = total_trucks_needed / len(sample_scenarios) if sample_scenarios else 0
        avg_distance_per_scenario = total_distance / len(sample_scenarios) if sample_scenarios else 0

        print(f"  样本统计 (前{len(sample_scenarios)}个场景):")
        print(f"    平均每场景卡车数: {avg_trucks_per_scenario:.2f}")
        print(f"    平均每场景距离: {avg_distance_per_scenario:.1f}km")
        print(f"    卡车容量设置: {self.truck_capacity}")
        print(f"    固定成本: {self.truck_fixed_cost}, 每公里成本: {self.truck_km_cost}")
        print(f"  ================================")

    def calculate_truck_cost_batch(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        批量计算卡车成本 - 智能分组批量求解

        将具有相同储物柜配置的场景分组进行批量求解，
        以最大化DRL批量求解的效率，减少"different locker IDs"警告。

        Args:
            batch_active_lockers_info: 批量活跃储物柜信息列表

        Returns:
            卡车成本列表
        """
        if not DRL_AVAILABLE:
            print("  DRL不可用，使用简化估算计算批量卡车成本")
            return self._calculate_simplified_batch_costs(batch_active_lockers_info)

        if not batch_active_lockers_info:
            return []

        try:
            # 智能分组：按储物柜配置分组
            groups = self._group_scenarios_by_locker_config(batch_active_lockers_info)

            if hasattr(self, '_current_replication') and self._current_replication == 1:
                print(f"  将 {len(batch_active_lockers_info)} 个场景分为 {len(groups)} 组进行批量求解")

            # 初始化结果列表
            batch_costs = [0.0] * len(batch_active_lockers_info)
            drl_solver = self._get_drl_solver(make_plots=False)

            # 对每组进行批量求解
            for group_idx, (locker_ids, scenarios) in enumerate(groups.items()):
                scenario_indices = [idx for idx, _ in scenarios]
                scenario_data = [data for _, data in scenarios]

                if len(scenario_data) > 1:
                    # 批量求解
                    group_costs = drl_solver.solve_batch(scenario_data, return_route_info=False)
                else:
                    # 单个求解
                    group_costs = [drl_solver.solve(scenario_data[0], return_route_info=False)]

                # 将结果放回原始位置
                for i, cost in enumerate(group_costs):
                    batch_costs[scenario_indices[i]] = cost

            if hasattr(self, '_current_replication') and self._current_replication == 1:
                print(f"  分组批量求解成功，平均卡车成本: {sum(batch_costs)/len(batch_costs):.2f}")
            return batch_costs
        except Exception as e:
            print(f"  DRL批量求解失败: {str(e)}，回退到逐个求解")
            return self._fallback_individual_solving(batch_active_lockers_info)

    def _group_scenarios_by_locker_config(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]):
        """
        按储物柜配置分组场景，相同配置的场景可以批量求解

        Returns:
            Dict: 键为储物柜ID元组，值为(场景索引, 场景数据)的列表
        """
        groups = {}

        for idx, scenario in enumerate(batch_active_lockers_info):
            # 生成储物柜配置的键（储物柜ID和坐标的组合）
            locker_config_key = tuple(sorted(
                (locker_id, tuple(info['coord']))
                for locker_id, info in scenario.items()
            ))

            if locker_config_key not in groups:
                groups[locker_config_key] = []
            groups[locker_config_key].append((idx, scenario))

        return groups




    def _calculate_simplified_batch_costs(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        简化估算批量成本计算
        """
        batch_costs = []
        for active_lockers_info in batch_active_lockers_info:
            batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _calculate_simplified_cost(self, active_lockers_info: Dict[int, Dict[str, Any]]) -> float:
        """
        单个场景的简化成本估算
        """
        if not active_lockers_info:
            return 0.0

        total_demand = sum(info['demand'] for info in active_lockers_info.values())
        num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
        return num_trucks * self.truck_fixed_cost

    def _fallback_individual_solving(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        回退到逐个求解的方法
        """
        batch_costs = []
        for i, active_lockers_info in enumerate(batch_active_lockers_info):
            try:
                cost = self.calculate_truck_cost([], {}, make_plots=False, active_lockers_info_override=active_lockers_info)
                batch_costs.append(cost)
            except Exception as e2:
                print(f"  场景 {i+1} 求解失败: {str(e2)}，使用简化估算")
                batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _check_saa_termination_criteria(self):
        """
        检查SAA终止条件
        返回: (should_terminate: bool, gap_info: str)
        """
        import scipy.stats as stats

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 至少需要2次有效复制才能计算统计量
        if len(valid_obj_k) < 2 or len(valid_ub_k_prime) < 2:
            return False, f"有效解数量不足 ({len(valid_obj_k)}, {len(valid_ub_k_prime)} < 2)，需要至少2次有效复制"

        # 找到当前最佳解的信息
        best_validation_cost = float('inf')
        for i, ub_val in enumerate(self.saa_upper_bounds_k_prime):
            if ub_val != float('inf') and ub_val < best_validation_cost:
                best_validation_cost = ub_val

        # 计算正确的SAA统计量
        m = len(valid_obj_k)  # 当前迭代次数
        statistical_lower_bound = np.mean(valid_obj_k)  # cost_N^m = (1/m) * Σ cost_N^{m'}
        statistical_upper_bound = best_validation_cost  # UB_{N'}(ŝ) (最佳解的验证成本)

        # SAA Gap计算 (按正确的论文公式)
        saa_gap = statistical_upper_bound - statistical_lower_bound
        gap_percent = saa_gap / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 计算下界方差：δ²(cost_N) = (1/[m(m-1)]) * Σ(cost_N^{m'} - cost_N^m)²
        if m > 1:
            variance_sum = sum((cost_val - statistical_lower_bound)**2 for cost_val in valid_obj_k)
            var_lower_bound = variance_sum / (m * (m - 1))
            std_lower_bound = np.sqrt(var_lower_bound)
        else:
            var_lower_bound = 0
            std_lower_bound = 0

        # 计算上界方差：δ²_{N'}(ŝ) = (1/[N'(N'-1)]) * Σ(Q(ŝ,ξ_n) - cost_{N'}(ŝ))²
        # 这里简化处理，假设每个复制的验证样本数量相同
        N_prime = SAA_SAMPLES_K_PRIME
        if len(valid_ub_k_prime) > 1:
            # 使用最佳解对应的上界值计算方差
            best_ub_index = valid_ub_k_prime.index(best_validation_cost)
            # 简化计算：使用所有上界值的方差除以验证样本数量
            var_upper_bound = np.var(valid_ub_k_prime, ddof=1) / N_prime
        else:
            var_upper_bound = 0

        # Gap的总方差：δ²_Gap(ŝ) = δ²(cost_N) + δ²_{N'}(ŝ)
        gap_variance = var_lower_bound + var_upper_bound
        gap_std = np.sqrt(gap_variance) if gap_variance > 0 else 0
        variance_percent = gap_variance / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 置信区间计算 (使用t分布)
        n_replications = len(valid_obj_k)
        if n_replications > 1:
            t_critical = stats.t.ppf(1 - SAA_CONFIDENCE_LEVEL_ALPHA/2, n_replications - 1)
            # 下界置信区间
            lb_margin = t_critical * std_lower_bound / np.sqrt(n_replications) if std_lower_bound > 0 else 0
        else:
            lb_margin = 0

        # SAA终止条件检查 (必须同时满足两个条件)
        # 条件1: 相对差距阈值 Gap_{N,N'}(ŝ) / cost_{N'}(ŝ) ≤ ε (如 ε = 3%)
        relative_gap_condition = (gap_percent <= SAA_GAP_TOLERANCE_PERCENT and saa_gap >= 0)

        # 条件2: 方差阈值 δ²_Gap(ŝ) / cost_{N'}(ŝ) ≤ ε' (如 ε' = 5%)
        variance_condition = variance_percent <= SAA_VARIANCE_TOLERANCE_PERCENT

        # 主要终止条件：必须同时满足相对差距阈值和方差阈值
        should_terminate = relative_gap_condition and variance_condition

        # 如果gap为负，说明统计估计不稳定，不应该终止
        if saa_gap < 0:
            should_terminate = False

        # 计算下界变异系数
        cv_lower_bound = std_lower_bound / statistical_lower_bound if statistical_lower_bound > 0 else float('inf')

        # 构建详细的状态信息
        gap_condition_status = "✓" if relative_gap_condition else "✗"
        variance_condition_status = "✓" if variance_condition else "✗"

        gap_info = (f"相对差距: {gap_percent:.2%} {gap_condition_status} (阈值: ≤{SAA_GAP_TOLERANCE_PERCENT:.0%}), "
                   f"方差比例: {variance_percent:.2%} {variance_condition_status} (阈值: ≤{SAA_VARIANCE_TOLERANCE_PERCENT:.0%}), "
                   f"LB(m={m}): {statistical_lower_bound:.2f}, "
                   f"UB: {statistical_upper_bound:.2f}, "
                   f"Gap: {saa_gap:.2f}, "
                   f"δ²_Gap: {gap_variance:.4f}")

        return should_terminate, gap_info

    def _get_gurobi_status_message(self, status):
        """获取Gurobi求解状态的详细说明"""
        status_messages = {
            2: "最优解 (OPTIMAL)",
            3: "不可行 (INFEASIBLE)",
            4: "不可行或无界 (INF_OR_UNBD)",
            5: "无界 (UNBOUNDED)",
            6: "目标值超出截断值 (CUTOFF)",
            7: "达到迭代限制 (ITERATION_LIMIT)",
            8: "达到节点限制 (NODE_LIMIT)",
            9: "达到时间限制 (TIME_LIMIT)",
            10: "达到解数量限制 (SOLUTION_LIMIT)",
            11: "被中断 (INTERRUPTED)",
            12: "数值困难 (NUMERIC)",
            13: "次优解 (SUBOPTIMAL)",
            14: "求解中 (INPROGRESS)",
            15: "达到用户目标限制 (USER_OBJ_LIMIT)"
        }
        return status_messages.get(status, f"未知状态({status})")

    def _print_saa_solution(self, saa_solution_dict: Dict):
        if not saa_solution_dict:
            print("SAA 未找到有效解。")
            return

        print("\n" + "=" * 60 + "\nSAA 优化结果\n" + "=" * 60)
        print(f"  最终评估目标值 (在 {SAA_SAMPLES_K_PRIME} 个样本上): {saa_solution_dict.get('objective_value_k_prime_estimate', 'N/A'):.2f}")
        print(f"  估计的卡车成本 (在 {SAA_SAMPLES_K_PRIME} 个样本上): {saa_solution_dict.get('truck_cost_k_prime_estimate', 'N/A'):.2f}")

        # 添加详细的成本构成分析
        self._print_detailed_cost_breakdown(saa_solution_dict)

        y_star_final = saa_solution_dict.get('selected_lockers_y', {})
        n_star_final = saa_solution_dict.get('drone_allocations_n', {})

        selected_lockers_print = [j for j, val in y_star_final.items() if val > 0.5]
        print(f"  选定的储物柜站点 (y*): {selected_lockers_print}")
        print(f"  各站点无人机分配 (n*):")
        for j_site_p in selected_lockers_print:
            print(f"    位置 {j_site_p}: 配备 {round(n_star_final.get(j_site_p,0))} 架无人机")

        print("\n  修正的两阶段模型说明:")
        print("  第一阶段决策：储物柜选址和无人机配置（已确定）")
        print("  第二阶段决策：根据实际需求场景动态优化客户分配和配送")
        print("  这样可以更好地应对需求不确定性，避免过度保守的分配策略。")

        # 简化的汇总统计（在修正模型中，客户分配在第二阶段动态决定）
        customer_assignment_quantities_final_print = {i: {} for i in self.customers}
        customer_assignments_primary_final_print = {i: [] for i in self.customers}
        unassigned_customers_final_print = list(self.customers)  # 在修正模型中，所有客户都在第二阶段分配

        print(f"\n  分配汇总:")
        print(f"    储物柜数量: {len(selected_lockers_print)}")
        print(f"    无人机总数: {sum(round(n_star_final.get(j,0)) for j in selected_lockers_print)}")
        print(f"    总期望需求量: {sum(self.expected_demand.values()):.2f}")
        print(f"    注意: 客户分配将在第二阶段根据实际需求动态优化")

        if DRL_AVAILABLE:
            viz_solution_saa = {
                'selected_lockers': {j:True for j in selected_lockers_print},
                'customer_assignments_primary': {},  # 空的，因为客户分配在第二阶段
                'unassigned_customers_by_u': list(self.customers),  # 所有客户都在第二阶段分配
                'drone_allocations': {j: round(n_star_final.get(j,0)) for j in selected_lockers_print},
                'objective_value': saa_solution_dict.get('objective_value_k_prime_estimate', 0.0)
            }
            print("\n  显示SAA最终解的可视化:")
            print("  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）")
            print("  ↳ 客户分配将在第二阶段根据实际需求场景动态优化")
            print("  ↳ 上述成本是考虑需求不确定性后的期望值")
            self.visualize_solution(viz_solution_saa)

    def visualize_solution(self, solution: Dict):
        if not DRL_AVAILABLE: return
        if not solution: print("无解决方案可供可视化。"); return
        temp_solution_for_viz = solution.copy()
        primary_assignments = solution.get('customer_assignments_primary', {})

        # 修改：支持多储物柜分配的可视化
        # 直接传递完整的储物柜列表给可视化函数
        customer_assignments_viz = {}
        for cust, lockers in primary_assignments.items():
            if lockers: # 如果有分配的储物柜
                # 直接传递储物柜列表，visualization.py会自动处理多储物柜连线
                customer_assignments_viz[cust] = lockers

        temp_solution_for_viz['customer_assignments'] = customer_assignments_viz


        customer_service_modes = {}
        for customer_id in customer_assignments_viz: # 使用调整后的键
            customer_service_modes[customer_id] = 0
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # unassigned_customers 可能需要从 solution['unassigned_customers_by_u'] 获取
        if 'unassigned_customers_by_u' in solution:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']


        # 移除可能引起混淆的旧键
        if 'customer_assignments_primary' in temp_solution_for_viz: del temp_solution_for_viz['customer_assignments_primary']
        if 'unassigned_customers_by_u' in temp_solution_for_viz and 'unassigned_customers' in temp_solution_for_viz :
             if temp_solution_for_viz['unassigned_customers_by_u'] == temp_solution_for_viz['unassigned_customers']:
                 del temp_solution_for_viz['unassigned_customers_by_u']


        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="SAA 无人机配送网络规划 (基于期望需求分配)"
        )
        if plt_fig:
            import matplotlib.pyplot as plt
            plt.show(block=False) # 使用 block=False 避免阻塞后续代码，如果需要交互则去掉
            plt.pause(1) # 暂停一下，确保图像显示

# --- 主程序入口 ---
if __name__ == "__main__":
    start_time_main = time.time()
    print(f"设置全局随机种子: {RANDOM_SEED}")
    if DRL_AVAILABLE:
        set_drl_log_level(logging.WARNING)
        print("DRL日志级别已设置为WARNING")
    else:
        print("DRL模块不可用，相关功能将跳过。")


    print("\n创建随机需求的示例数据 (使用期望需求)...")
    print("=" * 60)
    print("[saa_g_r.py] 成本计算方法改进：统一时间单位")
    print("=" * 60)
    print("修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用")
    print("修正后方案：使用资本回收因子将所有固定成本统一为日成本单位")
    print("优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差")
    print("=" * 60)

    stochastic_data_instance = create_deterministic_example_instance(
        demand_level="low",
        locker_cost_level="medium",
        drone_cost_level="medium",
        drone_transport_cost_level="medium",
        use_generated_distances=True,
        num_customers=20, # 与3.py保持一致
        num_sites=3,    # 与3.py保持一致
        use_kmeans_clustering=True,
        random_seed=RANDOM_SEED,
        # 年化成本参数
        annual_interest_rate=0.04,    # 4% 年利率
        equipment_life_years=10,      # 10年设备生命周期
        operating_days_per_year=365   # 365天年运营天数
    )
    stochastic_data_instance['expected_demand'] = stochastic_data_instance.pop('demand_deterministic')
    print("随机需求数据 (期望值) 已创建。")

    print("\n所有客户期望需求 (λᵢ_bar):")
    for customer_id_main, demand_val_main in stochastic_data_instance['expected_demand'].items():
        print(f"  客户 {customer_id_main}: {demand_val_main} 订单/天 (期望)")
    total_expected_demand_val_main = sum(stochastic_data_instance['expected_demand'].values())
    print(f"总期望需求: {total_expected_demand_val_main} 订单/天")

    # 调试：显示关键距离信息
    print(f"\n调试信息 - 距离矩阵样本:")
    for i in [1, 2, 3]:
        for j in [1, 2, 3, 4]:
            if (i, j) in stochastic_data_instance['distance_matrix']:
                dist = stochastic_data_instance['distance_matrix'][i, j]
                print(f"  客户{i}到储物柜{j}: {dist:.2f}km")

    # 调试：显示储物柜坐标
    print(f"\n调试信息 - 储物柜坐标:")
    for j, coord in stochastic_data_instance['site_coords'].items():
        print(f"  储物柜{j}: {coord}")

    # 调试：检查飞行距离限制
    print(f"\n调试信息 - 飞行距离约束检查:")
    max_flight_dist = stochastic_data_instance['max_flight_distance']
    print(f"  最大飞行距离: {max_flight_dist}km")
    reachable_count = 0
    total_pairs = 0
    for i in range(1, 21):
        for j in range(1, 5):
            if (i, j) in stochastic_data_instance['distance_matrix']:
                flight_dist = 2 * stochastic_data_instance['distance_matrix'][i, j]
                total_pairs += 1
                if flight_dist <= max_flight_dist:
                    reachable_count += 1
    print(f"  可达客户-储物柜对: {reachable_count}/{total_pairs} ({reachable_count/total_pairs*100:.1f}%)")


    print("\n" + "=" * 60 + "\n求解带随机需求的无人机配送网络设计问题 (SAA)\n" + "=" * 60)
    solve_start_time_saa_main = time.time()

    optimizer_saa_main = StochasticDroneDeliveryOptimizerSAA()
    optimizer_saa_main.set_parameters(**stochastic_data_instance)

    final_saa_solution = optimizer_saa_main.solve_saa(
        time_limit_per_replication=120,  # 增加时间限制以提高求解质量
        mip_gap_per_replication=0.02     # 降低MIP Gap以提高精度
    )

    solve_time_saa_main = time.time() - solve_start_time_saa_main

    if final_saa_solution:
        optimizer_saa_main._print_saa_solution(final_saa_solution)
        print(f"\nSAA模型求解总耗时: {solve_time_saa_main:.2f} 秒")
    else:
        print("\n⚠ SAA未能找到可行解或优化失败。")

    total_time_main = time.time() - start_time_main
    print(f"\n总运行时间: {total_time_main:.2f} 秒")
    if DRL_AVAILABLE:
        print("测试完成。如果图像窗口仍然打开，请手动关闭。")