#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Gurobi验证ALNS解：固定ALNS的储物柜配置，用Gurobi精确求解第二阶段

这个脚本使用Gurobi精确求解器来验证ALNS解的质量，提供可信的对比基准
"""

import random
import numpy as np
import sys
import os
import gurobipy as gp
from gurobipy import GRB
import time

# 设置随机种子 - 与g_i.py保持一致
RANDOM_SEED = 616  # 使用与原始实验相同的种子
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

def calculate_cost_components():
    """
    计算成本组件，使用与g_i.py和alns.py完全相同的参数
    """
    # 年化成本计算参数（与原始代码完全一致）
    annual_interest_rate = 0.04
    equipment_life_years = 10
    operating_days_per_year = 365
    
    # 计算资本回收因子
    IR = annual_interest_rate
    T_life = equipment_life_years
    capital_recovery_factor = (IR * (1 + IR)**T_life) / ((1 + IR)**T_life - 1)
    
    # 储物柜成本
    locker_initial_cost = 15000  # medium level
    locker_annual_cost = locker_initial_cost * capital_recovery_factor
    locker_daily_cost = locker_annual_cost / operating_days_per_year
    
    # 无人机成本
    drone_initial_cost = 4000  # medium level
    drone_annual_cost = drone_initial_cost * capital_recovery_factor
    drone_daily_cost = drone_annual_cost / operating_days_per_year
    
    # 其他成本参数
    transport_unit_cost = 0.02  # medium level
    truck_fixed_cost = 100
    truck_km_cost = 0.5
    penalty_cost_unassigned = 40.0
    
    return {
        'locker_daily_cost': locker_daily_cost,
        'drone_daily_cost': drone_daily_cost,
        'transport_unit_cost': transport_unit_cost,
        'truck_fixed_cost': truck_fixed_cost,
        'truck_km_cost': truck_km_cost,
        'penalty_cost_unassigned': penalty_cost_unassigned,
        'capital_recovery_factor': capital_recovery_factor
    }

def generate_test_coordinates():
    """
    生成与原始实验相同的坐标（使用相同随机种子和范围）
    """
    local_random = random.Random(RANDOM_SEED)

    # 客户坐标 - 与g_i.py保持一致的范围
    customer_coords = {}
    for i in range(1, 16):  # 15个客户
        customer_coords[i] = (
            local_random.uniform(0, 15),  # 修正：与g_i.py一致使用0-15范围
            local_random.uniform(0, 15)
        )

    # 储物柜站点坐标 - 与g_i.py保持一致的范围
    site_coords = {}
    for j in range(1, 5):  # 4个候选站点
        site_coords[j] = (
            local_random.uniform(3, 12),
            local_random.uniform(3, 12)
        )

    return customer_coords, site_coords

def calculate_distance(coord1, coord2):
    """计算欧几里得距离"""
    return np.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)

def generate_demand_scenarios(num_scenarios, seed_offset=0):
    """
    生成多个需求场景（使用与g_i.py完全相同的参数和方法）
    """
    scenarios = []

    # 使用与g_i.py相同的需求生成方法
    # g_i.py使用demand_level="low"，对应(2,4)范围的随机整数作为期望需求
    local_random = random.Random(RANDOM_SEED)

    # 生成期望需求（与g_i.py的create_integrated_saa_example_instance一致）
    expected_demands = {}
    for customer in range(1, 16):
        expected_demands[customer] = local_random.randint(2, 4)  # demand_level="low"对应(2,4)

    for s in range(num_scenarios):
        # 使用numpy生成泊松分布
        np.random.seed(RANDOM_SEED + seed_offset + s)
        demand_scenario = {}

        for customer in range(1, 16):
            lambda_val = expected_demands[customer]
            demand_scenario[customer] = max(0, np.random.poisson(lambda_val))

        scenarios.append(demand_scenario)

    return scenarios

def solve_with_gurobi_fixed_config(selected_lockers, drone_allocation, cost_params,
                                 customer_coords, site_coords, num_scenarios=40):
    """
    使用Gurobi求解器评估固定配置的成本

    Args:
        selected_lockers: 选择的储物柜列表
        drone_allocation: 无人机分配字典 {locker_id: drone_count}
        cost_params: 成本参数字典
        customer_coords: 客户坐标
        site_coords: 站点坐标
        num_scenarios: 场景数量

    Returns:
        dict: 详细的成本分解
    """
    print(f"  🔧 使用Gurobi评估配置: 储物柜{selected_lockers}, 无人机分配{drone_allocation}")

    # 第一阶段固定成本
    locker_fixed_cost = len(selected_lockers) * cost_params['locker_daily_cost']
    total_drones = sum(drone_allocation.values())
    drone_fixed_cost = total_drones * cost_params['drone_daily_cost']
    first_stage_cost = locker_fixed_cost + drone_fixed_cost

    print(f"    第一阶段成本: 储物柜{locker_fixed_cost:.2f} + 无人机{drone_fixed_cost:.2f} = {first_stage_cost:.2f}")

    # 生成需求场景
    scenarios = generate_demand_scenarios(num_scenarios)

    # 计算距离矩阵
    distances = {}
    for customer in range(1, 16):
        for locker in selected_lockers:
            dist = calculate_distance(customer_coords[customer], site_coords[locker])
            distances[(customer, locker)] = dist

    # 储物柜间距离（用于卡车成本）
    locker_distances = {}
    depot_coord = (0, 0)
    for locker in selected_lockers:
        locker_distances[(0, locker)] = calculate_distance(depot_coord, site_coords[locker])

    for i, locker1 in enumerate(selected_lockers):
        for j, locker2 in enumerate(selected_lockers):
            if i != j:
                dist = calculate_distance(site_coords[locker1], site_coords[locker2])
                locker_distances[(locker1, locker2)] = dist
    
    # 使用Gurobi精确求解第二阶段
    print(f"    🚀 开始Gurobi求解，场景数: {num_scenarios}")

    try:
        # 创建Gurobi模型
        model = gp.Model("Fixed_Config_Validation")
        model.setParam('OutputFlag', 0)  # 静默模式
        model.setParam('TimeLimit', 300)  # 5分钟时间限制
        model.setParam('MIPGap', 0.02)   # 2% MIP Gap

        # 参数定义
        customers = list(range(1, 16))
        lockers = selected_lockers
        scenarios_range = list(range(num_scenarios))

        # 储物柜容量
        locker_capacities = {}
        for locker in lockers:
            drone_count = drone_allocation.get(locker, 0)
            locker_capacities[locker] = min(30, drone_count * 15)  # 物理容量限制

        # 决策变量
        # x[i,j,s]: 场景s下客户i分配给储物柜j的需求量
        x = model.addVars(customers, lockers, scenarios_range,
                         vtype=GRB.CONTINUOUS, name="x")

        # u[i,s]: 场景s下客户i未满足的需求量
        u = model.addVars(customers, scenarios_range,
                         vtype=GRB.CONTINUOUS, name="u")

        # 卡车路径变量（简化为二进制变量表示是否访问储物柜）
        truck_visit = model.addVars(lockers, scenarios_range,
                                   vtype=GRB.BINARY, name="truck_visit")

        # 目标函数：最小化期望第二阶段成本
        transport_cost = gp.quicksum(
            2 * cost_params['transport_unit_cost'] * distances[(i, j)] * x[i, j, s]
            for i in customers for j in lockers for s in scenarios_range
        )

        penalty_cost = gp.quicksum(
            cost_params['penalty_cost_unassigned'] * u[i, s]
            for i in customers for s in scenarios_range
        )

        # 简化的卡车成本：固定成本 + 访问成本
        truck_cost = gp.quicksum(
            cost_params['truck_fixed_cost'] / num_scenarios +
            gp.quicksum(locker_distances.get((0, j), 5) * cost_params['truck_km_cost'] * truck_visit[j, s]
                       for j in lockers)
            for s in scenarios_range
        )

        model.setObjective((transport_cost + penalty_cost + truck_cost) / num_scenarios, GRB.MINIMIZE)

        # 约束条件
        for s in scenarios_range:
            scenario_demand = scenarios[s]

            # 需求满足约束
            for i in customers:
                model.addConstr(
                    gp.quicksum(x[i, j, s] for j in lockers) + u[i, s] == scenario_demand[i],
                    name=f"demand_{i}_{s}"
                )

            # 储物柜容量约束
            for j in lockers:
                model.addConstr(
                    gp.quicksum(x[i, j, s] for i in customers) <= locker_capacities[j],
                    name=f"capacity_{j}_{s}"
                )

            # 卡车访问逻辑
            for j in lockers:
                model.addConstr(
                    gp.quicksum(x[i, j, s] for i in customers) <=
                    locker_capacities[j] * truck_visit[j, s],
                    name=f"truck_visit_{j}_{s}"
                )

        # 求解
        start_time = time.time()
        model.optimize()
        solve_time = time.time() - start_time

        if model.status == GRB.OPTIMAL or model.status == GRB.TIME_LIMIT:
            obj_value = model.objVal

            # 计算成本分解
            total_transport = sum(
                2 * cost_params['transport_unit_cost'] * distances[(i, j)] * x[i, j, s].x
                for i in customers for j in lockers for s in scenarios_range
            ) / num_scenarios

            total_penalty = sum(
                cost_params['penalty_cost_unassigned'] * u[i, s].x
                for i in customers for s in scenarios_range
            ) / num_scenarios

            total_truck = cost_params['truck_fixed_cost'] + sum(
                locker_distances.get((0, j), 5) * cost_params['truck_km_cost'] * truck_visit[j, s].x
                for j in lockers for s in scenarios_range
            ) / num_scenarios

            second_stage_cost = obj_value
            total_cost = first_stage_cost + second_stage_cost

            print(f"    ✅ Gurobi求解成功，耗时: {solve_time:.1f}秒")
            print(f"    状态: {model.status}, 目标值: {obj_value:.2f}")

            return {
                'total_cost': total_cost,
                'first_stage_cost': first_stage_cost,
                'locker_fixed_cost': locker_fixed_cost,
                'drone_fixed_cost': drone_fixed_cost,
                'second_stage_cost': second_stage_cost,
                'transport_cost': total_transport,
                'penalty_cost': total_penalty,
                'truck_cost': total_truck,
                'solve_time': solve_time,
                'gurobi_status': model.status
            }
        else:
            print(f"    ❌ Gurobi求解失败，状态: {model.status}")
            return None

    except Exception as e:
        print(f"    ❌ Gurobi求解出错: {str(e)}")
        return None

def main():
    """主验证函数"""
    print("=" * 80)
    print("使用Gurobi验证ALNS解质量")
    print("=" * 80)

    # 1. 计算成本参数
    cost_params = calculate_cost_components()
    print(f"成本参数:")
    print(f"  储物柜日成本: {cost_params['locker_daily_cost']:.2f} 元/天")
    print(f"  无人机日成本: {cost_params['drone_daily_cost']:.2f} 元/天")
    print(f"  运输单位成本: {cost_params['transport_unit_cost']:.3f} 元/公里")
    print(f"  卡车固定成本: {cost_params['truck_fixed_cost']:.0f} 元/天")
    print(f"  惩罚成本: {cost_params['penalty_cost_unassigned']:.0f} 元/订单")

    # 2. 生成坐标
    customer_coords, site_coords = generate_test_coordinates()
    print(f"\n生成坐标: {len(customer_coords)}个客户, {len(site_coords)}个候选站点")

    # 3. 定义要比较的解
    solutions = {
        'ALNS解': {
            'lockers': [1, 2],
            'drones': {1: 1, 2: 2}
        },
        'g_i.py解': {
            'lockers': [2, 3, 4],
            'drones': {2: 1, 3: 1, 4: 1}
        }
    }

    # 4. 使用Gurobi评估每个解
    results = {}
    num_scenarios = 40  # 与原始SAA相同的场景数
    print(f"\n🚀 开始使用Gurobi评估解的质量（{num_scenarios}个场景）...")
    print("=" * 70)

    for solution_name, config in solutions.items():
        print(f"\n🔍 评估 {solution_name}:")

        result = solve_with_gurobi_fixed_config(
            config['lockers'],
            config['drones'],
            cost_params,
            customer_coords,
            site_coords,
            num_scenarios=num_scenarios
        )

        if result is not None:
            results[solution_name] = result

            print(f"    📊 总成本: {result['total_cost']:.2f} 元/天")
            print(f"    - 储物柜固定: {result['locker_fixed_cost']:.2f} 元/天")
            print(f"    - 无人机固定: {result['drone_fixed_cost']:.2f} 元/天")
            print(f"    - 运输成本: {result['transport_cost']:.2f} 元/天")
            print(f"    - 惩罚成本: {result['penalty_cost']:.2f} 元/天")
            print(f"    - 卡车成本: {result['truck_cost']:.2f} 元/天")
            print(f"    ⏱️ 求解时间: {result['solve_time']:.1f} 秒")
        else:
            print(f"    ❌ {solution_name} 求解失败")

    # 5. 对比分析
    if len(results) == 2:
        print(f"\n📊 Gurobi精确求解对比分析:")
        print("=" * 70)

        alns_cost = results['ALNS解']['total_cost']
        gurobi_cost = results['g_i.py解']['total_cost']
        improvement = gurobi_cost - alns_cost
        improvement_pct = (improvement / gurobi_cost) * 100

        print(f"ALNS解总成本:   {alns_cost:.2f} 元/天")
        print(f"g_i.py解总成本: {gurobi_cost:.2f} 元/天")
        print(f"成本差异:       {improvement:+.2f} 元/天 ({improvement_pct:+.2f}%)")

        if improvement > 1:
            print("✅ ALNS找到了更好的解！")
        elif improvement < -1:
            print("❌ g_i.py的解更好")
        else:
            print("➡️ 两个解质量相当")

        # 详细成本对比
        print(f"\n📋 详细成本对比:")
        print("=" * 70)
        print(f"{'成本项目':<15} {'ALNS解':<12} {'g_i.py解':<12} {'差异':<12}")
        print("-" * 55)

        cost_items = [
            ('储物柜固定', 'locker_fixed_cost'),
            ('无人机固定', 'drone_fixed_cost'),
            ('运输成本', 'transport_cost'),
            ('惩罚成本', 'penalty_cost'),
            ('卡车成本', 'truck_cost'),
            ('总成本', 'total_cost')
        ]

        for item_name, item_key in cost_items:
            alns_val = results['ALNS解'][item_key]
            gurobi_val = results['g_i.py解'][item_key]
            diff = alns_val - gurobi_val
            print(f"{item_name:<15} {alns_val:<12.2f} {gurobi_val:<12.2f} {diff:+<12.2f}")

    # 6. 与原始报告的成本对比
    print(f"\n📋 与原始报告对比:")
    print("=" * 70)

    original_alns_cost = 135.30
    original_gurobi_cost = 143.58

    if 'ALNS解' in results:
        gurobi_verified_alns = results['ALNS解']['total_cost']
        print(f"原始ALNS报告:     {original_alns_cost:.2f} 元/天")
        print(f"Gurobi验证ALNS:   {gurobi_verified_alns:.2f} 元/天")
        print(f"验证差异:         {gurobi_verified_alns - original_alns_cost:+.2f} 元/天")
        print()

    if 'g_i.py解' in results:
        gurobi_verified_gurobi = results['g_i.py解']['total_cost']
        print(f"原始g_i.py报告:   {original_gurobi_cost:.2f} 元/天")
        print(f"Gurobi验证g_i.py: {gurobi_verified_gurobi:.2f} 元/天")
        print(f"验证差异:         {gurobi_verified_gurobi - original_gurobi_cost:+.2f} 元/天")

    print(f"\n🎯 结论:")
    print("=" * 70)
    print("本验证使用Gurobi精确求解器，固定储物柜配置，优化第二阶段决策")
    print("提供了更可信的解质量对比基准")

    if 'ALNS解' in results and 'g_i.py解' in results:
        if results['ALNS解']['total_cost'] < results['g_i.py解']['total_cost']:
            print("✅ 验证确认：ALNS解确实优于g_i.py解")
        else:
            print("❌ 验证结果：g_i.py解实际更优")

    print("\n💡 注意：验证结果可能与原始报告有差异，原因包括：")
    print("  - 不同的随机种子和场景生成")
    print("  - 简化的模型假设")
    print("  - 不同的求解参数设置")

if __name__ == "__main__":
    main()
