D:\download\Anaconda\envs\pytorch\python.exe E:\代码\self\对比\gurobi_SAA\alns.py 
✅ SciPy可用，将使用稀疏矩阵优化内存使用
⚠️ memory-profiler不可用，建议安装: pip install memory-profiler
✅ Numba可用，但已简化为统一Python实现
[内存监控] 开始监控，检查间隔: 10秒
内存监控已启动
设置全局随机种子: 620
DRL日志级别已设置为ERROR（屏蔽WARNING信息）

创建随机需求的示例数据 (使用期望需求)...
============================================================
成本计算方法改进：统一时间单位
============================================================
修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用
修正后方案：使用资本回收因子将所有固定成本统一为日成本单位
优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差
============================================================
  成本计算参数:
    年利率 (IR): 4.0%
    设备生命周期 (T_life): 10年
    资本回收因子: 0.123291
    年运营天数: 365天
  储物柜成本转换:
    初始建设成本: 15,000元
    年化成本: 1,849.36元/年
    日均固定成本: 5.07元/天
  无人机成本转换:
    初始采购成本: 4,000元
    年化成本: 493.16元/年
    日均固定成本: 1.35元/天

  成本单位统一性检查:
    储物柜固定成本: 5.07 元/天
    无人机固定成本: 1.35 元/天
    无人机运输成本: 0.020 元/公里 (按实际运输量)
    卡车固定成本: 100 元/天
    卡车运输成本: 0.5 元/公里 (按实际运输量)
    ✓ 所有固定成本已统一为日成本单位
    ✓ 运输成本保持按实际使用量计费
随机需求数据 (期望值) 已创建。

所有客户期望需求 (λᵢ_bar):
  客户 1: 2 订单/天 (期望)
  客户 2: 3 订单/天 (期望)
  客户 3: 2 订单/天 (期望)
  客户 4: 2 订单/天 (期望)
  客户 5: 4 订单/天 (期望)
  客户 6: 3 订单/天 (期望)
  客户 7: 4 订单/天 (期望)
  客户 8: 2 订单/天 (期望)
  客户 9: 3 订单/天 (期望)
  客户 10: 4 订单/天 (期望)
  客户 11: 4 订单/天 (期望)
  客户 12: 2 订单/天 (期望)
  客户 13: 4 订单/天 (期望)
  客户 14: 3 订单/天 (期望)
  客户 15: 3 订单/天 (期望)
总期望需求: 45 订单/天

============================================================
求解带随机需求的无人机配送网络设计问题 (SAA)
============================================================
使用ALNS方法求解SAA问题...

开始SAA优化（使用ALNS），最多 10 次复制...
SAA终止条件 (必须同时满足):
  1. 相对差距阈值: Gap/UB ≤ 3% 且 Gap ≥ 0
  2. 方差阈值: δ²_Gap/UB ≤ 5%
  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)
  最少需要 2 次有效复制
生成 2000 个固定验证样本 (所有复制共用)...

--- SAA 复制 1/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 1...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 45.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 140.95
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [2, 4]
    策略2成功，目标值: 149.23
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [3, 1, 4]
    策略3成功，目标值: 149.06
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 161.83
  初始解目标值: 149.23 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 drone_reduction_removal -> smart_drone_tuner
[严重警告] 进程内存使用过高: 3639.9 MB - 建议立即清理
    [精确评估 #1] 方案[2, 4]: 352.49元/天
  迭代 2: 找到历史最优解，目标值: 147.56
    [精确评估 #2] 方案[1, 4]: 197.35元/天
    [精确评估 #3] 方案[1, 2]: 185.94元/天
      [精确移除] 移除储物柜4, 成本增加: 38.38元/天
      [精确移除] 移除储物柜4, 成本增加: 38.38元/天
  迭代 4: 找到历史最优解，目标值: 143.65
      [精确插入] 添加储物柜2(1架无人机), 改进: 58.75元/天
      [精确移除] 移除储物柜3, 成本增加: 42.29元/天
      [精确移除] 移除储物柜4, 成本增加: -6.02元/天
      [精确移除] 移除储物柜3, 成本增加: 42.29元/天
      [精确移除] 移除储物柜3, 成本增加: 42.29元/天
  重启 1/2
      [精确移除] 移除储物柜4, 成本增加: 32.02元/天
      [精确移除] 移除储物柜3, 成本增加: 42.29元/天
      [精确移除] 移除储物柜1, 成本增加: 539.06元/天
      [精确移除] 移除储物柜3, 成本增加: 42.29元/天
  重启 2/2
      [精确移除] 移除储物柜3, 成本增加: 42.29元/天
      [精确移除] 移除储物柜4, 成本增加: -6.02元/天
      [精确移除] 移除储物柜1, 成本增加: 539.06元/天
      [精确移除] 移除储物柜1, 成本增加: 539.06元/天
    迭代 50: 执行 random_locker_removal -> drone_optimization
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 24.72秒
  总迭代次数: 52
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 5/9 次改进 (55.6%)
    N1_Drone: 0/9 次改进 (0.0%)
    N2_AddDrop: 5/9 次改进 (55.6%)
[严重警告] 进程内存使用过高: 3695.5 MB - 建议立即清理
  最终目标值: 159.62
  精确评估次数: 9
  复制 1 在 40 个样本上的目标值: 143.65
  评估复制 1 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.50秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.50秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.30秒
    步骤3: 使用DRL批量计算卡车成本...
  将 2000 个场景分为 1 组进行批量求解
  分组批量求解成功，平均卡车成本: 115.38
    DRL批量卡车成本计算完成，耗时: 6.18秒
  验证阶段批量求解总耗时: 6.97秒
  平均每场景: 3.5毫秒
  复制 1 在 2000 个样本上的平均目标值 (UB估计): 143.50
  复制 1 在 2000 个样本上的平均卡车成本: 115.38
  时间分析: ALNS求解 30.05s, 验证评估 7.03s, 总计 37.08s

--- SAA 复制 2/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 2...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 45.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 140.95
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [3, 2]
    策略2成功，目标值: 144.23
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [1, 2, 4, 3]
    策略3成功，目标值: 151.02
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 151.61
  初始解目标值: 140.95 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 drastic_reduction_removal -> exact_cost_insertion
    [精确评估 #1] 方案[1, 2, 3]: 159.62元/天
    [精确评估 #2] 方案[1, 2, 3, 4]: 152.37元/天
      [精确插入] 添加储物柜4(1架无人机), 改进: 7.25元/天
    [精确评估 #3] 方案[1]: 756.42元/天
      [精确插入] 添加储物柜4(1架无人机), 改进: 557.72元/天
[严重警告] 进程内存使用过高: 3702.3 MB - 建议立即清理
    [精确评估 #1] 方案[1, 2, 4]: 168.16元/天
[严重警告] 进程内存使用过高: 3705.0 MB - 建议立即清理
      [精确移除] 移除储物柜2, 成本增加: 30.53元/天
      [精确移除] 移除储物柜3, 成本增加: 55.04元/天
      [精确插入] 添加储物柜1(1架无人机), 改进: 924.61元/天
      [精确移除] 移除储物柜3, 成本增加: 55.04元/天
      [精确移除] 移除储物柜3, 成本增加: 55.04元/天
      [精确移除] 移除储物柜2, 成本增加: -8.72元/天
  重启 1/2
      [精确移除] 移除储物柜4, 成本增加: 557.72元/天
      [精确移除] 移除储物柜4, 成本增加: 557.72元/天
      [精确移除] 移除储物柜3, 成本增加: 556.60元/天
      [精确移除] 移除储物柜3, 成本增加: 55.04元/天
      [精确移除] 移除储物柜3, 成本增加: 55.04元/天
  重启 2/2
  迭代 33: 找到历史最优解，目标值: 140.18
      [精确移除] 移除储物柜3, 成本增加: 677.93元/天
      [精确移除] 移除储物柜2, 成本增加: 553.17元/天
      [精确移除] 移除储物柜2, 成本增加: 553.17元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 16.48秒
  总迭代次数: 49
  重启次数: 2/2
  最终温度: 75.9988
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 6/9 次改进 (66.7%)
    N1_Drone: 4/9 次改进 (44.4%)
    N2_AddDrop: 2/5 次改进 (40.0%)
  最终目标值: 162.70
  精确评估次数: 13
  复制 2 在 40 个样本上的目标值: 140.24
  评估复制 2 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
[严重警告] 进程内存使用过高: 3710.7 MB - 建议立即清理
  FastAssignmentSolver批量求解完成，耗时: 0.41秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.41秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.31秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 3.69秒
  验证阶段批量求解总耗时: 4.41秒
  平均每场景: 2.2毫秒
  复制 2 在 2000 个样本上的平均目标值 (UB估计): 144.01
  复制 2 在 2000 个样本上的平均卡车成本: 115.33
  时间分析: ALNS求解 21.66s, 验证评估 4.47s, 总计 26.13s

✓ SAA终止条件满足，在第 2 次复制后停止
  相对差距: 1.08% ✓ (阈值: ≤3%), 方差比例: 2.03% ✓ (阈值: ≤5%), LB(m=2): 141.94, UB: 143.50, Gap: 1.56, δ²_Gap: 2.9115

📊 SAA 统计结果汇总 (2 次有效复制，使用ALNS)
============================================================
  下界估计 cost_N^m: 141.94 元/天
    ↳ 计算方法: 前2次复制的小样本优化成本的算术平均
    ↳ 含义: 系统真实期望成本的下界估计
    - 下界标准差: 1.71
    - 下界方差 δ²(cost_N): 1.4557
  上界估计 cost_2000(ŝ): 143.50 元/天
    ↳ 计算方法: 最佳解在2000个大样本场景下的平均成本
    ↳ 含义: 最佳方案长期运营的期望日均成本
  SAA Gap: 1.56 元/天 (1.1%)
  所有复制验证成本统计: 143.75 ± 0.25 元/天

🏆 最佳解详情 (来自复制 1)
  总成本: 143.50 元/天
  开放储物柜: 3 个
  成本构成: 储物柜 15.20 + 无人机(部署+运输) 12.76 + 卡车(固定+运输) 115.38 + 惩罚 0.00

🔍 [最终精确评估] 使用统一的精确评估方法...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
[严重警告] 进程内存使用过高: 3706.2 MB - 建议立即清理
  启发式最优解的精确成本: 150.98 元/天
  启发式评估成本: 143.50 元/天
  精确评估成本: 150.98 元/天
  成本差异: 7.48 元/天 (+5.2%)
  🔧 为确保与g_i.py可比性，重新计算精确卡车成本...
  精确卡车成本: 115.38 元/天
  ✅ 启发式解成本更低，但使用精确卡车成本调整
  调整后成本: 143.50 → 143.50 元/天

🏆 [最终结果] SAA求解完成
  最终成本: 143.50 元/天
  评估方法: hybrid
  SAA Gap: 1.56 元/天
  最佳解选定的储物柜 (y*): [1, 2, 3]
  最佳解无人机分配 (n*): {1: 1, 2: 3, 3: 1}

============================================================
SAA 优化结果 (ALNS算法)
============================================================
  正在使用与最终成本一致的评估方法进行成本分解（方法: hybrid）...
  检测到混合评估方法（hybrid），使用精确方法进行成本分解
  ℹ️ 注意：为确保一致性，所有成本组件都使用精确计算
  使用精确评估方法计算成本分解（基于2000个验证样本）...
[严重警告] 进程内存使用过高: 3707.4 MB - 建议立即清理
    场景1: 总需求=53.0, 总分配=53.0, 短缺=0.0, 惩罚=0.00
    场景2: 总需求=32.0, 总分配=32.0, 短缺=0.0, 惩罚=0.00
    场景3: 总需求=48.0, 总分配=48.0, 短缺=0.0, 惩罚=0.00
  精确方法重新计算结果: 无人机运输成本=6.18, 惩罚成本=8.66
  ⚠️ 警告：成本分解不一致！
    最终成本（来自hybrid评估）: 143.50 元/天
    分解成本总和: 152.18 元/天
    差异: 8.69 元/天
  ℹ️ 这表明成本分解方法与最终评估方法不匹配
  📊 核心指标:
    开放储物柜数量: 3 个
    总成本: 143.50 元/天 (评估方法: hybrid)
    无人机成本(部署+运输): 12.94 元/天 (9.0%)
    卡车成本(固定+运输): 115.38 元/天 (80.4%) [DRL精确计算]

  💰 详细成本分解:
    储物柜固定成本: 15.20 元/天 (10.6%)
    无人机成本(部署+运输): 12.94 元/天 (9.0%)
    卡车成本(固定+运输): 115.38 元/天 (80.4%)
    惩罚成本: 8.66 元/天 (6.0%)

  🏪 储物柜配置:
    选定站点: [1, 2, 3]
    位置 1: 1 架无人机
    位置 2: 3 架无人机
    位置 3: 1 架无人机
    无人机总数: 5 架

  📈 运营指标:
    总期望需求量: 45.00 订单/天
    平均每储物柜服务: 15.00 订单/天
    平均每无人机服务: 9.00 订单/天

  📝 模型说明:
    第一阶段决策：储物柜选址和无人机配置（已确定）
    第二阶段决策：根据实际需求场景动态优化客户分配和配送
    成本为考虑需求不确定性后的期望日均成本

  显示SAA最终解的可视化:
  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）
  ↳ 客户分配将在第二阶段根据实际需求场景动态优化
  ↳ 上述成本是考虑需求不确定性后的期望值

SAA模型求解总耗时: 77.51 秒

📊 改进策略效果总结:
==================================================
✅ SAA求解策略: 复制过程使用启发式评估，最后进行精确验证
✅ 最终评估方法: hybrid
✅ 最终解成本: 143.50 元/天
🔧 与g_i.py可比性: 高度可比（卡车成本使用DRL精确计算）
✅ SAA Gap: 1.56 元/天 (1.1%)

🔍 成本计算验证:
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
✅ 使用统一的启发式评估，无需额外验证

🎯 改进策略优势:
  1. 复制过程效率提升：只使用启发式评估
  2. 解质量保证：最后使用精确求解器验证
  3. 智能选择：比较两种方法，选择更优解

总运行时间: 84.63 秒
[内存监控] 停止监控

[内存摘要]
  最大内存: 3710.7 MB
  最小内存: 309.1 MB
  平均内存: 2946.1 MB
  当前内存: 3707.4 MB
  监控样本: 9 个
测试完成。如果图像窗口仍然打开，请手动关闭。

进程已结束,退出代码0
