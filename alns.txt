D:\download\Anaconda\envs\pytorch\python.exe E:\代码\self\对比\gurobi_SAA\alns.py 
✅ SciPy可用，将使用稀疏矩阵优化内存使用
⚠️ memory-profiler不可用，建议安装: pip install memory-profiler
✅ Numba可用，但已简化为统一Python实现
[内存监控] 开始监控，检查间隔: 10秒
内存监控已启动
设置全局随机种子: 602
DRL日志级别已设置为ERROR（屏蔽WARNING信息）

创建随机需求的示例数据 (使用期望需求)...
============================================================
成本计算方法改进：统一时间单位
============================================================
修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用
修正后方案：使用资本回收因子将所有固定成本统一为日成本单位
优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差
============================================================
  成本计算参数:
    年利率 (IR): 4.0%
    设备生命周期 (T_life): 10年
    资本回收因子: 0.123291
    年运营天数: 365天
  储物柜成本转换:
    初始建设成本: 15,000元
    年化成本: 1,849.36元/年
    日均固定成本: 5.07元/天
  无人机成本转换:
    初始采购成本: 4,000元
    年化成本: 493.16元/年
    日均固定成本: 1.35元/天

  成本单位统一性检查:
    储物柜固定成本: 5.07 元/天
    无人机固定成本: 1.35 元/天
    无人机运输成本: 0.020 元/公里 (按实际运输量)
    卡车固定成本: 100 元/天
    卡车运输成本: 0.5 元/公里 (按实际运输量)
    ✓ 所有固定成本已统一为日成本单位
    ✓ 运输成本保持按实际使用量计费
随机需求数据 (期望值) 已创建。

所有客户期望需求 (λᵢ_bar):
  客户 1: 2 订单/天 (期望)
  客户 2: 3 订单/天 (期望)
  客户 3: 2 订单/天 (期望)
  客户 4: 2 订单/天 (期望)
  客户 5: 3 订单/天 (期望)
  客户 6: 4 订单/天 (期望)
  客户 7: 4 订单/天 (期望)
  客户 8: 3 订单/天 (期望)
  客户 9: 2 订单/天 (期望)
  客户 10: 3 订单/天 (期望)
  客户 11: 3 订单/天 (期望)
  客户 12: 3 订单/天 (期望)
  客户 13: 2 订单/天 (期望)
  客户 14: 2 订单/天 (期望)
  客户 15: 4 订单/天 (期望)
总期望需求: 42 订单/天

============================================================
求解带随机需求的无人机配送网络设计问题 (SAA)
============================================================
使用ALNS方法求解SAA问题...

开始SAA优化（使用ALNS），最多 10 次复制...
SAA终止条件 (必须同时满足):
  1. 相对差距阈值: Gap/UB ≤ 3% 且 Gap ≥ 0
  2. 方差阈值: δ²_Gap/UB ≤ 5%
  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)
  最少需要 2 次有效复制
生成 2000 个固定验证样本 (所有复制共用)...

--- SAA 复制 1/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 1...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [2, 3]
    策略2成功，目标值: 136.19
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [3, 1, 4, 2]
    策略3成功，目标值: 145.63
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 142.24
  初始解目标值: 142.24 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 exact_cost_removal -> balanced_repair
[严重警告] 进程内存使用过高: 3500.4 MB - 建议立即清理
    [精确评估 #1] 方案[1, 2, 3]: 142.24元/天
    [精确评估 #2] 方案[2, 3]: 136.14元/天
    [精确评估 #3] 方案[1, 3]: 146.09元/天
      [精确移除] 移除储物柜3, 成本增加: -8.94元/天
  迭代 1: 找到历史最优解，目标值: 133.30
      [精确插入] 添加储物柜2(1架无人机), 改进: 388.09元/天
      [精确移除] 移除储物柜4, 成本增加: 406.57元/天
      [精确移除] 移除储物柜4, 成本增加: 374.98元/天
      [精确移除] 移除储物柜4, 成本增加: -6.08元/天
  重启 1/2
      [精确移除] 移除储物柜2, 成本增加: 388.09元/天
      [精确移除] 移除储物柜3, 成本增加: -6.25元/天
      [精确移除] 移除储物柜2, 成本增加: 388.09元/天
  重启 2/2
      [精确移除] 移除储物柜3, 成本增加: 415.88元/天
      [精确移除] 移除储物柜4, 成本增加: 374.98元/天
      [精确移除] 移除储物柜3, 成本增加: -8.94元/天
      [精确移除] 移除储物柜2, 成本增加: 388.09元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 14.75秒
  总迭代次数: 49
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 3/9 次改进 (33.3%)
    N1_Drone: 1/9 次改进 (11.1%)
    N2_AddDrop: 2/8 次改进 (25.0%)
  最终目标值: 133.30
  精确评估次数: 12
[严重警告] 进程内存使用过高: 3687.8 MB - 建议立即清理
  复制 1 在 40 个样本上的目标值: 133.40
  评估复制 1 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.42秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.42秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.29秒
    步骤3: 使用DRL批量计算卡车成本...
  将 2000 个场景分为 1 组进行批量求解
  分组批量求解成功，平均卡车成本: 113.76
    DRL批量卡车成本计算完成，耗时: 2.40秒
  验证阶段批量求解总耗时: 3.11秒
  平均每场景: 1.6毫秒
  复制 1 在 2000 个样本上的平均目标值 (UB估计): 133.86
  复制 1 在 2000 个样本上的平均卡车成本: 113.76
  时间分析: ALNS求解 19.52s, 验证评估 3.18s, 总计 22.70s

--- SAA 复制 2/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 2...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [4, 3]
    策略2成功，目标值: 136.25
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [1, 3, 2]
    策略3成功，目标值: 139.54
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 145.63
  初始解目标值: 136.25 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 exact_cost_removal -> drone_optimization
    [精确评估 #1] 方案[3, 4]: 150.12元/天
    [精确评估 #2] 方案[4]: 556.73元/天
    [精确评估 #3] 方案[3]: 582.74元/天
      [精确移除] 移除储物柜3, 成本增加: 406.61元/天
      [精确移除] 移除储物柜4, 成本增加: 403.87元/天
  迭代 5: 找到历史最优解，目标值: 132.02
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: -7.36元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
  迭代 20: 找到历史最优解，目标值: 128.29
      [精确移除] 移除储物柜4, 成本增加: -9.12元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
  重启 1/2
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: -7.44元/天
      [精确移除] 移除储物柜4, 成本增加: -7.44元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
    迭代 50: 执行 random_locker_removal -> smart_drone_tuner
  重启 2/2
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜2, 成本增加: -8.55元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 3.21秒
  总迭代次数: 68
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 6/13 次改进 (46.2%)
    N1_Drone: 2/13 次改进 (15.4%)
    N2_AddDrop: 4/11 次改进 (36.4%)
[严重警告] 进程内存使用过高: 3695.3 MB - 建议立即清理
  最终目标值: 142.04
  精确评估次数: 8
  复制 2 在 40 个样本上的目标值: 128.38
  评估复制 2 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.40秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.40秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.29秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 2.80秒
  验证阶段批量求解总耗时: 3.49秒
  平均每场景: 1.7毫秒
  复制 2 在 2000 个样本上的平均目标值 (UB估计): 130.27
  复制 2 在 2000 个样本上的平均卡车成本: 109.38
  时间分析: ALNS求解 7.56s, 验证评估 3.56s, 总计 11.11s
  当前SAA状态: 相对差距: -0.48% ✗ (阈值: ≤3%), 方差比例: 4.84% ✓ (阈值: ≤5%), LB(m=2): 130.89, UB: 130.27, Gap: -0.62, δ²_Gap: 6.3078

--- SAA 复制 3/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 3...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [2, 4]
    策略2成功，目标值: 141.98
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [1, 4, 2, 3]
    策略3成功，目标值: 148.33
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 522.25
  初始解目标值: 137.41 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 exact_cost_removal -> regret_insertion
    [精确评估 #1] 方案[1, 3, 4]: 151.31元/天
    [精确评估 #2] 方案[3, 4]: 146.07元/天
    [精确评估 #3] 方案[1, 4]: 146.41元/天
      [精确移除] 移除储物柜4, 成本增加: -9.27元/天
    [精确评估 #1] 方案[2, 3, 4]: 138.76元/天
      [精确移除] 移除储物柜3, 成本增加: 379.36元/天
  迭代 6: 找到历史最优解，目标值: 132.02
      [精确移除] 移除储物柜4, 成本增加: 374.98元/天
      [精确移除] 移除储物柜4, 成本增加: 374.98元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
  重启 1/2
      [精确移除] 移除储物柜3, 成本增加: 415.88元/天
      [精确移除] 移除储物柜4, 成本增加: -9.27元/天
  迭代 37: 找到历史最优解，目标值: 130.74
      [精确移除] 移除储物柜3, 成本增加: 415.88元/天
      [精确移除] 移除储物柜4, 成本增加: 374.98元/天
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
    迭代 50: 执行 drastic_reduction_removal -> greedy_locker_insertion
  重启 2/2
      [精确移除] 移除储物柜3, 成本增加: 379.36元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 4.85秒
  总迭代次数: 69
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 6/13 次改进 (46.2%)
    N1_Drone: 2/13 次改进 (15.4%)
    N2_AddDrop: 4/11 次改进 (36.4%)
[严重警告] 进程内存使用过高: 3696.7 MB - 建议立即清理
  最终目标值: 130.74
  精确评估次数: 11
  复制 3 在 40 个样本上的目标值: 130.77
  评估复制 3 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.41秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.41秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.33秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 2.53秒
  验证阶段批量求解总耗时: 3.27秒
  平均每场景: 1.6毫秒
  复制 3 在 2000 个样本上的平均目标值 (UB估计): 131.61
  复制 3 在 2000 个样本上的平均卡车成本: 112.58
  时间分析: ALNS求解 8.87s, 验证评估 3.33s, 总计 12.21s
  当前SAA状态: 相对差距: -0.45% ✗ (阈值: ≤3%), 方差比例: 1.62% ✓ (阈值: ≤5%), LB(m=3): 130.85, UB: 130.27, Gap: -0.58, δ²_Gap: 2.1049

--- SAA 复制 4/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 4...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [1, 4]
    策略2成功，目标值: 137.96
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [1, 4, 2, 3]
    策略3成功，目标值: 151.04
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 134.72
  初始解目标值: 134.72 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 random_locker_removal -> balanced_repair
  迭代 1: 找到历史最优解，目标值: 132.02
    [精确评估 #1] 方案[3]: 639.77元/天
    [精确评估 #2] 方案[1, 3]: 142.04元/天
    [精确评估 #3] 方案[2, 3]: 130.74元/天
      [精确插入] 添加储物柜2(1架无人机), 改进: 509.04元/天
  迭代 4: 找到历史最优解，目标值: 130.74
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确插入] 添加储物柜3(1架无人机), 改进: 411.06元/天
      [精确移除] 移除储物柜3, 成本增加: 379.36元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
  重启 1/2
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜4, 成本增加: -7.36元/天
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
  重启 2/2
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜3, 成本增加: 379.36元/天
[严重警告] 进程内存使用过高: 3698.2 MB - 建议立即清理    迭代 50: 执行 random_locker_removal -> greedy_locker_insertion

  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 3.24秒
  总迭代次数: 52
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 5/9 次改进 (55.6%)
    N1_Drone: 1/9 次改进 (11.1%)
    N2_AddDrop: 4/8 次改进 (50.0%)
  最终目标值: 130.74
  精确评估次数: 8
  复制 4 在 40 个样本上的目标值: 130.77
  评估复制 4 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.40秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.40秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.28秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 2.81秒
  验证阶段批量求解总耗时: 3.50秒
  平均每场景: 1.7毫秒
  复制 4 在 2000 个样本上的平均目标值 (UB估计): 131.61
  复制 4 在 2000 个样本上的平均卡车成本: 112.58
  时间分析: ALNS求解 7.30s, 验证评估 3.56s, 总计 10.86s
  当前SAA状态: 相对差距: -0.43% ✗ (阈值: ≤3%), 方差比例: 0.81% ✓ (阈值: ≤5%), LB(m=4): 130.83, UB: 130.27, Gap: -0.56, δ²_Gap: 1.0531

--- SAA 复制 5/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 5...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [3, 1]
    策略2成功，目标值: 130.99
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [3, 4, 2]
    策略3成功，目标值: 144.17
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 137.93
  初始解目标值: 130.99 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 random_locker_removal -> drone_optimization
    [精确评估 #1] 方案[1, 3]: 144.73元/天
    [精确评估 #2] 方案[3]: 582.74元/天
      [精确移除] 移除储物柜3, 成本增加: 373.34元/天
      [精确移除] 移除储物柜3, 成本增加: 373.34元/天
    [精确评估 #3] 方案[1, 2]: 131.95元/天
    [精确评估 #1] 方案[1, 2]: 131.95元/天
      [精确移除] 移除储物柜4, 成本增加: 385.51元/天
      [精确移除] 移除储物柜4, 成本增加: 385.51元/天
  重启 1/2
      [精确移除] 移除储物柜3, 成本增加: 403.76元/天
      [精确移除] 移除储物柜3, 成本增加: 373.34元/天
      [精确移除] 移除储物柜4, 成本增加: -7.43元/天
      [精确移除] 移除储物柜2, 成本增加: 386.11元/天
[严重警告] 进程内存使用过高: 3699.9 MB - 建议立即清理
  重启 2/2
      [精确移除] 移除储物柜2, 成本增加: 386.11元/天
      [精确移除] 移除储物柜4, 成本增加: 385.51元/天
      [精确移除] 移除储物柜3, 成本增加: 403.76元/天
      [精确移除] 移除储物柜2, 成本增加: 386.11元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 2.93秒
  总迭代次数: 48
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 4/9 次改进 (44.4%)
    N1_Drone: 1/9 次改进 (11.1%)
    N2_AddDrop: 3/8 次改进 (37.5%)
  最终目标值: 144.73
  精确评估次数: 9
  复制 5 在 40 个样本上的目标值: 131.05
  评估复制 5 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.41秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.41秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.29秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 3.05秒
  验证阶段批量求解总耗时: 3.75秒
  平均每场景: 1.9毫秒
  复制 5 在 2000 个样本上的平均目标值 (UB估计): 131.68
  复制 5 在 2000 个样本上的平均卡车成本: 109.38
  时间分析: ALNS求解 7.02s, 验证评估 3.82s, 总计 10.84s
  当前SAA状态: 相对差距: -0.47% ✗ (阈值: ≤3%), 方差比例: 0.49% ✓ (阈值: ≤5%), LB(m=5): 130.88, UB: 130.27, Gap: -0.61, δ²_Gap: 0.6340

--- SAA 复制 6/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 6...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [4, 1]
    策略2成功，目标值: 139.31
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [1, 2, 3, 4]
    策略3成功，目标值: 145.63
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 151.04
  初始解目标值: 137.41 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 drastic_reduction_removal -> greedy_locker_insertion
    [精确评估 #1] 方案[1, 2, 4]: 139.38元/天
    [精确评估 #1] 方案[1, 2, 4]: 139.38元/天
    [精确评估 #2] 方案[1]: 521.39元/天
  迭代 5: 找到历史最优解，目标值: 135.23
[严重警告] 进程内存使用过高: 3701.4 MB - 建议立即清理
    [精确评估 #3] 方案[2, 3, 4]: 138.76元/天
      [精确移除] 移除储物柜4, 成本增加: 406.57元/天
  迭代 11: 找到历史最优解，目标值: 132.02
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: -7.36元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: 406.57元/天
  重启 1/2
      [精确移除] 移除储物柜3, 成本增加: 379.36元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: -7.36元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
  重启 2/2
    迭代 50: 执行 worst_locker_removal -> exact_cost_insertion
      [精确移除] 移除储物柜3, 成本增加: 379.36元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
  迭代 57: 找到历史最优解，目标值: 130.74
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜2, 成本增加: 389.37元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 5.02秒
  总迭代次数: 73
  重启次数: 2/2
  最终温度: 58.4447
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 4/14 次改进 (28.6%)
    N1_Drone: 2/14 次改进 (14.3%)
    N2_AddDrop: 2/12 次改进 (16.7%)
  最终目标值: 130.74
  精确评估次数: 11
  复制 6 在 40 个样本上的目标值: 130.77
  评估复制 6 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.40秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.40秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.29秒
    步骤3: 使用DRL批量计算卡车成本...
[严重警告] 进程内存使用过高: 3706.6 MB - 建议立即清理
    DRL批量卡车成本计算完成，耗时: 2.83秒
  验证阶段批量求解总耗时: 3.53秒
  平均每场景: 1.8毫秒
  复制 6 在 2000 个样本上的平均目标值 (UB估计): 131.61
  复制 6 在 2000 个样本上的平均卡车成本: 112.58
  时间分析: ALNS求解 9.39s, 验证评估 3.59s, 总计 12.98s
  当前SAA状态: 相对差距: -0.45% ✗ (阈值: ≤3%), 方差比例: 0.32% ✓ (阈值: ≤5%), LB(m=6): 130.86, UB: 130.27, Gap: -0.59, δ²_Gap: 0.4231

--- SAA 复制 7/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 7...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [1, 2]
    策略2成功，目标值: 138.78
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [1, 4, 2, 3]
    策略3成功，目标值: 146.98
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 146.14
  初始解目标值: 137.41 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 drone_reduction_removal -> smart_drone_tuner
    [精确评估 #1] 方案[1, 2, 3, 4]: 144.28元/天
    [精确评估 #2] 方案[2, 4]: 164.04元/天
    [精确评估 #3] 方案[4]: 561.95元/天
      [精确移除] 移除储物柜4, 成本增加: 377.76元/天
    [精确评估 #1] 方案[1, 2]: 131.95元/天
  迭代 8: 找到历史最优解，目标值: 131.95
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: -7.43元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
  重启 1/2
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
  迭代 25: 找到历史最优解，目标值: 128.29
      [精确移除] 移除储物柜3, 成本增加: 415.88元/天
      [精确移除] 移除储物柜3, 成本增加: 415.88元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
  重启 2/2
      [精确移除] 移除储物柜4, 成本增加: 377.76元/天
    迭代 50: 执行 exact_cost_removal -> diversified_insertion
      [精确移除] 移除储物柜4, 成本增加: -7.44元/天
      [精确移除] 移除储物柜4, 成本增加: -7.44元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 3.71秒
  总迭代次数: 57
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 6/11 次改进 (54.5%)
    N1_Drone: 3/11 次改进 (27.3%)
    N2_AddDrop: 3/8 次改进 (37.5%)
[严重警告] 进程内存使用过高: 3704.4 MB - 建议立即清理
  最终目标值: 142.04
  精确评估次数: 8
  复制 7 在 40 个样本上的目标值: 128.38
  评估复制 7 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.39秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.39秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.29秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 2.82秒
  验证阶段批量求解总耗时: 3.50秒
  平均每场景: 1.7毫秒
  复制 7 在 2000 个样本上的平均目标值 (UB估计): 130.27
  复制 7 在 2000 个样本上的平均卡车成本: 109.38
  时间分析: ALNS求解 8.26s, 验证评估 3.56s, 总计 11.82s
  当前SAA状态: 相对差距: -0.18% ✗ (阈值: ≤3%), 方差比例: 0.33% ✓ (阈值: ≤5%), LB(m=7): 130.51, UB: 130.27, Gap: -0.24, δ²_Gap: 0.4276

--- SAA 复制 8/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 8...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [1, 4]
    策略2成功，目标值: 137.96
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [3, 4, 2]
    策略3成功，目标值: 141.46
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 522.25
  初始解目标值: 141.46 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 geographic_removal -> greedy_locker_insertion
    [精确评估 #1] 方案[1, 3]: 144.74元/天
    [精确评估 #1] 方案[1, 3]: 144.74元/天
    [精确评估 #2] 方案[3]: 639.77元/天
    [精确评估 #3] 方案[1]: 521.39元/天
      [精确移除] 移除储物柜3, 成本增加: 376.65元/天
  迭代 5: 找到历史最优解，目标值: 132.02
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
      [精确移除] 移除储物柜4, 成本增加: -7.36元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
  重启 1/2
      [精确移除] 移除储物柜3, 成本增加: 411.06元/天
      [精确移除] 移除储物柜3, 成本增加: 376.65元/天
      [精确移除] 移除储物柜4, 成本增加: -7.36元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
  重启 2/2
      [精确移除] 移除储物柜3, 成本增加: 415.88元/天
      [精确移除] 移除储物柜4, 成本增加: 377.76元/天
      [精确移除] 移除储物柜4, 成本增加: -7.36元/天
      [精确移除] 移除储物柜4, 成本增加: 388.84元/天
    迭代 50: 执行 drone_adjustment_removal -> smart_drone_tuner
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 3.67秒
  总迭代次数: 53
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 2/10 次改进 (20.0%)
    N1_Drone: 0/10 次改进 (0.0%)
    N2_AddDrop: 2/10 次改进 (20.0%)
[严重警告] 进程内存使用过高: 3706.2 MB - 建议立即清理
  最终目标值: 131.95
  精确评估次数: 9
  复制 8 在 40 个样本上的目标值: 132.05
  评估复制 8 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.40秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.40秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.30秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 2.43秒
  验证阶段批量求解总耗时: 3.13秒
  平均每场景: 1.6毫秒
  复制 8 在 2000 个样本上的平均目标值 (UB估计): 132.51
  复制 8 在 2000 个样本上的平均卡车成本: 113.76
  时间分析: ALNS求解 7.95s, 验证评估 3.22s, 总计 11.17s
  当前SAA状态: 相对差距: -0.33% ✗ (阈值: ≤3%), 方差比例: 0.28% ✓ (阈值: ≤5%), LB(m=8): 130.70, UB: 130.27, Gap: -0.43, δ²_Gap: 0.3583

--- SAA 复制 9/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 9...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [4, 2]
    策略2成功，目标值: 140.63
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [3, 1, 2, 4]
    策略3成功，目标值: 148.33
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 522.25
  初始解目标值: 137.41 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 drastic_reduction_removal -> smart_drone_tuner
  迭代 2: 找到历史最优解，目标值: 128.29
    [精确评估 #1] 方案[3]: 639.77元/天
    [精确评估 #2] 方案[1]: 521.39元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
    [精确评估 #3] 方案[2, 3]: 130.74元/天
      [精确移除] 移除储物柜2, 成本增加: -8.55元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
  重启 1/2
      [精确移除] 移除储物柜2, 成本增加: 389.44元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
      [精确移除] 移除储物柜3, 成本增加: 393.11元/天
  重启 2/2
      [精确移除] 移除储物柜3, 成本增加: 429.76元/天
      [精确移除] 移除储物柜4, 成本增加: 377.76元/天
      [精确移除] 移除储物柜4, 成本增加: -7.43元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 3.16秒
  总迭代次数: 50
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 3/9 次改进 (33.3%)
    N1_Drone: 1/9 次改进 (11.1%)
    N2_AddDrop: 2/8 次改进 (25.0%)
[严重警告] 进程内存使用过高: 3707.4 MB - 建议立即清理
  最终目标值: 142.04
  精确评估次数: 8
  复制 9 在 40 个样本上的目标值: 128.38
  评估复制 9 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.41秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.41秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.29秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 3.10秒
  验证阶段批量求解总耗时: 3.80秒
  平均每场景: 1.9毫秒
  复制 9 在 2000 个样本上的平均目标值 (UB估计): 130.27
  复制 9 在 2000 个样本上的平均卡车成本: 109.38
  时间分析: ALNS求解 7.53s, 验证评估 3.86s, 总计 11.39s
  当前SAA状态: 相对差距: -0.13% ✗ (阈值: ≤3%), 方差比例: 0.26% ✓ (阈值: ≤5%), LB(m=9): 130.44, UB: 130.27, Gap: -0.17, δ²_Gap: 0.3450

--- SAA 复制 10/10 (使用ALNS) ---
  已生成 40 个需求场景用于求解。
  开始ALNS求解复制 10...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  开始ALNS求解，时间限制: 120秒
  策略1: 确定性最优策略...
      总期望需求: 42.0
      平均储物柜容量: 30.0
      估算最少储物柜: 2
      目标储物柜数量: 3
  使用全部 40 个训练场景进行评估
    策略1成功，目标值: 137.41
  策略2: 随机化策略 (尝试 1)...
      随机少储物柜解: [4, 2]
    策略2成功，目标值: 140.63
  策略3: 随机化策略 (尝试 2)...
      随机中等储物柜解: [3, 1, 2]
    策略3成功，目标值: 139.54
  策略4: 随机化策略 (尝试 3)...
    策略4成功，目标值: 136.61
  初始解目标值: 136.61 (启发式评估)
  ✅ 使用智能评估策略：启发式评估为主，关键时刻精确评估
  ⚡ 预期求解速度提升3-5倍
    迭代 1: 执行 exact_cost_removal -> smart_drone_tuner
    [精确评估 #1] 方案[1, 4]: 150.47元/天
    [精确评估 #2] 方案[4]: 556.73元/天
    [精确评估 #3] 方案[1]: 521.75元/天
      [精确移除] 移除储物柜4, 成本增加: 371.28元/天
      [精确移除] 移除储物柜4, 成本增加: 403.87元/天
  迭代 5: 找到历史最优解，目标值: 132.02
      [精确移除] 移除储物柜4, 成本增加: 371.28元/天
      [精确移除] 移除储物柜2, 成本增加: 389.73元/天
  迭代 14: 找到历史最优解，目标值: 130.78
  迭代 15: 找到历史最优解，目标值: 128.29
      [精确移除] 移除储物柜3, 成本增加: 393.46元/天
      [精确移除] 移除储物柜3, 成本增加: 393.46元/天
      [精确移除] 移除储物柜3, 成本增加: 393.46元/天
      [精确移除] 移除储物柜3, 成本增加: 393.46元/天
  重启 1/2
      [精确移除] 移除储物柜3, 成本增加: 411.01元/天
      [精确移除] 移除储物柜3, 成本增加: 411.01元/天
      [精确移除] 移除储物柜3, 成本增加: 411.01元/天
      [精确移除] 移除储物柜2, 成本增加: 389.73元/天
[严重警告] 进程内存使用过高: 3708.9 MB - 建议立即清理
  重启 2/2
      [精确移除] 移除储物柜4, 成本增加: 403.87元/天
    迭代 50: 执行 random_locker_removal -> drone_optimization
      [精确移除] 移除储物柜3, 成本增加: 411.01元/天
      [精确移除] 移除储物柜3, 成本增加: 410.66元/天
      [精确移除] 移除储物柜4, 成本增加: 371.28元/天
  达到最大重启次数，终止搜索
  ALNS求解完成，耗时: 3.98秒
  总迭代次数: 63
  重启次数: 2/2
  最终温度: 77.5498
  连续无改进次数: 15
  终止原因: 达到最大重启次数(2)
  局部搜索统计: 8/12 次改进 (66.7%)
    N1_Drone: 0/12 次改进 (0.0%)
    N2_AddDrop: 8/12 次改进 (66.7%)
  最终目标值: 142.04
  精确评估次数: 9
  复制 10 在 40 个样本上的目标值: 128.38
  评估复制 10 的解在 2000 个固定验证样本上的性能...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
  验证阶段：使用批量求解 2000 个场景...
    步骤1: 批量求解客户分配...
  使用FastAssignmentSolver批量求解 2000 个验证场景...
    进度: 10.0% (200/2000)，已用时: 0.1秒
  FastAssignmentSolver批量求解完成，耗时: 0.41秒
  平均每场景: 0.2毫秒
    客户分配批量求解完成，耗时: 0.41秒
    步骤2: 批量计算运输和惩罚成本...
    运输和惩罚成本计算完成，耗时: 0.29秒
    步骤3: 使用DRL批量计算卡车成本...
    DRL批量卡车成本计算完成，耗时: 2.80秒
  验证阶段批量求解总耗时: 3.50秒
  平均每场景: 1.7毫秒
  复制 10 在 2000 个样本上的平均目标值 (UB估计): 130.27
  复制 10 在 2000 个样本上的平均卡车成本: 109.38
  时间分析: ALNS求解 7.95s, 验证评估 3.56s, 总计 11.51s

✓ SAA终止条件满足，在第 10 次复制后停止
  相对差距: 0.03% ✓ (阈值: ≤3%), 方差比例: 0.24% ✓ (阈值: ≤5%), LB(m=10): 130.24, UB: 130.27, Gap: 0.03, δ²_Gap: 0.3185

📊 SAA 统计结果汇总 (10 次有效复制，使用ALNS)
============================================================
  下界估计 cost_N^m: 130.24 元/天
    ↳ 计算方法: 前10次复制的小样本优化成本的算术平均
    ↳ 含义: 系统真实期望成本的下界估计
    - 下界标准差: 1.69
    - 下界方差 δ²(cost_N): 0.0318
  上界估计 cost_2000(ŝ): 130.27 元/天
    ↳ 计算方法: 最佳解在2000个大样本场景下的平均成本
    ↳ 含义: 最佳方案长期运营的期望日均成本
  SAA Gap: 0.03 元/天 (0.0%)
  所有复制验证成本统计: 131.40 ± 1.12 元/天

🏆 最佳解详情 (来自复制 2)
  总成本: 130.27 元/天
  开放储物柜: 2 个
  成本构成: 储物柜 10.13 + 无人机(部署+运输) 9.34 + 卡车(固定+运输) 109.38 + 惩罚 3.60

🔍 [最终精确评估] 使用统一的精确评估方法...
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
[严重警告] 进程内存使用过高: 3710.0 MB - 建议立即清理
[严重警告] 进程内存使用过高: 3710.2 MB - 建议立即清理
  启发式最优解的精确成本: 136.92 元/天
  启发式评估成本: 130.27 元/天
  精确评估成本: 136.92 元/天
  成本差异: 6.65 元/天 (+5.1%)
  🔧 为确保与g_i.py可比性，重新计算精确卡车成本...
  精确卡车成本: 109.38 元/天
  ✅ 启发式解成本更低，但使用精确卡车成本调整
  调整后成本: 130.27 → 130.27 元/天

🏆 [最终结果] SAA求解完成
  最终成本: 130.27 元/天
  评估方法: hybrid
  SAA Gap: 0.03 元/天
  最佳解选定的储物柜 (y*): [1, 3]
  最佳解无人机分配 (n*): {1: 1, 3: 1}

============================================================
SAA 优化结果 (ALNS算法)
============================================================
  正在使用与最终成本一致的评估方法进行成本分解（方法: hybrid）...
  检测到混合评估方法（hybrid），使用精确方法进行成本分解
  ℹ️ 注意：为确保一致性，所有成本组件都使用精确计算
  使用精确评估方法计算成本分解（基于2000个验证样本）...
    场景1: 总需求=38.0, 总分配=38.0, 短缺=0.0, 惩罚=0.00
    场景2: 总需求=42.0, 总分配=42.0, 短缺=0.0, 惩罚=0.00
    场景3: 总需求=56.0, 总分配=56.0, 短缺=0.0, 惩罚=0.00
  精确方法重新计算结果: 无人机运输成本=6.26, 惩罚成本=10.38
  ⚠️ 检测到成本分解不一致，使用详细分解的总和作为最终成本
    原评估成本（hybrid）: 130.27 元/天
    详细分解总和: 138.85 元/天
    差异: 8.58 元/天
  ✅ 采用详细成本分解的总和: 138.85 元/天
  📊 核心指标:
    开放储物柜数量: 2 个
    总成本: 138.85 元/天
    无人机成本(部署+运输): 8.96 元/天 (6.5%)
    卡车成本(固定+运输): 109.38 元/天 (78.8%) [DRL精确计算]

  💰 详细成本分解:
    储物柜固定成本: 10.13 元/天 (7.3%)
    无人机成本(部署+运输): 8.96 元/天 (6.5%)
    卡车成本(固定+运输): 109.38 元/天 (78.8%)
    惩罚成本: 10.38 元/天 (7.5%)
  ✅ 已更新SAA解决方案中的总成本为: 138.85 元/天

  🏪 储物柜配置:
    选定站点: [1, 3]
    位置 1: 1 架无人机
    位置 3: 1 架无人机
    无人机总数: 2 架

  📈 运营指标:
    总期望需求量: 42.00 订单/天
    平均每储物柜服务: 21.00 订单/天
    平均每无人机服务: 21.00 订单/天

  📝 模型说明:
    第一阶段决策：储物柜选址和无人机配置（已确定）
    第二阶段决策：根据实际需求场景动态优化客户分配和配送
    成本为考虑需求不确定性后的期望日均成本

  显示SAA最终解的可视化:
  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）
  ↳ 客户分配将在第二阶段根据实际需求场景动态优化
  ↳ 上述成本是考虑需求不确定性后的期望值

SAA模型求解总耗时: 140.59 秒

📊 改进策略效果总结:
==================================================
✅ SAA求解策略: 复制过程使用启发式评估，最后进行精确验证
✅ 最终评估方法: hybrid
✅ 最终解成本: 138.85 元/天
🔧 与g_i.py可比性: 高度可比（卡车成本使用DRL精确计算）
✅ SAA Gap: 0.03 元/天 (0.0%)

🔍 成本计算验证:
  初始化FastAssignmentSolver...
  FastAssignmentSolver初始化完成
✅ 使用统一的启发式评估，无需额外验证

🎯 改进策略优势:
  1. 复制过程效率提升：只使用启发式评估
  2. 解质量保证：最后使用精确求解器验证
  3. 智能选择：比较两种方法，选择更优解

总运行时间: 149.95 秒
[内存监控] 停止监控

[内存摘要]
  最大内存: 3710.2 MB
  最小内存: 308.8 MB
  平均内存: 3462.8 MB
  当前内存: 3710.2 MB
  监控样本: 15 个
测试完成。如果图像窗口仍然打开，请手动关闭。

进程已结束,退出代码0
