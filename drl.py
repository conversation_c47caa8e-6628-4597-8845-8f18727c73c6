# drl_cvrp_solver.py
from typing import Dict, List, Tuple, Any
import torch
import os
import sys
import numpy as np
import tempfile
import logging
import math
import time

# Import the necessary modules for DRL solver
from CVRPTester import CVRPTester as Tester
from CVRPEnv import CVRPEnv
from CVRPModel import CVRPModel

# Configure logging - 设置为INFO级别以显示详细日志输出
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加一个函数来动态调整日志级别
def set_drl_log_level(level=logging.INFO):
    """设置DRL模块的日志级别
    Args:
        level: 日志级别 (logging.DEBUG, logging.INFO, logging.WARNING, logging.ERROR)
    """
    logger.setLevel(level)
    # 同时设置根日志记录器的级别
    logging.getLogger().setLevel(level)

class DRL_CVRP_Solver:
    def __init__(self, depot_coord: Tuple[float, float],
                 truck_capacity: float,
                 truck_fixed_cost: float,  # 每次派遣卡车的固定成本
                 truck_km_cost: float,  # 卡车每公里成本
                 keep_temp_files: bool = False,  # 是否保留临时.pt文件
                 max_temp_files: int = 5,  # 最大保留的临时文件数量
                 make_plots: bool = True):  # 是否生成路线图
        """
        初始化DRL CVRP求解器。

        Args:
            depot_coord: 仓库坐标 (x, y)
            truck_capacity: 卡车容量
            truck_fixed_cost: 每次派遣卡车的固定成本
            truck_km_cost: 卡车每公里成本
            keep_temp_files: 是否保留临时.pt文件，默认False（自动清理）
            max_temp_files: 最大保留的临时文件数量，默认5个
            make_plots: 是否生成路线图，默认True
        """
        self.depot_coord = depot_coord
        self.truck_capacity = truck_capacity
        self.truck_fixed_cost = truck_fixed_cost
        self.truck_km_cost = truck_km_cost
        self.keep_temp_files = keep_temp_files
        self.max_temp_files = max_temp_files
        self.make_plots = make_plots
        self.make_plots = make_plots

        # DRL模型参数 - 不预设problem_size，将根据实际节点数动态设置
        self.env_params = {
            'pomo_size': 10,     # 默认POMO大小，将在solve方法中根据problem_size调整
        }

        self.model_params = {
            'embedding_dim': 128,
            'sqrt_embedding_dim': 128**(1/2),
            'encoder_layer_num': 6,
            'qkv_dim': 16,
            'head_num': 8,
            'logit_clipping': 10,
            'ff_hidden_dim': 512,
            'eval_type': 'argmax',
        }

        # 检测CUDA可用性
        self.use_cuda = torch.cuda.is_available()
        self.cuda_device_num = 0 if self.use_cuda else -1

        # 设置测试参数
        self.tester_params = {
            'use_cuda': self.use_cuda,
            'cuda_device_num': self.cuda_device_num,
            'model_load': {
                'path': './result/CVRP20_model',  # 预训练模型路径
                'epoch': 2000,  # 预训练模型的epoch版本
            },
            'test_episodes': 1,
            'test_batch_size': 1,
            'augmentation_enable': False,  # 启用增强
            'aug_factor': 8,
            'aug_batch_size': 1,
            'test_data_load': {
                'enable': True,
                'filename': None  # 将在solve方法中设置
            },
            'make_plots': self.make_plots,  # 根据初始化参数决定是否生成图表
            '2opt_iterations': 100  # 启用2-opt优化以提高解质量
        }

        # 初始化DRL模型
        try:
            logger.info(f"DRL CVRP Solver Initialized: Depot {depot_coord}, Capacity {truck_capacity}, Fixed Cost {truck_fixed_cost}, KM Cost {truck_km_cost}")
            logger.info(f"Temp file management: keep_temp_files={keep_temp_files}, max_temp_files={max_temp_files}")
            opt_status = 'DISABLED' if self.tester_params['2opt_iterations'] == 0 else f'ENABLED ({self.tester_params["2opt_iterations"]} iterations)'
            logger.info(f"2-opt optimization: {opt_status}")
        except Exception as e:
            logger.error(f"Error initializing DRL CVRP Solver: {str(e)}")
            raise

    def _prepare_vrp_data(self, active_lockers_info: Dict[int, Dict[str, Any]]) -> str:
        """
        准备VRP数据并保存为.pt文件

        Args:
            active_lockers_info: 活跃储物柜信息

        Returns:
            文件路径
        """
        try:
            # 确保output文件夹存在
            output_dir = os.path.abspath("./output")
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 创建文件名，使用时间戳确保唯一性
            timestamp = int(time.time())
            file_path = os.path.join(output_dir, f"vrp_data_{timestamp}.pt")

            # 规范化路径
            file_path = os.path.normpath(file_path)

            # 提取坐标和需求
            num_lockers = len(active_lockers_info)
            if num_lockers == 0:
                logger.warning("No active lockers to create VRP problem")
                return None

            # 使用实际节点数量，不再添加虚拟节点
            actual_problem_size = num_lockers

            # 设置问题规模为实际节点数量
            logger.info(f"Setting problem_size to actual node count: {actual_problem_size}")
            self.env_params['problem_size'] = actual_problem_size

            # 创建问题数据
            depot_xy = torch.tensor([self.depot_coord], dtype=torch.float32).unsqueeze(0)  # [1, 1, 2]

            # 提取所有储物柜坐标和需求
            locker_ids = list(active_lockers_info.keys())  # 保存ID顺序
            locker_coords = []
            locker_demands = []

            for locker_id in locker_ids:
                info = active_lockers_info[locker_id]
                locker_coords.append(info['coord'])
                # 将需求转换为整数
                locker_demands.append(int(round(info['demand'])))

            logger.info(f"Prepared data for {num_lockers} real lockers with depot at {self.depot_coord}")

            # 转换为张量
            node_xy = torch.tensor([locker_coords], dtype=torch.float32)  # [1, actual_problem_size, 2]
            node_demand = torch.tensor([locker_demands], dtype=torch.float32)  # [1, actual_problem_size]

            # 使用卡车容量作为demand_scaler，确保DRL模型与实际容量匹配
            demand_scaler = self.truck_capacity
            logger.info(f"Using truck capacity {self.truck_capacity} as demand_scaler for consistency")

            # 保持原始卡车容量设置，不要被demand_scaler覆盖
            original_truck_capacity = self.truck_capacity
            logger.info(f"Keeping original truck capacity: {original_truck_capacity}, demand_scaler: {demand_scaler}")

            # 保存原始需求值用于后续计算
            original_demands = node_demand.clone()

            # 应用需求缩放
            node_demand = node_demand / float(demand_scaler)
            logger.info(f"Applied demand scaling with factor {demand_scaler}")

            # 归一化坐标到[0,1]范围 - 只考虑实际节点的范围
            real_coords = locker_coords
            max_x = max(max(coord[0] for coord in real_coords), self.depot_coord[0])
            max_y = max(max(coord[1] for coord in real_coords), self.depot_coord[1])
            min_x = min(min(coord[0] for coord in real_coords), self.depot_coord[0])
            min_y = min(min(coord[1] for coord in real_coords), self.depot_coord[1])

            # 避免除以零
            x_range = max(max_x - min_x, 1e-6)
            y_range = max(max_y - min_y, 1e-6)

            # 归一化
            depot_xy[0, 0, 0] = (depot_xy[0, 0, 0] - min_x) / x_range
            depot_xy[0, 0, 1] = (depot_xy[0, 0, 1] - min_y) / y_range

            # 归一化所有节点
            for i in range(actual_problem_size):
                node_xy[0, i, 0] = (node_xy[0, i, 0] - min_x) / x_range
                node_xy[0, i, 1] = (node_xy[0, i, 1] - min_y) / y_range

            logger.info(f"Final data shapes - Depot: {depot_xy.shape}, Nodes: {node_xy.shape}, Demands: {node_demand.shape}")

            # 保存为.pt文件
            torch.save({
                'depot_xy': depot_xy,
                'node_xy': node_xy,
                'node_demand': node_demand,
                'original_demands': original_demands,  # 保存原始需求值
                'demand_scaler': demand_scaler,  # 保存需求缩放因子
                'min_x': min_x, 'max_x': max_x,
                'min_y': min_y, 'max_y': max_y,
                'locker_ids': locker_ids,  # 保存ID顺序以便后续参考
                'actual_node_count': num_lockers,  # 保存实际节点数量
                'real_coords': locker_coords,  # 保存真实坐标
                'real_depot_coord': self.depot_coord,  # 保存真实仓库坐标
            }, file_path)

            # 验证文件是否成功保存
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                logger.info(f"VRP data saved to {file_path} (size: {file_size} bytes)")
            else:
                logger.error(f"Failed to save VRP data to {file_path}")
                return None

            # 如果不保留临时文件，清理旧文件
            if not self.keep_temp_files:
                self._cleanup_old_temp_files(output_dir)

            return file_path
        except Exception as e:
            logger.error(f"Error preparing VRP data: {str(e)}")
            return None

    def _solve_batch_individually(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]], return_route_info: bool = False):
        """
        回退方法：逐个求解每个场景
        """
        logger.info("Falling back to individual solving for each scenario")
        costs = []
        route_infos = []

        for i, scenario in enumerate(batch_active_lockers_info):
            logger.info(f"Solving scenario {i+1}/{len(batch_active_lockers_info)} individually")
            if return_route_info:
                cost, route_info = self.solve(scenario, return_route_info=True)
                costs.append(cost)
                route_infos.append(route_info)
            else:
                cost = self.solve(scenario, return_route_info=False)
                costs.append(cost)

        if return_route_info:
            return costs, route_infos
        return costs

    def _prepare_batch_vrp_data(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> str:
        """
        准备批量VRP数据并保存为.pt文件

        Args:
            batch_active_lockers_info: 批量活跃储物柜信息

        Returns:
            文件路径
        """
        try:
            # 确保output文件夹存在
            output_dir = os.path.abspath("./output")
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            # 创建文件名，使用时间戳确保唯一性
            timestamp = int(time.time())
            file_path = os.path.join(output_dir, f"batch_vrp_data_{timestamp}.pt")

            # 规范化路径
            file_path = os.path.normpath(file_path)

            batch_size = len(batch_active_lockers_info)
            first_scenario = batch_active_lockers_info[0]

            # 使用第一个场景确定问题规模和坐标
            num_lockers = len(first_scenario)
            if num_lockers == 0:
                logger.warning("No active lockers in first scenario")
                return None

            actual_problem_size = num_lockers
            logger.info(f"Setting problem_size to actual node count: {actual_problem_size} for batch_size: {batch_size}")
            self.env_params['problem_size'] = actual_problem_size

            # 提取储物柜ID和坐标（所有场景共享）
            locker_ids = list(first_scenario.keys())
            locker_coords = [first_scenario[locker_id]['coord'] for locker_id in locker_ids]

            # 为每个场景提取需求
            batch_demands = []
            for scenario in batch_active_lockers_info:
                scenario_demands = [int(round(scenario[locker_id]['demand'])) for locker_id in locker_ids]
                batch_demands.append(scenario_demands)

            logger.info(f"Prepared batch data for {num_lockers} lockers across {batch_size} scenarios")

            # 创建批量张量
            depot_xy = torch.tensor([[self.depot_coord]] * batch_size, dtype=torch.float32)  # [batch_size, 1, 2]
            node_xy = torch.tensor([locker_coords] * batch_size, dtype=torch.float32)  # [batch_size, actual_problem_size, 2]
            node_demand = torch.tensor(batch_demands, dtype=torch.float32)  # [batch_size, actual_problem_size]

            # 使用卡车容量作为demand_scaler，确保DRL模型与实际容量匹配
            demand_scaler = self.truck_capacity
            logger.info(f"Using truck capacity {self.truck_capacity} as demand_scaler for consistency")


            # 保持原始卡车容量设置，不要被demand_scaler覆盖
            original_truck_capacity = self.truck_capacity
            logger.info(f"Keeping original truck capacity: {original_truck_capacity}, demand_scaler: {demand_scaler}")

            # 保存原始需求值用于后续计算
            original_demands = node_demand.clone()

            # 应用需求缩放
            node_demand = node_demand / float(demand_scaler)
            logger.info(f"Applied demand scaling with factor {demand_scaler}")

            # 归一化坐标到[0,1]范围
            max_x = max(max(coord[0] for coord in locker_coords), self.depot_coord[0])
            max_y = max(max(coord[1] for coord in locker_coords), self.depot_coord[1])
            min_x = min(min(coord[0] for coord in locker_coords), self.depot_coord[0])
            min_y = min(min(coord[1] for coord in locker_coords), self.depot_coord[1])

            # 避免除以零
            x_range = max(max_x - min_x, 1e-6)
            y_range = max(max_y - min_y, 1e-6)

            # 归一化仓库坐标
            depot_xy[:, 0, 0] = (depot_xy[:, 0, 0] - min_x) / x_range
            depot_xy[:, 0, 1] = (depot_xy[:, 0, 1] - min_y) / y_range

            # 归一化所有节点坐标
            for i in range(actual_problem_size):
                node_xy[:, i, 0] = (node_xy[:, i, 0] - min_x) / x_range
                node_xy[:, i, 1] = (node_xy[:, i, 1] - min_y) / y_range

            logger.info(f"Final batch data shapes - Depot: {depot_xy.shape}, Nodes: {node_xy.shape}, Demands: {node_demand.shape}")

            # 保存为.pt文件
            torch.save({
                'depot_xy': depot_xy,
                'node_xy': node_xy,
                'node_demand': node_demand,
                'original_demands': original_demands,
                'demand_scaler': demand_scaler,
                'min_x': min_x, 'max_x': max_x,
                'min_y': min_y, 'max_y': max_y,
                'locker_ids': locker_ids,
                'actual_node_count': num_lockers,
                'real_coords': locker_coords,
                'real_depot_coord': self.depot_coord,
                'batch_size': batch_size,
            }, file_path)

            # 验证文件是否成功保存
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                logger.info(f"Batch VRP data saved to {file_path} (size: {file_size} bytes)")
            else:
                logger.error(f"Failed to save batch VRP data to {file_path}")
                return None

            # 如果不保留临时文件，清理旧文件
            if not self.keep_temp_files:
                self._cleanup_old_temp_files(output_dir)

            return file_path
        except Exception as e:
            logger.error(f"Error preparing batch VRP data: {str(e)}")
            return None

    def _cleanup_old_temp_files(self, output_dir: str):
        """
        清理旧的临时.pt文件，只保留最新的几个文件

        Args:
            output_dir: 输出目录路径
        """
        try:
            import glob

            # 查找所有vrp_data_*.pt文件
            pattern = os.path.join(output_dir, "vrp_data_*.pt")
            temp_files = glob.glob(pattern)

            if len(temp_files) <= self.max_temp_files:
                return  # 文件数量未超过限制，无需清理

            # 按修改时间排序，最新的在前
            temp_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)

            # 删除超出限制的旧文件
            files_to_delete = temp_files[self.max_temp_files:]
            for file_path in files_to_delete:
                try:
                    os.remove(file_path)
                    logger.info(f"Cleaned up old temp file: {file_path}")
                except Exception as e:
                    logger.warning(f"Failed to delete temp file {file_path}: {str(e)}")

            logger.info(f"Cleaned up {len(files_to_delete)} old temp files, kept {self.max_temp_files} recent files")

        except Exception as e:
            logger.warning(f"Error during temp file cleanup: {str(e)}")

    def _calculate_real_distance(self, route_array: np.ndarray, saved_data: Dict) -> float:
        """
        根据DRL路线和真实坐标计算实际距离

        Args:
            route_array: DRL求解器返回的路线数组
            saved_data: 保存的VRP数据，包含真实坐标信息

        Returns:
            实际的总距离（公里）
        """
        try:
            real_coords = saved_data.get('real_coords', [])
            real_depot_coord = saved_data.get('real_depot_coord', self.depot_coord)

            if not real_coords:
                logger.warning("No real coordinates found in saved data, falling back to range-based calculation")
                return None

            total_distance = 0.0
            current_pos = real_depot_coord  # 从仓库开始

            # 遍历路线数组
            for node_idx in route_array:
                if node_idx == 0:  # 返回仓库
                    # 计算从当前位置到仓库的距离
                    dist = math.sqrt((current_pos[0] - real_depot_coord[0])**2 +
                                   (current_pos[1] - real_depot_coord[1])**2)
                    total_distance += dist
                    current_pos = real_depot_coord
                    logger.debug(f"Returned to depot, distance: {dist:.2f}, total: {total_distance:.2f}")
                else:
                    # 访问储物柜
                    locker_idx = node_idx - 1  # DRL中节点索引从1开始，real_coords从0开始
                    if 0 <= locker_idx < len(real_coords):
                        locker_coord = real_coords[locker_idx]
                        # 计算从当前位置到储物柜的距离
                        dist = math.sqrt((current_pos[0] - locker_coord[0])**2 +
                                       (current_pos[1] - locker_coord[1])**2)
                        total_distance += dist
                        current_pos = locker_coord
                        logger.debug(f"Visited locker {node_idx} at {locker_coord}, distance: {dist:.2f}, total: {total_distance:.2f}")
                    else:
                        logger.warning(f"Invalid locker index {locker_idx} for route node {node_idx}")

            # 如果路线没有以仓库结束，添加返回仓库的距离
            if len(route_array) > 0 and route_array[-1] != 0:
                dist = math.sqrt((current_pos[0] - real_depot_coord[0])**2 +
                               (current_pos[1] - real_depot_coord[1])**2)
                total_distance += dist
                logger.debug(f"Final return to depot, distance: {dist:.2f}, total: {total_distance:.2f}")

            logger.info(f"=== 距离计算详情 ===")
            logger.info(f"路线数组: {route_array}")
            logger.info(f"仓库坐标: {real_depot_coord}")
            logger.info(f"储物柜坐标: {real_coords}")
            logger.info(f"计算得到的总距离: {total_distance:.2f} km")
            logger.info(f"==================")
            return total_distance

        except Exception as e:
            logger.error(f"Error calculating real distance: {str(e)}")
            return None

    def _print_detailed_routes(self, route_info):
        """
        按照指定格式打印详细路线信息

        Args:
            route_info: 路线信息字典
        """
        if not route_info:
            return

        routes = route_info.get('routes', [])
        if not routes:
            return

        print("详细路线:")
        for route in routes:
            vehicle_id = route.get('vehicle_id', 0)
            lockers = route.get('lockers', [])
            total_demand = route.get('total_demand', 0)
            locker_coords = route.get('locker_coords', [])
            truck_capacity = route_info.get('truck_capacity', 1)

            # 构建路线字符串
            if lockers:
                route_str = f"仓库 → {' → '.join(map(str, lockers))} → 仓库"
            else:
                route_str = "仓库 → 仓库"

            print(f"  卡车 {vehicle_id}:")
            print(f"    路线: {route_str}")
            print(f"    配送储物柜: {lockers}")
            print(f"    此路线上总需求量: {total_demand:.2f} 订单")

            # 显示储物柜坐标
            if locker_coords and lockers:
                coord_strs = []
                for i, coord in enumerate(locker_coords):
                    if i < len(lockers):
                        coord_strs.append(f"储物柜{lockers[i]}({coord[0]:.1f}, {coord[1]:.1f})")
                if coord_strs:
                    print(f"    储物柜坐标: {', '.join(coord_strs)}")

            # 计算容量利用率
            utilization = (total_demand / truck_capacity) * 100 if truck_capacity > 0 else 0
            print(f"    此路线上容量利用率: {utilization:.1f}% ({total_demand:.2f}/{truck_capacity})")

    def _delete_temp_file(self, file_path: str):
        """
        删除指定的临时文件

        Args:
            file_path: 要删除的文件路径
        """
        try:
            if file_path and os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Deleted temp file: {file_path}")
        except Exception as e:
            logger.warning(f"Failed to delete temp file {file_path}: {str(e)}")

    def solve(self, active_lockers_info: Dict[int, Dict[str, Any]], return_route_info: bool = False):
        """
        使用DRL求解器计算卡车运输成本。

        Args:
            active_lockers_info: 一个字典，键是储物柜ID，值是包含储物柜信息（如坐标'coord'和需求'demand'）的字典。
                                 例如: {
                                     locker_id_1: {'coord': (x1, y1), 'demand': d1},
                                     locker_id_2: {'coord': (x2, y2), 'demand': d2},
                                 }
            return_route_info: 是否返回路线信息，True=返回(成本, 路线信息)，False=只返回成本

        Returns:
            如果return_route_info=False: 该场景下的卡车运输总成本
            如果return_route_info=True: (总成本, 路线信息字典)
        """
        batch_result = self.solve_batch([active_lockers_info], return_route_info)
        if return_route_info:
            costs, route_infos = batch_result
            return costs[0], route_infos[0]
        else:
            return batch_result[0]

    def solve_batch(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]], return_route_info: bool = False):
        """
        批量使用DRL求解器计算卡车运输成本。

        Args:
            batch_active_lockers_info: 一个列表，每个元素是一个场景的储物柜信息字典
            return_route_info: 是否返回路线信息

        Returns:
            如果return_route_info=False: 成本列表
            如果return_route_info=True: (成本列表, 路线信息列表)
        """
        if not batch_active_lockers_info:
            logger.info("No batch active lockers provided, returning empty results")
            if return_route_info:
                return [], []
            return []

        batch_size = len(batch_active_lockers_info)
        logger.info(f"Starting batch DRL solving for {batch_size} scenarios")

        # 检查是否所有场景都有相同的储物柜配置
        first_scenario = batch_active_lockers_info[0]
        if not first_scenario:
            logger.info("First scenario has no active lockers, returning zero costs")
            zero_results = [0.0] * batch_size
            if return_route_info:
                return zero_results, [None] * batch_size
            return zero_results

        # 验证所有场景是否有相同的储物柜ID（位置固定，只有需求不同）
        first_locker_ids = set(first_scenario.keys())
        for i, scenario in enumerate(batch_active_lockers_info[1:], 1):
            if set(scenario.keys()) != first_locker_ids:
                logger.warning(f"Scenario {i} has different locker IDs than scenario 0, falling back to individual solving")
                return self._solve_batch_individually(batch_active_lockers_info, return_route_info)

        # 验证所有场景的储物柜坐标是否相同
        for locker_id in first_locker_ids:
            first_coord = first_scenario[locker_id]['coord']
            for i, scenario in enumerate(batch_active_lockers_info[1:], 1):
                if scenario[locker_id]['coord'] != first_coord:
                    logger.warning(f"Scenario {i} has different coordinates for locker {locker_id}, falling back to individual solving")
                    return self._solve_batch_individually(batch_active_lockers_info, return_route_info)

        try:
            # 检查数据格式并修复所有场景
            for scenario_idx, scenario in enumerate(batch_active_lockers_info):
                for locker_id, info in scenario.items():
                    # 确保coord是元组形式
                    if not isinstance(info['coord'], tuple) and isinstance(info['coord'], (list, np.ndarray)):
                        batch_active_lockers_info[scenario_idx][locker_id]['coord'] = tuple(info['coord'])

                    # 确保demand是数值类型
                    if not isinstance(info['demand'], (int, float)):
                        batch_active_lockers_info[scenario_idx][locker_id]['demand'] = float(info['demand'])

            # 准备批量VRP数据
            file_path = self._prepare_batch_vrp_data(batch_active_lockers_info)
            if not file_path:
                logger.error("Failed to prepare batch VRP data")
                zero_results = [0.0] * batch_size
                if return_route_info:
                    return zero_results, [None] * batch_size
                return zero_results

            # 更新测试参数中的文件名和批量大小
            self.tester_params['test_data_load']['filename'] = file_path
            self.tester_params['test_episodes'] = batch_size  # 需要处理的总场景数
            self.tester_params['test_batch_size'] = batch_size # 批量大小等于验证场景数，确保1轮处理完毕

            # 调整POMO大小，确保不超过problem_size+1
            problem_size = self.env_params['problem_size']

            # 对于非常小的问题（如只有1-2个节点），使用最小的POMO大小
            if problem_size <= 2:
                safe_pomo_size = 1  # 对于极小规模问题使用最小POMO
            else:
                safe_pomo_size = min(problem_size, 10)  # 限制在10以内

            self.env_params['pomo_size'] = safe_pomo_size
            logger.info(f"Adjusted pomo_size to {safe_pomo_size} based on problem_size {problem_size}")

            # 打印参数信息
            logger.info(f"Running batch DRL solver with problem_size={self.env_params['problem_size']}, pomo_size={self.env_params['pomo_size']}, batch_size={batch_size}")
            logger.info(f"DRL test configuration: test_episodes={batch_size}, test_batch_size={batch_size} (single round processing)")
            # 构建2-opt优化状态信息
            if self.tester_params['2opt_iterations'] == 0:
                opt_status = 'DISABLED for faster processing'
            else:
                opt_status = f'ENABLED ({self.tester_params["2opt_iterations"]} iterations)'
            logger.info(f"2-opt optimization: {opt_status}")

            # 初始化测试器
            tester = Tester(
                env_params=self.env_params,
                model_params=self.model_params,
                tester_params=self.tester_params
            )

            # 运行测试并获取批量结果
            routes, costs = tester.run(save_result=True)

            if not routes or not costs:
                logger.warning("No routes or costs returned from batch DRL solver")
                zero_results = [0.0] * batch_size
                if return_route_info:
                    return zero_results, [None] * batch_size
                return zero_results

            # 处理批量结果
            # routes和costs应该是列表，每个元素对应一个场景的结果
            if len(costs) != batch_size:
                logger.warning(f"Expected {batch_size} results but got {len(costs)}, falling back to individual solving")
                return self._solve_batch_individually(batch_active_lockers_info, return_route_info)

            # 为每个场景计算实际成本
            batch_costs = []
            batch_route_infos = []

            # 加载原始数据以计算实际距离
            if not os.path.exists(file_path):
                logger.error(f"Batch VRP data file not found: {file_path}")
                logger.error(f"Current working directory: {os.getcwd()}")
                logger.error(f"Output directory exists: {os.path.exists(os.path.dirname(file_path))}")
                raise FileNotFoundError(f"Batch VRP data file not found: {file_path}")

            try:
                saved_data = torch.load(file_path)
                demand_scaler = saved_data.get('demand_scaler', 30)
                logger.info(f"Successfully loaded batch VRP data from {file_path}")
            except Exception as e:
                logger.error(f"Failed to load batch VRP data from {file_path}: {str(e)}")
                raise

            # 为每个场景处理结果
            for scenario_idx in range(batch_size):
                scenario_info = batch_active_lockers_info[scenario_idx]
                scenario_route = routes[scenario_idx] if scenario_idx < len(routes) else None
                scenario_cost = costs[scenario_idx] if scenario_idx < len(costs) else 0.0

                # 计算该场景的实际成本
                actual_cost, route_info = self._calculate_scenario_cost(
                    scenario_info, scenario_route, scenario_cost, saved_data, demand_scaler, return_route_info
                )

                batch_costs.append(actual_cost)
                if return_route_info:
                    batch_route_infos.append(route_info)

            logger.info(f"Batch DRL solving completed for {batch_size} scenarios")

            # 根据设置决定是否删除临时文件
            if not self.keep_temp_files:
                self._delete_temp_file(file_path)
            else:
                logger.info(f"Batch VRP data file kept at: {file_path}")

            if return_route_info:
                return batch_costs, batch_route_infos
            return batch_costs

        except Exception as e:
            logger.error(f"Error in batch DRL CVRP solver: {str(e)}")
            # 如果出错且创建了临时文件，尝试清理
            if 'file_path' in locals() and not self.keep_temp_files:
                self._delete_temp_file(file_path)
            # 如果出错，返回高成本作为惩罚
            zero_results = [9999999] * batch_size
            if return_route_info:
                return zero_results, [None] * batch_size
            return zero_results

    def _calculate_scenario_cost(self, scenario_info, scenario_route, scenario_cost, saved_data, demand_scaler, return_route_info=False):
        """
        计算单个场景的实际成本

        Args:
            scenario_info: 该场景的储物柜信息
            scenario_route: DRL返回的路线
            scenario_cost: DRL返回的成本
            saved_data: 保存的VRP数据
            demand_scaler: 需求缩放因子
            return_route_info: 是否返回路线信息

        Returns:
            (actual_cost, route_info) 或 actual_cost
        """
        try:
            # 计算实际距离
            if scenario_route is not None:
                route_array = scenario_route.cpu().numpy() if hasattr(scenario_route, 'cpu') else scenario_route
                actual_distance = self._calculate_real_distance(route_array, saved_data)
            else:
                actual_distance = 0.0

            # 如果真实距离计算失败，使用备用方法
            if actual_distance is None or actual_distance <= 0:
                logger.warning("Real distance calculation failed for scenario, using fallback")
                actual_distance = scenario_cost * 100  # 简单的备用计算

            # 计算需要的卡车数量
            total_demand = sum(int(round(info['demand'])) for info in scenario_info.values())

            # 使用原始设置的卡车容量，而不是demand_scaler
            truck_capacity = self.truck_capacity

            # 根据需求计算的卡车数量
            demand_trucks = math.ceil(total_demand / truck_capacity) if truck_capacity > 0 else 1

            # 分析路线中的卡车数量
            route_trucks = 1  # 默认至少1辆卡车
            if scenario_route is not None:
                route_array = scenario_route.cpu().numpy() if hasattr(scenario_route, 'cpu') else scenario_route
                if len(route_array) > 0:
                    # 计算路线中的卡车数量（通过计算从仓库出发的次数）
                    depot_visits = np.where(route_array == 0)[0]

                    # 处理路线数组中可能的填充问题
                    if len(depot_visits) > 2:
                        # 移除末尾连续的仓库访问点
                        last_nonzero_idx = len(route_array) - 1
                        while last_nonzero_idx > 0 and route_array[last_nonzero_idx] == 0:
                            last_nonzero_idx -= 1
                        valid_depot_visits = depot_visits[depot_visits <= last_nonzero_idx + 1]
                        depot_visits = valid_depot_visits

                    if len(depot_visits) <= 2:
                        route_trucks = 1
                    else:
                        route_trucks = len(depot_visits) - 1

                    route_trucks = max(1, route_trucks)

            # 决定最终使用的卡车数量
            # 优先考虑需求约束：如果总需求可以用更少卡车满足，就使用更少的卡车
            # 这避免了DRL路线解析错误导致的过多卡车使用
            if total_demand <= truck_capacity:
                # 总需求可以用1辆卡车满足，强制使用1辆卡车
                num_trucks = 1
                logger.info(f"总需求 {total_demand} ≤ 卡车容量 {truck_capacity}，强制使用1辆卡车")

                # 重新计算距离：使用TSP最短路径而不是DRL的多路线
                if len(scenario_info) > 1:
                    # 计算访问所有储物柜的最短路径
                    coords = [self.depot_coord] + [info['coord'] for info in scenario_info.values()]
                    tsp_distance = self._calculate_tsp_distance(coords)
                    actual_distance = tsp_distance
                    logger.info(f"使用TSP计算的单卡车路线距离: {actual_distance:.2f} km")
            else:
                # 需要多辆卡车，使用需求计算的结果
                num_trucks = demand_trucks
                logger.info(f"总需求 {total_demand} > 卡车容量 {truck_capacity}，使用 {demand_trucks} 辆卡车")

            # 计算总成本
            fixed_cost = num_trucks * self.truck_fixed_cost
            distance_cost = actual_distance * self.truck_km_cost
            total_cost = fixed_cost + distance_cost

            # 详细调试信息
            logger.info(f"=== DRL成本计算详情 ===")
            logger.info(f"总需求: {total_demand}")
            logger.info(f"卡车容量: {truck_capacity}")
            logger.info(f"需求计算卡车数: {demand_trucks}")
            logger.info(f"路线计算卡车数: {route_trucks}")
            logger.info(f"最终卡车数: {num_trucks}")
            logger.info(f"实际距离: {actual_distance:.2f} km")
            logger.info(f"固定成本: {fixed_cost:.2f} (= {num_trucks} × {self.truck_fixed_cost})")
            logger.info(f"距离成本: {distance_cost:.2f} (= {actual_distance:.2f} × {self.truck_km_cost})")
            logger.info(f"总成本: {total_cost:.2f}")
            logger.info(f"========================")

            # 构建路线信息（如果需要）
            route_info = None
            if return_route_info and scenario_route is not None:
                route_info = self._parse_route_info(scenario_route, saved_data, scenario_info, num_trucks, actual_distance, fixed_cost, distance_cost)

            return total_cost, route_info

        except Exception as e:
            logger.error(f"Error calculating scenario cost: {str(e)}")
            return 9999999, None

    def _calculate_tsp_distance(self, coords):
        """
        计算访问所有坐标点的近似最短路径距离（简化TSP）
        使用最近邻算法作为快速近似
        """
        if len(coords) <= 1:
            return 0.0

        if len(coords) == 2:
            # 只有仓库和一个储物柜
            return 2 * math.sqrt((coords[0][0] - coords[1][0])**2 + (coords[0][1] - coords[1][1])**2)

        # 最近邻算法
        unvisited = list(range(1, len(coords)))  # 除了起点（仓库）外的所有点
        current = 0  # 从仓库开始
        total_distance = 0.0

        while unvisited:
            # 找到最近的未访问点
            nearest_idx = min(unvisited,
                             key=lambda i: math.sqrt((coords[current][0] - coords[i][0])**2 +
                                                   (coords[current][1] - coords[i][1])**2))

            # 计算到最近点的距离
            distance = math.sqrt((coords[current][0] - coords[nearest_idx][0])**2 +
                               (coords[current][1] - coords[nearest_idx][1])**2)
            total_distance += distance

            # 移动到最近点
            current = nearest_idx
            unvisited.remove(nearest_idx)

        # 返回仓库
        return_distance = math.sqrt((coords[current][0] - coords[0][0])**2 +
                                  (coords[current][1] - coords[0][1])**2)
        total_distance += return_distance

        return total_distance

    def _parse_route_info(self, best_route, saved_data, active_lockers_info, num_trucks, actual_distance, fixed_cost, distance_cost):
        """
        解析DRL路线信息，构建可读的路线描述

        Args:
            best_route: DRL求解器返回的最佳路线张量
            saved_data: 保存的VRP数据
            active_lockers_info: 活跃储物柜信息
            num_trucks: 卡车数量
            actual_distance: 实际距离
            fixed_cost: 固定成本
            distance_cost: 距离成本

        Returns:
            路线信息字典
        """
        try:
            # 获取路线数组
            route_array = best_route.cpu().numpy()
            locker_ids = saved_data.get('locker_ids', list(active_lockers_info.keys()))

            # 解析路线：0表示仓库，其他数字表示储物柜索引
            # DRL路线格式通常是：[0, customer1, customer2, ..., 0, customer3, ..., 0]
            # 我们需要根据仓库访问点来分割路线

            # 找出所有仓库访问点
            depot_indices = np.where(route_array == 0)[0]

            routes = []

            # 如果有多个仓库访问点，说明有多条路线
            if len(depot_indices) > 1:
                for i in range(len(depot_indices) - 1):
                    start_idx = depot_indices[i]
                    end_idx = depot_indices[i + 1]

                    # 提取这段路线中的储物柜（排除仓库）
                    route_segment = route_array[start_idx + 1:end_idx]
                    current_route = []

                    for node in route_segment:
                        if node != 0 and 0 <= node-1 < len(locker_ids):
                            locker_id = locker_ids[node-1]
                            current_route.append(locker_id)

                    if current_route:  # 只添加非空路线
                        routes.append(current_route)
            else:
                # 只有一个仓库访问点或没有，说明是单一路线
                current_route = []
                for node in route_array:
                    if node != 0 and 0 <= node-1 < len(locker_ids):
                        locker_id = locker_ids[node-1]
                        current_route.append(locker_id)

                if current_route:
                    routes.append(current_route)

            # 构建路线信息
            route_details = []

            # 处理路线数量与计算的卡车数量不一致的情况
            if len(routes) > num_trucks:
                # 如果解析出的路线数量超过计算的卡车数量，合并路线
                logger.warning(f"Parsed {len(routes)} routes but calculated {num_trucks} trucks, merging routes")
                # 简单合并：将所有储物柜放在一条路线中
                all_lockers = []
                for route in routes:
                    all_lockers.extend(route)
                if all_lockers:
                    routes = [all_lockers]
            elif len(routes) < num_trucks and routes:
                # 如果解析出的路线数量少于计算的卡车数量
                if num_trucks > 1 and len(routes) == 1:
                    # 特殊情况：DRL建议多辆卡车但返回单一路线
                    # 这通常发生在DRL为了距离优化建议分割路线的情况
                    logger.info(f"DRL suggested {num_trucks} trucks but returned 1 route - displaying actual DRL route")
                    # 保持原始路线，但在输出中说明这是DRL的实际解决方案
                else:
                    logger.warning(f"Parsed {len(routes)} routes but calculated {num_trucks} trucks")

            for i, route in enumerate(routes):
                if route:  # 只处理非空路线
                    route_demand = sum(active_lockers_info.get(locker_id, {}).get('demand', 0) for locker_id in route)
                    locker_coords = [active_lockers_info.get(locker_id, {}).get('coord', (0, 0)) for locker_id in route]

                    # 计算这条路线的距离（使用真实坐标）
                    route_distance = 0.0
                    if locker_coords:
                        current_pos = self.depot_coord
                        for coord in locker_coords:
                            dist = math.sqrt((current_pos[0] - coord[0])**2 + (current_pos[1] - coord[1])**2)
                            route_distance += dist
                            current_pos = coord
                        # 返回仓库的距离
                        dist = math.sqrt((current_pos[0] - self.depot_coord[0])**2 + (current_pos[1] - self.depot_coord[1])**2)
                        route_distance += dist

                    route_info = {
                        'vehicle_id': i + 1,
                        'lockers': route,
                        'total_demand': route_demand,
                        'locker_coords': locker_coords,
                        'route_distance': route_distance
                    }
                    route_details.append(route_info)

            return {
                'num_trucks': num_trucks,
                'total_distance': actual_distance,
                'fixed_cost': fixed_cost,
                'distance_cost': distance_cost,
                'routes': route_details,
                'depot_coord': self.depot_coord,
                'truck_capacity': self.truck_capacity
            }

        except Exception as e:
            logger.error(f"Error parsing route info: {str(e)}")
            return {
                'num_trucks': num_trucks,
                'total_distance': actual_distance,
                'fixed_cost': fixed_cost,
                'distance_cost': distance_cost,
                'routes': [],
                'depot_coord': self.depot_coord,
                'truck_capacity': self.truck_capacity,
                'error': str(e)
            }