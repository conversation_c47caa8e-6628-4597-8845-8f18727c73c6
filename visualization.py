import matplotlib.pyplot as plt
import random
from typing import Dict, List

plt.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体
def visualize_solution(solution: Dict, customer_coords: Dict, site_coords: Dict, title: str = "Drone Delivery Network Plan"):
    """
    可视化无人机配送网络解决方案

    Args:
        solution: 优化问题的解决方案
        customer_coords: 客户坐标字典 {客户ID: (x, y)}
        site_coords: 储物柜坐标字典 {储物柜ID: (x, y)}
        title: 图表标题
    """
    if not solution:
        print("No solution to visualize.")
        return
    if not customer_coords or not site_coords:
        print("Customer or site coordinates not provided for visualization.")
        # 尝试生成随机坐标
        if not customer_coords:
             customer_coords = {c: (random.uniform(0,20), random.uniform(0,20)) for c in solution['customer_assignments'].keys()}
        if not site_coords:
             site_coords = {s: (random.uniform(3,17), random.uniform(3,17)) for s in solution['selected_lockers'].keys()}
        print("Generated random coordinates for visualization.")

    plt.figure(figsize=(14, 10))

    # 标签控制变量
    drone_delivery_label_done = False
    self_pickup_label_done = False
    unassigned_label_done = False
    unused_site_labels_done = False
    selected_site_labels_done = False

    # 获取客户服务模式信息
    customer_service_modes = solution.get('customer_service_modes', {})
    customer_assignments = solution.get('customer_assignments', {})
    unassigned_customers = solution.get('unassigned_customers', [])

    # 绘制客户点 - 按服务模式区分
    for cust_id, coord in customer_coords.items():
        if cust_id in unassigned_customers:
            # 未分配客户 - 灰色
            plt.scatter(coord[0], coord[1], c='gray', marker='x', s=100, alpha=0.7,
                       label='未分配客户' if not unassigned_label_done else "")
            plt.text(coord[0] + 0.1, coord[1] + 0.1, f"C{cust_id}\n(未分配)", fontsize=8, color='gray')
            unassigned_label_done = True
        elif cust_id in customer_assignments:
            service_mode = customer_service_modes.get(cust_id, 0)  # 0=无人机配送, 1=自主取件
            if service_mode == 1:
                # 自主取件客户 - 绿色圆形
                plt.scatter(coord[0], coord[1], c='green', marker='o', s=80,
                           label='自主取件客户' if not self_pickup_label_done else "")
                plt.text(coord[0] + 0.1, coord[1] + 0.1, f"C{cust_id}\n(自取)", fontsize=8, color='green', fontweight='bold')
                self_pickup_label_done = True
            else:
                # 无人机配送客户 - 蓝色圆形
                plt.scatter(coord[0], coord[1], c='blue', marker='o', s=80,
                           label='无人机配送客户' if not drone_delivery_label_done else "")
                plt.text(coord[0] + 0.1, coord[1] + 0.1, f"C{cust_id}\n(配送)", fontsize=8, color='blue')
                drone_delivery_label_done = True

    # 绘制储物柜点
    for site_id, coord in site_coords.items():
        if site_id not in solution['selected_lockers']:
            plt.scatter(coord[0], coord[1], c='gray', marker='s', s=100, alpha=0.5, label='Unused Sites' if not unused_site_labels_done else "")
            plt.text(coord[0] + 0.1, coord[1] + 0.1, f"S{site_id}", fontsize=9, color='gray')
            unused_site_labels_done = True
        else:
            num_drones = solution['drone_allocations'].get(site_id, 0)
            plt.scatter(coord[0], coord[1], c='red', marker='s', s=200, label='Selected Lockers' if not selected_site_labels_done else "")
            plt.text(coord[0] + 0.1, coord[1] + 0.1, f"S{site_id}\n(Drones: {num_drones})", fontsize=10, color='red', fontweight='bold')
            selected_site_labels_done = True

    # 绘制客户到储物柜的分配连线 - 支持多储物柜分配，用不同颜色区分
    # 定义颜色列表用于区分不同的储物柜连线
    connection_colors = ['blue', 'orange', 'purple', 'brown', 'pink', 'gray', 'olive', 'cyan']

    # 处理多储物柜分配的情况
    for cust_id, assigned_sites in solution['customer_assignments'].items():
        if cust_id not in customer_coords:
            continue

        cust_coord = customer_coords[cust_id]
        service_mode = customer_service_modes.get(cust_id, 0)

        # 如果assigned_sites是单个储物柜ID（兼容旧格式）
        if not isinstance(assigned_sites, list):
            assigned_sites = [assigned_sites]

        # 为每个分配的储物柜绘制连线
        for idx, assigned_site_id in enumerate(assigned_sites):
            if assigned_site_id not in site_coords:
                continue

            site_coord = site_coords[assigned_site_id]

            # 选择颜色：如果只有一个储物柜，使用蓝色；如果多个，使用不同颜色
            if len(assigned_sites) == 1:
                line_color = 'blue'
                line_style = '--'
                line_alpha = 0.7
            else:
                line_color = connection_colors[idx % len(connection_colors)]
                line_style = '-'  # 实线以区分多连接
                line_alpha = 0.8

            if service_mode == 1:
                # 自主取件客户 - 绿色系虚线（步行路径）
                plt.plot([cust_coord[0], site_coord[0]], [cust_coord[1], site_coord[1]],
                        color='green', linestyle=':', linewidth=2, alpha=0.8,
                        label='步行路径' if 'steps_path_label' not in locals() else "")
                locals()['steps_path_label'] = True
            else:
                # 无人机配送客户 - 根据储物柜数量选择颜色和样式
                if len(assigned_sites) == 1:
                    plt.plot([cust_coord[0], site_coord[0]], [cust_coord[1], site_coord[1]],
                            color=line_color, linestyle=line_style, linewidth=1.5, alpha=line_alpha,
                            label='无人机飞行路径' if 'drone_path_label' not in locals() else "")
                    locals()['drone_path_label'] = True
                else:
                    # 多储物柜连接，添加特殊标签
                    plt.plot([cust_coord[0], site_coord[0]], [cust_coord[1], site_coord[1]],
                            color=line_color, linestyle=line_style, linewidth=2, alpha=line_alpha,
                            label='多储物柜服务连线' if 'multi_locker_label' not in locals() else "")
                    locals()['multi_locker_label'] = True

    plt.xlabel("X坐标 (km)")
    plt.ylabel("Y坐标 (km)")

    # 添加服务模式统计信息到标题
    drone_count = sum(1 for mode in customer_service_modes.values() if mode == 0)
    pickup_count = sum(1 for mode in customer_service_modes.values() if mode == 1)
    unassigned_count = len(unassigned_customers)

    title_with_stats = f"{title}\n总成本: {solution['objective_value']:.2f} | 无人机配送: {drone_count}客户| 未分配: {unassigned_count}客户"
    plt.title(title_with_stats, fontsize=12)

    # 创建图例
    handles, labels = plt.gca().get_legend_handles_labels()
    by_label = dict(zip(labels, handles))
    plt.legend(by_label.values(), by_label.keys(), loc='upper left', bbox_to_anchor=(1.02, 1))

    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    return plt

def save_visualization(plt, filename="solution_visualization.png"):
    """
    保存可视化结果到文件
    
    Args:
        plt: matplotlib.pyplot对象
        filename: 保存的文件名
    """
    plt.savefig(filename)
    print(f"Visualization saved to {filename}")
    
def show_visualization(plt):
    """
    显示可视化结果
    
    Args:
        plt: matplotlib.pyplot对象
    """
    plt.show()

def analyze_solution_with_scenarios(solution: Dict, demand_scenarios: List[Dict], 
                                   customer_assignments: Dict, distance_matrix: Dict,
                                   drone_speed: float, loading_time: float, 
                                   transport_unit_cost: float, locker_fixed_cost: Dict,
                                   drone_cost: Dict, drone_allocations: Dict,
                                   site_coords: Dict, depot_coord: tuple,
                                   truck_capacity: float, truck_fixed_cost: float,
                                   truck_km_cost: float, H_drone_working_hours_per_day: float):
    """
    使用多个需求场景分析解决方案的稳健性
    
    Args:
        solution: 优化问题的解决方案
        demand_scenarios: 需求场景列表
        customer_assignments: 客户到储物柜的分配
        distance_matrix: 距离矩阵
        drone_speed: 无人机速度
        loading_time: 装载时间
        transport_unit_cost: 运输单位成本
        locker_fixed_cost: 储物柜固定成本
        drone_cost: 无人机成本
        drone_allocations: 无人机分配
        site_coords: 储物柜坐标
        depot_coord: 仓库坐标
        truck_capacity: 卡车容量
        truck_fixed_cost: 卡车固定成本
        truck_km_cost: 卡车公里成本
        H_drone_working_hours_per_day: 无人机每日工作小时数
    """
    import math
    from drl import DRL_CVRP_Solver
    from saa_utils import get_locker_demands_in_scenario
    
    if not solution:
        print("没有可分析的解决方案。")
        return
    
    # 获取选择的储物柜
    selected_lockers = list(solution['selected_lockers'].keys())
    
    print(f"\n======= 在{len(demand_scenarios)}个随机场景下分析解决方案的稳健性 =======")
    
    # 分析每个场景
    scenario_results = []
    for i, scenario in enumerate(demand_scenarios):
        print(f"\n--- 场景 {i+1} ---")
        
        # 计算每个储物柜在该场景下的总需求
        locker_demands = get_locker_demands_in_scenario(
            selected_lockers, customer_assignments, scenario
        )
        
        # 获取客户服务模式信息
        customer_service_modes = solution.get('customer_service_modes', {})

        # 打印每个储物柜的需求情况
        print("储物柜需求:")
        for locker_id, demand in locker_demands.items():
            capacity = drone_allocations[locker_id] * H_drone_working_hours_per_day
            utilization = 0
            drone_customers = 0
            pickup_customers = 0

            for customer_id, assigned_locker in customer_assignments.items():
                if assigned_locker == locker_id:
                    service_mode = customer_service_modes.get(customer_id, 0)
                    if service_mode == 0:  # 无人机配送
                        drone_customers += 1
                        utilization += scenario[customer_id] * ((2 * distance_matrix[customer_id, locker_id] / drone_speed) + loading_time)
                    else:  # 自主取件
                        pickup_customers += 1

            print(f"  储物柜 {locker_id}: 总需求={demand:.2f}, 无人机客户={drone_customers}, 自取客户={pickup_customers}")
            print(f"    无人机利用率={utilization/capacity*100:.2f}% ({utilization:.2f}/{capacity:.2f}小时)")

            # 检查容量约束
            if utilization > capacity:
                print(f"    警告: 储物柜 {locker_id} 无人机容量超限!")
        
        # 计算这个场景下的卡车运输成本
        drl_solver = DRL_CVRP_Solver(
            depot_coord=depot_coord,
            truck_capacity=truck_capacity,
            truck_fixed_cost=truck_fixed_cost,
            truck_km_cost=truck_km_cost,
            keep_temp_files=False,  # 可视化不保留临时文件
            max_temp_files=1       # 可视化最多保留1个临时文件
        )
        
        # 准备活跃储物柜信息
        active_lockers_info = {}
        for locker_id in selected_lockers:
            if locker_id in site_coords:
                active_lockers_info[locker_id] = {
                    'coord': site_coords[locker_id],
                    'demand': locker_demands[locker_id]
                }
        
        # 求解CVRP并获取卡车成本
        truck_cost = drl_solver.solve(active_lockers_info)
        
        # 计算此场景下的总成本
        locker_cost = sum(locker_fixed_cost[j] for j in selected_lockers)
        drone_cost_total = sum(drone_cost * drone_allocations[j] for j in selected_lockers)
        
        # 计算运输成本 - 只包含无人机配送客户
        transport_cost = 0
        for i, j in customer_assignments.items():
            service_mode = customer_service_modes.get(i, 0)
            if service_mode == 0:  # 只有无人机配送客户产生运输成本
                transport_cost += transport_unit_cost * 2 * distance_matrix[i, j] * scenario[i]
        
        total_cost = locker_cost + drone_cost_total + transport_cost + truck_cost
        
        print(f"场景 {i+1} 总成本: {total_cost:.2f}")
        print(f"  - 储物柜固定成本: {locker_cost:.2f}")
        print(f"  - 无人机成本: {drone_cost_total:.2f}")
        print(f"  - 无人机运输成本: {transport_cost:.2f}")
        print(f"  - 卡车运输成本: {truck_cost:.2f}")
        
        scenario_results.append(total_cost)
    
    # 显示统计信息
    avg_cost = sum(scenario_results) / len(scenario_results)
    min_cost = min(scenario_results)
    max_cost = max(scenario_results)
    std_dev = math.sqrt(sum((x - avg_cost) ** 2 for x in scenario_results) / len(scenario_results))
    
    print("\n===== 场景分析总结 =====")
    print(f"平均总成本: {avg_cost:.2f}")
    print(f"最小总成本: {min_cost:.2f}")
    print(f"最大总成本: {max_cost:.2f}")
    print(f"标准差: {std_dev:.2f}")
    print(f"最差/最佳成本比: {max_cost/min_cost:.2f}") 