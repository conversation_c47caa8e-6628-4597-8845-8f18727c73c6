#!/usr/bin/env python3
"""
内存使用分析工具
分析ALNS算法中各种缓存的内存占用情况
"""

import sys
import psutil
import os

def estimate_cache_memory_usage():
    """估算缓存内存使用量"""
    
    print("=" * 60)
    print("ALNS算法缓存内存使用分析")
    print("=" * 60)
    
    # 缓存配置
    caches = {
        "解缓存 (Solution Cache)": {
            "max_entries": 5000,
            "entry_size_bytes": 1024,  # 估算每个解条目1KB
            "description": "存储解的目标函数值"
        },
        "DRL缓存 (DRL Cache)": {
            "max_entries": 2000,
            "entry_size_bytes": 512,   # 估算每个DRL条目512B
            "description": "存储卡车路径成本"
        },
        "批量缓存 (Batch Cache)": {
            "max_entries": 1000,
            "entry_size_bytes": 2048,  # 估算每个批量条目2KB
            "description": "存储批量求解结果"
        },
        "分配缓存 (Assignment Cache)": {
            "max_entries": 3000,
            "entry_size_bytes": 1536,  # 估算每个分配条目1.5KB
            "description": "存储客户分配方案"
        }
    }
    
    total_memory_mb = 0
    
    for cache_name, config in caches.items():
        memory_bytes = config["max_entries"] * config["entry_size_bytes"]
        memory_mb = memory_bytes / (1024 * 1024)
        total_memory_mb += memory_mb
        
        print(f"\n{cache_name}:")
        print(f"  最大条目数: {config['max_entries']:,}")
        print(f"  每条目大小: {config['entry_size_bytes']} 字节")
        print(f"  最大内存占用: {memory_mb:.2f} MB")
        print(f"  用途: {config['description']}")
    
    print(f"\n{'='*60}")
    print(f"总估算内存占用: {total_memory_mb:.2f} MB")
    print(f"{'='*60}")
    
    return total_memory_mb

def get_current_memory_usage():
    """获取当前进程内存使用情况"""
    
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    
    print(f"\n当前进程内存使用情况:")
    print(f"  RSS (物理内存): {memory_info.rss / (1024*1024):.2f} MB")
    print(f"  VMS (虚拟内存): {memory_info.vms / (1024*1024):.2f} MB")
    
    # 系统内存使用情况
    system_memory = psutil.virtual_memory()
    print(f"\n系统内存使用情况:")
    print(f"  总内存: {system_memory.total / (1024*1024*1024):.2f} GB")
    print(f"  已使用: {system_memory.used / (1024*1024*1024):.2f} GB ({system_memory.percent:.1f}%)")
    print(f"  可用内存: {system_memory.available / (1024*1024*1024):.2f} GB")

def analyze_memory_growth():
    """分析内存增长模式"""
    
    print(f"\n内存增长分析:")
    print(f"  SAA算法特点:")
    print(f"    - 多次复制 (最多10次)")
    print(f"    - 每次复制生成40个训练场景")
    print(f"    - 2000个验证场景 (所有复制共用)")
    print(f"    - ALNS迭代过程中不断生成新解")
    
    print(f"\n  潜在内存增长点:")
    print(f"    1. 缓存累积: 随着迭代增加，缓存条目不断增加")
    print(f"    2. 场景数据: 大量需求场景数据存储")
    print(f"    3. 解空间探索: ALNS生成的中间解")
    print(f"    4. DRL求解器: 深度学习模型可能占用GPU/CPU内存")

def provide_memory_optimization_suggestions():
    """提供内存优化建议"""
    
    print(f"\n内存优化建议:")
    print(f"  1. 减少缓存大小:")
    print(f"     - 将解缓存从5000减少到2000")
    print(f"     - 将DRL缓存从2000减少到1000")
    print(f"     - 将分配缓存从3000减少到1500")
    
    print(f"\n  2. 改进缓存策略:")
    print(f"     - 使用LRU (最近最少使用) 淘汰策略")
    print(f"     - 定期清理缓存而不是等到满了再清理")
    print(f"     - 根据内存使用情况动态调整缓存大小")
    
    print(f"\n  3. 数据结构优化:")
    print(f"     - 使用更紧凑的数据表示")
    print(f"     - 压缩存储不常用的缓存条目")
    print(f"     - 使用弱引用避免循环引用")
    
    print(f"\n  4. 算法参数调整:")
    print(f"     - 减少SAA验证样本数量 (从2000到1000)")
    print(f"     - 减少ALNS迭代次数")
    print(f"     - 使用更小的问题规模进行测试")

if __name__ == "__main__":
    estimate_cache_memory_usage()
    get_current_memory_usage()
    analyze_memory_growth()
    provide_memory_optimization_suggestions()
