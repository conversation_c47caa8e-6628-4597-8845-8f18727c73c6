#!/usr/bin/env python3
"""
定期维护脚本
用于定期清理缓存和监控磁盘空间
"""

import os
import shutil
import subprocess
import sys
from datetime import datetime

def check_disk_space():
    """检查磁盘空间"""
    try:
        if os.name == 'nt':  # Windows
            disk_usage = shutil.disk_usage('C:')
        else:
            disk_usage = shutil.disk_usage('/')
        
        free_gb = disk_usage.free / (1024**3)
        total_gb = disk_usage.total / (1024**3)
        used_percent = ((disk_usage.total - disk_usage.free) / disk_usage.total) * 100
        
        print(f"磁盘空间状态:")
        print(f"  可用空间: {free_gb:.1f} GB")
        print(f"  总容量: {total_gb:.1f} GB")
        print(f"  使用率: {used_percent:.1f}%")
        
        # 如果可用空间少于2GB，发出警告
        if free_gb < 2.0:
            print("⚠️  警告: 磁盘空间不足，建议立即清理！")
            return True
        elif free_gb < 5.0:
            print("⚠️  注意: 磁盘空间较少，建议清理缓存")
            return True
        else:
            print("✅ 磁盘空间充足")
            return False
            
    except Exception as e:
        print(f"检查磁盘空间失败: {e}")
        return False

def quick_cleanup():
    """快速清理"""
    print("\n执行快速清理...")
    
    try:
        # 运行清理脚本
        result = subprocess.run([sys.executable, "cleanup_cache.py"], 
                              capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 项目缓存清理完成")
        else:
            print(f"⚠️  项目缓存清理出现问题: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⚠️  清理超时")
    except Exception as e:
        print(f"⚠️  清理失败: {e}")

def system_cleanup():
    """系统级清理"""
    print("\n执行系统级清理...")
    
    try:
        # 运行系统清理脚本
        result = subprocess.run([sys.executable, "system_cleanup.py"], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 系统缓存清理完成")
        else:
            print(f"⚠️  系统缓存清理出现问题: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⚠️  系统清理超时")
    except Exception as e:
        print(f"⚠️  系统清理失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print(f"系统维护 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # 检查磁盘空间
    need_cleanup = check_disk_space()
    
    if need_cleanup:
        print("\n磁盘空间不足，开始清理...")
        
        # 询问用户是否进行清理
        response = input("\n是否执行清理? (y/n): ").lower()
        if response in ['y', 'yes']:
            quick_cleanup()
            
            # 如果还是空间不足，进行系统级清理
            if check_disk_space():
                response = input("\n是否执行系统级清理? (y/n): ").lower()
                if response in ['y', 'yes']:
                    system_cleanup()
    
    print("\n维护完成！")
    print("\n建议:")
    print("1. 每周运行一次此维护脚本")
    print("2. 运行ALNS程序后及时清理缓存")
    print("3. 监控大型数据文件的存储")
    print("4. 考虑将数据文件存储到其他盘符")

if __name__ == "__main__":
    main()
