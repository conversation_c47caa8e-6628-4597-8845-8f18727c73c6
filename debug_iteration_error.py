#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试iteration未定义错误
"""

import sys
import os
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_iteration_error():
    """
    调试iteration参数传递问题
    """
    print("=" * 60)
    print("调试iteration未定义错误")
    print("=" * 60)
    
    try:
        # 导入模块
        print("正在导入3.py模块...")
        
        import importlib.util
        spec = importlib.util.spec_from_file_location("module3", "3.py")
        module3 = importlib.util.module_from_spec(spec)
        
        # 设置较小的参数以快速重现错误
        module3.SAA_MAX_REPLICATIONS_M = 1
        module3.SAA_SAMPLES_K = 3
        module3.SAA_SAMPLES_K_PRIME = 5
        module3.USE_INTELLIGENT_EVALUATION = True
        
        spec.loader.exec_module(module3)
        
        print("✅ 模块导入成功")
        
        # 创建测试实例
        print("\n正在创建测试实例...")
        
        stochastic_data_instance = module3.create_deterministic_example_instance(
            num_customers=3,
            num_sites=2,
            use_kmeans_clustering=False,
            demand_level="low",
            random_seed=42
        )
        
        print("✅ 测试实例创建成功")
        
        # 生成需求样本
        print("\n正在生成需求样本...")
        customers = stochastic_data_instance['customers']
        demand_samples = []
        
        import random
        random.seed(42)
        
        for _ in range(3):
            scenario = {}
            for customer in customers:
                base_demand = stochastic_data_instance['demand_deterministic'][customer]
                scenario[customer] = max(1, base_demand + random.randint(-1, 1))
            demand_samples.append(scenario)
        
        print("✅ 需求样本生成成功")
        
        # 创建ALNS求解器
        print("\n正在创建ALNS求解器...")
        
        alns_config = {
            'max_iterations': 100,  # 运行足够多的迭代以重现错误
            'max_iterations_without_improvement': 20,
            'initial_temperature': 50,
            'cooling_rate': 0.95
        }
        
        # 创建问题实例
        problem_instance = type('Problem', (), {})()
        for key, value in stochastic_data_instance.items():
            setattr(problem_instance, key, value)
        
        alns_solver = module3.ALNS_Solver(
            problem_instance=problem_instance,
            demand_samples=demand_samples,
            alns_config=alns_config
        )
        
        print("✅ ALNS求解器创建成功")
        
        # 运行求解测试
        print("\n正在运行求解测试...")
        start_time = time.time()
        
        try:
            # 运行求解
            result = alns_solver.solve(time_limit=30)  # 30秒时间限制
            
            if result:
                print("✅ 求解成功完成")
            else:
                print("⚠️  求解返回None")
                
        except Exception as e:
            print(f"❌ 求解过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
        
        test_time = time.time() - start_time
        print(f"\n测试耗时: {test_time:.2f} 秒")
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_individual_operators():
    """
    单独测试各个算子
    """
    print("\n" + "=" * 60)
    print("单独测试算子")
    print("=" * 60)
    
    try:
        import importlib.util
        spec = importlib.util.spec_from_file_location("module3", "3.py")
        module3 = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module3)
        
        # 创建简单的测试解
        test_solution = {
            'y': {1: 1, 2: 1, 3: 0},
            'n': {1: 2, 2: 1, 3: 0}
        }
        
        # 创建简单的ALNS实例
        stochastic_data_instance = module3.create_deterministic_example_instance(
            num_customers=3, num_sites=3, random_seed=42
        )
        
        problem_instance = type('Problem', (), {})()
        for key, value in stochastic_data_instance.items():
            setattr(problem_instance, key, value)
        
        demand_samples = [
            {1: 2, 2: 1, 3: 2},
            {1: 1, 2: 2, 3: 1},
            {1: 3, 2: 1, 3: 2}
        ]
        
        alns_solver = module3.ALNS_Solver(
            problem_instance=problem_instance,
            demand_samples=demand_samples
        )
        
        print("测试破坏算子...")
        for i, destroy_op in enumerate(alns_solver.destroy_operators):
            try:
                result = destroy_op(test_solution, i)
                print(f"✅ {destroy_op.__name__} 测试成功")
            except Exception as e:
                print(f"❌ {destroy_op.__name__} 测试失败: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n测试修复算子...")
        for i, repair_op in enumerate(alns_solver.repair_operators):
            try:
                result = repair_op(test_solution, i)
                print(f"✅ {repair_op.__name__} 测试成功")
            except Exception as e:
                print(f"❌ {repair_op.__name__} 测试失败: {e}")
                import traceback
                traceback.print_exc()
        
        print("\n测试目标函数...")
        try:
            obj_value = alns_solver._calculate_objective_heuristic(test_solution, 1)
            print(f"✅ 目标函数计算成功: {obj_value:.2f}")
        except Exception as e:
            print(f"❌ 目标函数计算失败: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"❌ 算子测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_iteration_error()
    test_individual_operators()
    
    print("\n" + "=" * 60)
    print("调试完成")
    print("=" * 60)
