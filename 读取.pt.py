import torch
import os
import glob

# 查找最新的批量VRP数据文件
output_dir = "./output"
batch_files = glob.glob(os.path.join(output_dir, "batch_vrp_data_*.pt"))

if batch_files:
    # 选择最新的文件
    latest_file = max(batch_files, key=os.path.getmtime)
    print(f"正在读取最新的批量VRP数据文件: {latest_file}")

    # 加载.pt文件
    data = torch.load(latest_file, map_location=torch.device('cpu'))

    print("\n=== 批量VRP数据文件内容 ===")
    for key, value in data.items():
        if isinstance(value, torch.Tensor):
            print(f"\n{key}: {value.shape} (dtype: {value.dtype})")
            if key in ['depot_xy', 'node_xy']:
                print(f"  前几个值: {value[:2] if len(value.shape) > 1 else value}")
            elif key == 'node_demand':
                print(f"  需求数据 (前2个场景): {value[:2] if len(value.shape) > 1 else value}")
            elif key == 'original_demands':
                print(f"  原始需求 (前2个场景): {value[:2] if len(value.shape) > 1 else value}")
        else:
            print(f"\n{key}: {value}")

    # 详细分析批量数据结构
    if 'batch_size' in data:
        batch_size = data['batch_size']
        print(f"\n=== 批量数据分析 ===")
        print(f"批量大小: {batch_size}")

        if 'depot_xy' in data:
            depot_xy = data['depot_xy']
            print(f"仓库坐标形状: {depot_xy.shape}")  # 应该是 [batch_size, 1, 2]
            print(f"所有场景的仓库坐标: {depot_xy.squeeze()}")

        if 'node_xy' in data:
            node_xy = data['node_xy']
            print(f"节点坐标形状: {node_xy.shape}")  # 应该是 [batch_size, num_nodes, 2]
            print(f"第一个场景的节点坐标: {node_xy[0]}")

        if 'node_demand' in data:
            node_demand = data['node_demand']
            print(f"节点需求形状: {node_demand.shape}")  # 应该是 [batch_size, num_nodes]
            print(f"所有场景的需求数据:")
            for i in range(min(batch_size, 5)):  # 只显示前5个场景
                print(f"  场景 {i}: {node_demand[i]}")

        if 'original_demands' in data:
            original_demands = data['original_demands']
            print(f"原始需求形状: {original_demands.shape}")
            print(f"所有场景的原始需求数据:")
            for i in range(min(batch_size, 5)):  # 只显示前5个场景
                print(f"  场景 {i}: {original_demands[i]}")

else:
    print("未找到批量VRP数据文件，尝试读取单个VRP文件...")

    # 查找单个VRP文件
    single_files = glob.glob(os.path.join(output_dir, "vrp_data_*.pt"))
    if single_files:
        latest_file = max(single_files, key=os.path.getmtime)
        print(f"正在读取最新的单个VRP数据文件: {latest_file}")

        data = torch.load(latest_file, map_location=torch.device('cpu'))

        print("\n=== 单个VRP数据文件内容 ===")
        for key, value in data.items():
            if isinstance(value, torch.Tensor):
                print(f"\n{key}: {value.shape} (dtype: {value.dtype})")
                print(f"  数据: {value}")
            else:
                print(f"\n{key}: {value}")
    else:
        print("未找到任何VRP数据文件")