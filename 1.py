import numpy as np
from typing import Dict, List, Tuple, Any
import random
import time
import math
from collections import defaultdict
import copy
import concurrent.futures
try:
    from drl import DRL_CVRP_Solver, set_drl_log_level
    from clustering import generate_locker_sites_with_kmeans
    from visualization import visualize_solution
    DRL_AVAILABLE = True
except ImportError:
    print("警告: drl, clustering, 或 visualization 模块未找到。DRL和可视化功能将不可用。")
    DRL_AVAILABLE = False
    # 提供这些类的虚拟实现，以避免 NameError
    class DRL_CVRP_Solver:
        def __init__(self, *args, **kwargs): pass
        def solve(self, *args, **kwargs): return 0.0, None # 返回一个默认值
    def set_drl_log_level(level): pass
    def generate_locker_sites_with_kmeans(customer_coords, num_sites, random_state):
        print("警告: K-Means聚类不可用，将随机生成储物柜站点。")
        local_random = random.Random(random_state)
        return {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in range(1, num_sites + 1)}
    def visualize_solution(*args, **kwargs):
        print("警告: 可视化功能不可用。")
        return None


import logging # logging 应该在顶部导入

# 设置随机种子以确保结果可重现
RANDOM_SEED = 606
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# SAA 参数 (调整以提高统计稳定性)
SAA_MAX_REPLICATIONS_M = 10  # 最大复制次数
SAA_MIN_REPLICATIONS_M = 2   # 最小复制次数 (设为最小值，主要依靠终止条件)
SAA_SAMPLES_K = 40          # 训练样本数量 N (减少以提高求解效率)
SAA_SAMPLES_K_PRIME = 2000   # 验证样本数量 N' (适当减少以平衡计算效率)
SAA_CONFIDENCE_LEVEL_ALPHA = 0.1  # 置信水平 (90%置信区间)
SAA_GAP_TOLERANCE_PERCENT = 0.03  # Gap阈值 ε' = 3%
SAA_VARIANCE_TOLERANCE_PERCENT = 0.05  # 方差阈值 ε = 5%

# ---------------------------------------------------------------------------
# create_deterministic_example_instance 函数 (全局作用域)
# ---------------------------------------------------------------------------
def create_deterministic_example_instance(
        num_customers: int = 15,
        num_sites: int = 6,
        use_kmeans_clustering: bool = False,
        demand_level: str = "medium",
        locker_cost_level: str = "medium",
        drone_cost_level: str = "medium",
        drone_transport_cost_level: str = "medium",
        use_generated_distances: bool = True,
        random_seed: int = RANDOM_SEED
):
    """创建示例问题的参数实例 (用于确定性或期望值)"""
    local_random = random.Random(random_seed)
    customers_list = list(range(1, num_customers + 1))
    sites_list = list(range(1, num_sites + 1))

    customer_coords_dict = {i: (local_random.uniform(0, 15), local_random.uniform(0, 15)) for i in customers_list}

    if use_kmeans_clustering and DRL_AVAILABLE: # K-Means依赖clustering模块
        site_coords_dict = generate_locker_sites_with_kmeans(customer_coords_dict, num_sites, random_state=random_seed)
    else:
        site_coords_dict = {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in sites_list}

    demand_params_dict = {"low": (1, 2), "medium": (2, 3), "high": (3, 4)}.get(demand_level, (2, 3))
    demand_dict_for_instance = {c: local_random.randint(demand_params_dict[0], demand_params_dict[1]) for c in customers_list}

    locker_base_cost_val = {"low": 6000, "medium": 8000, "high": 12000}.get(locker_cost_level, 8000)
    locker_fixed_cost_dict = {s: locker_base_cost_val for s in sites_list}

    drone_cost_val_param = {"low": 2500, "medium": 3500, "high": 4500}.get(drone_cost_level, 3500)
    transport_unit_cost_val_param = {"low": 0.008, "medium": 0.015, "high": 0.025}.get(drone_transport_cost_level, 0.015)

    drone_speed_param = 50.0
    loading_time_param = 300.0 / 3600.0
    max_flight_distance_param = 20.0
    H_drone_working_hours_per_day_param = 8.0
    penalty_cost_unassigned_param = 1000.0  # 高惩罚成本促使系统分配更多客户
    avg_locker_capacity_val = 30
    Q_locker_capacity_dict = {s: avg_locker_capacity_val for s in sites_list}

    depot_coord_param = (0, 0)
    truck_capacity_param = 90
    truck_fixed_cost_param = 1000
    truck_km_cost_param = 0.5

    distance_matrix_dict = {}
    if use_generated_distances:
        for c_id_val_dist, c_coord_val_dist in customer_coords_dict.items():
            for s_id_val_dist, s_coord_val_dist in site_coords_dict.items():
                dist_val = math.sqrt(
                    (c_coord_val_dist[0] - s_coord_val_dist[0]) ** 2 + (c_coord_val_dist[1] - s_coord_val_dist[1]) ** 2)
                distance_matrix_dict[c_id_val_dist, s_id_val_dist] = round(dist_val, 2)
    else:
        for c_id_val_dist_rand in customers_list:
            for s_id_val_dist_rand in sites_list:
                base_dist_val = math.sqrt(
                    (customer_coords_dict[c_id_val_dist_rand][0] - site_coords_dict[s_id_val_dist_rand][0]) ** 2 + \
                    (customer_coords_dict[c_id_val_dist_rand][1] - site_coords_dict[s_id_val_dist_rand][1]) ** 2)
                distance_matrix_dict[c_id_val_dist_rand, s_id_val_dist_rand] = round(
                    local_random.uniform(base_dist_val * 0.8, base_dist_val * 1.2), 1)

    return {
        'customers': customers_list, 'sites': sites_list,
        'demand_deterministic': demand_dict_for_instance,
        'distance_matrix': distance_matrix_dict, 'drone_speed': drone_speed_param,
        'loading_time': loading_time_param, 'max_flight_distance': max_flight_distance_param,
        'locker_fixed_cost': locker_fixed_cost_dict, 'transport_unit_cost': transport_unit_cost_val_param,
        'drone_cost': drone_cost_val_param, 'H_drone_working_hours_per_day': H_drone_working_hours_per_day_param,
        'penalty_cost_unassigned': penalty_cost_unassigned_param,
        'Q_locker_capacity': Q_locker_capacity_dict, 'customer_coords': customer_coords_dict,
        'site_coords': site_coords_dict, 'depot_coord': depot_coord_param,
        'truck_capacity': truck_capacity_param, 'truck_fixed_cost': truck_fixed_cost_param,
        'truck_km_cost': truck_km_cost_param
    }

# ---------------------------------------------------------------------------
# ALNS 统一求解器类定义
# ---------------------------------------------------------------------------
#
# 架构说明：
# 1. ALNS_Solver: 统一求解器，直接处理SAA生成的确定性两阶段问题
#    - 优化第一阶段决策：储物柜选址(y)和无人机配置(n)
#    - 通过快速启发式算法处理第二阶段客户分配
#    - 目标函数：在K个给定需求样本下的期望总成本
#
# 2. CustomerAssignmentALNS: 快速客户分配求解器
#    - 仅用于第二阶段子问题的快速求解
#    - 不是独立的ALNS，而是ALNS_Solver的辅助工具
#
# 这种设计避免了"两个ALNS"的问题，ALNS作为统一求解器处理整个SAA问题。
# ---------------------------------------------------------------------------
class ALNS_Solver:
    """
    ALNS (Adaptive Large Neighborhood Search) 两阶段随机规划求解器
    正确实现两阶段决策时序：需求实现前vs需求实现后

    解表示（修正版）：
    solution = {
        'y': {j: 0/1},           # 第一阶段：储物柜选址决策（需求实现前）
        'n': {j: int},           # 第一阶段：无人机配置决策（需求实现前）
    }

    注意：客户分配决策x不包含在解中，因为它们是第二阶段决策，
    需要在需求实现后根据具体场景动态优化。

    ALNS算子只操作第一阶段决策变量：
    - 破坏算子：移除/修改储物柜选址和无人机配置
    - 修复算子：重新配置储物柜选址和无人机数量
    - 目标函数：给定第一阶段决策，通过求解多个第二阶段子问题计算期望总成本

    这正确实现了两阶段随机规划的时序逻辑。
    """

    def __init__(self, problem_instance, demand_samples, alns_config=None):
        self.problem = problem_instance
        self.demand_samples = demand_samples
        self.num_scenarios = len(demand_samples)

        # ALNS参数配置（优化版本，平衡速度和质量）
        default_config = {
            # 模拟退火参数（适应更精确的代理模型）
            'initial_temperature': 100,    # 恢复较高初始温度，给算法更多探索空间
            'cooling_rate': 0.995,         # 稍微加快冷却速度，提高收敛效率
            'min_temperature': 0.01,       # T* = 0.01（论文设置）
            'max_iterations': 3000,        # 减少最大迭代次数，提高效率
            'max_iterations_without_improvement': 500,   # 减少无改进迭代次数

            # 自动温度计算参数（备用）
            'auto_temp_acceptance_prob': 0.5,
            'auto_temp_sample_size': 200,

            # 优化的评估策略参数
            'k_small': 20,                 # 进一步增加小样本数量
            'full_evaluation_frequency': 20,  # 大幅提高精确评估频率
            'quality_check_frequency': 50,    # 更频繁的质量检查
            'adaptive_evaluation': True,       # 启用自适应评估策略

            # 自适应权重更新参数（基于论文）
            'weight_update_coefficient': 0.1,  # μ = 0.1（论文设置）
            'weight_update_frequency': 100,    # 权重更新频率
            'use_score_based_weights': True,

            # 分数奖励机制（基于论文）
            'score_new_best': 5,      # δ_1 = 5（论文设置）
            'score_better': 3,        # δ_2 = 3（论文设置）
            'score_accepted': 1,      # δ_3 = 1（论文设置）

            # 增量评估参数
            'use_delta_evaluation': True,

            # 解质量保证参数
            'quality_threshold': 0.05,        # 解质量阈值（5%误差）
        }

        # 合并用户配置
        self.config = default_config.copy()
        if alns_config:
            self.config.update(alns_config)

        # 移除服务覆盖率约束，与saa_g_r.py保持一致

        # 解缓存机制，避免重复计算相同解的目标值
        self.solution_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
        self.precise_evaluations_count = 0  # 精确评估次数计数器

        # DRL卡车成本缓存，避免重复计算相同的储物柜配置
        self.drl_truck_cost_cache = {}
        self.drl_cache_hits = 0
        self.drl_cache_misses = 0

        # 自适应评估策略状态
        self.evaluation_accuracy_history = []
        self.last_quality_check_iteration = 0
        self.consecutive_poor_estimates = 0

        # 移除强化破坏模式相关变量

        # ALNS算子：操作第一阶段决策变量(y, n)
        # 破坏算子：移除/修改储物柜选址和无人机配置
        self.destroy_operators = [
            self.random_locker_removal,      # 随机移除储物柜
            self.worst_locker_removal,       # 移除效益最差的储物柜
            self.related_locker_removal,     # 移除相关储物柜
            self.drone_adjustment_removal,   # 调整无人机配置
            self.cluster_removal,            # 簇移除算子
            self.zone_removal,               # 区域移除算子
            self.radical_restructure         # 激进重构算子
        ]

        # 修复算子：重新配置储物柜选址和无人机数量
        self.repair_operators = [
            self.greedy_locker_insertion,    # 贪心插入储物柜
            self.regret_insertion,           # 后悔值插入储物柜
            self.drone_optimization          # 优化无人机配置
        ]

        # 初始化算子权重
        self.destroy_weights = {op.__name__: 1.0 for op in self.destroy_operators}
        self.repair_weights = {op.__name__: 1.0 for op in self.repair_operators}

        # 算子使用统计
        self.destroy_usage = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_usage = {op.__name__: 0 for op in self.repair_operators}

        # 算子成功统计（传统方式）
        self.destroy_success = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_success = {op.__name__: 0 for op in self.repair_operators}

        # 算子分数统计（新的基于分数的方式）
        self.destroy_scores = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_scores = {op.__name__: 0 for op in self.repair_operators}

        # 历史最优解记录
        self.historical_best_obj = float('inf')

    def _calculate_initial_temperature(self, initial_solution):
        """
        自动计算初始温度，使得接受一个平均恶化程度的解的概率为预设值
        """
        print(f"  正在自动计算初始温度...")

        sample_size = self.config['auto_temp_sample_size']
        acceptance_prob = self.config['auto_temp_acceptance_prob']

        # 计算初始解的目标值（使用快速评估保持一致性）
        initial_obj = self._calculate_objective_heuristic(initial_solution, 0)

        # 生成邻域解样本并计算目标值差异
        deltas = []
        valid_neighbors = 0
        feasible_neighbors = 0

        for i in range(sample_size):
            try:
                # 随机选择破坏和修复算子
                destroy_op = random.choice(self.destroy_operators)
                repair_op = random.choice(self.repair_operators)

                # 生成邻域解
                temp_solution = destroy_op(copy.deepcopy(initial_solution))
                neighbor_solution = repair_op(temp_solution, 0)

                if neighbor_solution is not None:
                    valid_neighbors += 1
                    if self._is_feasible(neighbor_solution):
                        feasible_neighbors += 1
                        neighbor_obj = self._calculate_objective_heuristic(neighbor_solution, 0)
                        delta = neighbor_obj - initial_obj
                        if delta > 0:  # 只考虑恶化的解
                            deltas.append(delta)
                        elif delta < 0:  # 改进的解也记录（用于调试）
                            deltas.append(abs(delta))  # 使用绝对值
            except Exception as e:
                continue

        print(f"  温度计算统计: 尝试{sample_size}次, 有效邻域{valid_neighbors}个, 可行邻域{feasible_neighbors}个, 有效差值{len(deltas)}个")

        if not deltas:
            print(f"  无法生成有效的邻域解样本，使用默认初始温度: 1000.0")
            return 1000.0

        # 计算平均恶化程度
        avg_delta = sum(deltas) / len(deltas)

        # 根据公式 p = exp(-avg_delta / T0) 计算初始温度
        # T0 = -avg_delta / ln(p)
        if acceptance_prob > 0 and acceptance_prob < 1:
            initial_temp = -avg_delta / math.log(acceptance_prob)
        else:
            initial_temp = avg_delta  # 回退值

        print(f"  自动计算的初始温度: {initial_temp:.2f} (基于{len(deltas)}个样本，平均恶化: {avg_delta:.2f})")
        return max(initial_temp, 1.0)  # 确保温度至少为1.0

    def solve(self, initial_solution=None, time_limit=300):
        """
        ALNS统一求解器主循环

        求解SAA生成的确定性两阶段随机规划问题：
        - 优化第一阶段决策：储物柜选址(y)和无人机配置(n)
        - 第二阶段客户分配通过快速启发式算法处理
        - 目标函数：在K个给定需求样本下的期望总成本

        Args:
            initial_solution: 初始第一阶段解 {'y': {}, 'n': {}}
            time_limit: 时间限制（秒）

        Returns:
            Dict: 最优第一阶段解
        """
        print(f"  开始ALNS求解，时间限制: {time_limit}秒")
        print(f"  ALNS参数设置（基于论文）:")
        print(f"    初始温度 T₀: {self.config['initial_temperature']}")
        print(f"    冷却因子 κ: {self.config['cooling_rate']}")
        print(f"    温度阈值 T*: {self.config['min_temperature']}")
        print(f"    最大迭代次数 It₁: {self.config['max_iterations']}")
        print(f"    无改进最大迭代 It₂: {self.config['max_iterations_without_improvement']}")
        print(f"    权重更新系数 μ: {self.config['weight_update_coefficient']}")
        print(f"    分数设置 δ₁/δ₂/δ₃: {self.config['score_new_best']}/{self.config['score_better']}/{self.config['score_accepted']}")

        start_time = time.time()

        # 生成初始解
        if initial_solution is None:
            current_solution = self.create_initial_solution()
        else:
            current_solution = copy.deepcopy(initial_solution)

        if current_solution is None:
            print("  无法生成初始解")
            return None

        # 【关键修改】使用快速评估函数计算初始目标值，用于迭代
        current_obj = self._calculate_objective_heuristic(current_solution, 0)
        # 使用精确评估函数计算真实的最优值
        best_obj = self.calculate_objective_cached(current_solution)
        best_solution = copy.deepcopy(current_solution)
        self.historical_best_obj = best_obj

        print(f"  初始解目标值(精确): {best_obj:.2f}, (启发式): {current_obj:.2f}")

        # 设置历史最优解
        self.historical_best_obj = best_obj

        # 使用论文中的固定初始温度
        temperature = self.config['initial_temperature']  # T₀ = 100
        print(f"  使用论文设置的初始温度: {temperature}")

        # ALNS主循环
        iteration = 0
        iterations_without_improvement = 0

        # 主循环（改进的终止条件）
        max_iterations = self.config['max_iterations']  # 减少到5000
        quality_convergence_count = 0  # 解质量收敛计数
        last_best_obj = float('inf')

        while (iteration < max_iterations and
               iterations_without_improvement < self.config['max_iterations_without_improvement'] and
               time.time() - start_time < time_limit):  # 简化终止条件，专注速度

            iteration += 1

            # 【修改】简化终止条件 - 连续无改进时直接停止
            # 降低无改进终止阈值，避免过长的搜索时间
            early_termination_threshold = 500  # 连续500次无改进就停止
            if iterations_without_improvement > early_termination_threshold:
                print(f"  >>> 迭代 {iteration}: 连续{early_termination_threshold}次无改进，提前终止搜索 <<<")
                print(f"      当前最优解: {best_obj:.2f}")
                break  # 直接跳出主循环

            # 选择破坏和修复算子（移除强化破坏模式）
            destroy_op = self._select_operator(self.destroy_operators, self.destroy_weights)

            # 选择修复算子
            repair_op = self._select_operator(self.repair_operators, self.repair_weights)

            # 记录算子使用
            self.destroy_usage[destroy_op.__name__] += 1
            self.repair_usage[repair_op.__name__] += 1

            # 生成新解
            try:
                temp_solution = destroy_op(copy.deepcopy(current_solution))
                new_solution = repair_op(temp_solution, iteration)

                if new_solution is None or not self._is_feasible(new_solution):
                    continue

                # 【核心修改】使用超快速评估函数（传递迭代次数以支持自适应DRL调用）
                new_obj = self._calculate_objective_heuristic(new_solution, iteration)

                # 标准ALNS接受准则：模拟退火
                accept = False
                delta = new_obj - current_obj

                # 1. 改进的解总是接受
                if delta < 0:
                    accept = True
                # 2. 模拟退火接受较差解
                elif temperature > 0 and random.random() < math.exp(-delta / temperature):
                    accept = True

                if accept:
                    current_solution = new_solution
                    current_obj = new_obj

                    # 记录算子成功（传统方式）
                    self.destroy_success[destroy_op.__name__] += 1
                    self.repair_success[repair_op.__name__] += 1

                    # 基于分数的奖励机制
                    score = 0

                    # 【重要】当快速评估找到一个潜在更优解时，才进行一次慢速但精确的评估
                    if new_obj < best_obj:
                        print(f"    迭代 {iteration}: 启发式发现潜在改进 {new_obj:.2f} < {best_obj:.2f}，进行精确评估...")
                        full_obj = self.calculate_objective_cached(new_solution)
                        self.precise_evaluations_count += 1
                        print(f"    精确评估结果: {full_obj:.2f}")
                        if full_obj < best_obj:
                            # 尝试局部搜索改进
                            improved_solution = self._local_search_improvement(new_solution)
                            if improved_solution:
                                improved_obj = self.calculate_objective_cached(improved_solution)
                                if improved_obj < full_obj:
                                    new_solution = improved_solution
                                    full_obj = improved_obj

                            best_solution = copy.deepcopy(new_solution)
                            best_obj = full_obj
                            iterations_without_improvement = 0

                            # 判断是否为历史最优解
                            if full_obj < self.historical_best_obj:
                                self.historical_best_obj = full_obj
                                score = self.config['score_new_best']  # 找到历史最优解
                                print(f"  迭代 {iteration}: 找到历史最优解，精确目标值: {best_obj:.2f}")
                                # 重置质量收敛计数
                                quality_convergence_count = 0
                                last_best_obj = best_obj
                            else:
                                score = self.config['score_better']  # 找到比当前解好的解
                                print(f"  迭代 {iteration}: 找到更优解，精确目标值: {best_obj:.2f}")
                                # 重置质量收敛计数
                                quality_convergence_count = 0
                                last_best_obj = best_obj
                        else:
                            iterations_without_improvement += 1
                            score = self.config['score_accepted']  # 被接受的解
                    elif new_obj < current_obj:
                        score = self.config['score_better']  # 找到比当前解好的解
                        iterations_without_improvement += 1
                    else:
                        score = self.config['score_accepted']  # 被接受的较差解
                        iterations_without_improvement += 1

                    # 更新算子分数
                    if self.config['use_score_based_weights']:
                        self.destroy_scores[destroy_op.__name__] += score
                        self.repair_scores[repair_op.__name__] += score
                else:
                    iterations_without_improvement += 1

                # 检查解质量收敛（如果没有找到更好解）
                if abs(best_obj - last_best_obj) < 0.01:  # 解质量变化很小
                    quality_convergence_count += 1
                else:
                    quality_convergence_count = 0
                    last_best_obj = best_obj

                # 降温
                temperature *= self.config['cooling_rate']

                # 定期更新算子权重
                if iteration % self.config['weight_update_frequency'] == 0:
                    self._update_operator_weights()

            except Exception as e:
                print(f"  迭代 {iteration} 出错: {str(e)}")
                continue

        solve_time = time.time() - start_time

        # 确定终止原因
        termination_reason = []
        if iteration >= max_iterations:
            termination_reason.append(f"达到最大迭代次数({max_iterations})")
        if temperature <= self.config['min_temperature']:
            termination_reason.append(f"温度降至阈值({self.config['min_temperature']})")
        if iterations_without_improvement >= self.config['max_iterations_without_improvement']:
            termination_reason.append(f"连续{self.config['max_iterations_without_improvement']}次无改进")
        if quality_convergence_count >= 200:
            termination_reason.append(f"解质量收敛(连续{quality_convergence_count}次变化<0.01)")
        if time.time() - start_time >= time_limit:
            termination_reason.append(f"达到时间限制({time_limit}秒)")

        print(f"  ALNS求解完成，耗时: {solve_time:.2f}秒")
        print(f"  总迭代次数: {iteration}")
        print(f"  最终温度: {temperature:.4f}")
        print(f"  连续无改进次数: {iterations_without_improvement}")
        print(f"  终止原因: {' & '.join(termination_reason) if termination_reason else '未知'}")

        # 使用精确评估验证最终解
        if best_solution is not None:
            print(f"  验证最终解质量...")
            exact_obj = self.problem.calculate_objective(best_solution, self.demand_samples)
            print(f"  启发式目标值: {best_obj:.2f}")
            print(f"  精确目标值: {exact_obj:.2f}")
            print(f"  误差: {abs(exact_obj - best_obj):.2f} ({abs(exact_obj - best_obj)/exact_obj*100:.1f}%)")

        # 输出详细的缓存统计信息
        total_evaluations = self.cache_hits + self.cache_misses
        if total_evaluations > 0:
            cache_hit_rate = self.cache_hits / total_evaluations * 100
            print(f"  解缓存统计: {self.cache_hits} 命中, {self.cache_misses} 未命中, 命中率: {cache_hit_rate:.1f}%")
            print(f"  缓存大小: {len(getattr(self, 'solution_cache', {}))}")
            if hasattr(self, 'precise_evaluations_count'):
                print(f"  精确评估次数: {self.precise_evaluations_count}")
        else:
            print(f"  警告：未进行任何精确评估，缓存未被使用")

        # 输出DRL卡车成本缓存统计
        total_drl_evaluations = self.drl_cache_hits + self.drl_cache_misses
        if total_drl_evaluations > 0:
            drl_cache_hit_rate = self.drl_cache_hits / total_drl_evaluations * 100
            print(f"  DRL卡车成本缓存: {self.drl_cache_hits} 命中, {self.drl_cache_misses} 未命中, 命中率: {drl_cache_hit_rate:.1f}%")
            print(f"  DRL缓存大小: {len(getattr(self, 'drl_truck_cost_cache', {}))}")
        else:
            print(f"  DRL卡车成本缓存: 未使用")

        return best_solution

    def _evaluate_solution_quality(self, solution, iteration=0):
        """
        改进的解质量评估方法，结合服务能力和成本
        """
        try:
            # 使用自适应评估策略
            base_obj = self.calculate_objective_fast(solution, iteration)

            # 只保留基本的可行性检查，移除服务覆盖率惩罚
            feasibility_penalty = self._calculate_feasibility_penalty(solution)

            total_obj = base_obj + feasibility_penalty

            # 记录评估精度（如果可能）
            if iteration > 0 and iteration % (self.config['quality_check_frequency'] * 2) == 0:
                self._check_evaluation_accuracy(solution, base_obj)

            return total_obj
        except:
            return float('inf')



    def _calculate_feasibility_penalty(self, solution):
        """计算解可行性惩罚 - 与saa_g_r.py约束一致"""
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        penalty = 0

        # 检查是否至少有一个储物柜 (对应saa_g_r.py的C1约束)
        if not selected_lockers:
            return 10000  # 适度惩罚

        # 检查无人机配置合理性 (对应saa_g_r.py的C6约束: n_j >= y_j)
        for j in selected_lockers:
            if n_star.get(j, 0) < 1:  # 开放储物柜至少需要1架无人机
                penalty += 1000

        return penalty

    def _check_evaluation_accuracy(self, solution, estimated_obj):
        """检查评估精度并调整策略"""
        try:
            exact_obj = self.problem.calculate_objective(solution, self.demand_samples)
            if exact_obj > 0:
                error_rate = abs(estimated_obj - exact_obj) / exact_obj
                self.evaluation_accuracy_history.append(error_rate)

                # 保持历史记录在合理范围内
                if len(self.evaluation_accuracy_history) > 20:
                    self.evaluation_accuracy_history.pop(0)

                # 如果误差过大，增加计数
                if error_rate > self.config['quality_threshold']:
                    self.consecutive_poor_estimates += 1
                else:
                    self.consecutive_poor_estimates = 0
        except:
            pass

    def _local_search_improvement(self, solution):
        """
        对解进行局部搜索改进
        """
        try:
            best_solution = copy.deepcopy(solution)
            best_obj = self._evaluate_solution_quality(solution)
            improved = False

            open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

            # 1. 尝试saa_g_r.py的最优配置
            saa_optimal = [1, 2, 4, 5, 6]
            if set(open_lockers) != set(saa_optimal):
                temp_solution = copy.deepcopy(solution)
                # 重置所有储物柜
                for j in self.problem.sites:
                    temp_solution['y'][j] = 0
                    temp_solution['n'][j] = 0
                # 设置saa_g_r.py的最优配置
                for j in saa_optimal:
                    if j in self.problem.sites:
                        temp_solution['y'][j] = 1
                        temp_solution['n'][j] = 1

                if self._is_feasible(temp_solution):
                    temp_obj = self._evaluate_solution_quality(temp_solution)
                    if temp_obj < best_obj:
                        best_solution = temp_solution
                        best_obj = temp_obj
                        improved = True

            # 2. 储物柜替换搜索
            for current_j in open_lockers:
                for new_j in self.problem.sites:
                    if new_j not in open_lockers:
                        temp_solution = copy.deepcopy(best_solution)
                        temp_solution['y'][current_j] = 0
                        temp_solution['n'][current_j] = 0
                        temp_solution['y'][new_j] = 1
                        temp_solution['n'][new_j] = 1

                        if self._is_feasible(temp_solution):
                            temp_obj = self._evaluate_solution_quality(temp_solution)
                            if temp_obj < best_obj:
                                best_solution = temp_solution
                                best_obj = temp_obj
                                improved = True

            # 3. 调整无人机数量
            for j in [j for j, val in best_solution['y'].items() if val > 0.5]:
                current_drones = best_solution['n'][j]
                for new_drones in [1, 2]:  # 只尝试1或2架
                    if new_drones != current_drones:
                        temp_solution = copy.deepcopy(best_solution)
                        temp_solution['n'][j] = new_drones

                        temp_obj = self._evaluate_solution_quality(temp_solution)
                        if temp_obj < best_obj:
                            best_solution = temp_solution
                            best_obj = temp_obj
                            improved = True

            return best_solution if improved else None
        except:
            return None

    def _solution_to_key(self, solution):
        """
        将解转换为可用作字典键的字符串
        """
        # 更精确的键生成：包含所有储物柜的状态和无人机数量
        y_items = tuple(sorted((j, int(val > 0.5)) for j, val in solution['y'].items()))
        n_items = tuple(sorted((j, int(val)) for j, val in solution['n'].items() if solution['y'].get(j, 0) > 0.5))
        return (y_items, n_items)

    def calculate_objective_cached(self, solution):
        """
        带缓存的目标函数计算，正确实现两阶段结构
        """
        # 生成解的键（只基于第一阶段决策变量）
        solution_key = self._solution_to_key(solution)

        # 检查缓存
        if solution_key in self.solution_cache:
            self.cache_hits += 1
            return self.solution_cache[solution_key]

        # 缓存未命中，计算目标值
        self.cache_misses += 1
        if self.cache_misses <= 5:  # 只显示前5次未命中的详细信息
            open_lockers = [j for j, val in solution['y'].items() if val > 0.5]
            drone_config = {j: solution['n'][j] for j in open_lockers}
            print(f"    缓存未命中 #{self.cache_misses}: 储物柜{open_lockers}, 无人机{drone_config}")

        # 使用两阶段目标函数：给定第一阶段决策，求解第二阶段期望成本
        obj_value = self.calculate_objective_two_stage(solution, self.demand_samples)

        # 存入缓存
        self.solution_cache[solution_key] = obj_value

        return obj_value

    def calculate_objective_two_stage(self, solution, demand_samples_k):
        """
        正确的两阶段目标函数计算

        给定第一阶段决策(y, n)，对每个需求场景求解第二阶段子问题，
        然后计算期望总成本。

        Args:
            solution: 第一阶段解 {'y': {}, 'n': {}}
            demand_samples_k: K个需求场景

        Returns:
            float: 期望总成本
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            return float('inf')  # 无效解

        # 第一阶段成本（here-and-now decisions）
        first_stage_cost = (sum(self.problem.locker_fixed_cost[j] for j in selected_lockers) +
                           sum(self.problem.drone_cost * n_star.get(j, 0) for j in selected_lockers))

        # 第二阶段期望成本（wait-and-see decisions）
        total_second_stage_costs = 0
        batch_active_lockers_info = []

        try:
            # 对每个需求场景求解第二阶段子问题
            for k_idx, demand_scenario in enumerate(demand_samples_k):
                # 求解第二阶段客户分配子问题
                optimal_assignment = self._solve_second_stage_subproblem(
                    y_star, n_star, selected_lockers, demand_scenario
                )

                # 计算该场景下的第二阶段成本
                transport_cost_k, penalty_cost_k, locker_demands = self._calculate_second_stage_costs(
                    optimal_assignment, demand_scenario, selected_lockers
                )

                # 准备卡车成本计算数据
                active_info = {j: {'coord': self.problem.site_coords[j], 'demand': round(demand)}
                              for j, demand in locker_demands.items() if demand > 1e-6}
                batch_active_lockers_info.append(active_info)

                total_second_stage_costs += (transport_cost_k + penalty_cost_k)

            # 批量计算卡车成本
            batch_truck_costs = self.problem.calculate_truck_cost_batch(batch_active_lockers_info)
            avg_truck_cost = sum(batch_truck_costs) / len(batch_truck_costs) if batch_truck_costs else 0

            # 计算期望第二阶段成本
            avg_second_stage_cost = total_second_stage_costs / len(demand_samples_k)

            return first_stage_cost + avg_second_stage_cost + avg_truck_cost

        except Exception as e:
            print(f"    两阶段目标函数计算失败: {str(e)}")
            return float('inf')

    def _solve_second_stage_subproblem(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        求解第二阶段客户分配子问题

        给定第一阶段决策和具体需求场景，求解最优客户分配
        """
        # 使用快速启发式求解器
        if not hasattr(self.problem, 'fast_solver') or self.problem.fast_solver is None:
            self.problem.fast_solver = CustomerAssignmentALNS(self.problem)

        return self.problem.fast_solver.solve_assignment_heuristic(
            y_star, n_star, selected_lockers, demand_scenario
        )

    def _calculate_second_stage_costs(self, assignment, demand_scenario, selected_lockers):
        """
        计算第二阶段成本：运输成本和惩罚成本
        """
        transport_cost = 0
        locker_demands = {j: 0 for j in selected_lockers}

        # 计算运输成本和储物柜需求
        for (customer, locker), quantity in assignment.items():
            if quantity > 0:
                distance = self.problem.distance.get((customer, locker), 0)
                transport_cost += 2 * self.problem.transport_unit_cost * distance * quantity
                locker_demands[locker] += quantity

        # 计算惩罚成本
        total_assigned = sum(assignment.values())
        total_demand = sum(demand_scenario.values())
        penalty_cost = self.problem.penalty_cost_unassigned * max(0, total_demand - total_assigned)

        return transport_cost, penalty_cost, locker_demands

    def calculate_objective_fast(self, solution, iteration=0):
        """
        改进的自适应目标函数计算：智能平衡速度和精度
        """
        # 自适应评估策略
        if self.config['adaptive_evaluation']:
            return self._adaptive_evaluation(solution, iteration)

        # 传统策略：每隔一定迭代次数进行完整评估
        if iteration % self.config['full_evaluation_frequency'] == 0:
            # 完整评估，使用缓存
            return self.calculate_objective_cached(solution)
        else:
            # 使用改进的小样本评估替代纯启发式
            return self._small_sample_evaluation(solution)

    def _adaptive_evaluation(self, solution, iteration):
        """
        自适应评估策略：根据历史精度动态选择评估方法
        """
        # 定期质量检查
        if (iteration - self.last_quality_check_iteration >= self.config['quality_check_frequency'] or
            iteration % self.config['full_evaluation_frequency'] == 0):
            exact_obj = self.calculate_objective_cached(solution)
            self.last_quality_check_iteration = iteration
            return exact_obj

        # 如果连续估算误差过大，临时提高评估精度
        if self.consecutive_poor_estimates >= 3:
            return self._small_sample_evaluation(solution)

        # 正常情况使用小样本评估
        return self._small_sample_evaluation(solution)

    def _small_sample_evaluation(self, solution):
        """
        使用小样本进行更准确的评估（比纯启发式更精确）
        """
        # 使用k_small个样本进行评估
        small_samples = self.demand_samples[:self.config['k_small']]
        return self.problem.calculate_objective(solution, small_samples)

    def _calculate_objective_heuristic(self, solution, iteration=0):
        """
        竞争感知的代理目标函数：考虑客户间的容量竞争，使用DRL精确计算卡车成本
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = {j for j, val in y_star.items() if val > 0.5}

        if not selected_lockers:
            return float('inf')

        # 1. 第一阶段成本（不变）
        first_stage_cost = sum(self.problem.locker_fixed_cost.get(j, 0) for j in selected_lockers) + \
                          sum(self.problem.drone_cost * n_star.get(j, 0) for j in selected_lockers)

        # 2. 第二阶段成本估算（运输成本和惩罚成本）
        # 使用抽离出来的函数，能感知容量竞争
        total_transport_cost, total_penalty_cost, assigned_demand_to_locker = self._estimate_service_costs(solution)

        # 3. 【革命性改进】使用DRL直接计算精确的卡车成本
        active_lockers_info = {
            j: {'coord': self.problem.site_coords[j], 'demand': round(demand)}
            for j, demand in assigned_demand_to_locker.items() if demand > 0.5
        }

        if not active_lockers_info:
            estimated_truck_cost = 0
        else:
            # 使用DRL进行精确的卡车成本计算（带缓存优化和自适应策略）
            estimated_truck_cost = self._calculate_drl_truck_cost_cached(active_lockers_info, iteration)

        return first_stage_cost + total_transport_cost + total_penalty_cost + estimated_truck_cost

    def _fallback_truck_cost_estimation(self, active_lockers_info):
        """
        DRL不可用时的回退卡车成本估算方法
        """
        if not active_lockers_info:
            return 0

        # 使用原来的TSP近似方法作为回退
        total_demand = sum(info['demand'] for info in active_lockers_info.values())
        coords = [self.problem.depot_coord] + [info['coord'] for info in active_lockers_info.values()]

        # TSP距离估算
        if len(coords) > 1:
            centroid_x = sum(c[0] for c in coords) / len(coords)
            centroid_y = sum(c[1] for c in coords) / len(coords)
            avg_dist_to_centroid = sum(math.sqrt((c[0]-centroid_x)**2 + (c[1]-centroid_y)**2) for c in coords) / len(coords)
            estimated_distance = avg_dist_to_centroid * len(coords) * 2.5  # 经验系数
        else:
            estimated_distance = 0

        # 组合成本
        num_trucks = math.ceil(total_demand / self.problem.truck_capacity) if self.problem.truck_capacity > 0 else 0
        return (num_trucks * self.problem.truck_fixed_cost) + (estimated_distance * self.problem.truck_km_cost)

    def _calculate_drl_truck_cost_cached(self, active_lockers_info, iteration=0):
        """
        带缓存的DRL卡车成本计算，避免重复计算相同的储物柜配置
        支持自适应调用策略以平衡精度和速度
        """
        if not active_lockers_info:
            return 0

        # 生成缓存键：基于储物柜ID、坐标和需求的哈希
        cache_key_data = []
        for locker_id in sorted(active_lockers_info.keys()):
            info = active_lockers_info[locker_id]
            cache_key_data.append((locker_id, info['coord'], info['demand']))
        cache_key = hash(tuple(cache_key_data))

        # 检查缓存
        if cache_key in self.drl_truck_cost_cache:
            self.drl_cache_hits += 1
            return self.drl_truck_cost_cache[cache_key]

        # 缓存未命中，决定是否使用DRL
        self.drl_cache_misses += 1

        # 自适应DRL调用策略：
        # 1. 早期迭代（前1000次）：总是使用DRL以建立准确的搜索方向
        # 2. 中期迭代（1000-3000次）：50%概率使用DRL
        # 3. 后期迭代（3000+次）：25%概率使用DRL，主要依赖缓存
        use_drl = True
        if iteration > 1000:
            if iteration > 3000:
                use_drl = random.random() < 0.25  # 25%概率
            else:
                use_drl = random.random() < 0.50  # 50%概率

        if use_drl:
            try:
                # 获取DRL求解器（不生成图表以提高速度）
                drl_solver = self.problem._get_drl_solver(make_plots=False)
                if drl_solver is not None:
                    # 直接使用DRL计算精确的卡车成本
                    truck_cost = drl_solver.solve(active_lockers_info, return_route_info=False)

                    # 缓存结果
                    self.drl_truck_cost_cache[cache_key] = truck_cost

                    # 限制缓存大小，避免内存过度使用
                    if len(self.drl_truck_cost_cache) > 1000:
                        # 删除最旧的一半缓存项
                        keys_to_remove = list(self.drl_truck_cost_cache.keys())[:500]
                        for k in keys_to_remove:
                            del self.drl_truck_cost_cache[k]

                    return truck_cost
                else:
                    # DRL不可用时的回退方案
                    return self._fallback_truck_cost_estimation(active_lockers_info)
            except Exception as e:
                # DRL求解失败时的回退方案
                return self._fallback_truck_cost_estimation(active_lockers_info)
        else:
            # 不使用DRL，直接使用回退估算（但仍然缓存结果）
            truck_cost = self._fallback_truck_cost_estimation(active_lockers_info)
            self.drl_truck_cost_cache[cache_key] = truck_cost
            return truck_cost

    def _estimate_service_costs(self, solution):
        """
        估算服务成本：运输成本和惩罚成本，考虑容量竞争

        Returns:
            tuple: (total_transport_cost, total_penalty_cost, assigned_demand_to_locker)
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = {j for j, val in y_star.items() if val > 0.5}

        # 初始化
        total_transport_cost = 0
        total_penalty_cost = 0
        assigned_demand_to_locker = defaultdict(float)

        if not selected_lockers:
            return total_transport_cost, total_penalty_cost, assigned_demand_to_locker

        # 1. 估算每个储物柜的有效服务能力 (容量和无人机运力的较小者)
        effective_capacities = {j: 0.0 for j in selected_lockers}
        for j in selected_lockers:
            locker_cap = self.problem.Q_locker_capacity.get(j, 0)

            # 使用预计算的无人机运力（如果可用）
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                drone_cap = self.problem.fast_solver._get_drone_capacity_fast(j, n_star.get(j, 0))
            else:
                # 回退到简化计算
                num_drones = n_star.get(j, 0)
                if num_drones > 0:
                    # 简化的无人机运力估算
                    avg_service_time = 0.5  # 假设平均服务时间
                    drone_cap = num_drones * self.problem.H_drone_working_hours_per_day / avg_service_time
                else:
                    drone_cap = 0

            effective_capacities[j] = min(locker_cap, drone_cap)

        # 2. 模拟一个考虑竞争的贪心分配
        unassigned_demand = self.problem.expected_demand.copy()

        # 按多重标准排序客户，优先处理"困难"客户
        def customer_difficulty(i):
            # 获取可达储物柜
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                reachable = [j for j in self.problem.fast_solver.reachable_lockers.get(i, []) if j in selected_lockers]
            else:
                reachable = [j for j in selected_lockers
                           if (i, j) in self.problem.distance and
                           2 * self.problem.distance[i, j] <= self.problem.max_flight_distance]

            if not reachable:
                return (0, float('inf'))  # 无法服务的客户最优先

            # 计算可用总容量（考虑竞争）
            total_available_capacity = sum(effective_capacities.get(j, 0) for j in reachable)

            # 困难度 = 需求量 / 可用容量，值越大越困难
            difficulty = unassigned_demand[i] / max(total_available_capacity, 1e-6)

            return (len(reachable), difficulty)  # 先按可达储物柜数量排序，再按困难度排序

        customer_priority = sorted(self.problem.customers, key=customer_difficulty)

        # 3. 执行贪心分配
        for i in customer_priority:
            demand_to_assign = unassigned_demand[i]
            if demand_to_assign <= 1e-6:
                continue

            # 找到成本最低且有容量的储物柜
            best_j = -1
            min_cost = float('inf')

            # 获取可达储物柜
            if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                reachable_lockers = self.problem.fast_solver.reachable_lockers.get(i, [])
            else:
                # 回退到基于距离的可达性检查
                reachable_lockers = [j for j in selected_lockers
                                   if (i, j) in self.problem.distance and
                                   2 * self.problem.distance[i, j] <= self.problem.max_flight_distance]

            for j in reachable_lockers:
                if j in selected_lockers and effective_capacities[j] > 1e-6:
                    cost = 2 * self.problem.transport_unit_cost * self.problem.distance.get((i, j), float('inf'))
                    if cost < min_cost:
                        min_cost = cost
                        best_j = j

            if best_j != -1:
                assigned_amount = min(demand_to_assign, effective_capacities[best_j])
                total_transport_cost += min_cost * assigned_amount
                effective_capacities[best_j] -= assigned_amount
                unassigned_demand[i] -= assigned_amount
                assigned_demand_to_locker[best_j] += assigned_amount

                # 容量紧张惩罚：当储物柜容量接近耗尽时，增加额外成本
                original_capacity = min(self.problem.Q_locker_capacity.get(best_j, 1),
                                      self.problem.fast_solver._get_drone_capacity_fast(best_j, n_star.get(best_j, 0))
                                      if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver else 100)
                remaining_capacity_ratio = effective_capacities[best_j] / max(original_capacity, 1)

                if remaining_capacity_ratio < 0.2:  # 剩余容量不足20%
                    # 增加拥堵成本，模拟容量紧张对后续分配的影响
                    congestion_penalty = min_cost * assigned_amount * 0.1 * (1 - remaining_capacity_ratio)
                    total_transport_cost += congestion_penalty

        # 4. 计算总惩罚成本
        total_penalty_cost = sum(d * self.problem.penalty_cost_unassigned for d in unassigned_demand.values())

        return total_transport_cost, total_penalty_cost, assigned_demand_to_locker

    def _is_feasible(self, solution):
        """
        检查解是否满足基本约束条件（简化版本，避免过于严格）
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 1. 至少开设一个储物柜
        if not selected_lockers:
            return False

        # 2. 每个开放的储物柜至少配置一架无人机
        for j in selected_lockers:
            if n_star.get(j, 0) < 1:
                return False

        # 3. 基本合理性检查：无人机数量不能过多
        for j in selected_lockers:
            if n_star.get(j, 0) > 10:  # 限制最大无人机数量
                return False

        return True

    def create_initial_solution(self, return_multiple=False):
        """
        生成第一阶段初始解（修正版两阶段结构）

        返回格式：
        solution = {
            'y': {j: 0/1},                    # 第一阶段：储物柜选址
            'n': {j: num_drones},             # 第一阶段：无人机配置
        }

        注意：不包含客户分配决策x，因为它们是第二阶段决策，
        需要在需求实现后根据具体场景动态优化。

        Args:
            return_multiple: 如果为True，返回多个候选解列表；否则返回最佳解
        """
        try:
            # 调试信息：检查问题实例的基本信息
            print(f"  调试: 储物柜站点数量: {len(self.problem.sites)}, 站点: {self.problem.sites}")
            print(f"  调试: 客户数量: {len(self.problem.customers)}, 客户: {self.problem.customers[:5]}...")

            # 生成多个候选解
            candidates = []

            # 使用最优策略：clustering（基于性能分析结果）
            try:
                print(f"  使用clustering策略生成初始解...")
                solution = self._create_clustering_based_solution()

                if solution and self._is_feasible(solution):
                    obj = self._small_sample_evaluation(solution)
                    candidates.append({
                        'solution': solution,
                        'objective': obj,
                        'strategy': 'clustering'
                    })
                    print(f"    成功生成解，目标值: {obj:.2f}")
                else:
                    print(f"    clustering策略失败，使用备用解")
                    # 备用策略：简单解
                    fallback_solution = self._create_fallback_solution()
                    if fallback_solution and self._is_feasible(fallback_solution):
                        obj = self._small_sample_evaluation(fallback_solution)
                        candidates.append({
                            'solution': fallback_solution,
                            'objective': obj,
                            'strategy': 'fallback'
                        })
                        print(f"    备用解目标值: {obj:.2f}")
            except Exception as e:
                print(f"    clustering策略失败: {str(e)}")
                # 备用策略：简单解
                try:
                    fallback_solution = self._create_fallback_solution()
                    if fallback_solution and self._is_feasible(fallback_solution):
                        obj = self._small_sample_evaluation(fallback_solution)
                        candidates.append({
                            'solution': fallback_solution,
                            'objective': obj,
                            'strategy': 'fallback'
                        })
                        print(f"    备用解目标值: {obj:.2f}")
                except Exception as e2:
                    print(f"    备用策略也失败: {str(e2)}")

            # 如果没有生成任何候选解，使用回退解
            if not candidates:
                fallback = self._create_fallback_solution()
                if fallback:
                    candidates.append({
                        'solution': fallback,
                        'objective': self._small_sample_evaluation(fallback),
                        'strategy': 'fallback'
                    })

            if not candidates:
                return [] if return_multiple else None

            # 按目标值排序
            candidates.sort(key=lambda x: x['objective'])

            if return_multiple:
                # 返回前3-5个不同的候选解，确保多样性
                diverse_candidates = []
                for candidate in candidates[:8]:  # 从前8个中选择
                    # 检查是否与已选择的解足够不同
                    is_diverse = True
                    for selected in diverse_candidates:
                        if self._solutions_too_similar(candidate['solution'], selected['solution']):
                            is_diverse = False
                            break

                    if is_diverse:
                        diverse_candidates.append(candidate)
                        if len(diverse_candidates) >= 5:  # 最多返回5个
                            break

                print(f"  生成了 {len(diverse_candidates)} 个多样化候选解:")
                for i, candidate in enumerate(diverse_candidates):
                    print(f"    候选解{i+1}: {candidate['strategy']}, 目标值: {candidate['objective']:.2f}")

                return [c['solution'] for c in diverse_candidates]
            else:
                # 返回最佳解
                return candidates[0]['solution']

        except Exception as e:
            print(f"  初始解生成失败: {e}")
            fallback = self._create_fallback_solution()
            return [fallback] if return_multiple and fallback else fallback

    def _solutions_too_similar(self, solution1, solution2, threshold=0.7):
        """
        检查两个解是否过于相似

        Args:
            solution1, solution2: 要比较的解
            threshold: 相似度阈值，超过此值认为过于相似
        """
        if not solution1 or not solution2:
            return False

        # 比较储物柜选择的相似度
        lockers1 = set(j for j, val in solution1['y'].items() if val > 0.5)
        lockers2 = set(j for j, val in solution2['y'].items() if val > 0.5)

        if not lockers1 or not lockers2:
            return False

        # 计算Jaccard相似度
        intersection = len(lockers1.intersection(lockers2))
        union = len(lockers1.union(lockers2))
        jaccard_similarity = intersection / union if union > 0 else 0

        # 如果储物柜选择相似度过高，认为解过于相似
        return jaccard_similarity > threshold

    def _create_greedy_solution(self):
        """基于贪心策略的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化所有储物柜为关闭状态
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算每个储物柜的"吸引力"分数
        locker_scores = {}
        for j in self.problem.sites:
            score = 0
            reachable_customers = 0
            total_expected_demand = 0

            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers += 1
                        # 距离越近，分数越高
                        distance_score = 1.0 / (1.0 + self.problem.distance[i, j])
                        demand_score = self.problem.expected_demand[i]
                        score += distance_score * demand_score
                        total_expected_demand += self.problem.expected_demand[i]

            # 考虑储物柜固定成本
            if reachable_customers > 0:
                cost_penalty = self.problem.locker_fixed_cost[j] / 10000.0  # 归一化
                locker_scores[j] = score - cost_penalty
            else:
                locker_scores[j] = -float('inf')  # 无法服务任何客户

        # 贪心选择储物柜，确保满足服务能力要求
        sorted_lockers = sorted(locker_scores.items(), key=lambda x: x[1], reverse=True)

        # 计算总需求
        total_demand = sum(self.problem.expected_demand.values())

        # 选择足够的储物柜以避免过高的惩罚成本
        if sorted_lockers and sorted_lockers[0][1] > -float('inf'):
            # 更积极的初始选择：选择更多储物柜以确保服务覆盖
            positive_score_lockers = [s for s in sorted_lockers if s[1] > 0]
            num_to_select = min(max(4, len(positive_score_lockers) // 2), 8)  # 选择4-8个储物柜
            num_to_select = max(1, num_to_select)  # 至少选择1个

            # 确保不超过可用储物柜数量
            num_to_select = min(num_to_select, len(sorted_lockers))

            for i in range(num_to_select):
                if i < len(sorted_lockers):  # 双重检查索引有效性
                    j = sorted_lockers[i][0]
                    solution['y'][j] = 1

                # 估算需要的无人机数量
                estimated_demand = 0
                for customer in self.problem.customers:
                    if (customer, j) in self.problem.distance:
                        flight_distance = 2 * self.problem.distance[customer, j]
                        if flight_distance <= self.problem.max_flight_distance:
                            # 简单分配：按距离权重分配需求
                            weight = 1.0 / (1.0 + self.problem.distance[customer, j])
                            estimated_demand += self.problem.expected_demand[customer] * weight / num_to_select

                # 计算所需无人机数量（更精确的计算）
                if estimated_demand > 0:
                    # 计算该储物柜的平均服务距离
                    total_distance = 0
                    reachable_count = 0
                    for customer in self.problem.customers:
                        if (customer, j) in self.problem.distance:
                            flight_distance = 2 * self.problem.distance[customer, j]
                            if flight_distance <= self.problem.max_flight_distance:
                                total_distance += self.problem.distance[customer, j]
                                reachable_count += 1

                    if reachable_count > 0:
                        avg_distance = total_distance / reachable_count
                        avg_service_time = (2 * avg_distance / self.problem.drone_speed) + self.problem.loading_time
                        total_time_needed = estimated_demand * avg_service_time
                        drones_needed = math.ceil(total_time_needed / self.problem.H_drone_working_hours_per_day)
                        # 确保有足够的无人机运力，考虑服务能力要求
                        min_drones = max(1, math.ceil(estimated_demand / (self.problem.H_drone_working_hours_per_day / avg_service_time)))
                        # 适当增加无人机配置以提供服务缓冲
                        recommended_drones = max(min_drones, math.ceil(drones_needed * 1.5))  # 增加50%缓冲
                        solution['n'][j] = min(recommended_drones, 15)  # 增加上限到15架
                    else:
                        solution['n'][j] = 2  # 如果无法计算，默认2架
                else:
                    solution['n'][j] = 1  # 至少1架无人机

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution



    def _create_coverage_based_solution(self):
        """基于服务覆盖率的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算每个储物柜的覆盖能力
        coverage_scores = {}
        for j in self.problem.sites:
            covered_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        covered_demand += self.problem.expected_demand[i]
            coverage_scores[j] = covered_demand

        # 选择覆盖能力最强的储物柜
        sorted_by_coverage = sorted(coverage_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前5个储物柜，并智能配置无人机
        for i in range(min(5, len(sorted_by_coverage))):
            j = sorted_by_coverage[i][0]
            if sorted_by_coverage[i][1] > 0:  # 确保有覆盖能力
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution

    def _create_balanced_solution(self):
        """平衡成本和覆盖的初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 计算成本效益比
        efficiency_scores = {}
        for j in self.problem.sites:
            covered_demand = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        covered_demand += self.problem.expected_demand[i]

            if covered_demand > 0:
                # 效益 = 覆盖需求 / (固定成本 + 无人机成本)
                total_cost = self.problem.locker_fixed_cost[j] + self.problem.drone_cost
                efficiency_scores[j] = covered_demand / total_cost
            else:
                efficiency_scores[j] = 0

        # 选择效益最高的储物柜
        sorted_by_efficiency = sorted(efficiency_scores.items(), key=lambda x: x[1], reverse=True)

        # 选择前5个储物柜，智能配置无人机
        for i in range(min(5, len(sorted_by_efficiency))):
            j = sorted_by_efficiency[i][0]
            if sorted_by_efficiency[i][1] > 0:
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution

    def _create_saa_inspired_solution(self):
        """基于saa_g_r.py最优解的启发式初始解"""
        # 初始化完整解结构
        solution = {'y': {}, 'n': {}, 'x': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        # 初始化客户分配（所有场景）
        for k in range(self.num_scenarios):
            for i in self.problem.customers:
                for j in self.problem.sites:
                    solution['x'][(i, j, k)] = 0

        # 基于saa_g_r.py的最优解模式：选择储物柜[1,2,4,5,6]
        optimal_pattern = [1, 2, 4, 5, 6]
        for j in optimal_pattern:
            if j in self.problem.sites:
                solution['y'][j] = 1

                # 【改进】智能配置无人机数量，而非固定1架
                estimated_demand = self._estimate_locker_demand(j, solution)
                recommended_drones = self._calculate_recommended_drones(j, estimated_demand)
                solution['n'][j] = recommended_drones

        # 为选中的储物柜生成客户分配
        selected_lockers = [j for j, val in solution['y'].items() if val > 0.5]
        if selected_lockers:
            solution = self._generate_customer_assignments(solution, selected_lockers)

        return solution

    def _create_clustering_based_solution(self):
        """基于客户聚类的第一阶段初始解（修正版两阶段结构）"""
        # 初始化第一阶段解结构
        solution = {'y': {}, 'n': {}}

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 0
            solution['n'][j] = 0

        try:
            # 1. 使用距离矩阵进行简单聚类
            # 将客户按照到储物柜的距离模式进行分组
            customer_distance_patterns = {}
            sites_list = list(self.problem.sites)[:5]  # 只使用前5个储物柜作为参考

            for i in self.problem.customers:
                pattern = []
                for j in sites_list:
                    dist = self.problem.distance.get((i, j), 999)
                    pattern.append(dist)
                customer_distance_patterns[i] = pattern

            # 2. 简单聚类：基于距离模式的相似性
            num_clusters = min(len(self.problem.sites), random.randint(4, 6))
            clusters = [[] for _ in range(num_clusters)]

            # 随机分配客户到聚类
            customers = list(self.problem.customers)
            random.shuffle(customers)
            for idx, customer in enumerate(customers):
                clusters[idx % num_clusters].append(customer)

            # 3. 为每个聚类选择最佳储物柜
            selected_lockers = set()
            for cluster in clusters:
                if not cluster:
                    continue

                best_locker = None
                min_total_dist = float('inf')

                for j in self.problem.sites:
                    if j not in selected_lockers:
                        # 计算该储物柜到聚类中所有客户的总距离
                        total_dist = 0
                        valid_customers = 0

                        for i in cluster:
                            if (i, j) in self.problem.distance:
                                flight_distance = 2 * self.problem.distance[i, j]
                                if flight_distance <= self.problem.max_flight_distance:
                                    total_dist += self.problem.distance[i, j]
                                    valid_customers += 1

                        if valid_customers > 0:
                            avg_dist = total_dist / valid_customers
                            # 考虑服务能力：距离越近，服务的客户越多，越好
                            score = avg_dist / (valid_customers + 1)
                            if score < min_total_dist:
                                min_total_dist = score
                                best_locker = j

                if best_locker:
                    selected_lockers.add(best_locker)

            # 4. 确保至少有3个储物柜
            while len(selected_lockers) < 3 and len(selected_lockers) < len(self.problem.sites):
                remaining = set(self.problem.sites) - selected_lockers
                if remaining:
                    remaining_list = list(remaining)
                    if remaining_list:  # 双重检查确保列表不为空
                        selected_lockers.add(random.choice(remaining_list))
                    else:
                        break  # 如果没有剩余储物柜，退出循环
                else:
                    break  # 如果没有剩余储物柜，退出循环

            # 5. 构建第一阶段解：储物柜选址和无人机配置
            for j in selected_lockers:
                solution['y'][j] = 1
                solution['n'][j] = 2  # 初始配置2架无人机，更激进的配置

            return solution

        except Exception as e:
            # 如果失败，返回None让上层处理
            return None

    # 注意：_generate_customer_assignments方法已删除
    # 在修正的两阶段结构中，客户分配是第二阶段决策，
    # 需要在需求实现后根据具体场景动态优化，不在解中预先存储

    def _solve_assignment_for_scenario(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        为单个场景求解客户分配（贪心算法）

        Returns:
            Dict: {(customer, locker): quantity}
        """
        assignment = {}

        # 计算储物柜容量
        locker_capacities = {}
        drone_capacities = {}

        for j in selected_lockers:
            locker_capacities[j] = self.problem.Q_locker_capacity[j]
            drone_capacities[j] = n_star[j] * self.problem.H_drone_working_hours_per_day

        # 按需求量排序客户（优先分配高需求客户）
        customers_by_demand = sorted(demand_scenario.items(), key=lambda x: x[1], reverse=True)

        # 贪心分配
        for customer, demand in customers_by_demand:
            remaining_demand = demand

            # 获取该客户的可达储物柜（按距离排序）
            reachable_lockers = []
            for j in selected_lockers:
                if (customer, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[customer, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        cost = 2 * self.problem.transport_unit_cost * self.problem.distance[customer, j]
                        reachable_lockers.append((cost, j))

            # 按成本排序
            reachable_lockers.sort()

            for cost, j in reachable_lockers:
                if remaining_demand <= 0:
                    break

                # 检查容量约束
                available_locker = locker_capacities[j]
                available_drone = drone_capacities[j] / self.problem.service_time_per_unit
                available = min(available_locker, available_drone, remaining_demand)

                if available > 0:
                    assignment[(customer, j)] = assignment.get((customer, j), 0) + available
                    locker_capacities[j] -= available
                    drone_capacities[j] -= available * self.problem.service_time_per_unit
                    remaining_demand -= available

        return assignment



    def _create_fallback_solution(self):
        """备用简单第一阶段解（修正版两阶段结构）"""
        # 初始化第一阶段解结构
        solution = {'y': {}, 'n': {}}

        # 检查是否有可用的储物柜站点
        if not self.problem.sites:
            print("  警告: 没有可用的储物柜站点")
            return None

        # 选择第一个储物柜站点作为备用解
        first_site = self.problem.sites[0]

        # 初始化储物柜选址和无人机配置
        for j in self.problem.sites:
            solution['y'][j] = 1 if j == first_site else 0
            solution['n'][j] = 2 if j == first_site else 0

        return solution

    def _select_operator(self, operators, weights):
        """
        根据权重随机选择算子
        """
        total_weight = sum(weights[op.__name__] for op in operators)
        if total_weight <= 0:
            return random.choice(operators)

        rand_val = random.uniform(0, total_weight)
        cumulative_weight = 0

        for op in operators:
            cumulative_weight += weights[op.__name__]
            if rand_val <= cumulative_weight:
                return op

        return operators[-1]  # 回退

    def _update_operator_weights(self):
        """
        根据算子的成功率或分数更新权重
        """
        if self.config['use_score_based_weights']:
            # 基于分数的权重更新
            self._update_weights_by_score()
        else:
            # 传统的基于成功率的权重更新
            self._update_weights_by_success_rate()

    def _reset_operator_weights(self):
        """
        重置算子权重和统计信息，用于重启机制
        """
        print(f"      重置算子权重和统计信息...")

        # 重置权重为初始值
        self.destroy_weights = {op.__name__: 1.0 for op in self.destroy_operators}
        self.repair_weights = {op.__name__: 1.0 for op in self.repair_operators}

        # 重置使用统计
        self.destroy_usage = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_usage = {op.__name__: 0 for op in self.repair_operators}

        # 重置成功统计
        self.destroy_success = {op.__name__: 0 for op in self.destroy_operators}
        self.repair_success = {op.__name__: 0 for op in self.repair_operators}

        # 重置分数统计
        self.destroy_scores = {op.__name__: 0.0 for op in self.destroy_operators}
        self.repair_scores = {op.__name__: 0.0 for op in self.repair_operators}

    def _update_weights_by_score(self):
        """
        基于论文公式的权重更新策略：ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
        其中：ω为权重，μ为更新系数，π为算子得分，β为算子使用次数
        """
        mu = self.config['weight_update_coefficient']  # μ = 0.1

        # 更新破坏算子权重
        for op in self.destroy_operators:
            op_name = op.__name__
            if self.destroy_usage[op_name] > 0:
                # π/β：平均得分
                avg_score = self.destroy_scores[op_name] / self.destroy_usage[op_name]
                # ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
                self.destroy_weights[op_name] = (self.destroy_weights[op_name] * (1 - mu) +
                                               mu * avg_score)
            self.destroy_weights[op_name] = max(0.1, self.destroy_weights[op_name])  # 最小权重

        # 更新修复算子权重
        for op in self.repair_operators:
            op_name = op.__name__
            if self.repair_usage[op_name] > 0:
                # π/β：平均得分
                avg_score = self.repair_scores[op_name] / self.repair_usage[op_name]
                # ω^(χ+1) = ω^χ(1-μ) + μ(π/β)
                self.repair_weights[op_name] = (self.repair_weights[op_name] * (1 - mu) +
                                              mu * avg_score)
            self.repair_weights[op_name] = max(0.1, self.repair_weights[op_name])  # 最小权重

    def _update_weights_by_success_rate(self):
        """
        传统的基于成功率的权重更新策略
        """
        decay = self.config['weight_decay']

        # 更新破坏算子权重
        for op in self.destroy_operators:
            op_name = op.__name__
            if self.destroy_usage[op_name] > 0:
                success_rate = self.destroy_success[op_name] / self.destroy_usage[op_name]
                self.destroy_weights[op_name] = (self.destroy_weights[op_name] * decay +
                                               success_rate * (1 - decay))
            self.destroy_weights[op_name] = max(0.1, self.destroy_weights[op_name])  # 最小权重

        # 更新修复算子权重
        for op in self.repair_operators:
            op_name = op.__name__
            if self.repair_usage[op_name] > 0:
                success_rate = self.repair_success[op_name] / self.repair_usage[op_name]
                self.repair_weights[op_name] = (self.repair_weights[op_name] * decay +
                                              success_rate * (1 - decay))
            self.repair_weights[op_name] = max(0.1, self.repair_weights[op_name])  # 最小权重

    # ===== 破坏算子 =====

    def random_locker_removal(self, solution):
        """
        随机移除储物柜（修正版：只操作第一阶段决策变量）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution  # 至少保留一个储物柜

        # 移除数量：1到一半的储物柜
        num_to_remove = random.randint(1, max(1, len(open_lockers) // 2))
        lockers_to_remove = random.sample(open_lockers, num_to_remove)

        for j in lockers_to_remove:
            # 只移除第一阶段决策：储物柜选址和无人机配置
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def worst_locker_removal(self, solution):
        """
        移除贡献最小的储物柜
        贡献度 = 该储物柜对整体解质量的贡献，通过移除前后的目标函数差值计算
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution

        # 计算每个储物柜的真实贡献度（移除该储物柜后的成本增加）
        current_obj = self._calculate_objective_heuristic(solution, 0)
        locker_contributions = {}

        for j in open_lockers:
            # 创建移除储物柜j的临时解
            temp_solution = copy.deepcopy(solution)
            temp_solution['y'][j] = 0
            temp_solution['n'][j] = 0

            # 计算移除后的目标函数值
            temp_obj = self._calculate_objective_heuristic(temp_solution, 0)

            # 贡献度 = 移除后的成本增加（越小说明贡献越小）
            contribution = temp_obj - current_obj
            locker_contributions[j] = contribution

        # 移除贡献最小的储物柜（即移除后成本增加最少的）
        sorted_lockers = sorted(locker_contributions.items(), key=lambda x: x[1])
        num_to_remove = random.randint(1, max(1, len(open_lockers) // 3))  # 最多移除1/3

        for i in range(min(num_to_remove, len(sorted_lockers))):
            j = sorted_lockers[i][0]
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def related_locker_removal(self, solution):
        """
        移除地理位置相近的储物柜
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 1:
            return new_solution

        # 随机选择一个种子储物柜
        seed_locker = random.choice(open_lockers)

        # 找到与种子储物柜距离最近的其他储物柜
        distances = []
        for j in open_lockers:
            if j != seed_locker and j in self.problem.site_coords and seed_locker in self.problem.site_coords:
                coord1 = self.problem.site_coords[seed_locker]
                coord2 = self.problem.site_coords[j]
                dist = math.sqrt((coord1[0] - coord2[0])**2 + (coord1[1] - coord2[1])**2)
                distances.append((j, dist))

        # 按距离排序，移除最近的几个
        distances.sort(key=lambda x: x[1])
        num_to_remove = random.randint(1, max(1, min(3, len(distances))))

        for i in range(num_to_remove):
            j = distances[i][0]
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def drone_adjustment_removal(self, solution):
        """
        调整无人机配置（减少无人机数量）
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        # 随机选择几个储物柜    减少无人机
        num_to_adjust = random.randint(1, max(1, len(open_lockers)))
        lockers_to_adjust = random.sample(open_lockers, num_to_adjust)

        for j in lockers_to_adjust:
            current_drones = solution['n'][j]
            if current_drones > 1:
                reduction = random.randint(1, max(1, int(current_drones // 2)))
                new_solution['n'][j] = max(1, current_drones - reduction)

        return new_solution

    def cluster_removal(self, solution, iteration=0):
        """
        簇移除：基于客户分配关系，移除功能上相似的储物柜。
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in new_solution['y'].items() if val > 0.5]

        if len(open_lockers) <= 2:
            return new_solution

        # 1. 估算每个客户主要由哪个储物柜服务
        customer_main_locker = {}
        for i in self.problem.customers:
            best_j = -1
            min_dist = float('inf')
            for j in open_lockers:
                if (i, j) in self.problem.distance:
                    dist = self.problem.distance[i, j]
                    if dist < min_dist:
                        min_dist = dist
                        best_j = j
            if best_j != -1:
                customer_main_locker[i] = best_j

        # 2. 随机选择一个种子储物柜
        seed_locker = random.choice(open_lockers)

        # 3. 找到与种子储物柜服务相似客户群体的其他储物柜
        seed_customers = {i for i, j in customer_main_locker.items() if j == seed_locker}
        if not seed_customers:
            return new_solution  # 种子储物柜没服务客户，无法形成簇

        cluster_to_remove = {seed_locker}
        for j in open_lockers:
            if j != seed_locker:
                other_customers = {i for i, l in customer_main_locker.items() if l == j}
                # 计算Jaccard相似度
                intersection = len(seed_customers.intersection(other_customers))
                union = len(seed_customers.union(other_customers))
                if union > 0 and (intersection / union) > 0.2:  # 相似度阈值
                    cluster_to_remove.add(j)

        # 4. 移除整个簇（但至少保留一个储物柜）
        if len(open_lockers) - len(cluster_to_remove) < 1:
            # 如果要移除所有，则只移除一部分
            cluster_to_remove = set(random.sample(list(cluster_to_remove), len(cluster_to_remove) - 1))

        for j in cluster_to_remove:
            new_solution['y'][j] = 0
            new_solution['n'][j] = 0

        return new_solution

    def zone_removal(self, solution, iteration=0):
        """
        区域移除算子：基于地理位置的大范围破坏
        """
        try:
            y_star = solution['y']
            n_star = solution['n']
            open_lockers = [j for j, val in y_star.items() if val > 0.5]

            if len(open_lockers) <= 1:
                return solution

            # 随机选择一个中心储物柜
            center_j = random.choice(open_lockers)

            # 计算所有储物柜到中心的距离
            distances = []
            for j in open_lockers:
                if j != center_j:
                    # 使用客户作为中介计算储物柜间的"服务距离"
                    min_dist = float('inf')
                    for i in self.problem.customers:
                        if ((center_j, i) in self.problem.distance and
                            (j, i) in self.problem.distance):
                            dist = abs(self.problem.distance[center_j, i] - self.problem.distance[j, i])
                            min_dist = min(min_dist, dist)

                    if min_dist < float('inf'):
                        distances.append((j, min_dist))

            if not distances:
                return solution

            # 按距离排序，移除最近的几个储物柜（形成一个"空白区域"）
            distances.sort(key=lambda x: x[1])
            removal_count = min(len(distances), max(1, len(open_lockers) // 3))

            new_solution = copy.deepcopy(solution)
            new_solution['y'][center_j] = 0  # 移除中心
            new_solution['n'][center_j] = 0

            for i in range(removal_count):
                j = distances[i][0]
                new_solution['y'][j] = 0
                new_solution['n'][j] = 0

            return new_solution

        except Exception as e:
            return solution

    def radical_restructure(self, solution, iteration=0):
        """
        激进重构算子：大幅度改变解的结构，专门用于跳出局部最优
        """
        try:
            y_star = solution['y']
            n_star = solution['n']
            open_lockers = [j for j, val in y_star.items() if val > 0.5]

            if len(open_lockers) <= 2:
                return solution

            new_solution = copy.deepcopy(solution)

            # 策略1: 随机关闭50-70%的储物柜
            if random.random() < 0.4:
                removal_ratio = random.uniform(0.5, 0.7)
                removal_count = max(1, int(len(open_lockers) * removal_ratio))
                lockers_to_remove = random.sample(open_lockers, removal_count)

                for j in lockers_to_remove:
                    new_solution['y'][j] = 0
                    new_solution['n'][j] = 0

            # 策略2: 【改进】智能重新配置无人机分布
            elif random.random() < 0.7:
                # 保持储物柜选择，但智能重新分配无人机
                for j in open_lockers:
                    # 重新估算该储物柜的需求和推荐无人机数量
                    estimated_demand = self._estimate_locker_demand(j, new_solution)
                    recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

                    # 添加一些随机扰动以增加多样性
                    perturbation = random.randint(-1, 2)  # -1, 0, 1, 2的随机扰动
                    final_drones = max(1, recommended_drones + perturbation)
                    new_solution['n'][j] = min(final_drones, 8)  # 限制最大值

            # 策略3: 混合策略 - 部分关闭 + 重新配置
            else:
                # 关闭30-50%的储物柜
                removal_ratio = random.uniform(0.3, 0.5)
                removal_count = max(1, int(len(open_lockers) * removal_ratio))
                lockers_to_remove = random.sample(open_lockers, removal_count)

                for j in lockers_to_remove:
                    new_solution['y'][j] = 0
                    new_solution['n'][j] = 0

                # 【改进】对剩余储物柜智能重新配置无人机
                remaining_lockers = [j for j in open_lockers if j not in lockers_to_remove]
                for j in remaining_lockers:
                    # 重新估算需求和推荐无人机数量
                    estimated_demand = self._estimate_locker_demand(j, new_solution)
                    recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

                    # 添加随机扰动
                    perturbation = random.randint(-1, 2)
                    final_drones = max(1, recommended_drones + perturbation)
                    new_solution['n'][j] = min(final_drones, 8)

            return new_solution

        except Exception as e:
            return solution

    # ===== 修复算子 =====

    def greedy_locker_insertion(self, solution, iteration=0):
        """
        贪心插入储物柜（修正版：只操作第一阶段决策变量）
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 简化评估：选择能服务最多客户的储物柜
        best_locker = None
        best_drones = 1
        max_reachable_customers = 0

        for j in closed_lockers:
            # 计算该储物柜能服务的客户数量
            reachable_customers = 0
            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers += 1

            if reachable_customers > max_reachable_customers:
                max_reachable_customers = reachable_customers
                best_locker = j
                # 根据可服务客户数量估算无人机需求
                best_drones = min(3, max(1, reachable_customers // 10))

        # 插入最佳储物柜（只修改第一阶段决策）
        if best_locker is not None:
            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

        return new_solution

    def _estimate_insertion_delta(self, solution, locker_j, num_drones):
        """
        快速估算插入储物柜j的成本变化（增量评估）
        """
        # 1. 增加的固定成本
        delta_cost = self.problem.locker_fixed_cost[locker_j] + self.problem.drone_cost * num_drones

        # 2. 估算由于新储物柜的加入而减少的运输成本和惩罚成本
        cost_reduction = 0

        # 计算当前解中每个客户的最佳服务成本
        current_customer_costs = {}
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        for i in self.problem.customers:
            min_cost = float('inf')
            for j in open_lockers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, j]
                        min_cost = min(min_cost, transport_cost)

            if min_cost == float('inf'):
                # 客户无法被服务，使用惩罚成本
                current_customer_costs[i] = self.problem.penalty_cost_unassigned
            else:
                current_customer_costs[i] = min_cost

        # 计算新储物柜能为每个客户提供的服务成本
        for i in self.problem.customers:
            if (i, locker_j) in self.problem.distance:
                flight_distance = 2 * self.problem.distance[i, locker_j]
                if flight_distance <= self.problem.max_flight_distance:
                    new_transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, locker_j]

                    # 如果新储物柜能提供更好的服务，计算成本减少
                    if new_transport_cost < current_customer_costs[i]:
                        # 简化：假设客户需求按期望值分配
                        expected_demand = self.problem.expected_demand[i]
                        cost_reduction += (current_customer_costs[i] - new_transport_cost) * expected_demand

        return delta_cost - cost_reduction

    def regret_insertion(self, solution, iteration=0):
        """
        后悔值插入法（支持增量成本估算）
        """
        new_solution = copy.deepcopy(solution)
        closed_lockers = [j for j, val in solution['y'].items() if val < 0.5]

        if not closed_lockers:
            return new_solution

        # 计算每个储物柜的插入成本和后悔值
        insertion_costs = {}

        if self.config['use_delta_evaluation']:
            # 使用增量成本估算
            for j in closed_lockers:
                costs = []
                for num_drones in range(1, 4):
                    try:
                        delta_cost = self._estimate_insertion_delta(solution, j, num_drones)
                        costs.append((delta_cost, num_drones))
                    except:
                        costs.append((float('inf'), num_drones))

                # 排序找到最好和第二好的插入位置
                costs.sort()
                if len(costs) >= 2:
                    best_cost, best_drones = costs[0]
                    second_best_cost, _ = costs[1]
                    regret = second_best_cost - best_cost
                    insertion_costs[j] = (regret, best_cost, best_drones)
                elif len(costs) == 1:
                    best_cost, best_drones = costs[0]
                    insertion_costs[j] = (0, best_cost, best_drones)
        else:
            # 使用快速启发式评估
            current_obj = self._calculate_objective_heuristic(solution, 0)

            for j in closed_lockers:
                costs = []
                for num_drones in range(1, 4):
                    temp_solution = copy.deepcopy(solution)
                    temp_solution['y'][j] = 1
                    temp_solution['n'][j] = num_drones

                    try:
                        temp_obj = self._calculate_objective_heuristic(temp_solution, 0)
                        cost = temp_obj - current_obj
                        costs.append((cost, num_drones))
                    except:
                        costs.append((float('inf'), num_drones))

                # 排序找到最好和第二好的插入位置
                costs.sort()
                if len(costs) >= 2:
                    best_cost, best_drones = costs[0]
                    second_best_cost, _ = costs[1]
                    regret = second_best_cost - best_cost
                    insertion_costs[j] = (regret, best_cost, best_drones)
                elif len(costs) == 1:
                    best_cost, best_drones = costs[0]
                    insertion_costs[j] = (0, best_cost, best_drones)

        # 选择后悔值最大的储物柜插入
        if insertion_costs:
            best_locker = max(insertion_costs.keys(),
                            key=lambda x: insertion_costs[x][0])
            _, _, best_drones = insertion_costs[best_locker]

            new_solution['y'][best_locker] = 1
            new_solution['n'][best_locker] = best_drones

        return new_solution

    def drone_optimization(self, solution, iteration=0):
        """
        智能无人机配置优化：基于需求估算合理的无人机数量
        """
        new_solution = copy.deepcopy(solution)
        open_lockers = [j for j, val in solution['y'].items() if val > 0.5]

        if not open_lockers:
            return new_solution

        # 对每个开放的储物柜智能优化无人机数量
        for j in open_lockers:
            # 1. 估算该储物柜的服务需求
            estimated_demand = self._estimate_locker_demand(j, solution)

            # 2. 基于需求计算推荐的无人机数量
            recommended_drones = self._calculate_recommended_drones(j, estimated_demand)

            # 3. 在推荐值附近搜索最优配置
            best_drones = solution['n'][j]
            best_obj = self._calculate_objective_heuristic(solution, iteration)

            # 搜索范围：推荐值 ± 2，但至少包含1-5的基本范围
            search_range = set(range(1, 6))  # 基本范围
            search_range.update(range(max(1, recommended_drones - 2), recommended_drones + 3))  # 推荐值附近
            search_range = sorted(search_range)

            for num_drones in search_range:
                if num_drones == solution['n'][j]:
                    continue

                temp_solution = copy.deepcopy(solution)
                temp_solution['n'][j] = num_drones

                try:
                    temp_obj = self._calculate_objective_heuristic(temp_solution, iteration)
                    if temp_obj < best_obj:
                        best_obj = temp_obj
                        best_drones = num_drones
                except:
                    continue

            new_solution['n'][j] = best_drones

        return new_solution

    def _estimate_locker_demand(self, locker_j, solution):
        """
        估算储物柜j的服务需求量
        """
        # 获取该储物柜可以服务的客户
        if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
            # 使用预计算的可达性数据
            reachable_customers = []
            for i in self.problem.customers:
                if locker_j in self.problem.fast_solver.reachable_lockers.get(i, []):
                    reachable_customers.append(i)
        else:
            # 回退到距离计算
            reachable_customers = []
            for i in self.problem.customers:
                if (i, locker_j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, locker_j]
                    if flight_distance <= self.problem.max_flight_distance:
                        reachable_customers.append(i)

        if not reachable_customers:
            return 0

        # 估算该储物柜的潜在服务需求
        # 考虑与其他储物柜的竞争，使用保守估算
        selected_lockers = {j for j, val in solution['y'].items() if val > 0.5}
        total_potential_demand = sum(self.problem.expected_demand[i] for i in reachable_customers)

        # 计算竞争因子：该储物柜在所有可达储物柜中的"吸引力"
        competition_factor = 1.0
        if len(selected_lockers) > 1:
            # 简化的竞争模型：假设需求在可达储物柜间均匀分配
            avg_competitors = 0
            for i in reachable_customers:
                if hasattr(self.problem, 'fast_solver') and self.problem.fast_solver:
                    competitors = len([j for j in self.problem.fast_solver.reachable_lockers.get(i, []) if j in selected_lockers])
                else:
                    competitors = len([j for j in selected_lockers
                                     if (i, j) in self.problem.distance and
                                     2 * self.problem.distance[i, j] <= self.problem.max_flight_distance])
                avg_competitors += max(1, competitors)

            competition_factor = len(reachable_customers) / max(1, avg_competitors)

        estimated_demand = total_potential_demand * competition_factor
        return max(0, estimated_demand)

    def _calculate_recommended_drones(self, locker_j, estimated_demand):
        """
        基于估算需求计算推荐的无人机数量
        """
        if estimated_demand <= 1e-6:
            return 1  # 至少1架

        # 计算平均服务时间
        if hasattr(self.problem, 'fast_solver') and hasattr(self.problem.fast_solver, 'locker_avg_service_time'):
            avg_service_time = self.problem.fast_solver.locker_avg_service_time.get(locker_j, 0.5)
        else:
            # 回退到简化估算
            avg_service_time = 0.5  # 假设平均服务时间为0.5小时

        if avg_service_time <= 0:
            return 1

        # 计算所需的总工作时间
        total_work_hours = estimated_demand * avg_service_time

        # 计算所需的无人机数量
        required_drones = math.ceil(total_work_hours / self.problem.H_drone_working_hours_per_day)

        # 添加一定的缓冲（10-20%）
        buffer_factor = random.uniform(1.1, 1.2)
        recommended_drones = math.ceil(required_drones * buffer_factor)

        # 限制在合理范围内
        return max(1, min(recommended_drones, 8))

# ---------------------------------------------------------------------------
# 快速客户分配求解器（第二阶段子问题辅助工具）
# ---------------------------------------------------------------------------
class CustomerAssignmentALNS:
    """
    快速客户分配求解器

    这不是一个独立的ALNS算法，而是ALNS_Solver的辅助工具，
    专门用于快速求解第二阶段客户分配子问题。

    当ALNS_Solver优化第一阶段决策(y, n)时，需要评估每个候选解
    在多个需求场景下的成本，这就需要快速求解大量的客户分配子问题。

    该类提供了：
    1. 快速贪心启发式算法（主要使用）
    2. 简化的ALNS算法（可选，用于提高解质量）
    """

    def __init__(self, problem_instance):
        self.problem = problem_instance

        # 客户分配专用的破坏算子
        self.destroy_operators = [
            self.random_customer_removal,      # 随机移除客户分配
            self.worst_assignment_removal,     # 移除成本最高的分配
            self.locker_based_removal,         # 基于储物柜的批量移除
            self.distance_based_removal        # 基于距离的移除
        ]

        # 客户分配专用的修复算子
        self.repair_operators = [
            self.greedy_customer_insertion,    # 贪心插入客户
            self.regret_customer_insertion,    # 后悔值插入
            self.capacity_aware_insertion      # 容量感知插入
        ]

        # 算子权重
        self.destroy_weights = {op.__name__: 1.0 for op in self.destroy_operators}
        self.repair_weights = {op.__name__: 1.0 for op in self.repair_operators}

        # 预计算优化数据结构
        self._precompute_efficiency_data()
        self._precompute_drone_capacities()

        # 缓存相关
        self.cache = {}
        self.cache_hits = 0
        self.cache_misses = 0

    def solve_assignment_alns(self, y_star, n_star, selected_lockers, demand_scenario, time_limit=5):
        """
        使用ALNS算法快速求解单个场景的客户分配问题

        Args:
            y_star: 储物柜选址决策
            n_star: 无人机配置决策
            selected_lockers: 选中的储物柜列表
            demand_scenario: 需求场景
            time_limit: 时间限制（秒）

        Returns:
            Dict: 客户分配方案 {(customer, locker): quantity}
        """
        # 生成初始解（贪心分配）
        current_solution = self._create_initial_assignment(y_star, n_star, selected_lockers, demand_scenario)

        # 如果问题规模很小，直接返回贪心解
        problem_size = len(self.problem.customers) * len(selected_lockers)
        if problem_size <= 50:
            return current_solution

        # ALNS主循环（简化版，专注速度）
        best_solution = current_solution.copy()
        best_obj = self._calculate_assignment_cost(current_solution, demand_scenario)
        current_obj = best_obj

        temperature = 10.0  # 较低的初始温度
        max_iterations = min(100, problem_size // 5)  # 根据问题规模调整迭代次数
        cooling_rate = 0.95

        start_time = time.time()

        for iteration in range(max_iterations):
            if time.time() - start_time > time_limit:
                break

            # 选择算子（简化的轮盘赌选择）
            destroy_op = self._select_operator_simple(self.destroy_operators, self.destroy_weights)
            repair_op = self._select_operator_simple(self.repair_operators, self.repair_weights)

            # 生成新解
            try:
                temp_solution = destroy_op(current_solution, demand_scenario, selected_lockers)
                new_solution = repair_op(temp_solution, y_star, n_star, selected_lockers, demand_scenario)

                if new_solution is None:
                    continue

                # 评估新解
                new_obj = self._calculate_assignment_cost(new_solution, demand_scenario)

                # 接受准则（简化的模拟退火）
                accept = False
                delta = new_obj - current_obj

                if delta < 0:  # 改进解
                    accept = True
                elif temperature > 0 and random.random() < math.exp(-delta / temperature):
                    accept = True

                if accept:
                    current_solution = new_solution
                    current_obj = new_obj

                    if new_obj < best_obj:
                        best_solution = new_solution.copy()
                        best_obj = new_obj

                # 降温
                temperature *= cooling_rate

            except Exception as e:
                continue

        return best_solution

    def _select_operator_simple(self, operators, weights):
        """根据权重选择算子"""
        total_weight = sum(weights[op.__name__] for op in operators)
        if total_weight <= 0:
            return random.choice(operators)

        rand_val = random.uniform(0, total_weight)
        cumulative_weight = 0

        for op in operators:
            cumulative_weight += weights[op.__name__]
            if rand_val <= cumulative_weight:
                return op

        return operators[-1]

    def _precompute_efficiency_data(self):
        """
        预计算客户-储物柜的效率信息，避免重复计算
        """
        self.customer_locker_efficiency = {}
        self.reachable_lockers = {}

        for i in self.problem.customers:
            self.reachable_lockers[i] = []
            self.customer_locker_efficiency[i] = {}

            for j in self.problem.sites:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        # 成本效率 = 1 / (运输成本 + 距离惩罚)
                        transport_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[i, j]
                        efficiency = 1.0 / (transport_cost + self.problem.distance[i, j])
                        self.customer_locker_efficiency[i][j] = efficiency
                        self.reachable_lockers[i].append(j)

            # 预排序：按效率从高到低
            self.reachable_lockers[i].sort(
                key=lambda j: self.customer_locker_efficiency[i][j],
                reverse=True
            )

    def _precompute_drone_capacities(self):
        """
        预计算每个储物柜的无人机运力相关数据
        """
        self.locker_avg_service_time = {}
        self.locker_reachable_customers = {}

        for j in self.problem.sites:
            total_distance = 0
            reachable_customers = 0
            self.locker_reachable_customers[j] = []

            for i in self.problem.customers:
                if (i, j) in self.problem.distance:
                    flight_distance = 2 * self.problem.distance[i, j]
                    if flight_distance <= self.problem.max_flight_distance:
                        total_distance += self.problem.distance[i, j]
                        reachable_customers += 1
                        self.locker_reachable_customers[j].append(i)

            if reachable_customers > 0:
                avg_distance = total_distance / reachable_customers
                self.locker_avg_service_time[j] = (2 * avg_distance / self.problem.drone_speed) + self.problem.loading_time
            else:
                self.locker_avg_service_time[j] = float('inf')

    # ===== ALNS破坏算子 =====

    def random_customer_removal(self, assignment, demand_scenario, selected_lockers):
        """随机移除部分客户的分配"""
        new_assignment = assignment.copy()
        if not assignment:
            return new_assignment

        # 随机选择要移除的分配数量（10-30%）
        num_to_remove = max(1, min(len(assignment) // 3, len(assignment) * 3 // 10))
        keys_to_remove = random.sample(list(assignment.keys()), num_to_remove)

        for key in keys_to_remove:
            if key in new_assignment:
                del new_assignment[key]

        return new_assignment

    def worst_assignment_removal(self, assignment, demand_scenario, selected_lockers):
        """移除成本最高的分配"""
        new_assignment = assignment.copy()
        if not assignment:
            return new_assignment

        # 计算每个分配的单位成本
        assignment_costs = []
        for (customer, locker), quantity in assignment.items():
            if quantity > 0 and (customer, locker) in self.problem.distance:
                unit_cost = 2 * self.problem.transport_unit_cost * self.problem.distance[customer, locker]
                assignment_costs.append((unit_cost, (customer, locker)))

        if not assignment_costs:
            return new_assignment

        # 按成本排序，移除最贵的20-40%
        assignment_costs.sort(reverse=True)
        num_to_remove = max(1, len(assignment_costs) // 3)

        for i in range(min(num_to_remove, len(assignment_costs))):
            _, key = assignment_costs[i]
            if key in new_assignment:
                del new_assignment[key]

        return new_assignment

    def locker_based_removal(self, assignment, demand_scenario, selected_lockers):
        """移除某个储物柜的所有客户分配"""
        new_assignment = assignment.copy()
        if not assignment or not selected_lockers:
            return new_assignment

        # 选择一个储物柜，移除其所有分配
        assigned_lockers = list(set(j for (i, j) in assignment.keys()))
        if assigned_lockers:
            target_locker = random.choice(assigned_lockers)
            keys_to_remove = [(i, j) for (i, j) in assignment.keys() if j == target_locker]

            for key in keys_to_remove:
                if key in new_assignment:
                    del new_assignment[key]

        return new_assignment

    def distance_based_removal(self, assignment, demand_scenario, selected_lockers):
        """基于距离移除分配（移除距离较远的分配）"""
        new_assignment = assignment.copy()
        if not assignment:
            return new_assignment

        # 计算每个分配的距离
        assignment_distances = []
        for (customer, locker), quantity in assignment.items():
            if quantity > 0 and (customer, locker) in self.problem.distance:
                distance = self.problem.distance[customer, locker]
                assignment_distances.append((distance, (customer, locker)))

        if not assignment_distances:
            return new_assignment

        # 按距离排序，移除最远的25%
        assignment_distances.sort(reverse=True)
        num_to_remove = max(1, len(assignment_distances) // 4)

        for i in range(min(num_to_remove, len(assignment_distances))):
            _, key = assignment_distances[i]
            if key in new_assignment:
                del new_assignment[key]

        return new_assignment

    # ===== ALNS修复算子 =====

    def greedy_customer_insertion(self, partial_assignment, y_star, n_star, selected_lockers, demand_scenario):
        """贪心插入未分配的客户"""
        new_assignment = partial_assignment.copy()

        # 找出未分配的客户需求
        unassigned_demand = {}
        for customer, demand in demand_scenario.items():
            assigned = sum(partial_assignment.get((customer, j), 0) for j in selected_lockers)
            if assigned < demand:
                unassigned_demand[customer] = demand - assigned

        if not unassigned_demand:
            return new_assignment

        # 计算剩余容量
        remaining_locker_capacity = {}
        remaining_drone_capacity = {}

        for j in selected_lockers:
            used_locker = sum(partial_assignment.get((i, j), 0) for i in demand_scenario.keys())
            used_drone_time = sum(partial_assignment.get((i, j), 0) * self.problem.service_time_per_unit
                                for i in demand_scenario.keys())

            remaining_locker_capacity[j] = self.problem.Q_locker_capacity[j] - used_locker
            remaining_drone_capacity[j] = n_star[j] * self.problem.H_drone_working_hours_per_day - used_drone_time

        # 贪心分配
        for customer in sorted(unassigned_demand.keys(), key=lambda x: unassigned_demand[x], reverse=True):
            remaining = unassigned_demand[customer]

            # 按成本效益排序储物柜
            locker_costs = []
            for j in selected_lockers:
                if (customer, j) in self.problem.distance:
                    cost = 2 * self.problem.transport_unit_cost * self.problem.distance[customer, j]
                    available_locker = max(0, remaining_locker_capacity[j])
                    available_drone = max(0, remaining_drone_capacity[j] / self.problem.service_time_per_unit)
                    available = min(available_locker, available_drone)
                    if available > 0:
                        locker_costs.append((cost, j, available))

            locker_costs.sort()  # 按成本排序

            for cost, j, available in locker_costs:
                if remaining <= 0:
                    break

                assign_qty = min(remaining, available)
                if assign_qty > 0:
                    new_assignment[(customer, j)] = new_assignment.get((customer, j), 0) + assign_qty
                    remaining_locker_capacity[j] -= assign_qty
                    remaining_drone_capacity[j] -= assign_qty * self.problem.service_time_per_unit
                    remaining -= assign_qty

        return new_assignment

    def regret_customer_insertion(self, partial_assignment, y_star, n_star, selected_lockers, demand_scenario):
        """后悔值插入法"""
        new_assignment = partial_assignment.copy()

        # 找出未分配的客户需求
        unassigned_demand = {}
        for customer, demand in demand_scenario.items():
            assigned = sum(partial_assignment.get((customer, j), 0) for j in selected_lockers)
            if assigned < demand:
                unassigned_demand[customer] = demand - assigned

        if not unassigned_demand:
            return new_assignment

        # 计算剩余容量
        remaining_locker_capacity = {}
        remaining_drone_capacity = {}

        for j in selected_lockers:
            used_locker = sum(partial_assignment.get((i, j), 0) for i in demand_scenario.keys())
            used_drone_time = sum(partial_assignment.get((i, j), 0) * self.problem.service_time_per_unit
                                for i in demand_scenario.keys())

            remaining_locker_capacity[j] = self.problem.Q_locker_capacity[j] - used_locker
            remaining_drone_capacity[j] = n_star[j] * self.problem.H_drone_working_hours_per_day - used_drone_time

        # 计算每个客户的后悔值
        customer_regrets = []
        for customer, remaining_demand in unassigned_demand.items():
            costs = []
            for j in selected_lockers:
                if (customer, j) in self.problem.distance:
                    cost = 2 * self.problem.transport_unit_cost * self.problem.distance[customer, j]
                    available_locker = max(0, remaining_locker_capacity[j])
                    available_drone = max(0, remaining_drone_capacity[j] / self.problem.service_time_per_unit)
                    available = min(available_locker, available_drone)
                    if available > 0:
                        costs.append(cost)

            if len(costs) >= 2:
                costs.sort()
                regret = costs[1] - costs[0]  # 第二便宜 - 最便宜
                customer_regrets.append((regret, customer))
            elif len(costs) == 1:
                customer_regrets.append((float('inf'), customer))  # 只有一个选择，后悔值无穷大

        # 按后悔值排序，优先分配后悔值大的客户
        customer_regrets.sort(reverse=True)

        for regret, customer in customer_regrets:
            remaining = unassigned_demand[customer]

            # 找最便宜的储物柜
            best_cost = float('inf')
            best_locker = None
            best_available = 0

            for j in selected_lockers:
                if (customer, j) in self.problem.distance:
                    cost = 2 * self.problem.transport_unit_cost * self.problem.distance[customer, j]
                    available_locker = max(0, remaining_locker_capacity[j])
                    available_drone = max(0, remaining_drone_capacity[j] / self.problem.service_time_per_unit)
                    available = min(available_locker, available_drone)
                    if available > 0 and cost < best_cost:
                        best_cost = cost
                        best_locker = j
                        best_available = available

            if best_locker is not None:
                assign_qty = min(remaining, best_available)
                if assign_qty > 0:
                    new_assignment[(customer, best_locker)] = new_assignment.get((customer, best_locker), 0) + assign_qty
                    remaining_locker_capacity[best_locker] -= assign_qty
                    remaining_drone_capacity[best_locker] -= assign_qty * self.problem.service_time_per_unit

        return new_assignment

    def capacity_aware_insertion(self, partial_assignment, y_star, n_star, selected_lockers, demand_scenario):
        """容量感知插入法"""
        # 这个方法与贪心插入类似，但更注重容量平衡
        return self.greedy_customer_insertion(partial_assignment, y_star, n_star, selected_lockers, demand_scenario)

    # ===== ALNS辅助方法 =====

    def _create_initial_assignment(self, y_star, n_star, selected_lockers, demand_scenario):
        """创建初始分配解（使用贪心算法）"""
        assignment = {}

        # 计算储物柜容量
        locker_capacities = {}
        drone_capacities = {}

        for j in selected_lockers:
            locker_capacities[j] = self.problem.Q_locker_capacity[j]
            drone_capacities[j] = n_star[j] * self.problem.H_drone_working_hours_per_day

        # 按需求量排序客户（优先分配高需求客户）
        customers_by_demand = sorted(demand_scenario.items(), key=lambda x: x[1], reverse=True)

        # 贪心分配
        for customer, demand in customers_by_demand:
            remaining_demand = demand

            # 获取该客户的可达储物柜（按效率排序）
            if customer in self.reachable_lockers:
                reachable = [j for j in self.reachable_lockers[customer] if j in selected_lockers]
            else:
                reachable = selected_lockers

            for j in reachable:
                if remaining_demand <= 0:
                    break

                if (customer, j) in self.problem.distance:
                    # 检查容量约束
                    available_locker = locker_capacities[j]
                    available_drone = drone_capacities[j] / self.problem.service_time_per_unit
                    available = min(available_locker, available_drone, remaining_demand)

                    if available > 0:
                        assignment[(customer, j)] = available
                        locker_capacities[j] -= available
                        drone_capacities[j] -= available * self.problem.service_time_per_unit
                        remaining_demand -= available

        return assignment

    def _calculate_assignment_cost(self, assignment, demand_scenario):
        """计算分配方案的总成本"""
        transport_cost = 0
        penalty_cost = 0

        # 计算运输成本
        for (customer, locker), quantity in assignment.items():
            if quantity > 0 and (customer, locker) in self.problem.distance:
                transport_cost += 2 * self.problem.transport_unit_cost * self.problem.distance[customer, locker] * quantity

        # 计算惩罚成本
        for customer, demand in demand_scenario.items():
            assigned = sum(assignment.get((customer, j), 0) for j in self.problem.sites)
            shortage = max(0, demand - assigned)
            penalty_cost += shortage * self.problem.penalty_cost_unassigned

        return transport_cost + penalty_cost

    def solve_assignment_heuristic(self, y_star, n_star, selected_lockers, demand_scenario):
        """
        使用超优化的贪心启发式快速求解客户分配问题
        """
        # 快速检查：如果没有选中的储物柜，直接返回空分配
        if not selected_lockers:
            return {(i, j): 0.0 for i in self.problem.customers for j in self.problem.sites}

        # 缓存检查
        if hasattr(self.problem, 'use_assignment_cache') and self.problem.use_assignment_cache:
            cache_key = self._create_cache_key(selected_lockers, n_star, demand_scenario)
            if cache_key in self.cache:
                self.cache_hits += 1
                return self.cache[cache_key].copy()
            self.cache_misses += 1

        # 使用集合进行快速成员检查
        selected_lockers_set = set(selected_lockers)

        # 预分配结果字典，避免重复创建
        assignment = {}

        # 预计算储物柜的容量和无人机运力
        locker_capacities = {}
        locker_drone_capacities = {}
        locker_current_loads = {}

        for j in selected_lockers:
            locker_capacities[j] = self.problem.Q_locker_capacity[j]
            locker_drone_capacities[j] = self._get_drone_capacity_fast(j, n_star.get(j, 0))
            locker_current_loads[j] = 0.0

        # 初始化分配矩阵（只为选中的储物柜）
        for i in self.problem.customers:
            for j in selected_lockers:
                assignment[(i, j)] = 0.0

        # 按需求量排序客户，优先处理高需求客户
        customers_by_demand = [(i, demand_scenario[i]) for i in self.problem.customers if demand_scenario[i] > 1e-6]
        customers_by_demand.sort(key=lambda x: x[1], reverse=True)

        # 为每个客户贪心分配
        for i, total_demand in customers_by_demand:
            remaining_demand = total_demand

            # 获取该客户的可达储物柜（已按效率排序，只考虑选中的）
            reachable_selected = [j for j in self.reachable_lockers[i] if j in selected_lockers_set]

            # 贪心分配
            for j in reachable_selected:
                if remaining_demand <= 1e-6:
                    break

                # 快速计算可分配量
                available_capacity = locker_capacities[j] - locker_current_loads[j]
                available_drone_capacity = locker_drone_capacities[j] - locker_current_loads[j]

                if available_capacity <= 1e-6 or available_drone_capacity <= 1e-6:
                    continue

                max_assignable = min(remaining_demand, available_capacity, available_drone_capacity)

                if max_assignable > 1e-6:
                    assignment[(i, j)] = max_assignable
                    locker_current_loads[j] += max_assignable
                    remaining_demand -= max_assignable

        # 缓存结果
        if hasattr(self.problem, 'use_assignment_cache') and self.problem.use_assignment_cache:
            if 'cache_key' in locals():
                self.cache[cache_key] = assignment.copy()
                # 限制缓存大小，避免内存溢出
                if len(self.cache) > 10000:
                    # 删除最旧的一半缓存
                    keys_to_remove = list(self.cache.keys())[:5000]
                    for key in keys_to_remove:
                        del self.cache[key]

        return assignment

    def _create_cache_key(self, selected_lockers, n_star, demand_scenario):
        """
        创建缓存键
        """
        # 创建一个基于输入参数的哈希键
        lockers_tuple = tuple(sorted(selected_lockers))
        drones_tuple = tuple(n_star.get(j, 0) for j in selected_lockers)
        demand_tuple = tuple(round(demand_scenario.get(i, 0), 2) for i in sorted(self.problem.customers))
        return (lockers_tuple, drones_tuple, demand_tuple)

    def _get_drone_capacity_fast(self, locker_j, num_drones):
        """
        快速计算储物柜j的无人机运力（使用预计算数据）
        """
        if num_drones <= 0:
            return 0

        avg_service_time = self.locker_avg_service_time.get(locker_j, float('inf'))
        if avg_service_time == float('inf'):
            return 0

        # 每架无人机的日运力
        single_drone_capacity = self.problem.H_drone_working_hours_per_day / avg_service_time
        return num_drones * single_drone_capacity

# ---------------------------------------------------------------------------
# StochasticDroneDeliveryOptimizerSAA 类的定义
# ---------------------------------------------------------------------------
class StochasticDroneDeliveryOptimizerSAA:

    def __init__(self):
        self.customers = []
        self.sites = []
        self.expected_demand = {}
        self.distance = {}
        self.locker_fixed_cost = {}
        self.Q_locker_capacity = {}
        self.drone_speed = None
        self.loading_time = None
        self.max_flight_distance = None
        self.transport_unit_cost = None
        self.drone_cost = None
        self.H_drone_working_hours_per_day = None
        self.penalty_cost_unassigned = None
        self.service_time_per_unit = None  # 添加缺失的属性
        self.customer_coords = {}
        self.site_coords = {}
        self.depot_coord = None
        self.truck_capacity = None
        self.truck_fixed_cost = None
        self.truck_km_cost = None
        self.truck_distances = {}  # 卡车距离矩阵

        # SAA 相关结果存储
        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = [] # 存储每次复制在K'个样本上的平均卡车成本
        self.best_solution_validation_costs = []  # 存储最佳解在每个验证场景上的成本

        # DRL求解器实例 (每个优化器实例共享，如果参数不变)
        self._drl_solver = None
        self._drl_solver_params = None
        self._drl_solver_no_plots = None
        self._drl_solver_no_plots_params = None





        # 快速启发式求解器
        self.fast_solver = None
        self.use_assignment_cache = True  # 是否使用分配结果缓存
        self.assignment_cache = {}  # 缓存分配结果


    def set_parameters(self,
                       customers: List,
                       sites: List,
                       expected_demand: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        self.customers = customers
        self.sites = sites
        self.expected_demand = expected_demand
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned
        self.Q_locker_capacity = Q_locker_capacity
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        # 计算平均服务时间（用于容量约束）
        self._calculate_service_time_per_unit()

        # 计算卡车距离矩阵（仓库到储物柜，储物柜间距离）
        self._build_truck_distance_matrix()

    def _calculate_service_time_per_unit(self):
        """
        计算平均单位服务时间，用于无人机容量约束
        基于所有客户-储物柜对的平均距离计算
        """
        if not self.distance or not self.drone_speed or self.loading_time is None:
            # 如果缺少必要参数，使用默认值
            self.service_time_per_unit = 0.5  # 默认0.5小时/单位
            return

        total_service_time = 0
        count = 0

        # 计算所有可达客户-储物柜对的平均服务时间
        for (i, j), distance in self.distance.items():
            if i in self.customers and j in self.sites:
                flight_distance = 2 * distance  # 往返距离
                if flight_distance <= self.max_flight_distance:
                    service_time = (flight_distance / self.drone_speed) + self.loading_time
                    total_service_time += service_time
                    count += 1

        if count > 0:
            self.service_time_per_unit = total_service_time / count
        else:
            # 如果没有可达的客户-储物柜对，使用基于平均距离的估算
            if self.distance:
                avg_distance = sum(self.distance.values()) / len(self.distance)
                self.service_time_per_unit = (2 * avg_distance / self.drone_speed) + self.loading_time
            else:
                self.service_time_per_unit = 0.5  # 默认值

    def _build_truck_distance_matrix(self):
        """构建卡车距离矩阵，包括仓库到储物柜和储物柜间的距离"""
        self.truck_distances = {}

        if not self.depot_coord or not self.site_coords:
            return

        # 仓库到储物柜的距离
        for j in self.sites:
            if j in self.site_coords:
                dist = math.sqrt((self.depot_coord[0] - self.site_coords[j][0])**2 +
                               (self.depot_coord[1] - self.site_coords[j][1])**2)
                self.truck_distances[(0, j)] = dist
                self.truck_distances[(j, 0)] = dist

        # 储物柜间的距离
        for i in self.sites:
            for j in self.sites:
                if i != j and i in self.site_coords and j in self.site_coords:
                    dist = math.sqrt((self.site_coords[i][0] - self.site_coords[j][0])**2 +
                                   (self.site_coords[i][1] - self.site_coords[j][1])**2)
                    self.truck_distances[(i, j)] = dist

    def _get_drl_solver(self, make_plots: bool = True):
        if not DRL_AVAILABLE: return None # 如果DRL不可用，返回None

        current_params = (self.depot_coord, self.truck_capacity, self.truck_fixed_cost, self.truck_km_cost)
        if make_plots:
            if self._drl_solver is None or self._drl_solver_params != current_params:
                set_drl_log_level(logging.WARNING)
                self._drl_solver = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=True
                )
                self._drl_solver_params = current_params
            return self._drl_solver
        else:
            if self._drl_solver_no_plots is None or self._drl_solver_no_plots_params != current_params:
                set_drl_log_level(logging.WARNING)
                self._drl_solver_no_plots = DRL_CVRP_Solver(
                    depot_coord=self.depot_coord, truck_capacity=self.truck_capacity,
                    truck_fixed_cost=self.truck_fixed_cost, truck_km_cost=self.truck_km_cost,
                    keep_temp_files=False, max_temp_files=3, make_plots=False
                )
                self._drl_solver_no_plots_params = current_params
            return self._drl_solver_no_plots

    def calculate_truck_cost(self,
                             selected_lockers: List[int] = None,
                             x_qty_solution_values: Dict[Tuple[int, int], float] = None,
                             make_plots: bool = True,
                             return_route_info: bool = False,
                             active_lockers_info_override: Dict[int, Dict[str, Any]] = None):
        if not DRL_AVAILABLE or self.truck_capacity is None or self.truck_fixed_cost is None or self.truck_km_cost is None:
            # print("  [calculate_truck_cost] DRL不可用或卡车参数未设置，卡车成本返回0。")
            return (0.0, None) if return_route_info else 0.0

        # 如果提供了override参数，直接使用它
        if active_lockers_info_override is not None:
            if not active_lockers_info_override:
                return (0.0, None) if return_route_info else 0.0
            try:
                drl_solver = self._get_drl_solver(make_plots=make_plots)
                if drl_solver is None:
                    return (0.0, None) if return_route_info else 0.0

                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info_override, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            except Exception as e:
                # 使用简化估算
                total_demand = sum(info['demand'] for info in active_lockers_info_override.values())
                num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost

        # 原有逻辑：从selected_lockers和x_qty_solution_values构建active_lockers_info
        # 在修正的模型中，这个逻辑主要用于向后兼容
        if selected_lockers is None or not selected_lockers:
            return (0.0, None) if return_route_info else 0.0

        if x_qty_solution_values is None:
            # 如果没有客户分配信息，返回0成本
            return (0.0, None) if return_route_info else 0.0

        try:
            drl_solver = self._get_drl_solver(make_plots=make_plots)
            if drl_solver is None: return (0.0, None) if return_route_info else 0.0 # Double check

            locker_total_demands = {locker_id: 0.0 for locker_id in selected_lockers}
            for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                if locker_id in selected_lockers and quantity > 1e-6:
                    locker_total_demands[locker_id] += quantity
            active_lockers_info = {}
            for locker_id in selected_lockers:
                demand_at_locker = locker_total_demands.get(locker_id, 0)
                if demand_at_locker > 1e-6:
                    if locker_id in self.site_coords:
                        active_lockers_info[locker_id] = {
                            'coord': self.site_coords[locker_id],
                            'demand': round(demand_at_locker)  # 四舍五入为整数
                        }
            if active_lockers_info:
                truck_cost, route_info_drl = drl_solver.solve(active_lockers_info, return_route_info=True)
                return (truck_cost, route_info_drl) if return_route_info else truck_cost
            else:
                return (0.0, None) if return_route_info else 0.0
        except Exception as e:
            # print(f"  [calculate_truck_cost] DRL求解失败: {str(e)}, 使用简化估算")
            if x_qty_solution_values:
                total_demand_overall = 0
                for (customer_id, locker_id), quantity in x_qty_solution_values.items():
                    if locker_id in selected_lockers and quantity > 1e-6:
                        total_demand_overall += quantity
                num_trucks = math.ceil(total_demand_overall / self.truck_capacity) if self.truck_capacity > 0 else 1
                simplified_cost = num_trucks * self.truck_fixed_cost
                return (simplified_cost, None) if return_route_info else simplified_cost
            else:
                return (0.0, None) if return_route_info else 0.0

    def _generate_demand_samples(self, num_samples: int) -> List[Dict[int, float]]:
        """
        生成需求场景样本，客户需求服从泊松分布
        泊松分布的参数λ等于期望需求值，天然产生非负整数
        """
        demand_scenarios = []
        for _ in range(num_samples):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]  # 泊松分布参数λ
                # 使用泊松分布生成需求，λ既是均值也是方差
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)  # 转换为float以保持一致性
            demand_scenarios.append(current_scenario_demand)
        return demand_scenarios



    def calculate_objective(self, solution, demand_samples_k):
        """
        计算第一阶段解的目标函数值（修正版两阶段结构）

        现在解只包含第一阶段决策变量：
        - y: 储物柜选址
        - n: 无人机配置

        给定第一阶段决策，对每个需求场景求解第二阶段子问题，计算期望总成本。
        """
        y_star = solution['y']
        n_star = solution['n']
        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        if not selected_lockers:
            return float('inf')  # 无效解

        # 1. 计算第一阶段成本
        first_stage_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers) + \
                          sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)

        # 2. 对每个需求场景求解第二阶段子问题，计算期望成本
        total_second_stage_costs = 0
        batch_active_lockers_info = []

        try:
            # 对每个需求场景求解第二阶段客户分配子问题
            for k_idx, demand_scenario in enumerate(demand_samples_k):
                # 求解第二阶段最优客户分配
                optimal_assignment = self._solve_optimal_assignment_for_scenario(
                    y_star, n_star, selected_lockers, demand_scenario
                )

                # 计算该场景下的第二阶段成本
                transport_cost_k = sum(2 * self.transport_unit_cost * self.distance[i, j] * quantity
                                      for (i, j), quantity in optimal_assignment.items()
                                      if (i, j) in self.distance)

                # 计算惩罚成本
                total_assigned = sum(optimal_assignment.values())
                total_demand = sum(demand_scenario.values())
                penalty_cost_k = self.penalty_cost_unassigned * max(0, total_demand - total_assigned)

                # 准备卡车成本计算数据
                locker_demands = {j: sum(optimal_assignment.get((i, j), 0) for i in self.customers)
                                for j in selected_lockers}
                active_info = {j: {'coord': self.site_coords[j], 'demand': round(demand)}
                              for j, demand in locker_demands.items() if demand > 1e-6}
                batch_active_lockers_info.append(active_info)

                # 累加第二阶段成本（不含卡车成本）
                total_second_stage_costs += (transport_cost_k + penalty_cost_k)

            # 批量计算卡车成本
            batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)
            avg_truck_cost = sum(batch_truck_costs) / len(batch_truck_costs) if batch_truck_costs else 0

            # 计算期望值
            avg_second_stage_cost = total_second_stage_costs / len(demand_samples_k)

            # 返回总期望成本
            return first_stage_cost + avg_second_stage_cost + avg_truck_cost

        except Exception as e:
            # 如果计算失败，返回一个很大的值
            print(f"    两阶段目标函数计算失败: {str(e)}")
            return float('inf')

    def solve_saa_with_alns(self, time_limit_per_replication: int = 300):
        """
        使用ALNS统一求解器求解SAA问题

        SAA方法论：
        1. 对每个复制m，生成K个需求样本
        2. 使用ALNS求解确定性的两阶段问题：
           - 第一阶段：储物柜选址和无人机配置（ALNS优化）
           - 第二阶段：客户分配（快速启发式求解）
        3. 在固定的K'个验证样本上评估解质量
        4. 计算统计下界和上界，检查收敛条件

        ALNS在这里是统一的求解器，不是分阶段的算法。
        """
        print(f"\n开始SAA优化（使用ALNS），最多 {SAA_MAX_REPLICATIONS_M} 次复制...")
        print(f"SAA终止条件 (必须同时满足):")
        print(f"  1. 相对差距阈值: Gap/UB ≤ {SAA_GAP_TOLERANCE_PERCENT*100:.0f}% 且 Gap ≥ 0")
        print(f"  2. 方差阈值: δ²_Gap/UB ≤ {SAA_VARIANCE_TOLERANCE_PERCENT*100:.0f}%")
        print(f"  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)")
        print(f"  最少需要 {SAA_MIN_REPLICATIONS_M} 次有效复制")

        # 生成固定的验证样本集 (所有复制共用)
        print(f"生成 {SAA_SAMPLES_K_PRIME} 个固定验证样本 (所有复制共用)...")

        # 重置随机种子以确保验证样本一致性
        original_random_state = random.getstate()
        original_np_state = np.random.get_state()
        random.seed(RANDOM_SEED + 1000)  # 使用不同的种子避免与训练样本重复
        np.random.seed(RANDOM_SEED + 1000)

        self.fixed_validation_samples = self._generate_demand_samples(num_samples=SAA_SAMPLES_K_PRIME)

        # 恢复随机状态
        random.setstate(original_random_state)
        np.random.set_state(original_np_state)

        # 调试：输出前几个样本的统计信息
        if len(self.fixed_validation_samples) > 0:
            first_sample = self.fixed_validation_samples[0]
            total_demand_first = sum(first_sample.values())
            print(f"  调试：第一个验证样本总需求 = {total_demand_first:.2f}")

            # 更可靠的样本一致性检查
            sample_checksum = sum(sum(sample.values()) for sample in self.fixed_validation_samples[:10])
            first_10_demands = [sum(sample.values()) for sample in self.fixed_validation_samples[:10]]
            print(f"  调试：前10个样本总需求和 = {sample_checksum:.2f}")
            print(f"  调试：前10个样本需求 = {[f'{d:.2f}' for d in first_10_demands]}")

        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = []

        best_solution_info = {'y': None, 'n': None, 'avg_obj_k_prime': float('inf'), 'replication_idx': -1, 'truck_cost_k_prime': 0.0}

        for m_rep in range(SAA_MAX_REPLICATIONS_M):
            print(f"\n--- SAA 复制 {m_rep + 1}/{SAA_MAX_REPLICATIONS_M} (使用ALNS) ---")
            demand_samples_for_k = self._generate_demand_samples(num_samples=SAA_SAMPLES_K)
            print(f"  已生成 {SAA_SAMPLES_K} 个需求场景用于求解。")

            # 创建并运行ALNS求解器
            print(f"  开始ALNS求解复制 {m_rep + 1}...")
            alns_solver = ALNS_Solver(problem_instance=self, demand_samples=demand_samples_for_k)

            solve_start_time_rep = time.time()
            best_first_stage_solution = alns_solver.solve(time_limit=time_limit_per_replication)
            solve_time_rep = time.time() - solve_start_time_rep

            if best_first_stage_solution is not None:
                # 获取目标值（下界估计）
                obj_val_k = self.calculate_objective(best_first_stage_solution, demand_samples_for_k)
                self.saa_objective_values_k.append(obj_val_k)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K} 个样本上的目标值: {obj_val_k:.2f}")

                y_star_m = best_first_stage_solution['y']
                n_star_m = best_first_stage_solution['n']

                # 添加详细的调试信息（仅第一次复制）
                if m_rep == 0:  # 只在第一次复制时输出
                    self._debug_solution_analysis(m_rep + 1, y_star_m, n_star_m, {}, {}, {})

                self.saa_solutions_first_stage.append({
                    'y': y_star_m,
                    'n': n_star_m
                })

                print(f"  评估复制 {m_rep + 1} 的解在 {SAA_SAMPLES_K_PRIME} 个固定验证样本上的性能...")
                eval_start_time = time.time()
                self._current_replication = m_rep + 1  # 设置当前复制编号用于分析
                # 传递完整的第一阶段解
                first_stage_solution = self.saa_solutions_first_stage[-1]
                avg_obj_k_prime, avg_truck_cost_for_k_prime_eval, scenario_costs = self._evaluate_solution_on_new_samples_corrected(first_stage_solution, self.fixed_validation_samples)
                eval_time = time.time() - eval_start_time
                self.saa_upper_bounds_k_prime.append(avg_obj_k_prime)
                self.saa_truck_costs_k_prime.append(avg_truck_cost_for_k_prime_eval)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均目标值 (UB估计): {avg_obj_k_prime:.2f}")
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均卡车成本: {avg_truck_cost_for_k_prime_eval:.2f}")
                print(f"  时间分析: ALNS求解 {solve_time_rep:.2f}s, 验证评估 {eval_time:.2f}s, 总计 {solve_time_rep + eval_time:.2f}s")

                if avg_obj_k_prime < best_solution_info['avg_obj_k_prime']:
                    best_solution_info.update(first_stage_solution)  # 保存完整的第一阶段解
                    best_solution_info['avg_obj_k_prime'] = avg_obj_k_prime
                    best_solution_info['replication_idx'] = m_rep
                    best_solution_info['truck_cost_k_prime'] = avg_truck_cost_for_k_prime_eval
                    # 记录最佳解在每个验证场景上的成本
                    self.best_solution_validation_costs = scenario_costs
            else:
                print(f"  复制 {m_rep + 1} ALNS未能找到可行解。")
                self.saa_objective_values_k.append(float('inf'))
                self.saa_upper_bounds_k_prime.append(float('inf'))
                self.saa_truck_costs_k_prime.append(float('inf'))
                self.saa_solutions_first_stage.append(None)

            # 检查SAA终止条件 (从第2次复制开始检查)
            if m_rep + 1 >= SAA_MIN_REPLICATIONS_M:
                should_terminate, gap_info = self._check_saa_termination_criteria()
                if should_terminate:
                    print(f"\n✓ SAA终止条件满足，在第 {m_rep + 1} 次复制后停止")
                    print(f"  {gap_info}")
                    break
                else:
                    print(f"  当前SAA状态: {gap_info}")
            else:
                # 即使在最小复制次数之前，也显示当前状态（如果有足够的数据）
                if len([s for s in self.saa_solutions_first_stage if s is not None]) >= 2:
                    _, gap_info = self._check_saa_termination_criteria()
                    print(f"  当前SAA状态: {gap_info}")

        # 处理结果（与原来的solve_saa方法相同的逻辑）
        if not any(s is not None for s in self.saa_solutions_first_stage):
            print("\nSAA 未能产生任何有效解。")
            return None

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 正确的SAA下界：M次复制的训练目标值的平均
        statistical_lower_bound = np.mean(valid_obj_k) if valid_obj_k else float('inf')
        std_lower_bound = np.std(valid_obj_k) if valid_obj_k else float('inf')

        # 正确的SAA上界：最佳解在验证样本上的成本（不是平均）
        statistical_upper_bound = best_solution_info['avg_obj_k_prime'] if best_solution_info['y'] is not None else float('inf')

        # 所有复制验证成本的统计信息（仅用于分析）
        avg_all_validation_costs = np.mean(valid_ub_k_prime) if valid_ub_k_prime else float('inf')
        std_all_validation_costs = np.std(valid_ub_k_prime) if valid_ub_k_prime else float('inf')

        actual_replications = len([s for s in self.saa_solutions_first_stage if s is not None])
        print(f"\n--- SAA 结果汇总 ({actual_replications} 次有效复制，使用ALNS) ---")
        print(f"  下界估计 cost_N^m: {statistical_lower_bound:.2f}")
        print(f"    ↳ 计算方法: 前{actual_replications}次复制的小样本优化成本的算术平均")
        print(f"    ↳ 含义: 系统真实期望成本的下界估计")
        print(f"    - 下界标准差: {std_lower_bound:.2f}")
        print(f"    - 下界方差 δ²(cost_N): {std_lower_bound**2/(actual_replications*(actual_replications-1)) if actual_replications > 1 else 0:.4f}")
        print(f"  上界估计 cost_{SAA_SAMPLES_K_PRIME}(ŝ): {statistical_upper_bound:.2f}")
        print(f"    ↳ 计算方法: 最佳解在{SAA_SAMPLES_K_PRIME}个大样本场景下的平均成本")
        print(f"    ↳ 含义: 最佳方案长期运营的期望日均成本")
        print(f"  SAA Gap: {statistical_upper_bound - statistical_lower_bound:.2f} ({((statistical_upper_bound - statistical_lower_bound)/statistical_upper_bound*100):.1f}%)")
        print(f"  所有复制验证成本统计: {avg_all_validation_costs:.2f} ± {std_all_validation_costs:.2f}")

        if best_solution_info.get('y') is not None:
            print(f"\n最佳解来自复制 {best_solution_info['replication_idx'] + 1}，其在 {SAA_SAMPLES_K_PRIME} 个样本上的评估目标值为: {best_solution_info['avg_obj_k_prime']:.2f}")
            final_solution_saa = {
                'objective_value_k_prime_estimate': best_solution_info['avg_obj_k_prime'],
                'selected_lockers_y': best_solution_info['y'],
                'drone_allocations_n': best_solution_info['n'],
                'truck_cost_k_prime_estimate': best_solution_info['truck_cost_k_prime'],
            }
            print(f"  最佳解选定的储物柜 (y*): {[j for j, val in best_solution_info['y'].items() if val > 0.5]}")
            print(f"  最佳解无人机分配 (n*): {dict((j, round(val)) for j, val in best_solution_info['n'].items() if best_solution_info['y'].get(j,0) > 0.5)}")
            return final_solution_saa
        else:
            print("\nSAA 未能找到任何有效的最终解。")
            return None


    def _print_detailed_cost_breakdown(self, saa_solution_dict):
        """打印详细的成本构成分析 - 1.py ALNS版本"""
        print(f"\n  详细成本构成分析 (1.py - ALNS算法):")
        print(f"  " + "-" * 50)

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        locker_fixed_cost = sum(self.locker_fixed_cost[j] * y_star.get(j, 0) for j in selected_lockers)
        drone_deployment_cost = sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)
        first_stage_total = locker_fixed_cost + drone_deployment_cost

        print(f"  第一阶段成本 (here-and-now): {first_stage_total:.2f}")
        print(f"    - 储物柜固定成本: {locker_fixed_cost:.2f}")
        print(f"    - 无人机部署成本: {drone_deployment_cost:.2f}")

        # 【修复】使用与主评估流程相同的固定验证样本，确保成本分解的一致性
        print(f"  正在计算第二阶段期望成本 (wait-and-see)...")
        if hasattr(self, 'fixed_validation_samples') and self.fixed_validation_samples:
            sample_scenarios = self.fixed_validation_samples
            print(f"  使用固定验证样本集 ({len(sample_scenarios)} 个场景) 确保一致性")
        else:
            # 回退到生成新样本（不应该发生）
            sample_scenarios = self._generate_demand_samples(num_samples=100)
            print(f"  警告：未找到固定验证样本，生成新样本 ({len(sample_scenarios)} 个场景)")
        total_transport_cost = 0
        total_penalty_cost = 0
        total_truck_cost = 0

        for scenario_idx, scenario in enumerate(sample_scenarios):
            # 为该场景求解最优分配
            optimal_assignment = self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, scenario)

            # 计算该场景下的成本
            total_shortage = 0
            scenario_transport_cost = 0

            for i in self.customers:
                actual_demand = scenario[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage += shortage

                # 计算无人机运输成本
                for j in selected_lockers:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        scenario_transport_cost += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            scenario_penalty_cost = self.penalty_cost_unassigned * total_shortage

            total_transport_cost += scenario_transport_cost
            total_penalty_cost += scenario_penalty_cost

        # 计算第二阶段期望成本
        avg_transport_cost = total_transport_cost / len(sample_scenarios)
        avg_penalty_cost = total_penalty_cost / len(sample_scenarios)
        truck_cost_estimate = saa_solution_dict.get('truck_cost_k_prime_estimate', 0)

        second_stage_total = avg_transport_cost + avg_penalty_cost + truck_cost_estimate

        print(f"  第二阶段期望成本 (wait-and-see): {second_stage_total:.2f}")
        print(f"    - 无人机运输成本 (随实际需求变化): {avg_transport_cost:.2f}")
        print(f"    - 未分配惩罚成本 (随需求不确定性): {avg_penalty_cost:.2f}")
        print(f"    - 卡车运输成本 (随储物柜需求变化): {truck_cost_estimate:.2f}")

        total_cost = first_stage_total + second_stage_total
        print(f"  总成本 (重新计算): {total_cost:.2f}")

        # 显示与SAA主评估的差异（用于调试）
        main_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        difference = abs(total_cost - main_objective)
        print(f"  SAA主评估目标值: {main_objective:.2f}")
        print(f"  差异: {difference:.2f} ({difference/main_objective*100:.1f}%)")
        print(f"  注意: 应以SAA主评估目标值为准，此处重新计算仅用于成本构成分析")

        # 成本占比分析（基于SAA主评估目标值）
        if main_objective > 0:
            print(f"\n  成本占比分析 (基于SAA主评估目标值):")
            print(f"    - 第一阶段占比: {first_stage_total/main_objective*100:.1f}%")
            print(f"    - 第二阶段占比: {(main_objective-first_stage_total)/main_objective*100:.1f}%")
            print(f"    - 卡车成本占比: {truck_cost_estimate/main_objective*100:.1f}%")
            print(f"    - 无人机总成本占比: {(drone_deployment_cost + avg_transport_cost)/main_objective*100:.1f}%")
            print(f"    - 惩罚成本占比: {avg_penalty_cost/main_objective*100:.1f}%")



    def _evaluate_solution_on_new_samples_corrected(self, first_stage_solution: Dict, demand_samples_k_prime: List[Dict[int,float]]):
        """
        修正版：评估完整第一阶段解在新样本上的性能
        """
        import time
        eval_start_time = time.time()

        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        # 记录每个验证场景的总成本，用于计算Var(UB)
        scenario_total_costs = []

        # 提取第一阶段解
        y_star = first_stage_solution['y']
        n_star = first_stage_solution['n']

        # 第一阶段固定成本
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        first_stage_cost = locker_cost_fixed + drone_deployment_cost_fixed

        # 准备批量卡车成本计算的数据
        batch_active_lockers_info = []

        # 时间统计
        assignment_start_time = time.time()

        # 使用多线程并行求解所有场景的客户分配问题
        # 调试：检查_current_replication状态
        current_rep = getattr(self, '_current_replication', None)
        if current_rep == 1:
            print(f"  使用多线程并行求解 {num_scenarios_k_prime} 个场景的客户分配...")

        all_optimal_assignments = self._solve_assignments_parallel(y_star, n_star, selected_lockers_eval, demand_samples_k_prime)

        # 根据所有最优分配结果计算卡车运输需求
        for k_prime_idx, optimal_assignment in enumerate(all_optimal_assignments):
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 调试：检查前几个场景的分配结果（仅第一次复制）
            if k_prime_idx < 3 and hasattr(self, '_current_replication') and self._current_replication == 1:
                total_assigned = sum(optimal_assignment.get((i, j), 0) for i in self.customers for j in selected_lockers_eval)
                total_demand = sum(demand_scenario_k_prime.values())
                print(f"    场景 {k_prime_idx+1}: 总需求 {total_demand:.1f}, 总分配 {total_assigned:.1f}")

            # 根据最优分配计算每个储物柜的实际需求量
            active_lockers_info_k_prime = {}

            for j_locker in selected_lockers_eval:
                total_actual_demand_j = sum(optimal_assignment.get((i, j_locker), 0) for i in self.customers)
                if total_actual_demand_j > 1e-6:
                    active_lockers_info_k_prime[j_locker] = {
                        'coord': self.site_coords[j_locker],
                        'demand': round(total_actual_demand_j)
                    }

            batch_active_lockers_info.append(active_lockers_info_k_prime)

        assignment_time = time.time() - assignment_start_time

        # 使用DRL批量求解计算所有场景的卡车成本（仅第一次复制显示详细信息）
        truck_cost_start_time = time.time()
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  使用DRL批量求解计算 {num_scenarios_k_prime} 个验证场景的卡车成本...")
        batch_truck_costs = self.calculate_truck_cost_batch(batch_active_lockers_info)
        truck_cost_time = time.time() - truck_cost_start_time

        # 计算总成本并记录每个场景的成本
        cost_calc_start_time = time.time()
        for k_prime_idx in range(num_scenarios_k_prime):
            truck_cost_k_prime = batch_truck_costs[k_prime_idx]
            demand_scenario_k_prime = demand_samples_k_prime[k_prime_idx]

            # 使用缓存的最优分配结果，避免重复计算
            optimal_assignment = all_optimal_assignments[k_prime_idx]

            # 计算该场景下的第二阶段成本
            # 1. 无人机运输成本（基于最优分配）
            transport_cost_k_prime = 0
            total_shortage_k_prime = 0

            for i in self.customers:
                actual_demand = demand_scenario_k_prime[i]
                total_assigned = sum(optimal_assignment.get((i, j), 0) for j in selected_lockers_eval)
                shortage = max(0, actual_demand - total_assigned)
                total_shortage_k_prime += shortage

                # 计算无人机运输成本
                for j in selected_lockers_eval:
                    assigned_qty = optimal_assignment.get((i, j), 0)
                    if assigned_qty > 1e-6 and (i, j) in self.distance:
                        transport_cost_k_prime += 2 * self.transport_unit_cost * self.distance[i, j] * assigned_qty

            # 2. 未分配惩罚成本
            penalty_cost_k_prime = self.penalty_cost_unassigned * total_shortage_k_prime

            # 3. 卡车运输成本
            total_truck_cost_over_k_prime_samples += truck_cost_k_prime

            # 总成本 = 第一阶段成本 + 第二阶段成本
            total_cost_for_scenario_k_prime = (first_stage_cost + transport_cost_k_prime +
                                              penalty_cost_k_prime + truck_cost_k_prime)
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime

            # 记录每个场景的总成本
            scenario_total_costs.append(total_cost_for_scenario_k_prime)

        cost_calc_time = time.time() - cost_calc_start_time
        total_eval_time = time.time() - eval_start_time

        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')

        # 时间分析报告（仅第一次复制显示）
        if hasattr(self, '_current_replication') and self._current_replication == 1:
            print(f"  验证阶段详细时间分析:")
            print(f"    客户分配求解: {assignment_time:.2f}s ({assignment_time/total_eval_time*100:.1f}%)")
            print(f"    DRL卡车成本计算: {truck_cost_time:.2f}s ({truck_cost_time/total_eval_time*100:.1f}%)")
            print(f"    成本计算汇总: {cost_calc_time:.2f}s ({cost_calc_time/total_eval_time*100:.1f}%)")
            print(f"    验证总时间: {total_eval_time:.2f}s")

            # 显示缓存性能统计
            if self.fast_solver and hasattr(self.fast_solver, 'cache_hits'):
                total_requests = self.fast_solver.cache_hits + self.fast_solver.cache_misses
                if total_requests > 0:
                    cache_hit_rate = self.fast_solver.cache_hits / total_requests * 100
                    print(f"    缓存性能: 命中率 {cache_hit_rate:.1f}% ({self.fast_solver.cache_hits}/{total_requests})")
                    if cache_hit_rate > 0:
                        estimated_time_saved = assignment_time * (cache_hit_rate / 100) * 0.8  # 估算节省的时间
                        print(f"    估算节省时间: {estimated_time_saved:.2f}s")

        # 返回平均成本、卡车成本和每个场景的成本列表
        return avg_total_cost_k_prime, avg_truck_cost_k_prime, scenario_total_costs

    def _solve_assignments_parallel(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_samples: List[Dict[int, float]]) -> List[Dict]:
        """
        使用优化的多线程并行求解所有场景的客户分配问题
        """
        import concurrent.futures
        import threading

        # 注意：现在使用精确求解器，不再需要线程本地快速求解器

        def solve_single_scenario_optimized(demand_scenario, k_idx=None):
            """优化的单场景求解函数 - 使用与saa_g_r.py相同的精确求解器"""
            try:
                # 使用精确求解器（Gurobi或启发式回退）
                return self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, demand_scenario)
            except Exception as e:
                if k_idx is not None and hasattr(self, '_current_replication') and self._current_replication == 1:
                    print(f"  场景 {k_idx+1} 并行求解失败: {str(e)}")
                return {(i, j): 0.0 for i in self.customers for j in selected_lockers}

        # 使用线程池并行求解（经过测试，4个线程效果最佳）
        # 测试结果：4线程(34.36s) < 8线程(38.18s) < 6线程(43.15s)
        optimal_threads = 4  # 最优线程数
        max_workers = min(optimal_threads, len(demand_samples))

        # 调试：检查_current_replication状态
        current_rep = getattr(self, '_current_replication', None)
        if current_rep == 1:
            print(f"  使用 {max_workers} 个线程并行求解")

        # 准备所有场景的参数（与saa_g_r.py保持一致）
        scenario_args = [(k_idx, scenario) for k_idx, scenario in enumerate(demand_samples)]

        def solve_single_scenario_wrapper(args):
            k_idx, demand_scenario = args
            try:
                return self._solve_optimal_assignment_for_scenario(y_star, n_star, selected_lockers, demand_scenario)
            except Exception as e:
                if hasattr(self, '_current_replication') and self._current_replication == 1:
                    print(f"  场景 {k_idx+1} 并行求解失败: {str(e)}")
                return {(i, j): 0.0 for i in self.customers for j in selected_lockers}

        # 使用线程池并行求解（与saa_g_r.py保持一致）
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            all_assignments = list(executor.map(solve_single_scenario_wrapper, scenario_args))

        return all_assignments

    def _solve_optimal_assignment_for_scenario(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_scenario: Dict[int, float]) -> Dict:
        """
        为给定的需求场景求解最优客户分配
        使用快速启发式算法，因为ALNS已经在上层统一处理整个问题
        """
        # 使用快速启发式求解器（因为ALNS在上层统一处理）
        if not hasattr(self, 'fast_solver') or self.fast_solver is None:
            self.fast_solver = CustomerAssignmentALNS(self)

        # 直接使用贪心启发式求解，因为ALNS在上层已经优化了第一阶段决策
        return self.fast_solver.solve_assignment_heuristic(y_star, n_star, selected_lockers, demand_scenario)

    def _solve_assignment_with_gurobi(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_scenario: Dict[int, float]) -> Dict:
        """
        使用Gurobi精确求解器求解客户分配问题（与saa_g_r.py保持一致）
        """
        import gurobipy as gp
        from gurobipy import GRB

        try:
            model_scenario = gp.Model("ScenarioAssignment")
            model_scenario.setParam('OutputFlag', 0)
            model_scenario.setParam('Threads', 1)
            model_scenario.setParam('TimeLimit', 10)  # 10秒时间限制

            # 决策变量：实际配送量
            x_scenario = {}
            for i in self.customers:
                for j in selected_lockers:
                    x_scenario[i, j] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_{i}_{j}")

            # 短缺变量
            shortage_scenario = {}
            for i in self.customers:
                shortage_scenario[i] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"shortage_{i}")

            # 目标函数：最小化运输成本和惩罚成本
            transport_cost = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), 1000) * x_scenario[i, j]
                for i in self.customers for j in selected_lockers
                if (i, j) in self.distance
            )
            penalty_cost = gp.quicksum(
                self.penalty_cost_unassigned * shortage_scenario[i]
                for i in self.customers
            )
            model_scenario.setObjective(transport_cost + penalty_cost, GRB.MINIMIZE)

            # 约束条件
            for i in self.customers:
                # 需求平衡约束：分配量 + 短缺量 = 实际需求
                model_scenario.addConstr(
                    gp.quicksum(x_scenario[i, j] for j in selected_lockers) + shortage_scenario[i] == demand_scenario[i],
                    name=f"demand_balance_{i}"
                )

                # 飞行距离约束
                for j in selected_lockers:
                    if (i, j) in self.distance:
                        flight_distance = 2 * self.distance[i, j]
                        if flight_distance > self.max_flight_distance:
                            # 如果距离超限，强制分配量为0
                            model_scenario.addConstr(x_scenario[i, j] == 0, name=f"distance_limit_{i}_{j}")

            # 储物柜容量约束
            for j in selected_lockers:
                model_scenario.addConstr(
                    gp.quicksum(x_scenario[i, j] for i in self.customers) <= self.Q_locker_capacity[j],
                    name=f"locker_capacity_{j}"
                )

            # 无人机服务能力约束
            for j in selected_lockers:
                total_service_time = gp.quicksum(
                    x_scenario[i, j] * ((2 * self.distance.get((i, j), 1000) / self.drone_speed) + self.loading_time)
                    for i in self.customers
                    if (i, j) in self.distance
                )
                available_hours = n_star.get(j, 0) * self.H_drone_working_hours_per_day
                model_scenario.addConstr(
                    total_service_time <= available_hours,
                    name=f"drone_capacity_{j}"
                )

            model_scenario.optimize()

            if model_scenario.SolCount > 0:
                assignment = {(i, j): x_scenario[i, j].X for i in self.customers for j in selected_lockers}
                return assignment
            else:
                # 如果无解，返回空分配
                return {(i, j): 0.0 for i in self.customers for j in selected_lockers}

        except Exception as e:
            # 求解失败，抛出异常让上层处理
            raise e





    def _analyze_truck_cost_details_corrected(self, y_solution, n_solution, validation_samples, replication, first_stage_assignment, sample_active_lockers_info):
        """
        修正版的卡车成本详细分析
        """
        print(f"  === 复制 {replication} 卡车成本详细分析（修正版两阶段） ===")

        # 统计开放的储物柜
        open_lockers = [site for site in self.sites if y_solution.get(site, 0) > 0.5]
        print(f"  开放储物柜数量: {len(open_lockers)}")
        print(f"  开放储物柜ID: {open_lockers}")

        # 显示第一阶段客户分配
        print(f"  第一阶段客户分配（基于期望需求）:")
        for i in self.customers:
            assignments = []
            for j in open_lockers:
                qty = first_stage_assignment.get((i, j), 0)
                if qty > 0.5:
                    assignments.append(f"储物柜{j}({qty:.1f})")
            if assignments:
                print(f"    客户{i}: {', '.join(assignments)} (期望需求: {self.expected_demand[i]})")

        # 分析前几个验证场景
        print(f"  前{len(sample_active_lockers_info)}个验证场景的卡车需求:")
        for i, active_lockers_info in enumerate(sample_active_lockers_info):
            print(f"  --- 场景 {i+1} ---")
            if active_lockers_info:
                total_demand = sum(info['demand'] for info in active_lockers_info.values())
                print(f"    总需求: {total_demand:.1f}")
                for locker_id, info in active_lockers_info.items():
                    print(f"    储物柜{locker_id}: 需求{info['demand']:.1f}, 坐标{info['coord']}")
            else:
                print(f"    无卡车运输需求")

        print(f"  ================================")

    def _debug_solution_analysis(self, replication, y_solution, n_solution, x_qty_solution, z_solution, u_solution):
        """
        详细调试解的分配情况（修正版：不依赖第一阶段客户分配）
        """
        print(f"\n  🔍 复制 {replication} 解的详细调试分析:")
        print(f"  " + "=" * 60)

        # 1. 储物柜开放情况
        open_lockers = [j for j, val in y_solution.items() if val > 0.5]
        print(f"  开放储物柜: {open_lockers}")

        # 2. 无人机配置情况
        drone_config = {j: round(n_solution.get(j, 0)) for j in open_lockers}
        print(f"  无人机配置: {drone_config}")

        # 3. 第一阶段成本分析
        print(f"  第一阶段成本分析:")
        locker_cost = sum(self.locker_fixed_cost[j] * y_solution.get(j, 0) for j in open_lockers)
        drone_cost = sum(self.drone_cost * n_solution.get(j, 0) for j in open_lockers)
        first_stage_cost = locker_cost + drone_cost

        print(f"    储物柜固定成本: {locker_cost:.2f}")
        print(f"    无人机部署成本: {drone_cost:.2f}")
        print(f"    第一阶段总成本: {first_stage_cost:.2f}")

        # 4. 储物柜资源配置分析
        print(f"  储物柜资源配置:")
        for j in open_lockers:
            capacity_j = self.Q_locker_capacity[j]
            drone_count = n_solution.get(j, 0)
            available_hours = drone_count * self.H_drone_working_hours_per_day

            # 距离约束检查
            reachable_customers = []
            unreachable_customers = []
            for i in self.customers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        reachable_customers.append(i)
                    else:
                        unreachable_customers.append(i)

            print(f"    储物柜{j}: 容量{capacity_j}, 无人机{drone_count}架, 可用时间{available_hours:.1f}h")
            print(f"      可达客户: {len(reachable_customers)}/{len(self.customers)}")
            if unreachable_customers:
                print(f"      不可达客户: {unreachable_customers}")

        print(f"  注意: 在修正的两阶段模型中，客户分配在第二阶段根据实际需求动态优化")

        # 添加惩罚成本分析
        self._analyze_penalty_cost_causes(y_solution, n_solution)

    def _analyze_penalty_cost_causes(self, y_solution, n_solution):
        """
        分析惩罚成本高的原因
        """
        print(f"\n  🔍 惩罚成本分析:")
        print(f"  " + "=" * 50)

        open_lockers = [j for j, val in y_solution.items() if val > 0.5]
        total_expected_demand = sum(self.expected_demand.values())

        print(f"  总期望需求: {total_expected_demand:.1f}")
        print(f"  惩罚成本单价: {self.penalty_cost_unassigned:.0f} 元/单位")
        print(f"  开放储物柜: {open_lockers}")

        # 分析每个客户的可达性
        unreachable_customers = []
        reachable_customers = []
        total_unreachable_demand = 0

        for i in self.customers:
            customer_reachable = False
            min_distance = float('inf')

            for j in open_lockers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        customer_reachable = True
                        min_distance = min(min_distance, self.distance[i, j])

            if not customer_reachable:
                unreachable_customers.append(i)
                total_unreachable_demand += self.expected_demand[i]
            else:
                reachable_customers.append((i, min_distance))

        print(f"\n  距离约束分析:")
        print(f"    最大飞行距离: {self.max_flight_distance:.1f} km")
        print(f"    不可达客户数: {len(unreachable_customers)}/{len(self.customers)}")
        print(f"    不可达需求量: {total_unreachable_demand:.1f}/{total_expected_demand:.1f} ({total_unreachable_demand/total_expected_demand*100:.1f}%)")

        if unreachable_customers:
            print(f"    不可达客户: {unreachable_customers[:10]}{'...' if len(unreachable_customers) > 10 else ''}")

        # 分析容量约束
        print(f"\n  容量约束分析:")
        total_locker_capacity = sum(self.Q_locker_capacity[j] for j in open_lockers)
        total_drone_capacity = 0

        for j in open_lockers:
            locker_capacity = self.Q_locker_capacity[j]
            drone_count = n_solution.get(j, 0)

            # 计算无人机运力
            reachable_demand_for_j = 0
            avg_distance_j = 0
            reachable_count_j = 0

            for i in self.customers:
                if (i, j) in self.distance:
                    flight_distance = 2 * self.distance[i, j]
                    if flight_distance <= self.max_flight_distance:
                        reachable_demand_for_j += self.expected_demand[i]
                        avg_distance_j += self.distance[i, j]
                        reachable_count_j += 1

            if reachable_count_j > 0:
                avg_distance_j /= reachable_count_j
                avg_service_time = (2 * avg_distance_j / self.drone_speed) + self.loading_time
                drone_capacity_j = drone_count * self.H_drone_working_hours_per_day / avg_service_time
            else:
                drone_capacity_j = 0

            total_drone_capacity += drone_capacity_j

            print(f"    储物柜{j}: 容量{locker_capacity}, 无人机{drone_count}架, 运力{drone_capacity_j:.1f}")

        print(f"    总储物柜容量: {total_locker_capacity:.1f}")
        print(f"    总无人机运力: {total_drone_capacity:.1f}")
        print(f"    可达需求量: {total_expected_demand - total_unreachable_demand:.1f}")

        # 分析瓶颈
        effective_capacity = min(total_locker_capacity, total_drone_capacity)
        reachable_demand = total_expected_demand - total_unreachable_demand

        print(f"\n  瓶颈分析:")
        print(f"    有效服务容量: {effective_capacity:.1f}")
        print(f"    可达需求量: {reachable_demand:.1f}")

        if effective_capacity < reachable_demand:
            shortage = reachable_demand - effective_capacity
            print(f"    容量不足: {shortage:.1f} ({shortage/reachable_demand*100:.1f}%)")
            print(f"    容量不足惩罚成本: {shortage * self.penalty_cost_unassigned:.0f} 元")

        total_shortage = total_unreachable_demand + max(0, reachable_demand - effective_capacity)
        total_penalty = total_shortage * self.penalty_cost_unassigned

        print(f"\n  总惩罚成本预估:")
        print(f"    距离约束导致: {total_unreachable_demand * self.penalty_cost_unassigned:.0f} 元")
        print(f"    容量约束导致: {max(0, reachable_demand - effective_capacity) * self.penalty_cost_unassigned:.0f} 元")
        print(f"    总惩罚成本: {total_penalty:.0f} 元")
        print(f"    占总成本比例: {total_penalty/(total_penalty + 49500 + 2028):.1%}")



    def calculate_truck_cost_batch(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        批量计算卡车成本 - 智能分组批量求解

        将具有相同储物柜配置的场景分组进行批量求解，
        以最大化DRL批量求解的效率。

        Args:
            batch_active_lockers_info: 批量活跃储物柜信息列表

        Returns:
            卡车成本列表
        """
        if not DRL_AVAILABLE:
            print("  DRL不可用，使用简化估算计算批量卡车成本")
            return self._calculate_simplified_batch_costs(batch_active_lockers_info)

        if not batch_active_lockers_info:
            return []

        try:
            # 按储物柜配置分组
            groups = {}
            for i, scenario in enumerate(batch_active_lockers_info):
                locker_ids = tuple(sorted(scenario.keys()))
                if locker_ids not in groups:
                    groups[locker_ids] = []
                groups[locker_ids].append((i, scenario))

            if hasattr(self, '_current_replication') and self._current_replication == 1:
                print(f"  将 {len(batch_active_lockers_info)} 个场景分为 {len(groups)} 组进行批量求解")

            # 初始化结果列表
            batch_costs = [0.0] * len(batch_active_lockers_info)
            drl_solver = self._get_drl_solver(make_plots=False)

            # 对每组进行批量求解
            for group_idx, (locker_ids, scenarios) in enumerate(groups.items()):
                scenario_indices = [idx for idx, _ in scenarios]
                scenario_data = [data for _, data in scenarios]

                if len(scenario_data) > 1:
                    # 批量求解
                    group_costs = drl_solver.solve_batch(scenario_data, return_route_info=False)
                else:
                    # 单个求解
                    group_costs = [drl_solver.solve(scenario_data[0], return_route_info=False)]

                # 将结果放回原始位置
                for i, cost in enumerate(group_costs):
                    batch_costs[scenario_indices[i]] = cost

            if hasattr(self, '_current_replication') and self._current_replication == 1:
                print(f"  分组批量求解成功，平均卡车成本: {sum(batch_costs)/len(batch_costs):.2f}")
            return batch_costs
        except Exception as e:
            print(f"  DRL批量求解失败: {str(e)}，回退到逐个求解")
            return self._fallback_individual_solving(batch_active_lockers_info)





    def _calculate_simplified_batch_costs(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        简化估算批量成本计算
        """
        batch_costs = []
        for active_lockers_info in batch_active_lockers_info:
            batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _calculate_simplified_cost(self, active_lockers_info: Dict[int, Dict[str, Any]]) -> float:
        """
        单个场景的简化成本估算
        """
        if not active_lockers_info:
            return 0.0

        total_demand = sum(info['demand'] for info in active_lockers_info.values())
        num_trucks = math.ceil(total_demand / self.truck_capacity) if self.truck_capacity > 0 else 1
        return num_trucks * self.truck_fixed_cost

    def _fallback_individual_solving(self, batch_active_lockers_info: List[Dict[int, Dict[str, Any]]]) -> List[float]:
        """
        回退到逐个求解的方法
        """
        batch_costs = []
        for i, active_lockers_info in enumerate(batch_active_lockers_info):
            try:
                cost = self.calculate_truck_cost([], {}, make_plots=False, active_lockers_info_override=active_lockers_info)
                batch_costs.append(cost)
            except Exception as e2:
                print(f"  场景 {i+1} 求解失败: {str(e2)}，使用简化估算")
                batch_costs.append(self._calculate_simplified_cost(active_lockers_info))
        return batch_costs

    def _check_saa_termination_criteria(self):
        """
        检查SAA终止条件
        返回: (should_terminate: bool, gap_info: str)
        """
        import scipy.stats as stats

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 至少需要2次有效复制才能计算统计量
        if len(valid_obj_k) < 2 or len(valid_ub_k_prime) < 2:
            return False, f"有效解数量不足 ({len(valid_obj_k)}, {len(valid_ub_k_prime)} < 2)，需要至少2次有效复制"

        # 找到当前最佳解的信息
        best_validation_cost = float('inf')
        for i, ub_val in enumerate(self.saa_upper_bounds_k_prime):
            if ub_val != float('inf') and ub_val < best_validation_cost:
                best_validation_cost = ub_val

        # 计算正确的SAA统计量
        m = len(valid_obj_k)  # 当前迭代次数
        statistical_lower_bound = np.mean(valid_obj_k)  # cost_N^m = (1/m) * Σ cost_N^{m'}
        statistical_upper_bound = best_validation_cost  # UB_{N'}(ŝ) (最佳解的验证成本)

        # SAA Gap计算 (按正确的论文公式)
        saa_gap = statistical_upper_bound - statistical_lower_bound
        gap_percent = saa_gap / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 计算下界方差：δ²(cost_N) = (1/[m(m-1)]) * Σ(cost_N^{m'} - cost_N^m)²
        if m > 1:
            variance_sum = sum((cost_val - statistical_lower_bound)**2 for cost_val in valid_obj_k)
            var_lower_bound = variance_sum / (m * (m - 1))
            std_lower_bound = np.sqrt(var_lower_bound)
        else:
            var_lower_bound = 0
            std_lower_bound = 0

        # 计算上界方差：δ²_{N'}(ŝ) = (1/[N'(N'-1)]) * Σ(Q(ŝ,ξ_n) - cost_{N'}(ŝ))²
        # 这里简化处理，假设每个复制的验证样本数量相同
        N_prime = SAA_SAMPLES_K_PRIME
        if len(valid_ub_k_prime) > 1:
            # 使用最佳解对应的上界值计算方差
            best_ub_index = valid_ub_k_prime.index(best_validation_cost)
            # 简化计算：使用所有上界值的方差除以验证样本数量
            var_upper_bound = np.var(valid_ub_k_prime, ddof=1) / N_prime
        else:
            var_upper_bound = 0

        # Gap的总方差：δ²_Gap(ŝ) = δ²(cost_N) + δ²_{N'}(ŝ)
        gap_variance = var_lower_bound + var_upper_bound
        gap_std = np.sqrt(gap_variance) if gap_variance > 0 else 0
        variance_percent = gap_variance / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 置信区间计算 (使用t分布)
        n_replications = len(valid_obj_k)
        if n_replications > 1:
            t_critical = stats.t.ppf(1 - SAA_CONFIDENCE_LEVEL_ALPHA/2, n_replications - 1)
            # 下界置信区间
            lb_margin = t_critical * std_lower_bound / np.sqrt(n_replications) if std_lower_bound > 0 else 0
        else:
            lb_margin = 0

        # SAA终止条件检查 (必须同时满足两个条件)
        # 条件1: 相对差距阈值 Gap_{N,N'}(ŝ) / cost_{N'}(ŝ) ≤ ε (如 ε = 3%)
        relative_gap_condition = (gap_percent <= SAA_GAP_TOLERANCE_PERCENT and saa_gap >= 0)

        # 条件2: 方差阈值 δ²_Gap(ŝ) / cost_{N'}(ŝ) ≤ ε' (如 ε' = 5%)
        variance_condition = variance_percent <= SAA_VARIANCE_TOLERANCE_PERCENT

        # 主要终止条件：必须同时满足相对差距阈值和方差阈值
        should_terminate = relative_gap_condition and variance_condition

        # 如果gap为负，说明统计估计不稳定，不应该终止
        if saa_gap < 0:
            should_terminate = False

        # 计算下界变异系数
        cv_lower_bound = std_lower_bound / statistical_lower_bound if statistical_lower_bound > 0 else float('inf')

        # 构建详细的状态信息
        gap_condition_status = "✓" if relative_gap_condition else "✗"
        variance_condition_status = "✓" if variance_condition else "✗"

        gap_info = (f"相对差距: {gap_percent:.2%} {gap_condition_status} (阈值: ≤{SAA_GAP_TOLERANCE_PERCENT:.0%}), "
                   f"方差比例: {variance_percent:.2%} {variance_condition_status} (阈值: ≤{SAA_VARIANCE_TOLERANCE_PERCENT:.0%}), "
                   f"LB(m={m}): {statistical_lower_bound:.2f}, "
                   f"UB: {statistical_upper_bound:.2f}, "
                   f"Gap: {saa_gap:.2f}, "
                   f"δ²_Gap: {gap_variance:.4f}")

        return should_terminate, gap_info



    def _print_saa_solution(self, saa_solution_dict: Dict):
        if not saa_solution_dict:
            print("SAA 未找到有效解。")
            return

        print("\n" + "=" * 60 + "\nSAA 优化结果 (ALNS算法)\n" + "=" * 60)
        print(f"  最终评估目标值 (在 {SAA_SAMPLES_K_PRIME} 个样本上): {saa_solution_dict.get('objective_value_k_prime_estimate', 'N/A'):.2f}")
        print(f"  估计的卡车成本 (在 {SAA_SAMPLES_K_PRIME} 个样本上): {saa_solution_dict.get('truck_cost_k_prime_estimate', 'N/A'):.2f}")

        # 添加详细的成本构成分析
        self._print_detailed_cost_breakdown(saa_solution_dict)

        y_star_final = saa_solution_dict.get('selected_lockers_y', {})
        n_star_final = saa_solution_dict.get('drone_allocations_n', {})

        selected_lockers_print = [j for j, val in y_star_final.items() if val > 0.5]
        print(f"  选定的储物柜站点 (y*): {selected_lockers_print}")
        print(f"  各站点无人机分配 (n*):")
        for j_site_p in selected_lockers_print:
            print(f"    位置 {j_site_p}: 配备 {round(n_star_final.get(j_site_p,0))} 架无人机")

        print("\n  修正的两阶段模型说明:")
        print("  第一阶段决策：储物柜选址和无人机配置（已确定）")
        print("  第二阶段决策：根据实际需求场景动态优化客户分配和配送")
        print("  这样可以更好地应对需求不确定性，避免过度保守的分配策略。")

        # 简化的汇总统计（在修正模型中，客户分配在第二阶段动态决定）
        customer_assignment_quantities_final_print = {i: {} for i in self.customers}
        customer_assignments_primary_final_print = {i: [] for i in self.customers}
        unassigned_customers_final_print = list(self.customers)  # 在修正模型中，所有客户都在第二阶段分配

        print(f"\n  分配汇总:")
        print(f"    储物柜数量: {len(selected_lockers_print)}")
        print(f"    无人机总数: {sum(round(n_star_final.get(j,0)) for j in selected_lockers_print)}")
        print(f"    总期望需求量: {sum(self.expected_demand.values()):.2f}")
        print(f"    注意: 客户分配将在第二阶段根据实际需求动态优化")

        if DRL_AVAILABLE:
            viz_solution_saa = {
                'selected_lockers': {j:True for j in selected_lockers_print},
                'customer_assignments_primary': {},  # 空的，因为客户分配在第二阶段
                'unassigned_customers_by_u': list(self.customers),  # 所有客户都在第二阶段分配
                'drone_allocations': {j: round(n_star_final.get(j,0)) for j in selected_lockers_print},
                'objective_value': saa_solution_dict.get('objective_value_k_prime_estimate', 0.0)
            }
            print("\n  显示SAA最终解的可视化:")
            print("  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）")
            print("  ↳ 客户分配将在第二阶段根据实际需求场景动态优化")
            print("  ↳ 上述成本是考虑需求不确定性后的期望值")
            self.visualize_solution(viz_solution_saa)

    def visualize_solution(self, solution: Dict):
        if not DRL_AVAILABLE: return
        if not solution: print("无解决方案可供可视化。"); return
        temp_solution_for_viz = solution.copy()
        primary_assignments = solution.get('customer_assignments_primary', {})

        # 修改：支持多储物柜分配的可视化
        # 直接传递完整的储物柜列表给可视化函数
        customer_assignments_viz = {}
        for cust, lockers in primary_assignments.items():
            if lockers: # 如果有分配的储物柜
                # 直接传递储物柜列表，visualization.py会自动处理多储物柜连线
                customer_assignments_viz[cust] = lockers

        temp_solution_for_viz['customer_assignments'] = customer_assignments_viz


        customer_service_modes = {}
        for customer_id in customer_assignments_viz: # 使用调整后的键
            customer_service_modes[customer_id] = 0
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # unassigned_customers 可能需要从 solution['unassigned_customers_by_u'] 获取
        if 'unassigned_customers_by_u' in solution:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']


        # 移除可能引起混淆的旧键
        if 'customer_assignments_primary' in temp_solution_for_viz: del temp_solution_for_viz['customer_assignments_primary']
        if 'unassigned_customers_by_u' in temp_solution_for_viz and 'unassigned_customers' in temp_solution_for_viz :
             if temp_solution_for_viz['unassigned_customers_by_u'] == temp_solution_for_viz['unassigned_customers']:
                 del temp_solution_for_viz['unassigned_customers_by_u']


        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="SAA 无人机配送网络规划 (基于期望需求分配)"
        )
        if plt_fig:
            import matplotlib.pyplot as plt
            plt.show(block=False) # 使用 block=False 避免阻塞后续代码，如果需要交互则去掉
            plt.pause(1) # 暂停一下，确保图像显示

# --- 主程序入口 ---
if __name__ == "__main__":
    start_time_main = time.time()
    print(f"设置全局随机种子: {RANDOM_SEED}")
    if DRL_AVAILABLE:
        set_drl_log_level(logging.WARNING)
        print("DRL日志级别已设置为WARNING")
    else:
        print("DRL模块不可用，相关功能将跳过。")


    print("\n创建随机需求的示例数据 (使用期望需求)...")
    stochastic_data_instance = create_deterministic_example_instance(
        demand_level="low",
        locker_cost_level="medium",
        drone_cost_level="medium",
        drone_transport_cost_level="medium",
        use_generated_distances=True,
        num_customers=30, # 与g_i.py保持一致
        num_sites=5,    # 与g_i.py保持一致
        use_kmeans_clustering=True,
        random_seed=RANDOM_SEED
    )
    stochastic_data_instance['expected_demand'] = stochastic_data_instance.pop('demand_deterministic')
    print("随机需求数据 (期望值) 已创建。")

    print("\n所有客户期望需求 (λᵢ_bar):")
    for customer_id_main, demand_val_main in stochastic_data_instance['expected_demand'].items():
        print(f"  客户 {customer_id_main}: {demand_val_main} 订单/天 (期望)")
    total_expected_demand_val_main = sum(stochastic_data_instance['expected_demand'].values())
    print(f"总期望需求: {total_expected_demand_val_main} 订单/天")

    # 调试：显示关键距离信息
    print(f"\n调试信息 - 距离矩阵样本:")
    for i in [1, 2, 3]:
        for j in [1, 2, 3, 4]:
            if (i, j) in stochastic_data_instance['distance_matrix']:
                dist = stochastic_data_instance['distance_matrix'][i, j]
                print(f"  客户{i}到储物柜{j}: {dist:.2f}km")

    # 调试：显示储物柜坐标
    print(f"\n调试信息 - 储物柜坐标:")
    for j, coord in stochastic_data_instance['site_coords'].items():
        print(f"  储物柜{j}: {coord}")

    # 调试：检查飞行距离限制
    print(f"\n调试信息 - 飞行距离约束检查:")
    max_flight_dist = stochastic_data_instance['max_flight_distance']
    print(f"  最大飞行距离: {max_flight_dist}km")
    reachable_count = 0
    total_pairs = 0
    for i in range(1, 21):
        for j in range(1, 5):
            if (i, j) in stochastic_data_instance['distance_matrix']:
                flight_dist = 2 * stochastic_data_instance['distance_matrix'][i, j]
                total_pairs += 1
                if flight_dist <= max_flight_dist:
                    reachable_count += 1
    print(f"  可达客户-储物柜对: {reachable_count}/{total_pairs} ({reachable_count/total_pairs*100:.1f}%)")


    print("\n" + "=" * 60 + "\n求解带随机需求的无人机配送网络设计问题 (SAA)\n" + "=" * 60)
    solve_start_time_saa_main = time.time()

    optimizer_saa_main = StochasticDroneDeliveryOptimizerSAA()
    optimizer_saa_main.set_parameters(**stochastic_data_instance)

    # 使用ALNS方法求解SAA问题
    print("使用ALNS方法求解SAA问题...")
    final_saa_solution = optimizer_saa_main.solve_saa_with_alns(
        time_limit_per_replication=120  # ALNS时间限制
    )

    solve_time_saa_main = time.time() - solve_start_time_saa_main

    if final_saa_solution:
        optimizer_saa_main._print_saa_solution(final_saa_solution)
        print(f"\nSAA模型求解总耗时: {solve_time_saa_main:.2f} 秒")
    else:
        print("\n⚠ SAA未能找到可行解或优化失败。")

    total_time_main = time.time() - start_time_main
    print(f"\n总运行时间: {total_time_main:.2f} 秒")
    if DRL_AVAILABLE:
        print("测试完成。如果图像窗口仍然打开，请手动关闭。")




