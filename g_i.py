import gurobipy as gp
from gurobipy import GRB
import numpy as np
from typing import Dict, List, Tuple, Any
import random
import time
import math
import logging
try:
    from clustering import generate_locker_sites_with_kmeans
    from visualization import visualize_solution
    CLUSTERING_AVAILABLE = True
except ImportError:
    print("警告: clustering 或 visualization 模块未找到。相关功能将不可用。")
    CLUSTERING_AVAILABLE = False
    def generate_locker_sites_with_kmeans(customer_coords, num_sites, random_state):
        print("警告: K-Means聚类不可用，将随机生成储物柜站点。")
        local_random = random.Random(random_state)
        return {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in range(1, num_sites + 1)}
    def visualize_solution(*args, **kwargs):
        print("警告: 可视化功能不可用。")
        return None

# 设置随机种子以确保结果可重现
RANDOM_SEED = 612
random.seed(RANDOM_SEED)
np.random.seed(RANDOM_SEED)

# SAA 参数 (恢复正常参数)
SAA_MAX_REPLICATIONS_M = 10  # 最大复制次数
SAA_MIN_REPLICATIONS_M = 2   # 最小复制次数 (设为最小值，主要依靠终止条件)
SAA_SAMPLES_K = 40           # 训练样本数量 N (与ALNS保持一致)
SAA_SAMPLES_K_PRIME = 2000  # 验证样本数量 N' (调整为合理比例)
SAA_CONFIDENCE_LEVEL_ALPHA = 0.1  # 置信水平 (90%置信区间)
SAA_GAP_TOLERANCE_PERCENT = 0.03  # Gap阈值 ε' = 3%
SAA_VARIANCE_TOLERANCE_PERCENT = 0.05  # 方差阈值 ε = 5%


class IntegratedDroneDeliveryOptimizerSAA:
    """
    整体求解无人机配送网络优化问题 - SAA版本
    将储物柜选址、客户分配、无人机部署和卡车运输统一在一个模型中求解
    使用SAA方法处理需求不确定性
    """

    def __init__(self):
        # 模型相关
        self.model = None

        # 储物柜和无人机相关决策变量
        self.x_qty = {}  # xᵢⱼₖ: 客户i在场景k下分配给储物柜j的需求量 (Z+)
        self.z = {}  # zᵢⱼₖ: 客户i在场景k下是否分配给储物柜j (二进制)
        self.y = {}  # yⱼ: 是否在站点j开设储物柜 (二进制) - 第一阶段决策
        self.n = {}  # nⱼ: 储物柜j部署的无人机数量 (Z+) - 第一阶段决策
        # 移除冗余的self.u变量，因为惩罚成本通过直接计算短缺量实现

        # CVRP相关决策变量
        self.x_truck = {}  # x[i,j,v,k]: 卡车v在场景k下是否从节点i行驶到节点j (二进制)
        self.y_truck = {}  # y[v,k]: 卡车v在场景k下是否使用 (二进制)
        self.u_truck = {}  # u[i,v,k]: 节点i在场景k下卡车v路线中的访问顺序 (连续变量)

        # 基础数据
        self.customers = []  # I: 客户集合
        self.sites = []  # J: 候选储物柜站点集合
        self.expected_demand = {}  # λᵢ: 客户i的期望需求 (订单/天)
        self.distance = {}  # dᵢⱼ: 客户i到储物柜站点j的距离
        self.locker_fixed_cost = {}  # cˡⱼ: 在站点j开设储物柜的固定成本
        self.Q_locker_capacity = {}  # Qⱼ: 储物柜j的最大服务能力 (订单/天)

        # 系统参数
        self.drone_speed = None  # v: 无人机飞行速度 (公里/小时)
        self.loading_time = None  # t₀: 无人机装载时间 (小时)
        self.max_flight_distance = None  # d_max: 无人机最大飞行距离 (公里)
        self.transport_unit_cost = None  # cᵈ: 无人机单位运输成本 (元/公里)
        self.drone_cost = None  # pᵈ: 单架无人机购置成本
        self.H_drone_working_hours_per_day = None  # H: 每架无人机每日工作小时数
        self.penalty_cost_unassigned = None  # cᴾ: 未分配客户单位需求的惩罚成本

        # 坐标信息
        self.customer_coords = {}
        self.site_coords = {}

        # 卡车运输参数
        self.depot_coord = None  # 仓库坐标
        self.truck_capacity = None  # 卡车容量
        self.truck_fixed_cost = None  # 卡车固定成本
        self.truck_km_cost = None  # 卡车每公里成本

        # CVRP相关数据
        self.truck_distances = {}  # 卡车运输距离矩阵（仓库到储物柜，储物柜间）
        self.max_trucks = 0  # 最大卡车数量

        # 用于Big-M约束的大数
        self.BIG_M = 1e6

        # SAA 相关结果存储
        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.fixed_validation_samples = []

    def set_parameters(self,
                       customers: List,
                       sites: List,
                       expected_demand: Dict,
                       distance_matrix: Dict,
                       drone_speed: float,
                       loading_time: float,
                       max_flight_distance: float,
                       locker_fixed_cost: Dict,
                       transport_unit_cost: float,
                       drone_cost: float,
                       H_drone_working_hours_per_day: float,
                       penalty_cost_unassigned: float,
                       Q_locker_capacity: Dict,
                       customer_coords: Dict = None,
                       site_coords: Dict = None,
                       depot_coord: Tuple[float, float] = None,
                       truck_capacity: float = None,
                       truck_fixed_cost: float = None,
                       truck_km_cost: float = None):
        """设置模型参数"""
        # 设置基础参数
        self.customers = customers
        self.sites = sites
        self.expected_demand = expected_demand
        self.distance = distance_matrix
        self.drone_speed = drone_speed
        self.loading_time = loading_time
        self.max_flight_distance = max_flight_distance
        self.locker_fixed_cost = locker_fixed_cost
        self.transport_unit_cost = transport_unit_cost
        self.drone_cost = drone_cost
        self.H_drone_working_hours_per_day = H_drone_working_hours_per_day
        self.penalty_cost_unassigned = penalty_cost_unassigned
        self.Q_locker_capacity = Q_locker_capacity

        # 设置可选参数
        if customer_coords is not None: self.customer_coords = customer_coords
        if site_coords is not None: self.site_coords = site_coords
        if depot_coord is not None: self.depot_coord = depot_coord
        if truck_capacity is not None: self.truck_capacity = truck_capacity
        if truck_fixed_cost is not None: self.truck_fixed_cost = truck_fixed_cost
        if truck_km_cost is not None: self.truck_km_cost = truck_km_cost

        # 计算卡车运输距离矩阵
        self._calculate_truck_distances()

        # 估算最大卡车数量
        total_expected_demand = sum(self.expected_demand.values())
        self.max_trucks = max(1, math.ceil(total_expected_demand / self.truck_capacity)) if self.truck_capacity else len(self.sites)

        # 设置Big-M值
        if self.expected_demand:
            max_expected_demand_val = max(self.expected_demand.values())
            self.BIG_M = max(max_expected_demand_val * 2, 1e6)  # 乘以2考虑随机性
        else:
            self.BIG_M = 1e6

        print(f"参数设置完成: {len(customers)}个客户, {len(sites)}个候选站点, 最大{self.max_trucks}辆卡车")

    def _calculate_truck_distances(self):
        """计算卡车运输的距离矩阵（仓库-储物柜，储物柜-储物柜）"""
        if not self.depot_coord or not self.site_coords:
            return
            
        # 节点编号：0=仓库，1到n=储物柜站点
        # 仓库到储物柜的距离
        for j in self.sites:
            if j in self.site_coords:
                depot_x, depot_y = self.depot_coord
                site_x, site_y = self.site_coords[j]
                dist = math.sqrt((depot_x - site_x)**2 + (depot_y - site_y)**2)
                self.truck_distances[0, j] = dist
                self.truck_distances[j, 0] = dist
        
        # 储物柜之间的距离
        for j1 in self.sites:
            for j2 in self.sites:
                if j1 != j2 and j1 in self.site_coords and j2 in self.site_coords:
                    x1, y1 = self.site_coords[j1]
                    x2, y2 = self.site_coords[j2]
                    dist = math.sqrt((x1 - x2)**2 + (y1 - y2)**2)
                    self.truck_distances[j1, j2] = dist
                elif j1 == j2:
                    self.truck_distances[j1, j2] = 0

    def _generate_demand_samples(self, num_samples: int) -> List[Dict[int, float]]:
        """
        生成需求场景样本，与ALNS保持一致使用泊松分布
        修改：使用与ALNS完全相同的需求生成方式以确保公平对比
        """
        demand_scenarios = []
        for _ in range(num_samples):
            current_scenario_demand = {}
            for cust_id in self.customers:
                lambda_param = self.expected_demand[cust_id]  # 泊松分布参数λ
                # 【修改】使用与ALNS相同的泊松分布生成需求
                sampled_demand = np.random.poisson(lam=lambda_param)
                current_scenario_demand[cust_id] = float(sampled_demand)  # 转换为float以保持一致性
            demand_scenarios.append(current_scenario_demand)
        return demand_scenarios

    def _build_saa_model_for_one_replication(self, demand_samples_k: List[Dict[int, float]]):
        """为一次SAA复制构建整体优化模型 - 修正版两阶段结构"""
        model = gp.Model(f"SAA_IntegratedDroneDelivery_Rep")
        num_scenarios = len(demand_samples_k)

        # 第一阶段决策变量（不依赖场景）
        y_rep = {j: model.addVar(vtype=GRB.BINARY, name=f"y_{j}") for j in self.sites}  # 储物柜选址
        n_rep = {j: model.addVar(vtype=GRB.INTEGER, lb=0, name=f"n_{j}") for j in self.sites}  # 无人机配置

        # 第二阶段决策变量（依赖场景）- 包含实际配送量和卡车路径
        x_actual_rep = {}  # 实际配送量（仅受储物柜容量和无人机运力约束）
        shortage_rep = {}  # 短缺量（实际需求超出实际配送的部分）
        x_truck_rep = {}
        y_truck_rep = {}
        u_truck_rep = {}

        nodes = [0] + self.sites  # 所有节点（仓库+储物柜）

        for k_idx in range(num_scenarios):
            # 第二阶段实际配送变量
            for i in self.customers:
                for j in self.sites:
                    x_actual_rep[i, j, k_idx] = model.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_actual_{i}_{j}_{k_idx}")
                shortage_rep[i, k_idx] = model.addVar(vtype=GRB.INTEGER, lb=0, name=f"shortage_{i}_{k_idx}")

            # 卡车路径变量（第二阶段）
            for v in range(self.max_trucks):
                y_truck_rep[v, k_idx] = model.addVar(vtype=GRB.BINARY, name=f"y_truck_{v}_{k_idx}")

                for i in nodes:
                    for j in nodes:
                        if i != j:
                            x_truck_rep[i, j, v, k_idx] = model.addVar(vtype=GRB.BINARY, name=f"x_truck_{i}_{j}_{v}_{k_idx}")

                # 子回路消除变量（仅对储物柜节点）
                for j in self.sites:
                    u_truck_rep[j, v, k_idx] = model.addVar(vtype=GRB.CONTINUOUS, lb=1, ub=len(self.sites),
                                                          name=f"u_truck_{j}_{v}_{k_idx}")

        return model, y_rep, n_rep, x_actual_rep, shortage_rep, x_truck_rep, y_truck_rep, u_truck_rep

    def _set_saa_objective_and_constraints(self, model, demand_samples_k, y_rep, n_rep, x_actual_rep, shortage_rep, x_truck_rep, y_truck_rep, u_truck_rep):
        """为SAA模型设置目标函数和约束 - 修正版两阶段结构"""
        num_scenarios = len(demand_samples_k)
        nodes = [0] + self.sites

        # 第一阶段成本（不依赖需求场景）
        first_stage_cost = gp.quicksum(self.locker_fixed_cost[j] * y_rep[j] for j in self.sites) + \
                          gp.quicksum(self.drone_cost * n_rep[j] for j in self.sites)

        # 第二阶段期望成本（依赖需求场景实现）
        expected_second_stage_cost = gp.LinExpr()
        for k_idx in range(num_scenarios):
            demand_for_scenario_k = demand_samples_k[k_idx]

            # 第二阶段成本包括所有随需求场景变化的成本：
            # 1. 无人机运输成本（基于实际配送量）
            transport_cost_k = gp.quicksum(
                2 * self.transport_unit_cost * self.distance[i, j] * x_actual_rep[i, j, k_idx]
                for i in self.customers for j in self.sites if (i, j) in self.distance
            )

            # 2. 未分配惩罚成本（基于短缺量）
            unassigned_penalty_k = gp.quicksum(
                self.penalty_cost_unassigned * shortage_rep[i, k_idx]
                for i in self.customers
            )

            # 3. 卡车运输成本（固定成本 + 距离成本）
            truck_fixed_cost_k = gp.quicksum(self.truck_fixed_cost * y_truck_rep[v, k_idx] for v in range(self.max_trucks))
            truck_distance_cost_k = gp.quicksum(
                self.truck_distances.get((i, j), 0) * self.truck_km_cost * x_truck_rep[i, j, v, k_idx]
                for v in range(self.max_trucks)
                for i in nodes for j in nodes
                if i != j and (i, j, v, k_idx) in x_truck_rep
            )

            # 第二阶段总成本
            second_stage_cost_k = transport_cost_k + unassigned_penalty_k + truck_fixed_cost_k + truck_distance_cost_k
            expected_second_stage_cost += (1.0 / num_scenarios) * second_stage_cost_k

        # 总目标函数：第一阶段成本 + 第二阶段期望成本
        total_expected_cost = first_stage_cost + expected_second_stage_cost
        model.setObjective(total_expected_cost, GRB.MINIMIZE)

        # 添加约束
        self._add_saa_constraints(model, demand_samples_k, y_rep, n_rep, x_actual_rep, shortage_rep, x_truck_rep, y_truck_rep, u_truck_rep)

        model.update()
        return model

    def _add_saa_constraints(self, model, demand_samples_k, y_rep, n_rep, x_actual_rep, shortage_rep, x_truck_rep, y_truck_rep, u_truck_rep):
        """为SAA模型添加所有约束 - 修正版两阶段结构"""
        num_scenarios = len(demand_samples_k)
        nodes = [0] + self.sites

        # 第一阶段约束
        # 至少开设一个储物柜
        model.addConstr(gp.quicksum(y_rep[j] for j in self.sites) >= 1, "C1_AtLeastOneLocker_SAA")

        # 无人机配置约束
        for j in self.sites:
            model.addConstr(n_rep[j] >= y_rep[j], name=f"C6_MinOneDroneIfOpen_SAA_{j}")

        # 第二阶段约束（基于实际需求场景）
        for k_idx in range(num_scenarios):
            demand_for_scenario_k = demand_samples_k[k_idx]

            for i in self.customers:
                # 短缺量 = 实际需求 - 实际配送总量
                model.addConstr(
                    shortage_rep[i, k_idx] == demand_for_scenario_k[i] - gp.quicksum(x_actual_rep[i, j, k_idx] for j in self.sites),
                    name=f"ShortageDefinition_SAA_{i}_{k_idx}"
                )

                for j in self.sites:
                    # 实际配送量约束：只有开放的储物柜才能配送
                    model.addConstr(
                        x_actual_rep[i, j, k_idx] <= demand_for_scenario_k[i] * y_rep[j],
                        name=f"ActualDeliveryIfOpen_SAA_{i}_{j}_{k_idx}"
                    )

                    # 飞行距离限制
                    if (i, j) in self.distance:
                        model.addConstr(
                            2 * self.distance[i, j] * x_actual_rep[i, j, k_idx] <= self.max_flight_distance * demand_for_scenario_k[i],
                            name=f"FlightDistanceLimit_SAA_{i}_{j}_{k_idx}"
                        )

            # 储物柜容量约束（基于实际配送量）
            for j in self.sites:
                model.addConstr(
                    gp.quicksum(x_actual_rep[i, j, k_idx] for i in self.customers) <= self.Q_locker_capacity[j] * y_rep[j],
                    name=f"LockerCapacity_SAA_{j}_{k_idx}"
                )

                # 无人机运力约束（基于实际配送量）
                total_drone_hours_needed_j_k = gp.quicksum(
                    x_actual_rep[i, j, k_idx] * ((2 * self.distance[i, j] / self.drone_speed) + self.loading_time)
                    for i in self.customers if (i, j) in self.distance
                )
                drone_hours_supplied_j = n_rep[j] * self.H_drone_working_hours_per_day
                model.addConstr(
                    total_drone_hours_needed_j_k <= drone_hours_supplied_j,
                    name=f"DroneCapacity_SAA_{j}_{k_idx}"
                )

            # CVRP相关约束
            self._add_saa_cvrp_constraints(model, k_idx, demand_samples_k[k_idx], y_rep, x_actual_rep, x_truck_rep, y_truck_rep, u_truck_rep, nodes, k_idx)

    def _add_saa_cvrp_constraints(self, model, k_idx, demand_scenario_k, y_rep, x_actual_rep, x_truck_rep, y_truck_rep, u_truck_rep, nodes, scenario_idx):
        """为SAA模型添加CVRP相关约束 - 修正版"""

        # CVRP约束1: 每个开设的储物柜必须被恰好一辆卡车访问
        for j in self.sites:
            model.addConstr(
                gp.quicksum(x_truck_rep[i, j, v, k_idx] for i in nodes if i != j for v in range(self.max_trucks))
                == y_rep[j],
                name=f"CVRP_VisitOpenLocker_SAA_{j}_{k_idx}"
            )

        # CVRP约束2: 流平衡约束
        for v in range(self.max_trucks):
            for i in nodes:
                inflow = gp.quicksum(x_truck_rep[j, i, v, k_idx] for j in nodes if j != i)
                outflow = gp.quicksum(x_truck_rep[i, j, v, k_idx] for j in nodes if j != i)
                model.addConstr(inflow == outflow, name=f"CVRP_FlowBalance_SAA_{i}_{v}_{k_idx}")

        # CVRP约束3: 每辆卡车最多从仓库出发一次
        for v in range(self.max_trucks):
            model.addConstr(
                gp.quicksum(x_truck_rep[0, j, v, k_idx] for j in self.sites) <= y_truck_rep[v, k_idx],
                name=f"CVRP_TruckUsage_SAA_{v}_{k_idx}"
            )

        # CVRP约束4: 卡车容量约束（简化版 - 基于实际配送量）
        for v in range(self.max_trucks):
            # 计算卡车v承担的总需求量
            total_demand_on_truck = gp.LinExpr()
            for j in self.sites:
                # 储物柜j的实际需求量（所有分配给j的客户的实际配送量之和）
                locker_demand_j = gp.quicksum(x_actual_rep[i, j, k_idx] for i in self.customers)

                # 如果卡车v访问储物柜j，则承担该储物柜的需求
                truck_visits_j = gp.quicksum(x_truck_rep[j, l, v, k_idx] for l in nodes if l != j)
                total_demand_on_truck += locker_demand_j * truck_visits_j

            model.addConstr(
                total_demand_on_truck <= self.truck_capacity * y_truck_rep[v, k_idx],
                name=f"CVRP_Capacity_SAA_{v}_{k_idx}"
            )

        # CVRP约束5: 子回路消除约束（MTZ约束）
        for v in range(self.max_trucks):
            for i in self.sites:
                for j in self.sites:
                    if i != j:
                        model.addConstr(
                            u_truck_rep[i, v, k_idx] - u_truck_rep[j, v, k_idx] + len(self.sites) * x_truck_rep[i, j, v, k_idx]
                            <= len(self.sites) - 1,
                            name=f"CVRP_SubtourElim_SAA_{i}_{j}_{v}_{k_idx}"
                        )

        # 连接约束: 只有开设的储物柜才能被卡车访问
        for j in self.sites:
            for v in range(self.max_trucks):
                for i in nodes:
                    if i != j:
                        model.addConstr(
                            x_truck_rep[i, j, v, k_idx] <= y_rep[j],
                            name=f"Link_TruckVisitOpenLocker_SAA_{i}_{j}_{v}_{k_idx}"
                        )

    def solve_saa_with_fixed_solution(self, fixed_y, fixed_n, demand_samples):
        """
        使用固定的储物柜和无人机配置求解SAA模型

        Args:
            fixed_y: 固定的储物柜选择 {j: 0/1}
            fixed_n: 固定的无人机配置 {j: num_drones}
            demand_samples: 需求样本

        Returns:
            目标函数值
        """
        try:
            # 创建模型
            self.model = gp.Model("FixedSolutionSAA")
            self.model.setParam('OutputFlag', 0)  # 静默求解
            self.model.setParam('TimeLimit', 600)
            self.model.setParam('MIPGap', 0.1)

            # 添加变量和约束
            self._add_variables_saa(demand_samples)
            self._add_constraints_saa(demand_samples)

            # 固定储物柜选择
            for j in range(1, len(self.sites) + 1):
                self.y[j].setAttr('LB', fixed_y.get(j, 0))
                self.y[j].setAttr('UB', fixed_y.get(j, 0))

            # 固定无人机配置
            for j in range(1, len(self.sites) + 1):
                self.n[j].setAttr('LB', fixed_n.get(j, 0))
                self.n[j].setAttr('UB', fixed_n.get(j, 0))

            # 设置目标函数
            self._set_objective_saa(demand_samples)

            # 求解
            self.model.optimize()

            if self.model.status == gp.GRB.OPTIMAL:
                return self.model.objVal
            else:
                print(f"求解状态: {self.model.status}")
                return float('inf')

        except Exception as e:
            print(f"固定方案求解失败: {e}")
            return float('inf')

    def solve_saa(self, time_limit_per_replication: int = 600, mip_gap_per_replication: float = 0.02):
        """使用SAA方法求解整体优化问题"""
        saa_start_time = time.time()  # 记录SAA开始时间

        print(f"\n开始SAA整体优化，最多 {SAA_MAX_REPLICATIONS_M} 次复制...")
        print(f"SAA终止条件 (必须同时满足):")
        print(f"  1. 相对差距阈值: Gap/UB ≤ {SAA_GAP_TOLERANCE_PERCENT*100:.0f}% 且 Gap ≥ 0")
        print(f"  2. 方差阈值: δ²_Gap/UB ≤ {SAA_VARIANCE_TOLERANCE_PERCENT*100:.0f}%")
        print(f"  其中: Gap = UB - LB, δ²_Gap = δ²(LB) + δ²(UB)")
        print(f"  最少需要 {SAA_MIN_REPLICATIONS_M} 次有效复制")

        # 初始化求解状态统计
        self.saa_solve_status_summary = {
            'optimal': 0,
            'time_limit': 0,
            'other': 0,
            'total': 0
        }

        # 生成固定的验证样本集 (所有复制共用)
        print(f"生成 {SAA_SAMPLES_K_PRIME} 个固定验证样本 (所有复制共用)...")

        # 【修改】确保与ALNS使用相同的验证样本生成方式
        original_random_state = random.getstate()
        original_np_state = np.random.get_state()
        random.seed(RANDOM_SEED + 1000)  # 使用不同的种子避免与训练样本重复
        np.random.seed(RANDOM_SEED + 1000)

        self.fixed_validation_samples = self._generate_demand_samples(num_samples=SAA_SAMPLES_K_PRIME)

        # 恢复随机状态
        random.setstate(original_random_state)
        np.random.set_state(original_np_state)

        # 【新增】调试信息：显示验证样本的统计特征
        if len(self.fixed_validation_samples) > 0:
            first_sample = self.fixed_validation_samples[0]
            total_demand_first = sum(first_sample.values())
            print(f"  验证样本生成完成，第一个样本总需求: {total_demand_first:.1f}")
            print(f"  前3个客户在第一个样本中的需求: {[first_sample.get(i, 0) for i in range(1, 4)]}")

        self.saa_solutions_first_stage = []
        self.saa_objective_values_k = []
        self.saa_upper_bounds_k_prime = []
        self.saa_truck_costs_k_prime = []

        best_solution_info = {'y': None, 'n': None, 'avg_obj_k_prime': float('inf'), 'replication_idx': -1, 'truck_cost_k_prime': 0.0}

        for m_rep in range(SAA_MAX_REPLICATIONS_M):
            print(f"\n--- SAA 复制 {m_rep + 1}/{SAA_MAX_REPLICATIONS_M} ---")
            demand_samples_for_k = self._generate_demand_samples(num_samples=SAA_SAMPLES_K)
            print(f"  已生成 {SAA_SAMPLES_K} 个需求场景用于求解。")

            # 调试信息：显示前几个场景的需求以验证随机性
            if m_rep < 2:  # 只显示前两次复制的信息
                print(f"  调试：前3个场景的客户1需求: {[demand_samples_for_k[k][1] for k in range(min(3, len(demand_samples_for_k)))]}")
                total_demand_scenario_1 = sum(demand_samples_for_k[0].values())
                print(f"  调试：第1个场景总需求: {total_demand_scenario_1:.1f}")

            # 构建SAA模型
            saa_model_rep, y_vars, n_vars, x_actual_vars, shortage_vars, x_truck_vars, y_truck_vars, u_truck_vars = \
                self._build_saa_model_for_one_replication(demand_samples_for_k)

            # 设置目标函数和约束
            saa_model_rep = self._set_saa_objective_and_constraints(
                saa_model_rep, demand_samples_for_k, y_vars, n_vars, x_actual_vars, shortage_vars,
                x_truck_vars, y_truck_vars, u_truck_vars
            )

            # 设置求解参数
            saa_model_rep.setParam('TimeLimit', time_limit_per_replication)
            saa_model_rep.setParam('MIPGap', mip_gap_per_replication)  # 修复：使用传入的Gap参数
            saa_model_rep.setParam('OutputFlag', 0)
            saa_model_rep.setParam('LogToConsole', 0)
            saa_model_rep.setParam('Threads', 1)

            print(f"  开始求解复制 {m_rep + 1} 的SAA模型...")
            print(f"    模型统计: {saa_model_rep.NumVars} 个变量, {saa_model_rep.NumConstrs} 个约束")
            solve_start_time_rep = time.time()
            saa_model_rep.optimize()
            solve_time_rep = time.time() - solve_start_time_rep

            # 详细的求解状态分析
            status_msg = self._get_gurobi_status_message(saa_model_rep.status)
            print(f"  复制 {m_rep + 1} 求解完成，耗时: {solve_time_rep:.2f} 秒，状态: {saa_model_rep.status} ({status_msg})")

            # 【增强】详细的求解质量分析
            if saa_model_rep.SolCount > 0:
                print(f"    找到解的数量: {saa_model_rep.SolCount}")
                print(f"    最佳目标值: {saa_model_rep.ObjVal:.2f}")
                if hasattr(saa_model_rep, 'ObjBound'):
                    obj_bound = saa_model_rep.ObjBound
                    if obj_bound < float('inf'):
                        gap = abs(saa_model_rep.ObjVal - obj_bound) / abs(saa_model_rep.ObjVal) * 100
                        print(f"    最佳界限: {obj_bound:.2f}")
                        print(f"    MIP Gap: {gap:.2f}%")
                        if gap > mip_gap_per_replication * 100:
                            print(f"    ⚠ 警告: MIP Gap ({gap:.2f}%) 超过设定阈值 ({mip_gap_per_replication*100:.1f}%)")
                            print(f"    ⚠ 这意味着当前解可能不是最优解")
                    else:
                        print(f"    最佳界限: 无界")

                # 检查是否因时间限制终止
                if saa_model_rep.status == 9:  # TIME_LIMIT
                    print(f"    ⚠ 求解因时间限制终止，当前解可能不是最优解")
                    print(f"    ⚠ 建议增加时间限制或降低MIP Gap要求")
                    self.saa_solve_status_summary['time_limit'] += 1
                elif saa_model_rep.status == 2:  # OPTIMAL
                    print(f"    ✓ 找到最优解")
                    self.saa_solve_status_summary['optimal'] += 1
                else:
                    self.saa_solve_status_summary['other'] += 1
            else:
                print(f"    ⚠ 未找到任何可行解")
                self.saa_solve_status_summary['other'] += 1

            # 更新总计数
            self.saa_solve_status_summary['total'] += 1

            # 如果求解失败，进行诊断
            if saa_model_rep.status == 3:  # INFEASIBLE
                print(f"    模型不可行，正在计算IIS...")
                try:
                    saa_model_rep.computeIIS()
                    print(f"    IIS包含 {saa_model_rep.IISConstrCount} 个约束")
                except:
                    print(f"    无法计算IIS")
            elif saa_model_rep.status == 4:  # INF_OR_UNBD
                print(f"    模型不可行或无界，建议检查约束设置")

            if saa_model_rep.SolCount > 0:  # 即使不是OPTIMAL，但有解就评估
                obj_val_k = saa_model_rep.ObjVal
                self.saa_objective_values_k.append(obj_val_k)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K} 个样本上的目标值: {obj_val_k:.2f}")

                # 记录复制求解时间
                if not hasattr(self, 'saa_replication_times'):
                    self.saa_replication_times = []
                self.saa_replication_times.append(solve_time_rep)

                y_star_m = {j: y_vars[j].X for j in self.sites}
                n_star_m = {j: n_vars[j].X for j in self.sites}

                self.saa_solutions_first_stage.append({
                    'y': y_star_m,
                    'n': n_star_m
                })

                print(f"  评估复制 {m_rep + 1} 的解在 {SAA_SAMPLES_K_PRIME} 个固定验证样本上的性能...")
                # 传递完整的第一阶段解
                first_stage_solution = self.saa_solutions_first_stage[-1]
                avg_obj_k_prime, avg_truck_cost_k_prime = self._evaluate_solution_on_new_samples_corrected(first_stage_solution, self.fixed_validation_samples)
                self.saa_upper_bounds_k_prime.append(avg_obj_k_prime)
                self.saa_truck_costs_k_prime.append(avg_truck_cost_k_prime)
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均目标值 (UB估计): {avg_obj_k_prime:.2f}")
                print(f"  复制 {m_rep + 1} 在 {SAA_SAMPLES_K_PRIME} 个样本上的平均卡车成本: {avg_truck_cost_k_prime:.2f}")

                if avg_obj_k_prime < best_solution_info['avg_obj_k_prime']:
                    best_solution_info.update(first_stage_solution)  # 保存完整的第一阶段解
                    best_solution_info['avg_obj_k_prime'] = avg_obj_k_prime
                    best_solution_info['truck_cost_k_prime'] = avg_truck_cost_k_prime
                    best_solution_info['replication_idx'] = m_rep
            else:
                print(f"  复制 {m_rep + 1} 未能找到可行解。")
                self.saa_objective_values_k.append(float('inf'))
                self.saa_upper_bounds_k_prime.append(float('inf'))
                self.saa_truck_costs_k_prime.append(float('inf'))
                self.saa_solutions_first_stage.append(None)
  
            del saa_model_rep

            # 检查SAA终止条件 (从第2次复制开始检查)
            if m_rep + 1 >= SAA_MIN_REPLICATIONS_M:
                should_terminate, gap_info = self._check_saa_termination_criteria()
                if should_terminate:
                    print(f"\n✓ SAA终止条件满足，在第 {m_rep + 1} 次复制后停止")
                    print(f"  {gap_info}")
                    break
                else:
                    print(f"  当前SAA状态: {gap_info}")
            else:
                # 即使在最小复制次数之前，也显示当前状态（如果有足够的数据）
                if len([s for s in self.saa_solutions_first_stage if s is not None]) >= 2:
                    _, gap_info = self._check_saa_termination_criteria()
                    print(f"  当前SAA状态: {gap_info}")

        if not any(s is not None for s in self.saa_solutions_first_stage):
            print("\nSAA 未能产生任何有效解。")
            return None

        # 统计结果
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        avg_saa_obj_k = np.mean(valid_obj_k) if valid_obj_k else float('inf')
        std_saa_obj_k = np.std(valid_obj_k) if valid_obj_k else float('inf')
        avg_upper_bound = np.mean(valid_ub_k_prime) if valid_ub_k_prime else float('inf')
        std_upper_bound = np.std(valid_ub_k_prime) if valid_ub_k_prime else float('inf')

        actual_replications = len([s for s in self.saa_solutions_first_stage if s is not None])
        saa_total_time = time.time() - saa_start_time  # 计算SAA总耗时

        print(f"\n📊 SAA 统计结果汇总 ({actual_replications} 次有效复制，使用Gurobi)")
        print(f"=" * 60)
        print(f"  下界估计 cost_N^m: {avg_saa_obj_k:.2f} 元/天")
        print(f"    ↳ 计算方法: 前{actual_replications}次复制的小样本优化成本的算术平均")
        print(f"    ↳ 含义: 系统真实期望成本的下界估计")
        print(f"    - 下界标准差: {std_saa_obj_k:.2f}")
        print(f"    - 下界方差 δ²(cost_N): {std_saa_obj_k**2/(actual_replications*(actual_replications-1)) if actual_replications > 1 else 0:.4f}")
        print(f"  上界估计 cost_{SAA_SAMPLES_K_PRIME}(ŝ): {avg_upper_bound:.2f} 元/天")
        print(f"    ↳ 计算方法: 最佳解在{SAA_SAMPLES_K_PRIME}个大样本场景下的平均成本")
        print(f"    ↳ 含义: 最佳方案长期运营的期望日均成本")
        print(f"    - 上界标准差: {std_upper_bound:.2f}")
        print(f"  SAA Gap: {avg_upper_bound - avg_saa_obj_k:.2f} 元/天 ({((avg_upper_bound - avg_saa_obj_k)/avg_upper_bound*100):.1f}%)")

        # 详细时间统计
        print(f"  ⏱️ 求解时间统计:")
        print(f"    SAA总求解时间: {saa_total_time:.2f} 秒")
        if hasattr(self, 'saa_replication_times') and self.saa_replication_times:
            avg_replication_time = np.mean(self.saa_replication_times)
            total_replication_time = sum(self.saa_replication_times)
            print(f"    平均每次复制求解时间: {avg_replication_time:.2f} 秒")
            print(f"    复制求解总时间: {total_replication_time:.2f} 秒")
            print(f"    验证评估时间: {saa_total_time - total_replication_time:.2f} 秒")

        if best_solution_info['y'] is not None:
            # 计算最佳解的成本分解信息
            selected_lockers = [j for j, val in best_solution_info['y'].items() if val > 0.5]
            locker_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers)
            drone_cost = sum(self.drone_cost * best_solution_info['n'].get(j, 0) for j in selected_lockers)
            truck_cost = best_solution_info.get('truck_cost_k_prime', 0)
            total_cost = best_solution_info['avg_obj_k_prime']

            print(f"\n🏆 最佳解详情 (来自复制 {best_solution_info['replication_idx'] + 1})")
            print(f"  总成本: {total_cost:.2f} 元/天")
            print(f"  开放储物柜: {len(selected_lockers)} 个")

            # 使用精确计算进行成本分解（与详细成本分解保持一致）
            other_costs_temp = total_cost - locker_cost - drone_cost - truck_cost

            # 使用少量样本进行快速精确计算
            drone_transport_temp = 0
            penalty_temp = 0

            if hasattr(self, 'fixed_validation_samples') and self.fixed_validation_samples:
                # 使用前100个样本进行快速精确计算
                sample_count = min(100, len(self.fixed_validation_samples))
                total_transport = 0
                total_penalty = 0
                valid_scenarios = 0

                for scenario in self.fixed_validation_samples[:sample_count]:
                    scenario_result = self._solve_scenario_with_fixed_first_stage(
                        best_solution_info['y'], best_solution_info['n'], selected_lockers, scenario
                    )

                    if scenario_result is not None:
                        total_transport += scenario_result['transport_cost']
                        total_penalty += scenario_result['penalty_cost']
                        valid_scenarios += 1

                if valid_scenarios > 0:
                    drone_transport_temp = total_transport / valid_scenarios
                    penalty_temp = total_penalty / valid_scenarios
                else:
                    # 回退到估算方法
                    drone_transport_temp = other_costs_temp * 0.8
                    penalty_temp = other_costs_temp * 0.2
            else:
                # 回退到估算方法
                drone_transport_temp = other_costs_temp * 0.8
                penalty_temp = other_costs_temp * 0.2

            drone_total_temp = drone_cost + drone_transport_temp
            print(f"  成本构成: 储物柜 {locker_cost:.2f} + 无人机(部署+运输) {drone_total_temp:.2f} + 卡车(固定+运输) {truck_cost:.2f} + 惩罚 {penalty_temp:.2f}")

            final_solution_saa = {
                'objective_value_k_prime_estimate': best_solution_info['avg_obj_k_prime'],
                'truck_cost_k_prime_estimate': best_solution_info['truck_cost_k_prime'],
                'selected_lockers_y': best_solution_info['y'],
                'drone_allocations_n': best_solution_info['n'],
                'saa_solve_time': saa_total_time,  # 添加求解时间到结果中
            }
            return final_solution_saa
        else:
            print(f"\nSAA 未能找到任何有效的最终解。总耗时: {saa_total_time:.2f} 秒")
            return None

    def _print_detailed_cost_breakdown(self, saa_solution_dict):
        """打印详细的成本构成分析 - 标准两阶段随机规划结构"""
        print(f"\n  详细成本构成分析 (标准两阶段随机规划结构):")
        print(f"  " + "-" * 60)

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        selected_lockers = [j for j, val in y_star.items() if val > 0.5]

        # 第一阶段成本（here-and-now decisions）
        locker_fixed_cost = sum(self.locker_fixed_cost[j] * y_star.get(j, 0) for j in selected_lockers)
        drone_deployment_cost = sum(self.drone_cost * n_star.get(j, 0) for j in selected_lockers)
        first_stage_total = locker_fixed_cost + drone_deployment_cost

        print(f"  第一阶段成本 (here-and-now): {first_stage_total:.2f}")
        print(f"    - 储物柜选址固定成本: {locker_fixed_cost:.2f}")
        print(f"    - 无人机配置成本: {drone_deployment_cost:.2f}")

        # 使用样本估算第二阶段期望成本（wait-and-see decisions）
        print(f"  正在计算第二阶段期望成本 (wait-and-see)...")
        sample_scenarios = self._generate_demand_samples(num_samples=100)  # 使用更多样本提高精度
        total_transport_cost = 0
        total_penalty_cost = 0
        total_truck_cost = 0

        for scenario_idx, scenario in enumerate(sample_scenarios):
            # 为该场景求解最优客户分配和卡车路径
            scenario_result = self._solve_scenario_with_fixed_first_stage(y_star, n_star, selected_lockers, scenario)

            if scenario_result is not None:
                scenario_transport_cost = scenario_result['transport_cost']
                scenario_penalty_cost = scenario_result['penalty_cost']
                scenario_truck_cost = scenario_result['truck_cost']
            else:
                # 如果求解失败，使用简化估算
                scenario_transport_cost = 0
                scenario_penalty_cost = self.penalty_cost_unassigned * sum(scenario.values())
                scenario_truck_cost = self.truck_fixed_cost * len(selected_lockers)

            total_transport_cost += scenario_transport_cost
            total_penalty_cost += scenario_penalty_cost
            total_truck_cost += scenario_truck_cost

        # 计算第二阶段期望成本
        avg_transport_cost = total_transport_cost / len(sample_scenarios)
        avg_penalty_cost = total_penalty_cost / len(sample_scenarios)
        avg_truck_cost = total_truck_cost / len(sample_scenarios)
        truck_cost_estimate = saa_solution_dict.get('truck_cost_k_prime_estimate', avg_truck_cost)

        second_stage_total = avg_transport_cost + avg_penalty_cost + truck_cost_estimate

        print(f"  第二阶段期望成本 (wait-and-see): {second_stage_total:.2f}")
        print(f"    - 无人机运输成本 (随实际需求变化): {avg_transport_cost:.2f}")
        print(f"    - 未分配惩罚成本 (随需求不确定性): {avg_penalty_cost:.2f}")
        print(f"    - 卡车运输成本 (随储物柜需求变化): {truck_cost_estimate:.2f}")

        total_cost = first_stage_total + second_stage_total
        final_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        print(f"\n  总系统成本 (理论估计): {total_cost:.2f}")
        print(f"  ↳ 基于SAA求解样本的理论计算")
        print(f"  SAA上界估计 cost_{{N'}}(ŝ): {final_objective:.2f} ⭐ 【最终成本】")
        print(f"  ↳ 基于{SAA_SAMPLES_K_PRIME}个验证场景的实际评估，更准确可靠")
        print(f"  估计误差: {abs(total_cost - final_objective):.2f} (理论值与实际评估的差异)")

        # 两阶段成本占比分析
        if final_objective > 0:
            print(f"\n  两阶段成本结构分析:")
            print(f"    - 第一阶段 (here-and-now) 占比: {first_stage_total/final_objective*100:.1f}%")
            print(f"    - 第二阶段 (wait-and-see) 占比: {second_stage_total/final_objective*100:.1f}%")
            print(f"    - 确定性决策成本占比: {first_stage_total/final_objective*100:.1f}%")
            print(f"    - 不确定性应对成本占比: {second_stage_total/final_objective*100:.1f}%")

        # 需求不确定性影响分析
        print(f"\n  需求不确定性影响分析:")
        total_expected_demand = sum(self.expected_demand.values())

        print(f"    - 总期望需求: {total_expected_demand:.2f}")
        print(f"    - 平均惩罚成本: {avg_penalty_cost:.2f}")
        print(f"    - 惩罚成本占比: {avg_penalty_cost/final_objective*100:.1f}%" if final_objective > 0 else "    - 惩罚成本占比: N/A")

    def _evaluate_solution_on_new_samples(self, y_star: Dict, n_star: Dict, demand_samples_k_prime: List[Dict[int, float]]):
        """在新样本上评估给定的第一阶段解"""
        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        # 第一阶段固定成本
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j, 0) for j in self.sites if y_star.get(j, 0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j, 0) for j in self.sites if y_star.get(j, 0) > 0.5)
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 对每个验证场景求解第二阶段子问题
        for k_prime_idx, demand_scenario_k_prime in enumerate(demand_samples_k_prime):
            # 构建第二阶段子问题
            model_sp = gp.Model(f"SP_eval_{k_prime_idx}")
            model_sp.setParam('OutputFlag', 0)
            model_sp.setParam('Threads', 1)

            # 第二阶段变量
            x_sp = {}
            z_sp = {}
            # 移除冗余的u_sp变量，因为惩罚成本通过直接计算短缺量实现
            x_truck_sp = {}
            y_truck_sp = {}
            u_truck_sp = {}

            nodes = [0] + selected_lockers_eval

            # 储物柜分配变量
            for i in self.customers:
                for j_site in selected_lockers_eval:
                    x_sp[i, j_site] = model_sp.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_sp_{i}_{j_site}")
                    z_sp[i, j_site] = model_sp.addVar(vtype=GRB.BINARY, name=f"z_sp_{i}_{j_site}")

            # 卡车变量
            for v in range(self.max_trucks):
                y_truck_sp[v] = model_sp.addVar(vtype=GRB.BINARY, name=f"y_truck_sp_{v}")
                for i in nodes:
                    for j in nodes:
                        if i != j:
                            x_truck_sp[i, j, v] = model_sp.addVar(vtype=GRB.BINARY, name=f"x_truck_sp_{i}_{j}_{v}")
                for j in selected_lockers_eval:
                    u_truck_sp[j, v] = model_sp.addVar(vtype=GRB.CONTINUOUS, lb=1, ub=len(selected_lockers_eval),
                                                      name=f"u_truck_sp_{j}_{v}")

            # 第二阶段目标函数
            transport_cost_sp = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j_site), self.BIG_M) * x_sp.get((i, j_site), 0)
                for i in self.customers for j_site in selected_lockers_eval
            )
            unassigned_penalty_sp = gp.quicksum(
                self.penalty_cost_unassigned *
                (demand_scenario_k_prime[i] - gp.quicksum(x_sp.get((i, site_k_sp), 0) for site_k_sp in selected_lockers_eval))
                for i in self.customers
            )
            truck_fixed_cost_sp = gp.quicksum(self.truck_fixed_cost * y_truck_sp[v] for v in range(self.max_trucks))
            truck_distance_cost_sp = gp.quicksum(
                self.truck_distances.get((i, j), 0) * self.truck_km_cost * x_truck_sp[i, j, v]
                for v in range(self.max_trucks) for i in nodes for j in nodes
                if i != j and (i, j, v) in x_truck_sp
            )

            model_sp.setObjective(transport_cost_sp + unassigned_penalty_sp + truck_fixed_cost_sp + truck_distance_cost_sp, GRB.MINIMIZE)

            # 第二阶段约束
            self._add_second_stage_constraints(model_sp, demand_scenario_k_prime, selected_lockers_eval, n_star,
                                             x_sp, z_sp, x_truck_sp, y_truck_sp, u_truck_sp)

            model_sp.optimize()

            second_stage_cost_k_prime = 0
            truck_cost_k_prime = 0
            if model_sp.status == GRB.OPTIMAL:
                second_stage_cost_k_prime = model_sp.ObjVal
                # 计算卡车成本部分
                truck_cost_k_prime = sum(
                    self.truck_fixed_cost * y_truck_sp[v].X for v in range(self.max_trucks)
                ) + sum(
                    self.truck_distances.get((i, j), 0) * self.truck_km_cost * x_truck_sp[i, j, v].X
                    for v in range(self.max_trucks) for i in nodes for j in nodes
                    if i != j and (i, j, v) in x_truck_sp
                )
            else:
                # 如果子问题不可行，使用惩罚成本
                second_stage_cost_k_prime = self.BIG_M * sum(demand_scenario_k_prime.values())
                truck_cost_k_prime = 0

            total_cost_for_scenario_k_prime = locker_cost_fixed + drone_deployment_cost_fixed + second_stage_cost_k_prime
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime
            total_truck_cost_over_k_prime_samples += truck_cost_k_prime
            del model_sp

        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else 0.0
        return avg_total_cost_k_prime, avg_truck_cost_k_prime

    def _evaluate_solution_on_new_samples_corrected(self, first_stage_solution: Dict, demand_samples_k_prime: List[Dict[int, float]]):
        """
        修正版：评估完整第一阶段解在新样本上的性能
        按照正确的上界计算公式：cost_{N'}(ŝ) = (1/N') * Σ Q(ŝ, ξ_n)
        其中 Q(ŝ, ξ_n) 是解ŝ在场景ξ_n下的总系统成本
        """
        total_cost_over_k_prime_samples = 0
        total_truck_cost_over_k_prime_samples = 0
        num_scenarios_k_prime = len(demand_samples_k_prime)

        # 提取第一阶段解
        y_star = first_stage_solution['y']
        n_star = first_stage_solution['n']
        selected_lockers_eval = [j for j, val in y_star.items() if val > 0.5]

        # 计算第一阶段固定成本（在解ŝ确定后为常数）
        locker_cost_fixed = sum(self.locker_fixed_cost[j] * y_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)
        drone_deployment_cost_fixed = sum(self.drone_cost * n_star.get(j,0) for j in self.sites if y_star.get(j,0) > 0.5)

        # 对每个验证场景计算 Q(ŝ, ξ_n) - 总系统成本
        for k_prime_idx, demand_scenario_k_prime in enumerate(demand_samples_k_prime):
            # 为该场景求解最优客户分配和卡车路径
            scenario_result = self._solve_scenario_with_fixed_first_stage(y_star, n_star, selected_lockers_eval, demand_scenario_k_prime)

            if scenario_result is not None:
                drone_transport_cost_k_prime = scenario_result['transport_cost']
                penalty_cost_k_prime = scenario_result['penalty_cost']
                locker_actual_demands = scenario_result['locker_demands']
            else:
                # 如果求解失败，使用简化估算
                drone_transport_cost_k_prime = 0
                penalty_cost_k_prime = self.penalty_cost_unassigned * sum(demand_scenario_k_prime.values())
                locker_actual_demands = {j: 0 for j in selected_lockers_eval}

            # 5. 求解卡车路径子问题
            model_sp = gp.Model(f"SP_eval_{k_prime_idx}")
            model_sp.setParam('OutputFlag', 0)
            model_sp.setParam('Threads', 1)

            # 卡车变量
            x_truck_sp = {}
            y_truck_sp = {}
            u_truck_sp = {}
            nodes = [0] + selected_lockers_eval

            for v in range(self.max_trucks):
                y_truck_sp[v] = model_sp.addVar(vtype=GRB.BINARY, name=f"y_truck_sp_{v}")
                for i in nodes:
                    for j in nodes:
                        if i != j:
                            x_truck_sp[i, j, v] = model_sp.addVar(vtype=GRB.BINARY, name=f"x_truck_sp_{i}_{j}_{v}")
                for j in selected_lockers_eval:
                    u_truck_sp[j, v] = model_sp.addVar(vtype=GRB.CONTINUOUS, lb=1, ub=len(selected_lockers_eval),
                                                      name=f"u_truck_sp_{j}_{v}")

            # 卡车成本目标函数
            truck_fixed_cost_sp = gp.quicksum(self.truck_fixed_cost * y_truck_sp[v] for v in range(self.max_trucks))
            truck_distance_cost_sp = gp.quicksum(
                self.truck_distances.get((i, j), 0) * self.truck_km_cost * x_truck_sp[i, j, v]
                for v in range(self.max_trucks) for i in nodes for j in nodes
                if i != j and (i, j, v) in x_truck_sp
            )
            model_sp.setObjective(truck_fixed_cost_sp + truck_distance_cost_sp, GRB.MINIMIZE)

            # CVRP约束
            self._add_second_stage_cvrp_constraints(model_sp, selected_lockers_eval, locker_actual_demands,
                                                   x_truck_sp, y_truck_sp, u_truck_sp, nodes)

            model_sp.optimize()

            truck_cost_k_prime = 0
            if model_sp.status == GRB.OPTIMAL:
                truck_cost_k_prime = model_sp.ObjVal
            else:
                # 如果子问题不可行，使用简化的卡车成本估算
                truck_cost_k_prime = self.truck_fixed_cost * len(selected_lockers_eval)

            # Q(ŝ, ξ_n) = 总系统成本（包括所有成本组件）
            total_cost_for_scenario_k_prime = (locker_cost_fixed + drone_deployment_cost_fixed +
                                             drone_transport_cost_k_prime + penalty_cost_k_prime + truck_cost_k_prime)
            total_cost_over_k_prime_samples += total_cost_for_scenario_k_prime
            total_truck_cost_over_k_prime_samples += truck_cost_k_prime
            del model_sp

        # 上界 cost_{N'}(ŝ) = (1/N') * Σ Q(ŝ, ξ_n)
        avg_total_cost_k_prime = total_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else float('inf')
        avg_truck_cost_k_prime = total_truck_cost_over_k_prime_samples / num_scenarios_k_prime if num_scenarios_k_prime > 0 else 0.0
        return avg_total_cost_k_prime, avg_truck_cost_k_prime

    def _solve_scenario_with_fixed_first_stage(self, y_star: Dict, n_star: Dict, selected_lockers: List[int], demand_scenario: Dict[int, float]):
        """
        为给定的需求场景求解最优客户分配和卡车路径（固定第一阶段决策）
        """
        try:
            model_scenario = gp.Model("ScenarioOptimization")
            model_scenario.setParam('OutputFlag', 0)
            model_scenario.setParam('Threads', 1)
            model_scenario.setParam('TimeLimit', 30)  # 限制求解时间

            # 第二阶段决策变量：客户分配
            x_scenario = {}
            for i in self.customers:
                for j in selected_lockers:
                    x_scenario[i, j] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_{i}_{j}")

            # 短缺变量
            shortage_scenario = {}
            for i in self.customers:
                shortage_scenario[i] = model_scenario.addVar(vtype=GRB.INTEGER, lb=0, name=f"shortage_{i}")

            # 卡车路径变量
            x_truck_scenario = {}
            y_truck_scenario = {}
            u_truck_scenario = {}
            nodes = [0] + selected_lockers

            for v in range(self.max_trucks):
                y_truck_scenario[v] = model_scenario.addVar(vtype=GRB.BINARY, name=f"y_truck_{v}")
                for i in nodes:
                    for j in nodes:
                        if i != j:
                            x_truck_scenario[i, j, v] = model_scenario.addVar(vtype=GRB.BINARY, name=f"x_truck_{i}_{j}_{v}")
                for j in selected_lockers:
                    u_truck_scenario[j, v] = model_scenario.addVar(vtype=GRB.CONTINUOUS, lb=1, ub=len(selected_lockers),
                                                                  name=f"u_truck_{j}_{v}")

            # 目标函数：最小化第二阶段成本
            transport_cost = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), self.BIG_M) * x_scenario[i, j]
                for i in self.customers for j in selected_lockers
            )
            penalty_cost = gp.quicksum(
                self.penalty_cost_unassigned * shortage_scenario[i]
                for i in self.customers
            )
            truck_fixed_cost = gp.quicksum(self.truck_fixed_cost * y_truck_scenario[v] for v in range(self.max_trucks))
            truck_distance_cost = gp.quicksum(
                self.truck_distances.get((i, j), 0) * self.truck_km_cost * x_truck_scenario[i, j, v]
                for v in range(self.max_trucks) for i in nodes for j in nodes
                if i != j
            )

            model_scenario.setObjective(transport_cost + penalty_cost + truck_fixed_cost + truck_distance_cost, GRB.MINIMIZE)

            # 约束
            for i in self.customers:
                # 短缺量定义
                model_scenario.addConstr(
                    shortage_scenario[i] == demand_scenario[i] - gp.quicksum(x_scenario[i, j] for j in selected_lockers),
                    name=f"ShortageDefinition_{i}"
                )

                for j in selected_lockers:
                    # 实际配送量不超过实际需求
                    model_scenario.addConstr(
                        x_scenario[i, j] <= demand_scenario[i],
                        name=f"DeliveryLimit_{i}_{j}"
                    )

                    # 飞行距离限制
                    if (i, j) in self.distance:
                        model_scenario.addConstr(
                            2 * self.distance[i, j] * x_scenario[i, j] <= self.max_flight_distance * demand_scenario[i],
                            name=f"FlightDistance_{i}_{j}"
                        )

            # 储物柜容量约束
            for j in selected_lockers:
                model_scenario.addConstr(
                    gp.quicksum(x_scenario[i, j] for i in self.customers) <= self.Q_locker_capacity[j],
                    name=f"LockerCapacity_{j}"
                )

                # 无人机运力约束
                total_drone_hours_needed = gp.quicksum(
                    x_scenario[i, j] * ((2 * self.distance[i, j] / self.drone_speed) + self.loading_time)
                    for i in self.customers if (i, j) in self.distance
                )
                drone_hours_supplied = n_star.get(j, 0) * self.H_drone_working_hours_per_day
                model_scenario.addConstr(
                    total_drone_hours_needed <= drone_hours_supplied,
                    name=f"DroneCapacity_{j}"
                )

            # CVRP约束
            self._add_second_stage_cvrp_constraints_for_scenario(model_scenario, selected_lockers, x_scenario, x_truck_scenario, y_truck_scenario, u_truck_scenario, nodes)

            model_scenario.optimize()

            if model_scenario.SolCount > 0:
                # 提取解
                transport_cost_val = sum(
                    2 * self.transport_unit_cost * self.distance.get((i, j), 0) * x_scenario[i, j].X
                    for i in self.customers for j in selected_lockers
                    if (i, j) in self.distance
                )
                penalty_cost_val = sum(
                    self.penalty_cost_unassigned * shortage_scenario[i].X
                    for i in self.customers
                )
                truck_cost_val = sum(self.truck_fixed_cost * y_truck_scenario[v].X for v in range(self.max_trucks)) + \
                                sum(self.truck_distances.get((i, j), 0) * self.truck_km_cost * x_truck_scenario[i, j, v].X
                                    for v in range(self.max_trucks) for i in nodes for j in nodes
                                    if i != j and (i, j, v) in x_truck_scenario)

                locker_demands = {j: sum(x_scenario[i, j].X for i in self.customers) for j in selected_lockers}

                return {
                    'transport_cost': transport_cost_val,
                    'penalty_cost': penalty_cost_val,
                    'truck_cost': truck_cost_val,
                    'locker_demands': locker_demands
                }
            else:
                return None

        except Exception as e:
            print(f"  场景优化求解失败: {str(e)}")
            return None

    def _add_second_stage_cvrp_constraints_for_scenario(self, model_scenario, selected_lockers, x_scenario, x_truck_scenario, y_truck_scenario, u_truck_scenario, nodes):
        """为场景优化添加CVRP约束"""
        # 每个储物柜必须被恰好一辆卡车访问
        for j in selected_lockers:
            model_scenario.addConstr(
                gp.quicksum(x_truck_scenario[i, j, v] for i in nodes if i != j for v in range(self.max_trucks)) == 1
            )

        # 流平衡约束
        for v in range(self.max_trucks):
            for i in nodes:
                inflow = gp.quicksum(x_truck_scenario[j, i, v] for j in nodes if j != i)
                outflow = gp.quicksum(x_truck_scenario[i, j, v] for j in nodes if j != i)
                model_scenario.addConstr(inflow == outflow)

        # 卡车使用约束
        for v in range(self.max_trucks):
            model_scenario.addConstr(
                gp.quicksum(x_truck_scenario[0, j, v] for j in selected_lockers) <= y_truck_scenario[v]
            )

        # 卡车容量约束
        for v in range(self.max_trucks):
            total_demand_on_truck = gp.quicksum(
                gp.quicksum(x_scenario[i, j] for i in self.customers) *
                gp.quicksum(x_truck_scenario[j, l, v] for l in nodes if l != j)
                for j in selected_lockers
            )
            model_scenario.addConstr(total_demand_on_truck <= self.truck_capacity * y_truck_scenario[v])

        # 子回路消除约束
        for v in range(self.max_trucks):
            for i in selected_lockers:
                for j in selected_lockers:
                    if i != j:
                        model_scenario.addConstr(
                            u_truck_scenario[i, v] - u_truck_scenario[j, v] + len(selected_lockers) * x_truck_scenario[i, j, v]
                            <= len(selected_lockers) - 1
                        )

    def _add_second_stage_cvrp_constraints(self, model_sp, selected_lockers, locker_actual_demands, x_truck_sp, y_truck_sp, u_truck_sp, nodes):
        """为第二阶段子问题添加CVRP约束"""
        # 每个储物柜必须被恰好一辆卡车访问
        for j in selected_lockers:
            model_sp.addConstr(
                gp.quicksum(x_truck_sp[i, j, v] for i in nodes if i != j for v in range(self.max_trucks)) == 1
            )

        # 流平衡约束
        for v in range(self.max_trucks):
            for i in nodes:
                inflow = gp.quicksum(x_truck_sp[j, i, v] for j in nodes if j != i)
                outflow = gp.quicksum(x_truck_sp[i, j, v] for j in nodes if j != i)
                model_sp.addConstr(inflow == outflow)

        # 卡车使用约束
        for v in range(self.max_trucks):
            model_sp.addConstr(
                gp.quicksum(x_truck_sp[0, j, v] for j in selected_lockers) <= y_truck_sp[v]
            )

        # 卡车容量约束
        for v in range(self.max_trucks):
            total_demand_on_truck = gp.quicksum(
                locker_actual_demands.get(j, 0) * gp.quicksum(x_truck_sp[j, l, v] for l in nodes if l != j)
                for j in selected_lockers
            )
            model_sp.addConstr(total_demand_on_truck <= self.truck_capacity * y_truck_sp[v])

        # 子回路消除约束
        for v in range(self.max_trucks):
            for i in selected_lockers:
                for j in selected_lockers:
                    if i != j:
                        model_sp.addConstr(
                            u_truck_sp[i, v] - u_truck_sp[j, v] + len(selected_lockers) * x_truck_sp[i, j, v]
                            <= len(selected_lockers) - 1
                        )

    def _add_second_stage_constraints(self, model_sp, demand_scenario, selected_lockers, n_star,
                                    x_sp, z_sp, x_truck_sp, y_truck_sp, u_truck_sp):
        """为第二阶段子问题添加约束"""
        nodes = [0] + selected_lockers

        # 储物柜分配约束
        for i in self.customers:
            model_sp.addConstr(gp.quicksum(x_sp.get((i, j_site), 0) for j_site in selected_lockers) <= demand_scenario[i])
            for j_site in selected_lockers:
                model_sp.addConstr(x_sp[i, j_site] <= demand_scenario[i] * z_sp[i, j_site])
                model_sp.addConstr(x_sp[i, j_site] >= z_sp[i, j_site])
                if (i, j_site) in self.distance:
                    model_sp.addConstr(2 * self.distance[i, j_site] * z_sp[i, j_site] <= self.max_flight_distance)

        for j_site in selected_lockers:
            # 无人机服务能力约束
            total_drone_hours_needed_j_sp = gp.quicksum(
                x_sp.get((i, j_site), 0) * ((2 * self.distance.get((i, j_site), self.BIG_M) / self.drone_speed) + self.loading_time)
                for i in self.customers
            )
            model_sp.addConstr(total_drone_hours_needed_j_sp <= n_star.get(j_site, 0) * self.H_drone_working_hours_per_day)

            # 储物柜容量约束
            model_sp.addConstr(gp.quicksum(x_sp.get((i, j_site), 0) for i in self.customers) <= self.Q_locker_capacity[j_site])

        # CVRP约束
        # 每个储物柜必须被恰好一辆卡车访问
        for j in selected_lockers:
            model_sp.addConstr(
                gp.quicksum(x_truck_sp[i, j, v] for i in nodes if i != j for v in range(self.max_trucks)) == 1
            )

        # 流平衡约束
        for v in range(self.max_trucks):
            for i in nodes:
                inflow = gp.quicksum(x_truck_sp[j, i, v] for j in nodes if j != i)
                outflow = gp.quicksum(x_truck_sp[i, j, v] for j in nodes if j != i)
                model_sp.addConstr(inflow == outflow)

        # 卡车使用约束
        for v in range(self.max_trucks):
            model_sp.addConstr(
                gp.quicksum(x_truck_sp[0, j, v] for j in selected_lockers) <= y_truck_sp[v]
            )

        # 卡车容量约束
        for v in range(self.max_trucks):
            total_demand_on_truck = gp.quicksum(
                gp.quicksum(x_sp[i, j] for i in self.customers) *
                gp.quicksum(x_truck_sp[j, l, v] for l in nodes if l != j)
                for j in selected_lockers
            )
            model_sp.addConstr(total_demand_on_truck <= self.truck_capacity * y_truck_sp[v])

        # 子回路消除约束
        for v in range(self.max_trucks):
            for i in selected_lockers:
                for j in selected_lockers:
                    if i != j:
                        model_sp.addConstr(
                            u_truck_sp[i, v] - u_truck_sp[j, v] + len(selected_lockers) * x_truck_sp[i, j, v]
                            <= len(selected_lockers) - 1
                        )

    def _check_saa_termination_criteria(self):
        """检查SAA终止条件"""
        import scipy.stats as stats

        # 过滤掉inf值进行统计
        valid_obj_k = [val for val in self.saa_objective_values_k if val != float('inf')]
        valid_ub_k_prime = [val for val in self.saa_upper_bounds_k_prime if val != float('inf')]

        # 至少需要2次有效复制才能计算统计量
        if len(valid_obj_k) < 2 or len(valid_ub_k_prime) < 2:
            return False, f"有效解数量不足 ({len(valid_obj_k)}, {len(valid_ub_k_prime)} < 2)，需要至少2次有效复制"

        # 找到当前最佳解的信息
        best_validation_cost = float('inf')
        for i, ub_val in enumerate(self.saa_upper_bounds_k_prime):
            if ub_val != float('inf') and ub_val < best_validation_cost:
                best_validation_cost = ub_val

        # 计算正确的SAA统计量
        m = len(valid_obj_k)  # 当前迭代次数
        statistical_lower_bound = np.mean(valid_obj_k)  # cost_N^m = (1/m) * Σ cost_N^{m'}
        statistical_upper_bound = best_validation_cost  # UB_{N'}(ŝ) (最佳解的验证成本)

        # SAA Gap计算 (按正确的论文公式)
        saa_gap = statistical_upper_bound - statistical_lower_bound
        gap_percent = saa_gap / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # 计算下界方差：δ²(cost_N) = (1/[m(m-1)]) * Σ(cost_N^{m'} - cost_N^m)²
        if m > 1:
            variance_sum = sum((cost_val - statistical_lower_bound)**2 for cost_val in valid_obj_k)
            var_lower_bound = variance_sum / (m * (m - 1))
            std_lower_bound = np.sqrt(var_lower_bound)
        else:
            var_lower_bound = 0
            std_lower_bound = 0

        # 计算上界方差：δ²_{N'}(ŝ) = (1/[N'(N'-1)]) * Σ(Q(ŝ,ξ_n) - cost_{N'}(ŝ))²
        # 这里简化处理，假设每个复制的验证样本数量相同
        N_prime = SAA_SAMPLES_K_PRIME
        if len(valid_ub_k_prime) > 1:
            # 使用最佳解对应的上界值计算方差
            best_ub_index = valid_ub_k_prime.index(best_validation_cost)
            # 简化计算：使用所有上界值的方差除以验证样本数量
            var_upper_bound = np.var(valid_ub_k_prime, ddof=1) / N_prime
        else:
            var_upper_bound = 0

        # Gap的总方差：δ²_Gap(ŝ) = δ²(cost_N) + δ²_{N'}(ŝ)
        gap_variance = var_lower_bound + var_upper_bound
        gap_std = np.sqrt(gap_variance) if gap_variance > 0 else 0
        variance_percent = gap_variance / statistical_upper_bound if statistical_upper_bound > 0 else float('inf')

        # SAA终止条件检查 (必须同时满足两个条件)
        # 条件1: 相对差距阈值 Gap_{N,N'}(ŝ) / cost_{N'}(ŝ) ≤ ε (如 ε = 3%)
        relative_gap_condition = (gap_percent <= SAA_GAP_TOLERANCE_PERCENT and saa_gap >= 0)

        # 条件2: 方差阈值 δ²_Gap(ŝ) / cost_{N'}(ŝ) ≤ ε' (如 ε' = 5%)
        variance_condition = variance_percent <= SAA_VARIANCE_TOLERANCE_PERCENT

        # 主要终止条件：必须同时满足相对差距阈值和方差阈值
        should_terminate = relative_gap_condition and variance_condition

        # 如果gap为负，说明统计估计不稳定，不应该终止
        if saa_gap < 0:
            should_terminate = False

        # 计算下界变异系数
        cv_lower_bound = std_lower_bound / statistical_lower_bound if statistical_lower_bound > 0 else float('inf')

        # 构建详细的状态信息
        gap_condition_status = "✓" if relative_gap_condition else "✗"
        variance_condition_status = "✓" if variance_condition else "✗"

        gap_info = (f"相对差距: {gap_percent:.2%} {gap_condition_status} (阈值: ≤{SAA_GAP_TOLERANCE_PERCENT:.0%}), "
                   f"方差比例: {variance_percent:.2%} {variance_condition_status} (阈值: ≤{SAA_VARIANCE_TOLERANCE_PERCENT:.0%}), "
                   f"LB(m={m}): {statistical_lower_bound:.2f}, "
                   f"UB: {statistical_upper_bound:.2f}, "
                   f"Gap: {saa_gap:.2f}, "
                   f"δ²_Gap: {gap_variance:.4f}")

        return should_terminate, gap_info

    def _get_gurobi_status_message(self, status):
        """获取Gurobi求解状态的详细说明"""
        status_messages = {
            2: "最优解 (OPTIMAL)",
            3: "不可行 (INFEASIBLE)",
            4: "不可行或无界 (INF_OR_UNBD)",
            5: "无界 (UNBOUNDED)",
            6: "目标值超出截断值 (CUTOFF)",
            7: "达到迭代限制 (ITERATION_LIMIT)",
            8: "达到节点限制 (NODE_LIMIT)",
            9: "达到时间限制 (TIME_LIMIT)",
            10: "达到解数量限制 (SOLUTION_LIMIT)",
            11: "被中断 (INTERRUPTED)",
            12: "数值困难 (NUMERIC)",
            13: "次优解 (SUBOPTIMAL)",
            14: "求解中 (INPROGRESS)",
            15: "达到用户目标限制 (USER_OBJ_LIMIT)"
        }
        return status_messages.get(status, f"未知状态({status})")

    def _print_saa_solution(self, saa_solution_dict: Dict):
        if not saa_solution_dict:
            print("SAA 未找到有效解。")
            return

        print("\n" + "=" * 60 + "\nSAA 优化结果 (Gurobi精确求解)\n" + "=" * 60)

        # 提取解决方案信息
        y_star_final = saa_solution_dict.get('selected_lockers_y', {})
        n_star_final = saa_solution_dict.get('drone_allocations_n', {})
        selected_lockers_print = [j for j, val in y_star_final.items() if val > 0.5]
        total_objective = saa_solution_dict.get('objective_value_k_prime_estimate', 0)
        truck_cost = saa_solution_dict.get('truck_cost_k_prime_estimate', 0)

        # 计算成本分解 - 需要从other_costs中分离出无人机运输成本和惩罚成本
        locker_fixed_cost = sum(self.locker_fixed_cost[j] for j in selected_lockers_print)
        drone_deployment_cost = sum(self.drone_cost * n_star_final.get(j, 0) for j in selected_lockers_print)

        # 计算无人机运输成本和惩罚成本（从第二阶段成本中分离）
        # other_costs = 无人机运输成本 + 惩罚成本
        other_costs = total_objective - locker_fixed_cost - drone_deployment_cost - truck_cost

        # 直接使用已有的精确计算结果进行成本分解
        # Gurobi求解器在验证阶段已经精确计算了各项成本
        drone_transport_cost = 0
        penalty_cost = 0

        # 从验证阶段的精确计算结果中获取成本分解
        if hasattr(self, 'fixed_validation_samples') and self.fixed_validation_samples:
            total_transport = 0
            total_penalty = 0

            print(f"  正在使用精确求解结果进行成本分解（基于{len(self.fixed_validation_samples)}个验证样本）...")
            sample_count = len(self.fixed_validation_samples)
            valid_scenarios = 0

            for idx, scenario in enumerate(self.fixed_validation_samples):
                # 使用Gurobi精确求解该场景的客户分配和成本
                scenario_result = self._solve_scenario_with_fixed_first_stage(
                    y_star_final, n_star_final, selected_lockers_print, scenario
                )

                if scenario_result is not None:
                    scenario_transport = scenario_result['transport_cost']
                    scenario_penalty = scenario_result['penalty_cost']

                    total_transport += scenario_transport
                    total_penalty += scenario_penalty
                    valid_scenarios += 1

                    # 调试信息：显示前几个场景的详细信息
                    if idx < 3:
                        total_demand_scenario = sum(scenario.values())
                        print(f"    场景{idx+1}: 总需求={total_demand_scenario:.1f}, 运输成本={scenario_transport:.2f}, 惩罚成本={scenario_penalty:.2f}")

                # 每处理500个场景显示一次进度
                if (idx + 1) % 500 == 0:
                    print(f"    已处理 {idx + 1}/{sample_count} 个场景...")

            if valid_scenarios > 0:
                drone_transport_cost = total_transport / valid_scenarios
                penalty_cost = total_penalty / valid_scenarios
                print(f"  精确计算结果: 无人机运输成本={drone_transport_cost:.2f}, 惩罚成本={penalty_cost:.2f} (基于{valid_scenarios}个有效场景)")
            else:
                print(f"  ⚠ 无有效场景结果，使用近似估算")
                drone_transport_cost = other_costs * 0.8
                penalty_cost = other_costs * 0.2
        else:
            # 回退：假设other_costs中惩罚成本占比较小
            # 这是一个近似估算
            drone_transport_cost = other_costs * 0.8  # 假设80%是运输成本
            penalty_cost = other_costs * 0.2  # 假设20%是惩罚成本
            print(f"  使用近似估算: 无人机运输成本={drone_transport_cost:.2f}, 惩罚成本={penalty_cost:.2f}")

        # 重新计算分类后的成本
        drone_total_cost = drone_deployment_cost + drone_transport_cost  # 无人机成本（部署+运输）
        truck_total_cost = truck_cost  # 卡车成本（固定+运输）
        penalty_total_cost = penalty_cost  # 惩罚成本

        # 主要结果展示
        print(f"  📊 核心指标:")
        print(f"    开放储物柜数量: {len(selected_lockers_print)} 个")
        print(f"    总成本: {total_objective:.2f} 元/天")
        print(f"    无人机成本(部署+运输): {drone_total_cost:.2f} 元/天 ({drone_total_cost/total_objective*100:.1f}%)")
        print(f"    卡车成本(固定+运输): {truck_total_cost:.2f} 元/天 ({truck_total_cost/total_objective*100:.1f}%)")

        print(f"\n  💰 详细成本分解:")
        print(f"    储物柜固定成本: {locker_fixed_cost:.2f} 元/天 ({locker_fixed_cost/total_objective*100:.1f}%)")
        print(f"    无人机成本(部署+运输): {drone_total_cost:.2f} 元/天 ({drone_total_cost/total_objective*100:.1f}%)")
        print(f"    卡车成本(固定+运输): {truck_total_cost:.2f} 元/天 ({truck_total_cost/total_objective*100:.1f}%)")
        print(f"    其他成本(惩罚): {penalty_total_cost:.2f} 元/天 ({penalty_total_cost/total_objective*100:.1f}%)")

        print(f"\n  🏪 储物柜配置:")
        print(f"    选定站点: {selected_lockers_print}")
        total_drones = 0
        for j_site_p in selected_lockers_print:
            drones = round(n_star_final.get(j_site_p,0))
            total_drones += drones
            print(f"    位置 {j_site_p}: {drones} 架无人机")
        print(f"    无人机总数: {total_drones} 架")

        print(f"\n  📈 运营指标:")
        print(f"    总期望需求量: {sum(self.expected_demand.values()):.2f} 订单/天")
        print(f"    平均每储物柜服务: {sum(self.expected_demand.values())/len(selected_lockers_print):.2f} 订单/天")
        print(f"    平均每无人机服务: {sum(self.expected_demand.values())/total_drones:.2f} 订单/天" if total_drones > 0 else "    平均每无人机服务: N/A")

        # 【新增】求解质量总结
        if hasattr(self, 'saa_solve_status_summary'):
            status_summary = self.saa_solve_status_summary
            optimal_count = status_summary.get('optimal', 0)
            time_limit_count = status_summary.get('time_limit', 0)
            total_replications = status_summary.get('total', 0)

            print(f"\n  🎯 求解质量:")
            print(f"    总复制次数: {total_replications}")
            print(f"    达到最优解的复制: {optimal_count} ({optimal_count/total_replications*100:.1f}%)")
            if time_limit_count > 0:
                print(f"    因时间限制终止的复制: {time_limit_count} ({time_limit_count/total_replications*100:.1f}%)")
                print(f"    ⚠ 建议: 增加时间限制以获得更高质量的解")

            if optimal_count == total_replications:
                print(f"    ✓ 所有复制都找到了最优解，结果可信度高")
            elif optimal_count >= total_replications * 0.8:
                print(f"    ✓ 大部分复制找到最优解，结果质量较好")
            else:
                print(f"    ⚠ 较少复制找到最优解，建议调整求解参数")

        print("\n  📝 模型说明:")
        print("    第一阶段决策：储物柜选址和无人机配置（已确定）")
        print("    第二阶段决策：根据实际需求场景动态优化客户分配和配送")
        print("    成本为考虑需求不确定性后的期望日均成本")

        if CLUSTERING_AVAILABLE:
            viz_solution_saa = {
                'selected_lockers': {j:True for j in selected_lockers_print},
                'customer_assignments_primary': {},  # 空的，因为客户分配在第二阶段
                'unassigned_customers_by_u': list(self.customers),  # 所有客户都在第二阶段分配
                'drone_allocations': {j: round(n_star_final.get(j,0)) for j in selected_lockers_print},
                'objective_value': saa_solution_dict.get('objective_value_k_prime_estimate', 0.0)
            }
            print("\n  显示SAA最终解的可视化:")
            print("  ↳ 注意: 图中只显示第一阶段决策（储物柜选址和无人机配置）")
            print("  ↳ 客户分配将在第二阶段根据实际需求场景动态优化")
            print("  ↳ 上述成本是考虑需求不确定性后的期望值")
            self.visualize_solution(viz_solution_saa)

    # 注意：原有的确定性方法已被SAA方法替代
    # 如果需要确定性求解，可以设置SAA_SAMPLES_K=1, SAA_MAX_REPLICATIONS_M=1

    def get_solution(self):
        """获取SAA解决方案的结构化字典（兼容性方法）"""
        print("警告: 请使用solve_saa()方法获取SAA解决方案")
        return None

    def visualize_solution(self, solution: Dict):
        if not CLUSTERING_AVAILABLE: return
        if not solution: print("无解决方案可供可视化。"); return
        temp_solution_for_viz = solution.copy()
        primary_assignments = solution.get('customer_assignments_primary', {})

        # 修改：支持多储物柜分配的可视化
        # 直接传递完整的储物柜列表给可视化函数
        customer_assignments_viz = {}
        for cust, lockers in primary_assignments.items():
            if lockers: # 如果有分配的储物柜
                # 直接传递储物柜列表，visualization.py会自动处理多储物柜连线
                customer_assignments_viz[cust] = lockers

        temp_solution_for_viz['customer_assignments'] = customer_assignments_viz

        customer_service_modes = {}
        for customer_id in customer_assignments_viz: # 使用调整后的键
            customer_service_modes[customer_id] = 0
        temp_solution_for_viz['customer_service_modes'] = customer_service_modes

        # unassigned_customers 可能需要从 solution['unassigned_customers_by_u'] 获取
        if 'unassigned_customers_by_u' in solution:
            temp_solution_for_viz['unassigned_customers'] = solution['unassigned_customers_by_u']

        # 移除可能引起混淆的旧键
        if 'customer_assignments_primary' in temp_solution_for_viz: del temp_solution_for_viz['customer_assignments_primary']
        if 'unassigned_customers_by_u' in temp_solution_for_viz and 'unassigned_customers' in temp_solution_for_viz :
             if temp_solution_for_viz['unassigned_customers_by_u'] == temp_solution_for_viz['unassigned_customers']:
                 del temp_solution_for_viz['unassigned_customers_by_u']

        plt_fig = visualize_solution(
            solution=temp_solution_for_viz,
            customer_coords=self.customer_coords,
            site_coords=self.site_coords,
            title="SAA 无人机配送网络规划 (第一阶段决策)"
        )
        if plt_fig:
            import matplotlib.pyplot as plt
            plt.show(block=False) # 使用 block=False 避免阻塞后续代码，如果需要交互则去掉
            plt.pause(1) # 暂停一下，确保图像显示

    def _print_detailed_customer_assignment_analysis(self, saa_solution_dict: Dict, selected_lockers: List[int]):
        """
        输出详细的客户分配方案分析
        """
        print(f"\n  详细客户分配方案分析:")
        print(f"  ============================================================")

        y_star = saa_solution_dict.get('selected_lockers_y', {})
        n_star = saa_solution_dict.get('drone_allocations_n', {})

        # 使用第一阶段分配（基于期望需求）进行分析
        first_stage_assignment = self._solve_first_stage_assignment_for_analysis(y_star, n_star, selected_lockers)

        if first_stage_assignment:
            print(f"  基于期望需求的第一阶段客户分配:")

            # 按储物柜分组显示分配
            for j in selected_lockers:
                assigned_customers = []
                total_assigned_demand = 0

                for i in self.customers:
                    assigned_qty = first_stage_assignment.get((i, j), 0)
                    if assigned_qty > 0.1:  # 避免浮点数精度问题
                        assigned_customers.append((i, assigned_qty))
                        total_assigned_demand += assigned_qty

                print(f"    储物柜 {j}:")
                print(f"      总分配需求: {total_assigned_demand:.1f}")
                print(f"      容量利用率: {total_assigned_demand/self.Q_locker_capacity[j]*100:.1f}%")
                print(f"      分配的客户: {len(assigned_customers)} 个")

                if assigned_customers:
                    print(f"      详细分配:")
                    for customer_id, qty in sorted(assigned_customers):
                        expected_demand = self.expected_demand[customer_id]
                        allocation_rate = qty / expected_demand * 100 if expected_demand > 0 else 0
                        distance = self.distance.get((customer_id, j), 0)
                        print(f"        客户 {customer_id}: {qty:.1f}/{expected_demand:.1f} ({allocation_rate:.1f}%), 距离: {distance:.1f}km")
                else:
                    print(f"      无客户分配")
                print()

            # 分析未分配的客户
            unassigned_customers = []
            for i in self.customers:
                total_assigned = sum(first_stage_assignment.get((i, j), 0) for j in selected_lockers)
                expected_demand = self.expected_demand[i]
                if total_assigned < expected_demand - 0.1:
                    shortage = expected_demand - total_assigned
                    unassigned_customers.append((i, shortage, expected_demand))

            if unassigned_customers:
                print(f"  未完全分配的客户 ({len(unassigned_customers)} 个):")
                total_unassigned_demand = 0
                for customer_id, shortage, expected_demand in sorted(unassigned_customers):
                    print(f"    客户 {customer_id}: 短缺 {shortage:.1f}/{expected_demand:.1f} ({shortage/expected_demand*100:.1f}%)")
                    total_unassigned_demand += shortage
                print(f"  总未分配需求: {total_unassigned_demand:.1f}")
                print(f"  未分配比例: {total_unassigned_demand/sum(self.expected_demand.values())*100:.1f}%")
            else:
                print(f"  ✓ 所有客户的期望需求都已完全分配")

        else:
            print(f"  ⚠ 无法获取第一阶段客户分配信息")

    def _solve_first_stage_assignment_for_analysis(self, y_star: Dict, n_star: Dict, selected_lockers: List[int]) -> Dict:
        """
        为分析目的求解第一阶段的客户分配问题（基于期望需求）
        """
        try:
            import gurobipy as gp
            from gurobipy import GRB

            model_fs = gp.Model("FirstStageAssignmentAnalysis")
            model_fs.setParam('OutputFlag', 0)
            model_fs.setParam('Threads', 1)

            # 决策变量
            x_fs = {}
            for i in self.customers:
                for j in selected_lockers:
                    x_fs[i, j] = model_fs.addVar(vtype=GRB.INTEGER, lb=0, name=f"x_fs_{i}_{j}")

            # 目标函数：最小化运输成本和惩罚成本
            transport_cost_fs = gp.quicksum(
                2 * self.transport_unit_cost * self.distance.get((i, j), self.BIG_M) * x_fs[i, j]
                for i in self.customers for j in selected_lockers
            )
            penalty_cost_fs = gp.quicksum(
                self.penalty_cost_unassigned * (self.expected_demand[i] - gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers))
                for i in self.customers
            )
            model_fs.setObjective(transport_cost_fs + penalty_cost_fs, GRB.MINIMIZE)

            # 约束
            for i in self.customers:
                # 分配量不超过期望需求
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for j in selected_lockers) <= self.expected_demand[i])
                for j in selected_lockers:
                    # 飞行距离限制
                    if (i, j) in self.distance:
                        model_fs.addConstr(2 * self.distance[i, j] * x_fs[i, j] <= self.max_flight_distance * self.expected_demand[i])

            for j in selected_lockers:
                # 无人机服务能力约束
                total_hours_needed_j = gp.quicksum(
                    x_fs.get((i, j), 0) * ((2 * self.distance.get((i, j), self.BIG_M) / self.drone_speed) + self.loading_time)
                    for i in self.customers
                )
                model_fs.addConstr(total_hours_needed_j <= n_star.get(j, 0) * self.H_drone_working_hours_per_day)
                # 储物柜容量约束
                model_fs.addConstr(gp.quicksum(x_fs.get((i, j), 0) for i in self.customers) <= self.Q_locker_capacity[j])

            model_fs.optimize()

            if model_fs.status == GRB.OPTIMAL:
                assignment = {}
                for i in self.customers:
                    for j in selected_lockers:
                        if (i, j) in x_fs:
                            assignment[i, j] = x_fs[i, j].X
                del model_fs
                return assignment
            else:
                del model_fs
                return None

        except Exception as e:
            print(f"  第一阶段分配分析失败: {str(e)}")
            return None




def create_integrated_saa_example_instance(
        num_customers: int = 15,
        num_sites: int = 6,
        use_kmeans_clustering: bool = False,
        demand_level: str = "medium",
        locker_cost_level: str = "medium",
        drone_cost_level: str = "medium",
        drone_transport_cost_level: str = "medium",
        use_generated_distances: bool = True,
        random_seed: int = RANDOM_SEED,
        # 新增年化成本计算参数
        annual_interest_rate: float = 0.04,  # IR: 年利率 4%
        equipment_life_years: int = 10,      # T_life: 设备生命周期 10年
        operating_days_per_year: int = 365   # D_year: 年运营天数
):
    """
    创建整体优化SAA示例问题的参数实例

    修正版：使用年化成本计算，确保时间单位一致性
    - 储物柜和无人机成本从一次性投资转换为日均固定成本
    - 所有成本项统一使用日成本单位
    """
    local_random = random.Random(random_seed)
    customers_list = list(range(1, num_customers + 1))
    sites_list = list(range(1, num_sites + 1))

    # 生成客户坐标
    customer_coords_dict = {i: (local_random.uniform(0, 15), local_random.uniform(0, 15)) for i in customers_list}

    # 生成储物柜站点坐标
    if use_kmeans_clustering and CLUSTERING_AVAILABLE:
        site_coords_dict = generate_locker_sites_with_kmeans(customer_coords_dict, num_sites, random_state=random_seed)
    else:
        site_coords_dict = {j: (local_random.uniform(3, 12), local_random.uniform(3, 12)) for j in sites_list}

    # 生成客户期望需求（用于SAA）
    demand_params_dict = {"low": (2, 4), "medium": (4, 6), "high": (6, 8)}.get(demand_level, (2, 3))
    expected_demand_dict = {c: local_random.randint(demand_params_dict[0], demand_params_dict[1]) for c in customers_list}

    # === 修正版成本计算：统一时间单位为日成本 ===

    # 第1步：计算资本回收因子 (Capital Recovery Factor)
    IR = annual_interest_rate
    T_life = equipment_life_years
    capital_recovery_factor = (IR * (1 + IR)**T_life) / ((1 + IR)**T_life - 1)

    print(f"  [g_i.py] 成本计算参数:")
    print(f"    年利率 (IR): {IR*100:.1f}%")
    print(f"    设备生命周期 (T_life): {T_life}年")
    print(f"    资本回收因子: {capital_recovery_factor:.6f}")
    print(f"    年运营天数: {operating_days_per_year}天")

    # 第2步：储物柜初始建设成本 -> 日均固定成本
    locker_initial_cost_val = {"low": 10000, "medium": 15000, "high": 20000}.get(locker_cost_level, 10000)
    locker_annual_cost = locker_initial_cost_val * capital_recovery_factor  # c_l^a
    locker_daily_cost = locker_annual_cost / operating_days_per_year        # c_l^daily
    locker_fixed_cost_dict = {s: locker_daily_cost for s in sites_list}

    print(f"  [g_i.py] 储物柜成本转换:")
    print(f"    初始建设成本: {locker_initial_cost_val:,.0f}元")
    print(f"    年化成本: {locker_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {locker_daily_cost:.2f}元/天")

    # 第3步：无人机初始采购成本 -> 日均固定成本
    drone_initial_cost_val = {"low": 3000, "medium": 4000, "high": 5000}.get(drone_cost_level, 3000)
    drone_annual_cost = drone_initial_cost_val * capital_recovery_factor     # c_d^a
    drone_daily_cost = drone_annual_cost / operating_days_per_year           # c_d^daily
    drone_cost_val_param = drone_daily_cost

    print(f"  [g_i.py] 无人机成本转换:")
    print(f"    初始采购成本: {drone_initial_cost_val:,.0f}元")
    print(f"    年化成本: {drone_annual_cost:,.2f}元/年")
    print(f"    日均固定成本: {drone_daily_cost:.2f}元/天")

    # 无人机运输单位成本
    transport_unit_cost_val_param = {"low": 0.01, "medium": 0.02, "high": 0.03}.get(drone_transport_cost_level, 0.01)

    # 系统参数
    drone_speed_param = 50.0
    loading_time_param = 300.0 / 3600.0
    max_flight_distance_param = 15.0
    H_drone_working_hours_per_day_param = 8.0
    penalty_cost_unassigned_param = 40.0  # 降低惩罚成本，使其更合理

    # 储物柜最大服务能力
    avg_locker_capacity_val = 30
    Q_locker_capacity_dict = {s: avg_locker_capacity_val for s in sites_list}

    # 卡车运输相关参数
    depot_coord_param = (0, 0)  # 仓库坐标设置为(0,0)
    truck_capacity_param = 90
    truck_fixed_cost_param = 100
    truck_km_cost_param = 0.5

    # 生成距离矩阵
    distance_matrix_dict = {}
    if use_generated_distances:
        for c_id, c_coord in customer_coords_dict.items():
            for s_id, s_coord in site_coords_dict.items():
                dist = math.sqrt((c_coord[0] - s_coord[0]) ** 2 + (c_coord[1] - s_coord[1]) ** 2)
                distance_matrix_dict[c_id, s_id] = round(dist, 2)
    else:
        for c_id in customers_list:
            for s_id in sites_list:
                distance_matrix_dict[c_id, s_id] = local_random.uniform(1.0, 10.0)

    # === 成本单位统一性验证 ===
    print(f"\n  [g_i.py] 成本单位统一性检查:")
    print(f"    储物柜固定成本: {locker_daily_cost:.2f} 元/天")
    print(f"    无人机固定成本: {drone_daily_cost:.2f} 元/天")
    print(f"    无人机运输成本: {transport_unit_cost_val_param:.3f} 元/公里 (按实际运输量)")
    print(f"    卡车固定成本: {truck_fixed_cost_param} 元/天")
    print(f"    卡车运输成本: {truck_km_cost_param} 元/公里 (按实际运输量)")
    print(f"    ✓ 所有固定成本已统一为日成本单位")
    print(f"    ✓ 运输成本保持按实际使用量计费")

    return {
        'customers': customers_list,
        'sites': sites_list,
        'expected_demand': expected_demand_dict,
        'distance_matrix': distance_matrix_dict,
        'drone_speed': drone_speed_param,
        'loading_time': loading_time_param,
        'max_flight_distance': max_flight_distance_param,
        'locker_fixed_cost': locker_fixed_cost_dict,
        'transport_unit_cost': transport_unit_cost_val_param,
        'drone_cost': drone_cost_val_param,
        'H_drone_working_hours_per_day': H_drone_working_hours_per_day_param,
        'penalty_cost_unassigned': penalty_cost_unassigned_param,
        'Q_locker_capacity': Q_locker_capacity_dict,
        'customer_coords': customer_coords_dict,
        'site_coords': site_coords_dict,
        'depot_coord': depot_coord_param,
        'truck_capacity': truck_capacity_param,
        'truck_fixed_cost': truck_fixed_cost_param,
        'truck_km_cost': truck_km_cost_param
    }


def verify_fixed_solution(fixed_lockers, num_replications=10):
    """
    验证固定储物柜方案的性能

    Args:
        fixed_lockers: 固定的储物柜列表，如 [1, 4]
        num_replications: 验证的复制次数
    """
    print(f"\n🔍 验证固定方案 {fixed_lockers} 的性能")
    print("=" * 60)
    print(f"使用Gurobi精确求解验证ALNS找到的方案")
    print(f"验证参数: {num_replications}次复制, 每次40个训练样本 + 2000个验证样本")

    # 创建问题实例（与ALNS保持一致的参数）
    problem_instance = create_integrated_saa_example_instance(
        num_customers=10,
        num_sites=4,
        use_kmeans_clustering=False,
        demand_level="low",
        locker_cost_level="low",
        drone_cost_level="low",
        drone_transport_cost_level="low",
        use_generated_distances=True,
        random_seed=RANDOM_SEED
    )

    # 创建SAA优化器
    optimizer = IntegratedDroneDeliveryOptimizerSAA()
    optimizer.setup_problem(problem_instance)

    # 生成固定验证样本（与ALNS使用相同的种子和方法）
    np.random.seed(RANDOM_SEED)
    validation_samples = []
    for _ in range(2000):
        scenario = {}
        for i in range(1, len(problem_instance['customers']) + 1):
            expected_demand = problem_instance['customers'][i-1]['expected_demand']
            scenario[i] = np.random.poisson(expected_demand)
        validation_samples.append(scenario)

    print(f"生成了2000个固定验证样本用于一致性比较")

    verification_results = []

    for replication in range(1, num_replications + 1):
        print(f"\n--- 验证复制 {replication}/{num_replications} ---")

        # 生成训练样本
        np.random.seed(RANDOM_SEED + replication)
        demand_samples = []
        for _ in range(40):
            scenario = {}
            for i in range(1, len(problem_instance['customers']) + 1):
                expected_demand = problem_instance['customers'][i-1]['expected_demand']
                scenario[i] = np.random.poisson(expected_demand)
            demand_samples.append(scenario)

        # 创建固定方案的解
        fixed_y = {j: 1 if j in fixed_lockers else 0 for j in range(1, len(problem_instance['sites']) + 1)}
        fixed_n = {j: 1 if j in fixed_lockers else 0 for j in range(1, len(problem_instance['sites']) + 1)}

        print(f"  固定储物柜配置: {fixed_y}")
        print(f"  固定无人机配置: {fixed_n}")

        # 使用Gurobi精确求解这个固定方案
        try:
            start_time = time.time()

            # 求解40个样本的成本
            small_sample_cost = optimizer.solve_saa_with_fixed_solution(
                fixed_y, fixed_n, demand_samples
            )

            # 在2000个验证样本上评估
            validation_cost = optimizer.solve_saa_with_fixed_solution(
                fixed_y, fixed_n, validation_samples
            )

            solve_time = time.time() - start_time

            verification_results.append({
                'replication': replication,
                'small_sample_cost': small_sample_cost,
                'validation_cost': validation_cost,
                'solve_time': solve_time,
                'fixed_y': fixed_y.copy(),
                'fixed_n': fixed_n.copy()
            })

            print(f"  复制 {replication} 在 40 个样本上的目标值: {small_sample_cost:.2f} 元/天")
            print(f"  复制 {replication} 在 2000 个样本上的目标值: {validation_cost:.2f} 元/天")
            print(f"  求解时间: {solve_time:.2f} 秒")

        except Exception as e:
            print(f"  复制 {replication} 求解失败: {e}")
            continue

    # 统计结果
    if verification_results:
        small_costs = [r['small_sample_cost'] for r in verification_results]
        validation_costs = [r['validation_cost'] for r in verification_results]
        solve_times = [r['solve_time'] for r in verification_results]

        print(f"\n📊 固定方案 {fixed_lockers} Gurobi验证结果汇总")
        print("=" * 60)
        print(f"  有效复制次数: {len(verification_results)}")
        print(f"  40个样本平均成本: {np.mean(small_costs):.2f} ± {np.std(small_costs):.2f} 元/天")
        print(f"  2000个样本平均成本: {np.mean(validation_costs):.2f} ± {np.std(validation_costs):.2f} 元/天")
        print(f"  最佳验证成本: {min(validation_costs):.2f} 元/天")
        print(f"  平均求解时间: {np.mean(solve_times):.2f} ± {np.std(solve_times):.2f} 秒")
        print(f"  总求解时间: {sum(solve_times):.2f} 秒")

        # 与ALNS结果详细对比
        alns_cost = 132.33
        gurobi_best = min(validation_costs)
        gurobi_avg = np.mean(validation_costs)

        print(f"\n🔍 与ALNS结果详细对比:")
        print("=" * 60)
        print(f"  ALNS方案 [1,4] 成本: {alns_cost:.2f} 元/天")
        print(f"  Gurobi验证最佳成本: {gurobi_best:.2f} 元/天")
        print(f"  Gurobi验证平均成本: {gurobi_avg:.2f} ± {np.std(validation_costs):.2f} 元/天")
        print(f"  最佳成本差异: {gurobi_best - alns_cost:.2f} 元/天 ({((gurobi_best - alns_cost)/alns_cost*100):+.1f}%)")
        print(f"  平均成本差异: {gurobi_avg - alns_cost:.2f} 元/天 ({((gurobi_avg - alns_cost)/alns_cost*100):+.1f}%)")

        # 结论
        if gurobi_best <= alns_cost + 0.01:  # 考虑数值误差
            print(f"\n✅ 验证结论: Gurobi确认ALNS找到的[1,4]方案是最优或接近最优的")
        elif gurobi_best < alns_cost * 1.05:  # 5%以内
            print(f"\n✅ 验证结论: Gurobi验证结果与ALNS非常接近，差异在可接受范围内")
        else:
            print(f"\n⚠️  验证结论: Gurobi验证成本明显高于ALNS，可能存在问题需要进一步分析")

        return verification_results
    else:
        print("❌ 所有复制都失败了")
        return []


def compare_solutions():
    """
    对比ALNS找到的[1,4]方案与Gurobi找到的[1,2,3]方案
    """
    print("\n🔍 对比分析: ALNS方案[1,4] vs Gurobi方案[1,2,3]")
    print("=" * 80)

    # ALNS方案验证
    print("\n1️⃣ 验证ALNS找到的[1,4]方案:")
    alns_results = verify_fixed_solution([1, 4], num_replications=5)

    # Gurobi方案验证
    print("\n2️⃣ 验证Gurobi找到的[1,2,3]方案:")
    gurobi_results = verify_fixed_solution([1, 2, 3], num_replications=5)

    # 对比分析
    if alns_results and gurobi_results:
        alns_costs = [r['validation_cost'] for r in alns_results]
        gurobi_costs = [r['validation_cost'] for r in gurobi_results]

        alns_best = min(alns_costs)
        alns_avg = np.mean(alns_costs)
        gurobi_best = min(gurobi_costs)
        gurobi_avg = np.mean(gurobi_costs)

        print(f"\n📊 最终对比结果:")
        print("=" * 80)
        print(f"方案对比:")
        print(f"  ALNS方案 [1,4]:     最佳 {alns_best:.2f} 元/天, 平均 {alns_avg:.2f} ± {np.std(alns_costs):.2f} 元/天")
        print(f"  Gurobi方案 [1,2,3]: 最佳 {gurobi_best:.2f} 元/天, 平均 {gurobi_avg:.2f} ± {np.std(gurobi_costs):.2f} 元/天")
        print(f"")
        print(f"成本差异:")
        print(f"  最佳成本差异: {gurobi_best - alns_best:.2f} 元/天 ({((gurobi_best - alns_best)/alns_best*100):+.1f}%)")
        print(f"  平均成本差异: {gurobi_avg - alns_avg:.2f} 元/天 ({((gurobi_avg - alns_avg)/alns_avg*100):+.1f}%)")

        # 结论
        if alns_best < gurobi_best:
            print(f"\n🏆 结论: ALNS找到的[1,4]方案优于Gurobi的[1,2,3]方案")
            print(f"   - ALNS使用更少资源(2个储物柜 vs 3个)获得更低成本")
            print(f"   - 证明了启发式算法在此问题上的优越性")
        elif abs(alns_best - gurobi_best) < 0.01:
            print(f"\n🤝 结论: 两种方案成本相当，但ALNS方案使用更少资源")
        else:
            print(f"\n📊 结论: Gurobi方案成本更低，需要进一步分析")


def main():
    """主函数：演示SAA整体优化方法"""
    main_start_time = time.time()  # 记录主程序开始时间
    print(f"设置全局随机种子: {RANDOM_SEED}")
    if CLUSTERING_AVAILABLE:
        print("聚类功能可用")
    else:
        print("聚类模块不可用，相关功能将跳过。")

    print("\n创建随机需求的示例数据 (使用期望需求)...")
    print("=" * 60)
    print("[g_i.py] 成本计算方法改进：统一时间单位")
    print("=" * 60)
    print("修正前问题：储物柜和无人机成本为一次性投资，运输成本为日常费用")
    print("修正后方案：使用资本回收因子将所有固定成本统一为日成本单位")
    print("优势：确保成本比较的合理性，避免时间单位不一致导致的决策偏差")
    print("=" * 60)

    # 创建示例实例（使用与saa_g_r.py相同的函数以确保一致性）
    instance_params = create_integrated_saa_example_instance(
        num_customers=15,  # 与3.py保持一致
        num_sites=4,       # 与saa_g_r.py保持一致
        use_kmeans_clustering=True,
        demand_level="medium",  # 修改：与ALNS保持一致
        locker_cost_level="medium",
        drone_cost_level="medium",
        drone_transport_cost_level="medium",
        random_seed=RANDOM_SEED,
        # 年化成本参数
        annual_interest_rate=0.04,    # 4% 年利率
        equipment_life_years=10,      # 10年设备生命周期
        operating_days_per_year=365   # 365天年运营天数
    )

    # 确保参数格式与g_i.py的set_parameters方法一致
    # g_i.py期望distance_matrix参数，而不是distance
    if 'distance' in instance_params:
        instance_params['distance_matrix'] = instance_params.pop('distance')

    # 移除g_i.py不需要的参数（它会在内部计算truck_distances）
    if 'truck_distances' in instance_params:
        del instance_params['truck_distances']
    if 'max_trucks' in instance_params:
        del instance_params['max_trucks']
    print("随机需求数据 (期望值) 已创建。")

    print("\n所有客户期望需求 (λᵢ_bar):")
    for customer_id_main, demand_val_main in instance_params['expected_demand'].items():
        print(f"  客户 {customer_id_main}: {demand_val_main} 订单/天 (期望)")
    total_expected_demand_val_main = sum(instance_params['expected_demand'].values())
    print(f"总期望需求: {total_expected_demand_val_main} 订单/天")

    # 创建SAA优化器并设置参数
    optimizer = IntegratedDroneDeliveryOptimizerSAA()
    optimizer.set_parameters(**instance_params)

    print("\n" + "=" * 60 + "\n求解带随机需求的无人机配送网络设计问题 (SAA)\n" + "=" * 60)
    solve_start_time = time.time()  # 记录求解开始时间

    # 【求解参数说明】
    # time_limit_per_replication: 每次复制的时间限制（秒）
    #   - 3600秒(1小时): 高质量求解，适合最终结果
    #   - 1800秒(30分钟): 平衡质量和速度
    #   - 600秒(10分钟): 快速测试
    # mip_gap_per_replication: MIP Gap容忍度
    #   - 0.01 (1%): 高精度，接近最优解
    #   - 0.02 (2%): 平衡精度和速度
    #   - 0.05 (5%): 快速求解，可能偏离最优解

    print(f"\n求解参数设置:")
    print(f"  每次复制时间限制: 3600 秒 (1小时)")
    print(f"  MIP Gap容忍度: 2% (0.02)")
    print(f"  注意: 如果求解状态显示TIME_LIMIT，建议增加时间限制")

    saa_solution = optimizer.solve_saa(
        time_limit_per_replication=3600,  # 设置时间限制为3600秒(1小时)以提高求解质量
        mip_gap_per_replication=0.1     # 降低MIP Gap以提高精度
    )

    solve_end_time = time.time()  # 记录求解结束时间

    if saa_solution:
        optimizer._print_saa_solution(saa_solution)
        print(f"\nSAA模型求解总耗时: {solve_end_time - solve_start_time:.2f} 秒")

    else:
        print("\n⚠ SAA未能找到可行解或优化失败。")

    total_time_main = time.time() - main_start_time
    print(f"\n总运行时间: {total_time_main:.2f} 秒")
    if CLUSTERING_AVAILABLE:
        print("测试完成。如果图像窗口仍然打开，请手动关闭。")


if __name__ == "__main__":
    import sys

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "verify":
            # 验证ALNS找到的[1,4]方案
            print("🔍 验证ALNS找到的[1,4]方案")
            verify_fixed_solution([1, 4], num_replications=5)
        elif sys.argv[1] == "compare":
            # 对比ALNS和Gurobi方案
            print("🔍 对比ALNS和Gurobi方案")
            compare_solutions()
        elif sys.argv[1] == "verify_gurobi":
            # 验证Gurobi找到的[1,2,3]方案
            print("🔍 验证Gurobi找到的[1,2,3]方案")
            verify_fixed_solution([1, 2, 3], num_replications=5)
        else:
            print("使用方法:")
            print("  python g_i.py           # 正常运行SAA优化")
            print("  python g_i.py verify    # 验证ALNS的[1,4]方案")
            print("  python g_i.py verify_gurobi  # 验证Gurobi的[1,2,3]方案")
            print("  python g_i.py compare   # 对比两种方案")
    else:
        # 正常运行SAA优化
        main()
