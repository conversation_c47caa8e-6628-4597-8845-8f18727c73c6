#!/usr/bin/env python3
"""
简单的混合模式测试脚本
直接运行2.py并观察混合模式的效果
"""

import subprocess
import time
import sys
import os

def modify_config_in_file(filename, new_setting):
    """修改文件中的配置"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换配置行
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.strip().startswith('USE_EXACT_SECOND_STAGE_SOLVER ='):
                if isinstance(new_setting, str):
                    lines[i] = f'USE_EXACT_SECOND_STAGE_SOLVER = "{new_setting}"  # 测试配置'
                else:
                    lines[i] = f'USE_EXACT_SECOND_STAGE_SOLVER = {new_setting}  # 测试配置'
                break
        
        # 写回文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        return True
    except Exception as e:
        print(f"修改配置失败: {e}")
        return False

def run_test(mode_name, setting):
    """运行单个测试"""
    print(f"\n{'='*60}")
    print(f"测试模式: {mode_name}")
    print(f"配置: USE_EXACT_SECOND_STAGE_SOLVER = {setting}")
    print(f"{'='*60}")
    
    # 修改配置
    if not modify_config_in_file('2.py', setting):
        return None
    
    try:
        # 运行测试
        start_time = time.time()
        result = subprocess.run([sys.executable, '2.py'], 
                              capture_output=True, 
                              text=True, 
                              timeout=300)  # 5分钟超时
        end_time = time.time()
        
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ {mode_name} 测试成功")
            print(f"⏱️  耗时: {duration:.2f} 秒")
            
            # 提取关键信息
            output_lines = result.stdout.split('\n')
            final_cost = None
            saa_time = None
            
            for line in output_lines:
                if '最终目标值' in line or 'objective_value_k_prime_estimate' in line:
                    try:
                        # 尝试提取数值
                        import re
                        numbers = re.findall(r'\d+\.?\d*', line)
                        if numbers:
                            final_cost = float(numbers[0])
                    except:
                        pass
                elif 'SAA模型求解总耗时' in line:
                    try:
                        import re
                        numbers = re.findall(r'\d+\.?\d*', line)
                        if numbers:
                            saa_time = float(numbers[0])
                    except:
                        pass
            
            return {
                'mode': mode_name,
                'success': True,
                'total_time': duration,
                'saa_time': saa_time,
                'final_cost': final_cost,
                'output': result.stdout[-1000:]  # 保留最后1000字符
            }
        else:
            print(f"❌ {mode_name} 测试失败")
            print(f"错误输出: {result.stderr[:500]}")
            return {
                'mode': mode_name,
                'success': False,
                'error': result.stderr[:500]
            }
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {mode_name} 测试超时")
        return {
            'mode': mode_name,
            'success': False,
            'error': '测试超时'
        }
    except Exception as e:
        print(f"❌ {mode_name} 测试异常: {e}")
        return {
            'mode': mode_name,
            'success': False,
            'error': str(e)
        }

def main():
    """主函数"""
    print("🚀 开始混合模式性能对比测试")
    print("📋 测试计划:")
    print("   1. False模式 (纯启发式)")
    print("   2. Hybrid模式 (混合模式)")
    print("   3. Adaptive模式 (自适应)")
    
    # 备份原始配置
    try:
        with open('2.py', 'r', encoding='utf-8') as f:
            original_content = f.read()
    except:
        print("❌ 无法读取原始配置文件")
        return
    
    # 测试配置
    test_configs = [
        ("False模式", False),
        ("Hybrid模式", "hybrid"),
        ("Adaptive模式", "adaptive")
    ]
    
    results = []
    
    try:
        for mode_name, setting in test_configs:
            print(f"\n🔄 准备测试 {mode_name}...")
            time.sleep(1)
            
            result = run_test(mode_name, setting)
            if result:
                results.append(result)
            
            time.sleep(2)  # 测试间隔
        
        # 输出汇总结果
        print(f"\n{'='*80}")
        print("📊 测试结果汇总")
        print(f"{'='*80}")
        
        successful_results = [r for r in results if r['success']]
        
        if successful_results:
            print(f"{'模式':<15} {'总耗时(s)':<12} {'SAA耗时(s)':<12} {'最终成本':<12} {'性能评价':<15}")
            print("-" * 75)
            
            # 找到基准（False模式）
            base_time = None
            base_cost = None
            for result in successful_results:
                if 'False' in result['mode']:
                    base_time = result['total_time']
                    base_cost = result['final_cost']
                    break
            
            for result in successful_results:
                saa_time_str = f"{result['saa_time']:.1f}" if result['saa_time'] else "N/A"
                cost_str = f"{result['final_cost']:.0f}" if result['final_cost'] else "N/A"
                
                # 性能评价
                perf_eval = ""
                if base_time and base_cost:
                    time_ratio = result['total_time'] / base_time
                    if result['final_cost'] and base_cost:
                        cost_ratio = result['final_cost'] / base_cost
                        if time_ratio < 1.2 and cost_ratio < 1.05:
                            perf_eval = "🏆 优秀"
                        elif time_ratio < 1.5 and cost_ratio < 1.1:
                            perf_eval = "✅ 良好"
                        elif cost_ratio < 0.95:
                            perf_eval = "💰 成本优"
                        elif time_ratio < 0.8:
                            perf_eval = "⚡ 速度优"
                        else:
                            perf_eval = "📊 一般"
                
                print(f"{result['mode']:<15} {result['total_time']:<12.1f} {saa_time_str:<12} {cost_str:<12} {perf_eval:<15}")
        
        # 失败的测试
        failed_results = [r for r in results if not r['success']]
        if failed_results:
            print(f"\n❌ 失败的测试:")
            for result in failed_results:
                print(f"   {result['mode']}: {result.get('error', '未知错误')}")
        
        # 推荐
        if len(successful_results) >= 2:
            print(f"\n🎯 推荐分析:")
            
            # 找到最快的
            fastest = min(successful_results, key=lambda x: x['total_time'])
            print(f"   ⚡ 最快模式: {fastest['mode']} ({fastest['total_time']:.1f}s)")
            
            # 找到成本最低的
            cost_results = [r for r in successful_results if r['final_cost']]
            if cost_results:
                cheapest = min(cost_results, key=lambda x: x['final_cost'])
                print(f"   💰 最优成本: {cheapest['mode']} ({cheapest['final_cost']:.0f})")
            
            # 混合模式评价
            hybrid_result = next((r for r in successful_results if 'Hybrid' in r['mode']), None)
            if hybrid_result:
                print(f"   🔄 混合模式: 旨在平衡速度与质量")
                if base_time and hybrid_result['total_time']:
                    speed_ratio = hybrid_result['total_time'] / base_time
                    if speed_ratio < 1.3:
                        print(f"      ✅ 速度表现良好 (相对False模式: {speed_ratio:.1f}x)")
                    else:
                        print(f"      ⚠️  速度有所下降 (相对False模式: {speed_ratio:.1f}x)")
    
    finally:
        # 恢复原始配置
        try:
            with open('2.py', 'w', encoding='utf-8') as f:
                f.write(original_content)
            print(f"\n✅ 已恢复原始配置")
        except:
            print(f"\n⚠️  恢复原始配置失败，请手动检查2.py文件")
    
    print(f"\n🎉 测试完成！")

if __name__ == "__main__":
    main()
