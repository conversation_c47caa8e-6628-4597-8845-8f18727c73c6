#!/usr/bin/env python3
"""
测试改进后的ALNS是否能自主发现[1,2,3]方案
"""

import sys
import os
import time
import numpy as np

# 导入ALNS模块
from alns import *

def test_autonomous_search():
    """测试ALNS的自主搜索能力"""
    print("=" * 60)
    print("测试改进后的ALNS自主搜索能力")
    print("=" * 60)
    
    # 创建问题实例
    problem_data = create_deterministic_example_instance(
        num_customers=15,
        num_sites=4,
        use_kmeans_clustering=False,
        demand_level="medium",
        random_seed=611
    )
    
    # 创建问题对象
    problem = ProblemInstance()
    problem.set_parameters(**problem_data)
    
    # 生成需求样本
    demand_samples = []
    for _ in range(40):  # 使用40个样本，与ALNS一致
        scenario = {}
        for i in problem.customers:
            scenario[i] = np.random.poisson(problem.expected_demand[i])
        demand_samples.append(scenario)
    
    # 创建ALNS求解器
    alns_solver = ALNS_Solver(problem, demand_samples)
    
    print(f"\n开始ALNS自主搜索（时间限制: 120秒）...")
    print(f"目标：验证ALNS能否自主发现[1,2,3]方案")
    print(f"改进策略：")
    print(f"  1. 智能枚举初始解生成")
    print(f"  2. 连续模式插入算子")
    print(f"  3. 精确评估函数")
    print("-" * 50)
    
    start_time = time.time()
    
    # 运行ALNS
    best_solution = alns_solver.solve(time_limit=120)
    
    solve_time = time.time() - start_time
    
    if best_solution:
        selected_lockers = [j for j, val in best_solution['y'].items() if val > 0.5]
        print(f"\n🏆 ALNS自主搜索结果:")
        print(f"   选定储物柜: {sorted(selected_lockers)}")
        print(f"   求解时间: {solve_time:.2f} 秒")
        
        # 验证是否找到了[1,2,3]
        if set(selected_lockers) == {1, 2, 3}:
            print(f"   ✅ 成功！ALNS自主发现了[1,2,3]方案！")
            print(f"   🎯 这证明了改进策略的有效性")
        else:
            print(f"   ❌ 未找到[1,2,3]方案")
            print(f"   🔍 需要进一步分析搜索过程")
            
            # 比较与[1,2,3]的成本差异
            solution_123 = {'y': {1: 1, 2: 1, 3: 1, 4: 0}, 'n': {1: 1, 2: 1, 3: 1, 4: 0}}
            try:
                cost_found = alns_solver.calculate_objective_direct(best_solution)
                cost_123 = alns_solver.calculate_objective_direct(solution_123)
                print(f"   📊 成本比较:")
                print(f"      找到的方案{sorted(selected_lockers)}: {cost_found:.2f} 元/天")
                print(f"      [1,2,3]方案: {cost_123:.2f} 元/天")
                print(f"      差异: {cost_found - cost_123:.2f} 元/天")
                
                if cost_found <= cost_123:
                    print(f"   ✅ 找到的方案成本更优或相等")
                else:
                    print(f"   ⚠️  [1,2,3]方案成本更低，搜索策略需要进一步改进")
                    
            except Exception as e:
                print(f"   ❌ 成本比较失败: {str(e)}")
    else:
        print(f"\n❌ ALNS未找到解")
    
    return best_solution

if __name__ == "__main__":
    print("开始测试改进后的ALNS自主搜索能力...")
    
    # 运行多次测试以验证稳定性
    num_tests = 1  # 可以增加到3次来验证稳定性
    success_count = 0
    
    for test_num in range(num_tests):
        if num_tests > 1:
            print(f"\n{'='*20} 测试 {test_num + 1}/{num_tests} {'='*20}")
        
        result = test_autonomous_search()
        
        if result:
            selected_lockers = [j for j, val in result['y'].items() if val > 0.5]
            if set(selected_lockers) == {1, 2, 3}:
                success_count += 1
    
    if num_tests > 1:
        print(f"\n" + "=" * 60)
        print(f"总体测试结果: {success_count}/{num_tests} 次成功找到[1,2,3]方案")
        print(f"成功率: {success_count/num_tests*100:.1f}%")
        
        if success_count == num_tests:
            print(f"🎉 完美！ALNS能够稳定地自主发现最优方案")
        elif success_count > 0:
            print(f"✅ 良好！ALNS能够发现最优方案，但需要提高稳定性")
        else:
            print(f"❌ 需要进一步改进搜索策略")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
