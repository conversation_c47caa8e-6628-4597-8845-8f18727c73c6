#!/usr/bin/env python3
"""
最小化测试：验证smart_drone_tuner是否能找到[1,2]的最优无人机配置
"""

import sys
import os
import random
import numpy as np

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def main():
    print("=" * 60)
    print("最小化测试：验证无人机配置优化")
    print("=" * 60)
    
    try:
        from alns import ALNS_Solver, create_deterministic_example_instance
        
        # 创建问题实例
        problem_params = create_deterministic_example_instance(
            num_customers=20, num_sites=4, demand_level="medium", random_seed=606
        )
        
        # 创建简化的问题类
        class SimpleProblem:
            def __init__(self, params):
                for key, value in params.items():
                    setattr(self, key, value)
                
                # 修复属性名称
                if hasattr(self, 'demand_deterministic'):
                    self.expected_demand = self.demand_deterministic
                if hasattr(self, 'distance_matrix'):
                    self.distance = self.distance_matrix
        
        problem = SimpleProblem(problem_params)
        
        # 生成少量需求样本
        np.random.seed(606)
        demand_samples = []
        for k in range(10):  # 只用10个样本加快测试
            scenario = {}
            for customer in problem.customers:
                base_demand = problem.expected_demand[customer]
                scenario[customer] = np.random.poisson(base_demand)
            demand_samples.append(scenario)
        
        print(f"创建了 {len(demand_samples)} 个需求场景")
        
        # 创建ALNS求解器
        alns_solver = ALNS_Solver(problem, demand_samples, solver_mode="adaptive")
        
        print(f"\n测试smart_drone_tuner对[1,2]配置的优化...")
        
        # 创建一个[1,2]配置，但给它次优的无人机数量
        test_solution = {
            'y': {1: 1, 2: 1, 3: 0, 4: 0},
            'n': {1: 3, 2: 2, 3: 0, 4: 0}  # 次优配置：总共5架无人机
        }
        
        print(f"输入解: 储物柜[1,2], 无人机{{1:3, 2:2}} (总计5架)")
        
        # 调用smart_drone_tuner
        print(f"\n调用smart_drone_tuner...")
        optimized_solution = alns_solver.smart_drone_tuner(test_solution, 0)
        
        if optimized_solution:
            opt_lockers = [j for j, val in optimized_solution['y'].items() if val > 0.5]
            opt_drones = {j: optimized_solution['n'][j] for j in opt_lockers}
            total_drones = sum(opt_drones.values())
            
            print(f"\n结果:")
            print(f"  优化后储物柜: {opt_lockers}")
            print(f"  优化后无人机: {opt_drones}")
            print(f"  总无人机数: {total_drones}")
            
            # 检查是否找到了理想配置
            if opt_drones == {1: 1, 2: 1}:
                print(f"  🎯 成功！找到了每储物柜1架无人机的最优配置")
                return True
            elif total_drones < 5:
                print(f"  ✅ 改善！无人机数量从5减少到{total_drones}")
                return True
            else:
                print(f"  ⚠️  未改善：仍然是{total_drones}架无人机")
                return False
        else:
            print(f"  ❌ smart_drone_tuner返回None")
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    print(f"\n" + "=" * 60)
    if success:
        print("🎉 测试通过！smart_drone_tuner能够优化无人机配置")
        print("   建议运行完整的ALNS算法验证整体效果")
    else:
        print("🔧 测试未通过，需要进一步调试smart_drone_tuner")
    print("=" * 60)
